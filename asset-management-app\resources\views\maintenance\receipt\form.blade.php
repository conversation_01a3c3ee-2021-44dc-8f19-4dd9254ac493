@extends('layouts/contentNavbarLayout')

@section('title', 'Form Penerimaan Maintenance - ' . $maintenance->maintenance_number)

@section('page-style')
<style>
.signature-pad {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  cursor: crosshair;
}

.signature-controls {
  margin-top: 10px;
}

.asset-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
}

.maintenance-info {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
}
</style>
@endsection

@section('page-script')
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
let signaturePad;

document.addEventListener('DOMContentLoaded', function() {
  // Initialize signature pad
  const canvas = document.getElementById('signature-canvas');
  signaturePad = new SignaturePad(canvas, {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    penColor: 'rgb(0, 0, 0)'
  });

  // Resize canvas
  function resizeCanvas() {
    const ratio = Math.max(window.devicePixelRatio || 1, 1);
    canvas.width = canvas.offsetWidth * ratio;
    canvas.height = canvas.offsetHeight * ratio;
    canvas.getContext('2d').scale(ratio, ratio);
    signaturePad.clear();
  }

  window.addEventListener('resize', resizeCanvas);
  resizeCanvas();

  // Clear signature
  document.getElementById('clear-signature').addEventListener('click', function() {
    signaturePad.clear();
  });

  // Form submission
  document.getElementById('receipt-form').addEventListener('submit', function(e) {
    if (!signaturePad.isEmpty()) {
      document.getElementById('signature-data').value = signaturePad.toDataURL();
    }
  });

  // Auto-fill current date and time
  const now = new Date();
  const today = now.toISOString().split('T')[0];
  const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);
  
  document.getElementById('received_date').value = today;
  document.getElementById('received_time').value = currentTime;
});
</script>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card asset-info-card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h4 class="mb-1 text-white">
                <i class="ri-file-edit-line me-2"></i>
                Form Penerimaan Maintenance
              </h4>
              <p class="text-white-50 mb-0">
                Nomor: <strong>{{ $maintenance->maintenance_number }}</strong>
              </p>
            </div>
            <div class="col-md-4 text-end">
              <span class="badge bg-white text-primary fs-6">
                {{ $maintenance->status_text }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Form Section -->
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-edit-line me-2"></i>
            Form Penerimaan Asset
          </h6>
        </div>
        <div class="card-body">
          <form action="{{ route('maintenance.receipt.store', $maintenance->id) }}" method="POST" id="receipt-form">
            @csrf
            
            <div class="row">
              <!-- Receipt Date & Time -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="received_date" class="form-label">Tanggal Penerimaan <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('received_date') is-invalid @enderror" 
                         id="received_date" name="received_date" value="{{ old('received_date') }}" required>
                  @error('received_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="received_time" class="form-label">Waktu Penerimaan <span class="text-danger">*</span></label>
                  <input type="time" class="form-control @error('received_time') is-invalid @enderror" 
                         id="received_time" name="received_time" value="{{ old('received_time') }}" required>
                  @error('received_time')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Receiver Information -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="receiver_name" class="form-label">Nama Penerima <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('receiver_name') is-invalid @enderror" 
                         id="receiver_name" name="receiver_name" value="{{ old('receiver_name', auth()->user()->name) }}" required>
                  @error('receiver_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="receiver_position" class="form-label">Jabatan Penerima <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('receiver_position') is-invalid @enderror" 
                         id="receiver_position" name="receiver_position" value="{{ old('receiver_position') }}" required>
                  @error('receiver_position')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Asset Condition -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="asset_condition" class="form-label">Kondisi Asset Setelah Maintenance <span class="text-danger">*</span></label>
                  <select class="form-select @error('asset_condition') is-invalid @enderror" 
                          id="asset_condition" name="asset_condition" required>
                    <option value="">Pilih kondisi asset...</option>
                    <option value="excellent" {{ old('asset_condition') == 'excellent' ? 'selected' : '' }}>Sangat Baik</option>
                    <option value="good" {{ old('asset_condition') == 'good' ? 'selected' : '' }}>Baik</option>
                    <option value="fair" {{ old('asset_condition') == 'fair' ? 'selected' : '' }}>Cukup</option>
                    <option value="poor" {{ old('asset_condition') == 'poor' ? 'selected' : '' }}>Buruk</option>
                    <option value="damaged" {{ old('asset_condition') == 'damaged' ? 'selected' : '' }}>Rusak</option>
                  </select>
                  @error('asset_condition')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Notes -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="notes" class="form-label">Catatan Tambahan</label>
                  <textarea class="form-control @error('notes') is-invalid @enderror" 
                            id="notes" name="notes" rows="3" 
                            placeholder="Catatan tambahan mengenai kondisi asset atau maintenance...">{{ old('notes') }}</textarea>
                  @error('notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Digital Signature -->
              <div class="col-12">
                <div class="mb-3">
                  <label class="form-label">Tanda Tangan Digital</label>
                  <div class="signature-pad bg-white" style="height: 200px; position: relative;">
                    <canvas id="signature-canvas" style="width: 100%; height: 100%;"></canvas>
                  </div>
                  <div class="signature-controls">
                    <button type="button" id="clear-signature" class="btn btn-outline-secondary btn-sm">
                      <i class="ri-eraser-line me-1"></i>
                      Hapus Tanda Tangan
                    </button>
                    <small class="text-muted ms-3">
                      <i class="ri-information-line me-1"></i>
                      Tanda tangan pada area di atas (opsional)
                    </small>
                  </div>
                  <input type="hidden" id="signature-data" name="signature">
                </div>
              </div>

              <!-- Submit Buttons -->
              <div class="col-12">
                <div class="d-flex justify-content-between">
                  <a href="{{ route('maintenance.track', $maintenance->id) }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i>
                    Kembali
                  </a>
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i>
                    Simpan Penerimaan
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Information Sidebar -->
    <div class="col-lg-4">
      
      <!-- Asset Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-computer-line me-2"></i>
            Informasi Asset
          </h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label text-muted">Kode Asset</label>
            <p class="fw-bold text-primary">{{ $maintenance->asset->asset_code }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label text-muted">Nama Asset</label>
            <p class="fw-bold">{{ $maintenance->asset->name }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label text-muted">Kategori</label>
            <p>{{ $maintenance->asset->assetCategory->name ?? '-' }}</p>
          </div>
          <div class="mb-0">
            <label class="form-label text-muted">Cabang</label>
            <p>{{ $maintenance->asset->branch->name ?? '-' }}</p>
          </div>
        </div>
      </div>

      <!-- Maintenance Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-tools-line me-2"></i>
            Detail Maintenance
          </h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label text-muted">Judul</label>
            <p class="fw-bold">{{ $maintenance->title }}</p>
          </div>
          <div class="mb-3">
            <label class="form-label text-muted">Tipe</label>
            <p><span class="badge bg-info">{{ $maintenance->maintenance_type_text }}</span></p>
          </div>
          <div class="mb-3">
            <label class="form-label text-muted">Prioritas</label>
            <p><span class="badge {{ $maintenance->priority_badge_class }}">{{ $maintenance->priority_text }}</span></p>
          </div>
          <div class="mb-0">
            <label class="form-label text-muted">Tanggal Selesai</label>
            <p>{{ $maintenance->completed_date ? $maintenance->completed_date->format('d/m/Y') : '-' }}</p>
          </div>
        </div>
      </div>

      <!-- Supplier Information -->
      @if($maintenance->supplier)
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-building-line me-2"></i>
            Supplier
          </h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label text-muted">Nama Supplier</label>
            <p class="fw-bold">{{ $maintenance->supplier->name }}</p>
          </div>
          <div class="mb-0">
            <label class="form-label text-muted">Kode Supplier</label>
            <p>{{ $maintenance->supplier->supplier_code }}</p>
          </div>
        </div>
      </div>
      @endif

    </div>
  </div>

</div>
@endsection
