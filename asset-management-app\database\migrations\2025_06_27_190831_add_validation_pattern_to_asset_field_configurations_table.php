<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('asset_field_configurations', function (Blueprint $table) {
            $table->string('validation_pattern')->nullable()->after('help_text'); // Regex pattern for validation
            $table->string('validation_message')->nullable()->after('validation_pattern'); // Custom error message
            $table->string('input_mask')->nullable()->after('validation_message'); // Input mask for formatting
            $table->string('placeholder_text')->nullable()->after('input_mask'); // Custom placeholder
            $table->integer('min_length')->nullable()->after('placeholder_text'); // Minimum length
            $table->integer('max_length')->nullable()->after('min_length'); // Maximum length
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asset_field_configurations', function (Blueprint $table) {
            $table->dropColumn([
                'validation_pattern',
                'validation_message',
                'input_mask',
                'placeholder_text',
                'min_length',
                'max_length'
            ]);
        });
    }
};
