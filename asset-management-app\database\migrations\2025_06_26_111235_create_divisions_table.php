<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('divisions', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // IT, Finance, HR, Marketing, etc
            $table->string('code', 10); // IT, FIN, HR, MKT, etc
            $table->text('description')->nullable();
            $table->string('head_name')->nullable(); // Nama kepala divisi
            $table->string('head_email')->nullable(); // Email kepala divisi
            $table->string('head_phone')->nullable(); // Telepon kepala divisi
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('divisions');
    }
};
