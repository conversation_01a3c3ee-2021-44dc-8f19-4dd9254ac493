@extends('layouts.contentNavbarLayout')

@section('title', 'Laporan Stock Opname - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('stock-opnames.show', $stockOpname) }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-file-text-line me-2"></i>
                  Laporan Stock Opname
                </h5>
                <small class="text-muted">{{ $stockOpname->title }} - {{ $stockOpname->opname_number }}</small>
              </div>
            </div>
            <div>
              <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                <i class="ri-printer-line me-2"></i>
                Cetak Laporan
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-success">
              <i class="ri-check-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1">{{ $groupedDetails['found']->count() }}</h4>
          <p class="mb-0">Asset Ditemukan</p>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-danger">
              <i class="ri-close-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1">{{ $groupedDetails['missing']->count() }}</h4>
          <p class="mb-0">Asset Hilang</p>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-warning">
              <i class="ri-error-warning-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1">{{ $groupedDetails['damaged']->count() }}</h4>
          <p class="mb-0">Asset Rusak</p>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-info">
              <i class="ri-alert-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1">{{ $groupedDetails['discrepancy']->count() }}</h4>
          <p class="mb-0">Ketidaksesuaian</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Stock Opname Info -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Stock Opname
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Nomor Opname:</td>
                  <td><strong>{{ $stockOpname->opname_number }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Judul:</td>
                  <td>{{ $stockOpname->title }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Cabang:</td>
                  <td>{{ $stockOpname->branch->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Dibuat oleh:</td>
                  <td>{{ $stockOpname->creator->name }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Tanggal Mulai:</td>
                  <td>{{ $stockOpname->start_date ? $stockOpname->start_date->format('d/m/Y H:i') : '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Tanggal Selesai:</td>
                  <td>{{ $stockOpname->end_date ? $stockOpname->end_date->format('d/m/Y H:i') : '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Status:</td>
                  <td>
                    <span class="badge bg-success">{{ ucfirst($stockOpname->status) }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Total Asset:</td>
                  <td><strong>{{ $stockOpname->total_assets }}</strong></td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Reports -->
  <div class="row">
    <div class="col-12">
      <!-- Nav tabs -->
      <ul class="nav nav-tabs" id="reportTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="found-tab" data-bs-toggle="tab" data-bs-target="#found" type="button" role="tab">
            <i class="ri-check-line me-2"></i>
            Asset Ditemukan ({{ $groupedDetails['found']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="missing-tab" data-bs-toggle="tab" data-bs-target="#missing" type="button" role="tab">
            <i class="ri-close-line me-2"></i>
            Asset Hilang ({{ $groupedDetails['missing']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="damaged-tab" data-bs-toggle="tab" data-bs-target="#damaged" type="button" role="tab">
            <i class="ri-error-warning-line me-2"></i>
            Asset Rusak ({{ $groupedDetails['damaged']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="discrepancy-tab" data-bs-toggle="tab" data-bs-target="#discrepancy" type="button" role="tab">
            <i class="ri-alert-line me-2"></i>
            Ketidaksesuaian ({{ $groupedDetails['discrepancy']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="not-scanned-tab" data-bs-toggle="tab" data-bs-target="#not-scanned" type="button" role="tab">
            <i class="ri-time-line me-2"></i>
            Belum Di-scan ({{ $groupedDetails['not_scanned']->count() }})
          </button>
        </li>
      </ul>

      <!-- Tab content -->
      <div class="tab-content" id="reportTabsContent">
        <!-- Found Assets -->
        <div class="tab-pane fade show active" id="found" role="tabpanel">
          <div class="card">
            <div class="card-body">
              @if($groupedDetails['found']->count() > 0)
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Asset</th>
                        <th>Kondisi</th>
                        <th>Lokasi Ditemukan</th>
                        <th>Waktu Scan</th>
                        <th>Scanner</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($groupedDetails['found'] as $detail)
                        <tr>
                          <td><span class="badge bg-primary">{{ $detail->asset_code }}</span></td>
                          <td>{{ $detail->asset_name }}</td>
                          <td>
                            <span class="badge {{ $detail->getConditionBadgeClass() }}">
                              {{ ucfirst($detail->physical_condition) }}
                            </span>
                          </td>
                          <td>{{ $detail->location_found ?: '-' }}</td>
                          <td>{{ $detail->scanned_at->format('d/m/Y H:i') }}</td>
                          <td>{{ $detail->scanner->name ?? '-' }}</td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-check-line ri-48px text-muted mb-3"></i>
                  <h6 class="text-muted">Tidak Ada Asset Ditemukan</h6>
                </div>
              @endif
            </div>
          </div>
        </div>

        <!-- Missing Assets -->
        <div class="tab-pane fade" id="missing" role="tabpanel">
          <div class="card">
            <div class="card-body">
              @if($groupedDetails['missing']->count() > 0)
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Asset</th>
                        <th>Status Terakhir</th>
                        <th>Catatan</th>
                        <th>Waktu Scan</th>
                        <th>Scanner</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($groupedDetails['missing'] as $detail)
                        <tr>
                          <td><span class="badge bg-primary">{{ $detail->asset_code }}</span></td>
                          <td>{{ $detail->asset_name }}</td>
                          <td>
                            <span class="badge bg-info">{{ ucfirst($detail->expected_status) }}</span>
                          </td>
                          <td>{{ $detail->condition_notes ?: '-' }}</td>
                          <td>{{ $detail->scanned_at ? $detail->scanned_at->format('d/m/Y H:i') : '-' }}</td>
                          <td>{{ $detail->scanner->name ?? '-' }}</td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-close-line ri-48px text-muted mb-3"></i>
                  <h6 class="text-muted">Tidak Ada Asset Hilang</h6>
                </div>
              @endif
            </div>
          </div>
        </div>

        <!-- Damaged Assets -->
        <div class="tab-pane fade" id="damaged" role="tabpanel">
          <div class="card">
            <div class="card-body">
              @if($groupedDetails['damaged']->count() > 0)
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Asset</th>
                        <th>Kondisi</th>
                        <th>Catatan Kerusakan</th>
                        <th>Waktu Scan</th>
                        <th>Scanner</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($groupedDetails['damaged'] as $detail)
                        <tr>
                          <td><span class="badge bg-primary">{{ $detail->asset_code }}</span></td>
                          <td>{{ $detail->asset_name }}</td>
                          <td>
                            <span class="badge {{ $detail->getConditionBadgeClass() }}">
                              {{ ucfirst($detail->physical_condition) }}
                            </span>
                          </td>
                          <td>{{ $detail->condition_notes ?: '-' }}</td>
                          <td>{{ $detail->scanned_at->format('d/m/Y H:i') }}</td>
                          <td>{{ $detail->scanner->name ?? '-' }}</td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-error-warning-line ri-48px text-muted mb-3"></i>
                  <h6 class="text-muted">Tidak Ada Asset Rusak</h6>
                </div>
              @endif
            </div>
          </div>
        </div>

        <!-- Discrepancy Assets -->
        <div class="tab-pane fade" id="discrepancy" role="tabpanel">
          <div class="card">
            <div class="card-body">
              @if($groupedDetails['discrepancy']->count() > 0)
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Asset</th>
                        <th>Ketidaksesuaian</th>
                        <th>Waktu Scan</th>
                        <th>Scanner</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($groupedDetails['discrepancy'] as $detail)
                        <tr>
                          <td><span class="badge bg-primary">{{ $detail->asset_code }}</span></td>
                          <td>{{ $detail->asset_name }}</td>
                          <td>
                            <small class="text-warning">{{ $detail->getDiscrepancySummary() }}</small>
                          </td>
                          <td>{{ $detail->scanned_at->format('d/m/Y H:i') }}</td>
                          <td>{{ $detail->scanner->name ?? '-' }}</td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-alert-line ri-48px text-muted mb-3"></i>
                  <h6 class="text-muted">Tidak Ada Ketidaksesuaian</h6>
                </div>
              @endif
            </div>
          </div>
        </div>

        <!-- Not Scanned Assets -->
        <div class="tab-pane fade" id="not-scanned" role="tabpanel">
          <div class="card">
            <div class="card-body">
              @if($groupedDetails['not_scanned']->count() > 0)
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Asset</th>
                        <th>Status Terakhir</th>
                        <th>Kategori</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($groupedDetails['not_scanned'] as $detail)
                        <tr>
                          <td><span class="badge bg-primary">{{ $detail->asset_code }}</span></td>
                          <td>{{ $detail->asset_name }}</td>
                          <td>
                            <span class="badge bg-info">{{ ucfirst($detail->expected_status) }}</span>
                          </td>
                          <td>{{ $detail->asset->assetCategory->name ?? '-' }}</td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-time-line ri-48px text-muted mb-3"></i>
                  <h6 class="text-muted">Semua Asset Sudah Di-scan</h6>
                </div>
              @endif
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
@media print {
  .btn, .nav-tabs, .card-header {
    display: none !important;
  }
  
  .tab-content {
    display: block !important;
  }
  
  .tab-pane {
    display: block !important;
    opacity: 1 !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
}
</style>

@endsection
