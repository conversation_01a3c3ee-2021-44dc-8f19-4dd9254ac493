<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Branch;
use App\Models\Division;
use App\Helpers\BranchHelper;
use Illuminate\Support\Facades\Validator;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Employee::with(['branch', 'division']);

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nik', 'like', "%{$search}%")
                  ->orWhere('full_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('division_id')) {
            $query->where('division_id', $request->division_id);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('employee_status')) {
            $query->where('employee_status', $request->employee_status);
        }

        $employees = $query->orderBy('full_name')->paginate(15);

        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $divisions = Division::orderBy('name')->get();

        return view('master.employees.index', compact('employees', 'branches', 'divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get accessible branches for non-super admin users
        if (auth()->user()->isSuperAdmin()) {
            $branches = Branch::active()->orderBy('name')->get();
        } else {
            $branches = BranchHelper::getAccessibleBranches();
        }

        $divisions = Division::orderBy('name')->get();

        return view('master.employees.create', compact('branches', 'divisions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nik' => 'required|string|max:50|unique:employees,nik',
            'full_name' => 'required|string|max:255',
            'branch_id' => 'required|exists:branches,id',
            'division_id' => 'required|exists:divisions,id',
            'department' => 'nullable|string|max:255',
            'position' => 'required|string|max:255',
            'email' => 'nullable|email|unique:employees,email',
            'phone' => 'nullable|string|max:20',
            'join_date' => 'nullable|date',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'address' => 'nullable|string',
            'employee_status' => 'required|in:permanent,contract,intern',
            'is_active' => 'boolean',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($request->branch_id)) {
                return redirect()->back()
                    ->with('error', 'Anda tidak memiliki akses ke cabang tersebut.')
                    ->withInput();
            }
        }

        Employee::create($request->all());

        return redirect()->route('master.employees.index')
            ->with('success', 'Karyawan berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Employee $employee)
    {
        // Check branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data karyawan ini.');
            }
        }

        $employee->load(['branch', 'division', 'assets.assetCategory']);

        return view('master.employees.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Employee $employee)
    {
        // Check branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses untuk mengedit karyawan ini.');
            }
        }

        // Get accessible branches for non-super admin users
        if (auth()->user()->isSuperAdmin()) {
            $branches = Branch::active()->orderBy('name')->get();
        } else {
            $branches = BranchHelper::getAccessibleBranches();
        }

        $divisions = Division::orderBy('name')->get();

        return view('master.employees.edit', compact('employee', 'branches', 'divisions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Employee $employee)
    {
        // Check branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses untuk mengedit karyawan ini.');
            }
        }

        $validator = Validator::make($request->all(), [
            'nik' => 'required|string|max:50|unique:employees,nik,' . $employee->id,
            'full_name' => 'required|string|max:255',
            'branch_id' => 'required|exists:branches,id',
            'division_id' => 'required|exists:divisions,id',
            'department' => 'nullable|string|max:255',
            'position' => 'required|string|max:255',
            'email' => 'nullable|email|unique:employees,email,' . $employee->id,
            'phone' => 'nullable|string|max:20',
            'join_date' => 'nullable|date',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'address' => 'nullable|string',
            'employee_status' => 'required|in:permanent,contract,intern',
            'is_active' => 'boolean',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check new branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($request->branch_id)) {
                return redirect()->back()
                    ->with('error', 'Anda tidak memiliki akses ke cabang tersebut.')
                    ->withInput();
            }
        }

        $employee->update($request->all());

        return redirect()->route('master.employees.index')
            ->with('success', 'Data karyawan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Employee $employee)
    {
        // Check branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses untuk menghapus karyawan ini.');
            }
        }

        // Check if employee has assigned assets
        if ($employee->assets()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Tidak dapat menghapus karyawan yang masih memiliki asset yang di-assign.');
        }

        $employee->delete();

        return redirect()->route('master.employees.index')
            ->with('success', 'Karyawan berhasil dihapus.');
    }

    /**
     * Toggle employee status
     */
    public function toggleStatus(Employee $employee)
    {
        // Check branch access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($employee->branch_id)) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        }

        $employee->update(['is_active' => !$employee->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Status karyawan berhasil diubah.',
            'status' => $employee->is_active
        ]);
    }
}
