@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Purchase Order - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi / Purchase Order /</span> Edit {{ $purchaseOrder->po_number }}
  </h4>

  <form action="{{ route('purchase-orders.update', $purchaseOrder) }}" method="POST" id="poForm">
    @csrf
    @method('PUT')
    
    <!-- Header Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">Informasi Purchase Order</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Nomor PO</label>
            <input type="text" class="form-control" value="{{ $purchaseOrder->po_number }}" readonly>
          </div>
          <div class="col-md-3">
            <label class="form-label">Tanggal PO <span class="text-danger">*</span></label>
            <input type="date" class="form-control @error('po_date') is-invalid @enderror" 
                   name="po_date" value="{{ old('po_date', $purchaseOrder->po_date->format('Y-m-d')) }}" required>
            @error('po_date')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Tanggal Pengiriman</label>
            <input type="date" class="form-control @error('delivery_date') is-invalid @enderror" 
                   name="delivery_date" value="{{ old('delivery_date', $purchaseOrder->delivery_date?->format('Y-m-d')) }}">
            @error('delivery_date')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Cabang <span class="text-danger">*</span></label>
            <select class="form-select @error('branch_id') is-invalid @enderror" name="branch_id" required>
              <option value="">Pilih Cabang</option>
              @foreach($branches as $branch)
                <option value="{{ $branch->id }}" {{ old('branch_id', $purchaseOrder->branch_id) == $branch->id ? 'selected' : '' }}>
                  {{ $branch->name }}
                </option>
              @endforeach
            </select>
            @error('branch_id')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>

        <div class="row g-3 mt-2">
          <div class="col-md-6">
            <label class="form-label">Supplier <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="text" class="form-control @error('supplier_id') is-invalid @enderror" 
                     id="supplier_name" readonly value="{{ $purchaseOrder->supplier->name }}">
              <input type="hidden" name="supplier_id" id="supplier_id" value="{{ old('supplier_id', $purchaseOrder->supplier_id) }}">
              <button type="button" class="btn btn-outline-primary" id="btn-supplier-lookup">
                <i class="ri-search-line"></i>
              </button>
            </div>
            @error('supplier_id')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Pajak (%)</label>
            <input type="number" class="form-control @error('tax_percentage') is-invalid @enderror" 
                   name="tax_percentage" value="{{ old('tax_percentage', $purchaseOrder->tax_percentage) }}" min="0" max="100" step="0.01">
            @error('tax_percentage')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Diskon (%)</label>
            <input type="number" class="form-control @error('discount_percentage') is-invalid @enderror" 
                   name="discount_percentage" value="{{ old('discount_percentage', $purchaseOrder->discount_percentage) }}" min="0" max="100" step="0.01">
            @error('discount_percentage')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>

        <div class="row g-3 mt-2">
          <div class="col-md-6">
            <label class="form-label">Syarat Pembayaran</label>
            <input type="text" class="form-control @error('payment_terms') is-invalid @enderror" 
                   name="payment_terms" value="{{ old('payment_terms', $purchaseOrder->payment_terms) }}">
            @error('payment_terms')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-6">
            <label class="form-label">Alamat Pengiriman</label>
            <textarea class="form-control @error('delivery_address') is-invalid @enderror" 
                      name="delivery_address" rows="2">{{ old('delivery_address', $purchaseOrder->delivery_address) }}</textarea>
            @error('delivery_address')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>

    <!-- Items Section -->
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Item Purchase Order</h5>
        <button type="button" class="btn btn-primary btn-sm" id="add-item">
          <i class="ri-add-line me-1"></i>Tambah Item
        </button>
      </div>
      <div class="card-body">
        <div id="items-container">
          <!-- Existing items will be loaded here -->
        </div>
        
        <!-- Totals -->
        <div class="row mt-4">
          <div class="col-md-8"></div>
          <div class="col-md-4">
            <table class="table table-sm">
              <tr>
                <td><strong>Subtotal:</strong></td>
                <td class="text-end"><strong id="subtotal">Rp 0</strong></td>
              </tr>
              <tr>
                <td>Diskon:</td>
                <td class="text-end" id="discount">Rp 0</td>
              </tr>
              <tr>
                <td>Pajak:</td>
                <td class="text-end" id="tax">Rp 0</td>
              </tr>
              <tr class="table-primary">
                <td><strong>Total:</strong></td>
                <td class="text-end"><strong id="total">Rp 0</strong></td>
              </tr>
            </table>
            
            <!-- Terbilang -->
            <div class="mt-3">
              <small class="text-muted">Terbilang:</small>
              <div id="terbilang" class="fw-bold text-primary">-</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">Informasi Tambahan</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-6">
            <label class="form-label">Catatan</label>
            <textarea class="form-control @error('notes') is-invalid @enderror" 
                      name="notes" rows="3">{{ old('notes', $purchaseOrder->notes) }}</textarea>
            @error('notes')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-6">
            <label class="form-label">Syarat & Ketentuan</label>
            <textarea class="form-control @error('terms_conditions') is-invalid @enderror" 
                      name="terms_conditions" rows="3">{{ old('terms_conditions', $purchaseOrder->terms_conditions) }}</textarea>
            @error('terms_conditions')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <a href="{{ route('purchase-orders.show', $purchaseOrder) }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
          <div>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line me-1"></i>Update Purchase Order
            </button>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- Supplier Lookup Modal -->
<div class="modal fade" id="supplierLookupModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Pilih Supplier</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <input type="text" class="form-control" id="supplier-search" placeholder="Cari supplier...">
        </div>
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Kode</th>
                <th>Nama</th>
                <th>Kontak</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody id="supplier-list">
              @foreach($suppliers as $supplier)
                <tr>
                  <td>{{ $supplier->supplier_code }}</td>
                  <td>{{ $supplier->name }}</td>
                  <td>{{ $supplier->phone }}</td>
                  <td>
                    <button type="button" class="btn btn-sm btn-primary select-supplier" 
                            data-id="{{ $supplier->id }}" data-name="{{ $supplier->name }}">
                      Pilih
                    </button>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@section('page-script')
<script>
let itemIndex = 0;
const existingItems = @json($purchaseOrder->items);

$(document).ready(function() {
    // Load existing items
    loadExistingItems();

    // Supplier lookup
    $('#btn-supplier-lookup').click(function() {
        $('#supplierLookupModal').modal('show');
    });

    // Select supplier
    $(document).on('click', '.select-supplier', function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        $('#supplier_id').val(id);
        $('#supplier_name').val(name);
        $('#supplierLookupModal').modal('hide');
    });

    // Supplier search
    $('#supplier-search').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#supplier-list tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add item
    $('#add-item').click(function() {
        addItem();
    });

    // Calculate totals when tax or discount changes
    $('input[name="tax_percentage"], input[name="discount_percentage"]').on('input', function() {
        calculateTotals();
    });

    // Initial calculation
    calculateTotals();
});

function loadExistingItems() {
    existingItems.forEach(function(item, index) {
        addItem(item);
    });

    if (existingItems.length === 0) {
        addItem(); // Add empty item if no existing items
    }
}

function addItem(existingData = null) {
    const itemHtml = `
        <div class="item-row border rounded p-3 mb-3" data-index="${itemIndex}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Item #${itemIndex + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>

            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Nama Item <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][item_name]"
                           value="${existingData ? existingData.item_name : ''}" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Kode Item</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][item_code]"
                           value="${existingData ? (existingData.item_code || '') : ''}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Merk</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][brand]"
                           value="${existingData ? (existingData.brand || '') : ''}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Model</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][model]"
                           value="${existingData ? (existingData.model || '') : ''}">
                </div>
            </div>

            <div class="row g-3 mt-2">
                <div class="col-md-6">
                    <label class="form-label">Deskripsi</label>
                    <textarea class="form-control" name="items[${itemIndex}][item_description]" rows="2">${existingData ? (existingData.item_description || '') : ''}</textarea>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Spesifikasi</label>
                    <textarea class="form-control" name="items[${itemIndex}][specification]" rows="2">${existingData ? (existingData.specification || '') : ''}</textarea>
                </div>
            </div>

            <div class="row g-3 mt-2">
                <div class="col-md-2">
                    <label class="form-label">Qty <span class="text-danger">*</span></label>
                    <input type="number" class="form-control item-qty" name="items[${itemIndex}][quantity]"
                           min="1" value="${existingData ? existingData.quantity : 1}" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Satuan <span class="text-danger">*</span></label>
                    <select class="form-select" name="items[${itemIndex}][unit]" required>
                        <option value="pcs" ${existingData && existingData.unit === 'pcs' ? 'selected' : ''}>Pcs</option>
                        <option value="unit" ${existingData && existingData.unit === 'unit' ? 'selected' : ''}>Unit</option>
                        <option value="set" ${existingData && existingData.unit === 'set' ? 'selected' : ''}>Set</option>
                        <option value="box" ${existingData && existingData.unit === 'box' ? 'selected' : ''}>Box</option>
                        <option value="kg" ${existingData && existingData.unit === 'kg' ? 'selected' : ''}>Kg</option>
                        <option value="meter" ${existingData && existingData.unit === 'meter' ? 'selected' : ''}>Meter</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Harga Satuan <span class="text-danger">*</span></label>
                    <input type="number" class="form-control item-price" name="items[${itemIndex}][unit_price]"
                           min="0" step="0.01" value="${existingData ? existingData.unit_price : ''}" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Total Harga</label>
                    <input type="text" class="form-control item-total" readonly>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Catatan</label>
                    <textarea class="form-control" name="items[${itemIndex}][notes]" rows="1">${existingData ? (existingData.notes || '') : ''}</textarea>
                </div>
            </div>
        </div>
    `;

    $('#items-container').append(itemHtml);

    // Bind events for new item
    bindItemEvents(itemIndex);

    // Calculate initial total for this item
    if (existingData) {
        const row = $(`.item-row[data-index="${itemIndex}"]`);
        calculateItemTotal(row);
    }

    itemIndex++;
    updateItemNumbers();
}

function bindItemEvents(index) {
    const row = $(`.item-row[data-index="${index}"]`);

    // Remove item
    row.find('.remove-item').click(function() {
        if ($('.item-row').length > 1) {
            row.remove();
            updateItemNumbers();
            calculateTotals();
        } else {
            alert('Minimal harus ada 1 item');
        }
    });

    // Calculate item total
    row.find('.item-qty, .item-price').on('input', function() {
        calculateItemTotal(row);
        calculateTotals();
    });
}

function calculateItemTotal(row) {
    const qty = parseFloat(row.find('.item-qty').val()) || 0;
    const price = parseFloat(row.find('.item-price').val()) || 0;
    const total = qty * price;

    row.find('.item-total').val(formatCurrency(total));
}

function calculateTotals() {
    let subtotal = 0;

    $('.item-row').each(function() {
        const qty = parseFloat($(this).find('.item-qty').val()) || 0;
        const price = parseFloat($(this).find('.item-price').val()) || 0;
        subtotal += qty * price;
    });

    const discountPercentage = parseFloat($('input[name="discount_percentage"]').val()) || 0;
    const taxPercentage = parseFloat($('input[name="tax_percentage"]').val()) || 0;

    const discountAmount = subtotal * (discountPercentage / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (taxPercentage / 100);
    const total = afterDiscount + taxAmount;

    $('#subtotal').text(formatCurrency(subtotal));
    $('#discount').text(formatCurrency(discountAmount));
    $('#tax').text(formatCurrency(taxAmount));
    $('#total').text(formatCurrency(total));

    // Update terbilang
    updateTerbilang(total);
}

function updateTerbilang(amount) {
    if (amount > 0) {
        $.ajax({
            url: '{{ route("get-terbilang") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                amount: amount
            },
            success: function(response) {
                $('#terbilang').text(response.terbilang);
            },
            error: function() {
                $('#terbilang').text('-');
            }
        });
    } else {
        $('#terbilang').text('-');
    }
}

function updateItemNumbers() {
    $('.item-row').each(function(index) {
        $(this).find('h6').text(`Item #${index + 1}`);
    });
}

function formatCurrency(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
}
</script>
@endsection
