<?php

namespace App\Http\Controllers;

use App\Models\AssetMaintenance;
use App\Models\MaintenanceReceipt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MaintenanceTrackingController extends Controller
{
    /**
     * Track maintenance status via QR Code
     */
    public function track($id)
    {
        $maintenance = AssetMaintenance::with([
            'asset.branch',
            'asset.assetCategory',
            'asset.assignedEmployee',
            'supplier',
            'requestedBy',
            'assignedTo',
            'approvedBy'
        ])->find($id);

        if (!$maintenance) {
            return view('maintenance.tracking.not-found');
        }

        // Check if user can receive maintenance (only if logged in)
        $canReceive = false;
        $currentUser = null;
        if (auth()->check()) {
            $currentUser = auth()->user();
            // Check if user has permission to receive maintenance
            $canReceive = $this->userCanReceiveMaintenance($currentUser, $maintenance);
        }

        // Get existing receipt if any
        $receipt = MaintenanceReceipt::where('maintenance_id', $id)->first();

        return view('maintenance.tracking.show', compact('maintenance', 'canReceive', 'receipt', 'currentUser'));
    }

    /**
     * Show maintenance receipt form
     */
    public function showReceiptForm($id)
    {
        $maintenance = AssetMaintenance::with([
            'asset.branch',
            'asset.assetCategory',
            'asset.assignedEmployee',
            'supplier'
        ])->find($id);

        if (!$maintenance) {
            abort(404, 'Maintenance tidak ditemukan');
        }

        // Check permission
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Silakan login terlebih dahulu');
        }

        $user = auth()->user();
        $canReceive = $this->userCanReceiveMaintenance($user, $maintenance);

        if (!$canReceive) {
            abort(403, 'Anda tidak memiliki akses untuk menerima maintenance ini');
        }

        // Check if already received
        $existingReceipt = MaintenanceReceipt::where('maintenance_id', $id)->first();
        if ($existingReceipt) {
            return redirect()->route('maintenance.track', $id)
                           ->with('info', 'Maintenance ini sudah diterima sebelumnya');
        }

        return view('maintenance.receipt.form', compact('maintenance'));
    }

    /**
     * Store maintenance receipt
     */
    public function storeReceipt(Request $request, $id)
    {
        $maintenance = AssetMaintenance::find($id);

        if (!$maintenance) {
            abort(404, 'Maintenance tidak ditemukan');
        }

        // Check permission
        $user = auth()->user();
        $canReceive = $this->userCanReceiveMaintenance($user, $maintenance);

        if (!$canReceive) {
            abort(403, 'Anda tidak memiliki akses untuk menerima maintenance ini');
        }

        // Check if already received
        $existingReceipt = MaintenanceReceipt::where('maintenance_id', $id)->first();
        if ($existingReceipt) {
            return redirect()->route('maintenance.track', $id)
                           ->with('error', 'Maintenance ini sudah diterima sebelumnya');
        }

        $validator = Validator::make($request->all(), [
            'received_date' => 'required|date',
            'received_time' => 'required',
            'receiver_name' => 'required|string|max:255',
            'receiver_position' => 'required|string|max:255',
            'asset_condition' => 'required|in:excellent,good,fair,poor,damaged',
            'notes' => 'nullable|string',
            'signature' => 'nullable|string' // Base64 signature data
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        // Create receipt
        MaintenanceReceipt::create([
            'maintenance_id' => $id,
            'received_by' => $user->id,
            'received_date' => $request->received_date,
            'received_time' => $request->received_time,
            'receiver_name' => $request->receiver_name,
            'receiver_position' => $request->receiver_position,
            'asset_condition' => $request->asset_condition,
            'notes' => $request->notes,
            'signature_data' => $request->signature,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return redirect()->route('maintenance.track', $id)
                       ->with('success', 'Penerimaan maintenance berhasil dicatat');
    }

    /**
     * Check if user can receive maintenance
     */
    private function userCanReceiveMaintenance($user, $maintenance)
    {
        // Return false if user is null (not logged in)
        if (!$user) {
            return false;
        }

        // Check if user is super admin
        if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
            return true;
        }

        // Check if user is assigned to this maintenance
        if ($maintenance->assigned_to && $maintenance->assigned_to == $user->id) {
            return true;
        }

        // Check if user has maintenance_receive permission through role
        $userRole = $user->role ?? null;
        if ($userRole) {
            $hasPermission = $userRole->permissions()
                ->where('slug', 'maintenance_receive')
                ->exists();
            if ($hasPermission) {
                return true;
            }
        }

        return false;
    }
}
