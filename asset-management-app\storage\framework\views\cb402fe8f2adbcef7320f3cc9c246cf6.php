<?php $__env->startSection('title', 'Laporan Asset Digital'); ?>

<?php $__env->startSection('page-style'); ?>
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
<script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-0">
              <i class="ri-computer-line me-2"></i>
              Laporan Asset Digital
            </h5>
            <small class="text-muted">Laporan data asset digital dengan filter cabang, tipe lisensi, tanggal, dan status</small>
          </div>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
              <i class="ri-file-excel-line me-1"></i>
              Export Excel
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
              <i class="ri-refresh-line me-1"></i>
              Reset Filter
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-primary">
                <i class="ri-computer-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['total_assets'])); ?></h4>
              <small class="text-muted">Total Asset Digital</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-success">
                <i class="ri-check-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['active_assets'])); ?></h4>
              <small class="text-muted">Asset Aktif</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-warning">
                <i class="ri-time-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['expiring_soon'])); ?></h4>
              <small class="text-muted">Akan Expired (30 hari)</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-info">
                <i class="ri-money-dollar-circle-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">Rp <?php echo e(number_format($stats['total_value'], 0, ',', '.')); ?></h5>
              <small class="text-muted">Total Nilai Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Statistics Row -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-danger">
                <i class="ri-close-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['expired_assets'])); ?></h4>
              <small class="text-muted">Asset Expired</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-secondary">
                <i class="ri-pause-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['suspended_assets'])); ?></h4>
              <small class="text-muted">Asset Suspended</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-dark">
                <i class="ri-user-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['total_users'])); ?></h4>
              <small class="text-muted">Total Users Aktif</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-light text-dark">
                <i class="ri-group-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['total_max_users'])); ?></h4>
              <small class="text-muted">Total Kapasitas Users</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Form -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="ri-filter-line me-2"></i>
            Filter Laporan
          </h6>
        </div>
        <div class="card-body">
          <form method="GET" action="<?php echo e(route('reports.asset-digitals.index')); ?>" id="filterForm">
            <div class="row">
              <div class="col-md-3 mb-3">
                <label class="form-label">Cabang</label>
                <select name="branch_id" class="form-select">
                  <option value="">Semua Cabang</option>
                  <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                      <?php echo e($branch->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tipe Lisensi</label>
                <select name="license_type" class="form-select">
                  <option value="">Semua Tipe</option>
                  <?php $__currentLoopData = $licenseTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $licenseType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($licenseType->lookup_name); ?>" <?php echo e(request('license_type') == $licenseType->lookup_name ? 'selected' : ''); ?>>
                      <?php echo e($licenseType->lookup_name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                  <option value="">Semua Status</option>
                  <?php $__currentLoopData = $statusOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($value); ?>" <?php echo e(request('status') == $value ? 'selected' : ''); ?>>
                      <?php echo e($label); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Dari</label>
                <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Sampai</label>
                <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Expired Dari</label>
                <input type="date" name="expiry_from" class="form-control" value="<?php echo e(request('expiry_from')); ?>">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Expired Sampai</label>
                <input type="date" name="expiry_to" class="form-control" value="<?php echo e(request('expiry_to')); ?>">
              </div>
              <div class="col-md-3 mb-3 d-flex align-items-end">
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line me-1"></i>
                    Filter
                  </button>
                  <a href="<?php echo e(route('reports.asset-digitals.index')); ?>" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line me-1"></i>
                    Reset
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Asset Digital Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="ri-table-line me-2"></i>
            Data Asset Digital (<?php echo e($assetDigitals->total()); ?> asset)
          </h6>
          <div class="d-flex gap-2">
            <span class="badge bg-primary"><?php echo e($assetDigitals->count()); ?> ditampilkan</span>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Kode Asset</th>
                  <th>Nama Asset</th>
                  <th>Tipe Lisensi</th>
                  <th>Cabang</th>
                  <th>Supplier</th>
                  <th>Status</th>
                  <th>Users</th>
                  <th>Tanggal Expired</th>
                  <th>Harga</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $assetDigitals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assetDigital): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                  <td>
                    <strong><?php echo e($assetDigital->asset_code); ?></strong>
                  </td>
                  <td>
                    <div>
                      <strong><?php echo e($assetDigital->name); ?></strong>
                      <?php if($assetDigital->version): ?>
                        <br><small class="text-muted">v<?php echo e($assetDigital->version); ?></small>
                      <?php endif; ?>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info"><?php echo e($assetDigital->license_type); ?></span>
                  </td>
                  <td><?php echo e($assetDigital->branch->name ?? '-'); ?></td>
                  <td><?php echo e($assetDigital->supplier->name ?? '-'); ?></td>
                  <td>
                    <?php if($assetDigital->status == 'active'): ?>
                      <span class="badge bg-success">Aktif</span>
                    <?php elseif($assetDigital->status == 'inactive'): ?>
                      <span class="badge bg-secondary">Non-Aktif</span>
                    <?php elseif($assetDigital->status == 'expired'): ?>
                      <span class="badge bg-danger">Expired</span>
                    <?php elseif($assetDigital->status == 'suspended'): ?>
                      <span class="badge bg-warning">Suspended</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <span class="badge bg-dark">
                      <?php echo e($assetDigital->current_users ?? 0); ?>/<?php echo e($assetDigital->max_users ?? '∞'); ?>

                    </span>
                  </td>
                  <td>
                    <?php if($assetDigital->expiry_date): ?>
                      <?php echo e($assetDigital->expiry_date->format('d/m/Y')); ?>

                      <?php if($assetDigital->expiry_date->isPast()): ?>
                        <br><small class="text-danger">Sudah Expired</small>
                      <?php elseif($assetDigital->expiry_date->diffInDays() <= 30): ?>
                        <br><small class="text-warning"><?php echo e($assetDigital->expiry_date->diffInDays()); ?> hari lagi</small>
                      <?php endif; ?>
                    <?php else: ?>
                      -
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($assetDigital->purchase_price): ?>
                      Rp <?php echo e(number_format($assetDigital->purchase_price, 0, ',', '.')); ?>

                    <?php else: ?>
                      -
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        Aksi
                      </button>
                      <ul class="dropdown-menu">
                        <li>
                          <a href="<?php echo e(route('asset-digitals.show', $assetDigital)); ?>" class="dropdown-item">
                            <i class="ri-eye-line me-2"></i>
                            Detail
                          </a>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                  <td colspan="10" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                      <i class="ri-computer-line ri-48px text-muted mb-2"></i>
                      <span class="text-muted">Tidak ada data asset digital</span>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          <?php if($assetDigitals->hasPages()): ?>
          <div class="d-flex justify-content-center mt-4">
            <?php echo e($assetDigitals->appends(request()->query())->links()); ?>

          </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportToExcel() {
    // Get current filter values
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // Redirect to export URL with current filters
    window.location.href = '<?php echo e(route("reports.asset-digitals.export")); ?>?' + params.toString();
}

function resetFilters() {
    window.location.href = '<?php echo e(route("reports.asset-digitals.index")); ?>';
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/reports/asset-digitals/index.blade.php ENDPATH**/ ?>