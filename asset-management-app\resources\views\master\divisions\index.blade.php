@extends('layouts.contentNavbarLayout')

@section('title', 'Master Divisi - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Master Divisi
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('master.divisions.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah Divisi
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('master.divisions.index') }}">
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                   placeholder="Nama, kode, kepala divisi...">
          </div>
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <select class="form-select" name="is_active">
              <option value="">Semua Status</option>
              <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Aktif</option>
              <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Non-Aktif</option>
            </select>
          </div>
          <div class="col-md-5">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('master.divisions.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Divisions Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Divisi ({{ $divisions->total() }} divisi)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Kode</th>
            <th>Nama Divisi</th>
            <th>Kepala Divisi</th>
            <th>Kontak</th>
            <th>Jumlah User</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($divisions as $division)
          <tr>
            <td>
              <span class="badge bg-primary">{{ $division->code }}</span>
            </td>
            <td>
              <div>
                <strong>{{ $division->name }}</strong>
                @if($division->description)
                  <br><small class="text-muted">{{ Str::limit($division->description, 50) }}</small>
                @endif
              </div>
            </td>
            <td>
              @if($division->head_name)
                <div>
                  <strong>{{ $division->head_name }}</strong>
                </div>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              @if($division->head_email || $division->head_phone)
                <div>
                  @if($division->head_email)
                    <small class="d-block">
                      <i class="ri-mail-line me-1"></i>{{ $division->head_email }}
                    </small>
                  @endif
                  @if($division->head_phone)
                    <small class="d-block">
                      <i class="ri-phone-line me-1"></i>{{ $division->head_phone }}
                    </small>
                  @endif
                </div>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              <div>
                <span class="badge bg-info">{{ $division->users_count }} total</span>
                @if($division->active_users_count > 0)
                  <br><span class="badge bg-success badge-sm">{{ $division->active_users_count }} aktif</span>
                @endif
              </div>
            </td>
            <td>
              <span class="badge bg-{{ $division->is_active ? 'success' : 'secondary' }}">
                {{ $division->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('master.divisions.show', $division) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('master.divisions.edit', $division) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  @if($division->users_count == 0)
                  <div class="dropdown-divider"></div>
                  <form action="{{ route('master.divisions.destroy', $division) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus divisi ini?')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                  @endif
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="7" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-building-2-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada data divisi</h6>
                <p class="text-muted mb-3">Belum ada divisi yang terdaftar atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('master.divisions.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah Divisi Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($divisions->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $divisions->firstItem() }} - {{ $divisions->lastItem() }} dari {{ $divisions->total() }} data
        </div>
        {{ $divisions->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
