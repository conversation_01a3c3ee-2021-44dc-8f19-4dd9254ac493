
@extends('layouts/contentNavbarLayout')

@section('title', 'Asset Digital')

@section('content')
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Daftar Asset Digital</h5>
        @can('asset-digitals.create')
        <a href="{{ route('asset-digitals.create') }}" class="btn btn-primary">
          <i class="ri-add-line me-1"></i>Tambah Asset Digital
        </a>
        @endcan
      </div>
      
      <!-- Filters -->
      <div class="card-body">
        <form method="GET" action="{{ route('asset-digitals.index') }}" class="mb-4">
          <div class="row g-3">
            <div class="col-md-3">
              <label class="form-label">Pencarian</label>
              <input type="text" name="search" class="form-control" placeholder="Nama, kode, vendor, username..." value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
              <label class="form-label">Status</label>
              <select name="status" class="form-select">
                <option value="">Semua Status</option>
                @foreach($statuses as $status)
                  <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                    {{ ucfirst($status) }}
                  </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Tipe Lisensi</label>
              <select name="license_type" class="form-select">
                <option value="">Semua Tipe</option>
                @foreach($licenseTypes as $type)
                  <option value="{{ $type }}" {{ request('license_type') == $type ? 'selected' : '' }}>
                    {{ $type }}
                  </option>
                @endforeach
              </select>
            </div>
            @if(auth()->user()->isSuperAdmin())
            <div class="col-md-2">
              <label class="form-label">Cabang</label>
              <select name="branch_id" class="form-select">
                <option value="">Semua Cabang</option>
                @foreach($branches as $branch)
                  <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                    {{ $branch->name }}
                  </option>
                @endforeach
              </select>
            </div>
            @endif
            <div class="col-md-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="ri-search-line me-1"></i>Filter
                </button>
                <a href="{{ route('asset-digitals.index') }}" class="btn btn-outline-secondary">
                  <i class="ri-refresh-line me-1"></i>Reset
                </a>
              </div>
            </div>
          </div>
        </form>

        <!-- Data Table -->
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Kode Asset</th>
                <th>Nama</th>
                <th>Tipe Lisensi</th>
                <th>Vendor</th>
                <th>Username</th>
                <th>Status</th>
                <th>Assigned To</th>
                <th>Expiry Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              @forelse($assetDigitals as $asset)
                <tr>
                  <td>
                    <strong>{{ $asset->asset_code }}</strong>
                  </td>
                  <td>
                    <div>
                      <strong>{{ $asset->name }}</strong>
                      @if($asset->version)
                        <small class="text-muted d-block">v{{ $asset->version }}</small>
                      @endif
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-label-info">{{ $asset->license_type }}</span>
                  </td>
                  <td>{{ $asset->vendor ?? '-' }}</td>
                  <td>{{ $asset->username ?? '-' }}</td>
                  <td>
                    <span class="badge bg-label-{{ 
                      $asset->status === 'active' ? 'success' : 
                      ($asset->status === 'inactive' ? 'secondary' : 
                      ($asset->status === 'expired' ? 'danger' : 'warning')) 
                    }}">
                      {{ ucfirst($asset->status) }}
                    </span>
                    @if($asset->isExpiringSoon())
                      <small class="text-warning d-block">
                        <i class="ri-time-line"></i> Expires in {{ $asset->getDaysUntilExpiry() }} days
                      </small>
                    @endif
                  </td>
                  <td>
                    @if($asset->assignedTo)
                      <div>
                        <strong>{{ $asset->assignedTo->name }}</strong>
                        <small class="text-muted d-block">{{ $asset->assignedTo->nik }}</small>
                      </div>
                    @else
                      <span class="text-muted">Unassigned</span>
                    @endif
                  </td>
                  <td>
                    @if($asset->expiry_date)
                      <div>
                        {{ $asset->expiry_date->format('d/m/Y') }}
                        @if($asset->isExpired())
                          <small class="text-danger d-block">Expired</small>
                        @elseif($asset->isExpiringSoon())
                          <small class="text-warning d-block">Expiring Soon</small>
                        @endif
                      </div>
                    @else
                      <span class="text-muted">No Expiry</span>
                    @endif
                  </td>
                  <td>
                    <div class="dropdown">
                      <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        Actions
                      </button>
                      <div class="dropdown-menu">
                        <a class="dropdown-item" href="{{ route('asset-digitals.show', $asset) }}">
                          <i class="ri-eye-line me-2"></i>Detail
                        </a>
                        @can('asset-digitals.edit')
                        <a class="dropdown-item" href="{{ route('asset-digitals.edit', $asset) }}">
                          <i class="ri-pencil-line me-2"></i>Edit
                        </a>
                        @endcan
                        @can('asset-digitals.delete')
                        <div class="dropdown-divider"></div>
                        <form action="{{ route('asset-digitals.destroy', $asset) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Apakah Anda yakin ingin menghapus asset digital ini?')">
                          @csrf
                          @method('DELETE')
                          <button type="submit" class="dropdown-item text-danger">
                            <i class="ri-delete-bin-line me-2"></i>Hapus
                          </button>
                        </form>
                        @endcan
                      </div>
                    </div>
                  </td>
                </tr>
              @empty
                <tr>
                  <td colspan="9" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                      <i class="ri-database-2-line ri-48px text-muted mb-2"></i>
                      <p class="mb-0">Belum ada data asset digital</p>
                      @can('asset-digitals.create')
                      <a href="{{ route('asset-digitals.create') }}" class="btn btn-primary btn-sm mt-2">
                        <i class="ri-add-line me-1"></i>Tambah Asset Digital Pertama
                      </a>
                      @endcan
                    </div>
                  </td>
                </tr>
              @endforelse
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        @if($assetDigitals->hasPages())
        <div class="d-flex justify-content-center mt-4">
          {{ $assetDigitals->withQueryString()->links() }}
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
      icon: 'success',
      title: 'Berhasil!',
      text: '{{ session('success') }}',
      timer: 3000,
      showConfirmButton: false
    });
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
      icon: 'error',
      title: 'Error!',
      text: '{{ session('error') }}',
      timer: 3000,
      showConfirmButton: false
    });
  });
</script>
@endif
@endsection

