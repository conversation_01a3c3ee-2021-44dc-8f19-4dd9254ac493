@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Asset - Asset Management System')

@section('content')
<style>
.field-group-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #696cff;
}

.field-group-title {
  color: #566a7f;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.loading-spinner {
  display: none;
  text-align: center;
  padding: 2rem;
  color: #8592a3;
}

.dynamic-fields-container {
  min-height: 100px;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-pencil-line me-2"></i>
              Edit Asset
            </h5>
            <small class="text-muted">{{ $asset->name }}</small>
          </div>
          <div>
            <a href="{{ route('assets.show', $asset) }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="POST" action="{{ route('assets.update', $asset) }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <!-- Core Information -->
            <div class="field-group-section">
              <h6 class="field-group-title">
                <i class="ri-information-line me-2"></i>
                Informasi Dasar (Core Fields)
              </h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="name" class="form-label">Nama Asset <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                           id="name" name="name" value="{{ old('name', $asset->name) }}" required>
                    @error('name')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="asset_code" class="form-label">Kode Asset</label>
                    <input type="text" class="form-control @error('asset_code') is-invalid @enderror" 
                           id="asset_code" name="asset_code" value="{{ old('asset_code', $asset->asset_code) }}" readonly>
                    <div class="form-text">Kode asset tidak dapat diubah</div>
                    @error('asset_code')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label for="asset_category_id" class="form-label">Kategori Asset <span class="text-danger">*</span></label>
                    <select class="form-select @error('asset_category_id') is-invalid @enderror" 
                            id="asset_category_id" name="asset_category_id" required>
                      <option value="">Pilih Kategori</option>
                      @foreach(\App\Models\AssetCategory::active()->get() as $category)
                        <option value="{{ $category->id }}" {{ old('asset_category_id', $asset->asset_category_id) == $category->id ? 'selected' : '' }}>
                          {{ $category->name }}
                        </option>
                      @endforeach
                    </select>
                    @error('asset_category_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label for="asset_type_id" class="form-label">Tipe Asset</label>
                    <select class="form-select @error('asset_type_id') is-invalid @enderror" 
                            id="asset_type_id" name="asset_type_id">
                      <option value="">Pilih Tipe Asset</option>
                      @foreach(\App\Models\AssetType::active()->get() as $type)
                        <option value="{{ $type->id }}" data-category="{{ $type->asset_category_id }}" 
                                {{ old('asset_type_id', $asset->asset_type_id) == $type->id ? 'selected' : '' }}>
                          {{ $type->name }}
                        </option>
                      @endforeach
                    </select>
                    @error('asset_type_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label for="branch_id" class="form-label">Cabang <span class="text-danger">*</span></label>
                    <select class="form-select @error('branch_id') is-invalid @enderror" 
                            id="branch_id" name="branch_id" required>
                      <option value="">Pilih Cabang</option>
                      @foreach(\App\Helpers\BranchHelper::getAccessibleBranches() as $branch)
                        <option value="{{ $branch->id }}" {{ old('branch_id', $asset->branch_id) == $branch->id ? 'selected' : '' }}>
                          {{ $branch->name }}
                        </option>
                      @endforeach
                    </select>
                    @error('branch_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <!-- Supplier Field -->
              <div class="row">
                <div class="col-12">
                  <div class="mb-3">
                    <label for="supplier_display" class="form-label">Supplier</label>
                    <div class="input-group">
                      <input type="text" class="form-control @error('supplier_id') is-invalid @enderror"
                             id="supplier_display" name="supplier_display"
                             placeholder="Klik tombol untuk memilih supplier..." readonly>
                      <button type="button" class="btn btn-outline-primary" id="supplier_lookup_btn">
                        <i class="ri-search-line me-1"></i>
                        Cari Supplier
                      </button>
                      <button type="button" class="btn btn-outline-danger" id="supplier_clear" style="display: none;">
                        <i class="ri-close-line me-1"></i>
                        Hapus
                      </button>
                    </div>
                    <input type="hidden" id="supplier_id" name="supplier_id" value="{{ old('supplier_id', $asset->supplier_id) }}">
                    @error('supplier_id')
                      <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                    <div class="form-text">
                      <i class="ri-information-line me-1"></i>
                      Pilih supplier yang menyediakan asset ini (opsional)
                    </div>
                  </div>
                </div>
              </div>

              <!-- Purchase Order Field -->
              <div class="row">
                <div class="col-12">
                  <div class="mb-3">
                    <label for="purchase_order_display" class="form-label">Purchase Order</label>
                    <div class="input-group">
                      <input type="text" class="form-control @error('purchase_order_id') is-invalid @enderror"
                             id="purchase_order_display" name="purchase_order_display"
                             value="{{ old('purchase_order_display', $asset->purchaseOrder ? $asset->purchaseOrder->po_number . ' - ' . $asset->purchaseOrder->supplier->name : '') }}"
                             placeholder="Klik tombol untuk memilih PO..." readonly>
                      <button type="button" class="btn btn-outline-primary" id="po_lookup_btn">
                        <i class="ri-search-line me-1"></i>
                        Cari PO
                      </button>
                      <button type="button" class="btn btn-outline-danger" id="po_clear"
                              style="display: {{ $asset->purchase_order_id ? 'inline-block' : 'none' }};">
                        <i class="ri-close-line me-1"></i>
                        Hapus
                      </button>
                    </div>
                    <input type="hidden" id="purchase_order_id" name="purchase_order_id"
                           value="{{ old('purchase_order_id', $asset->purchase_order_id) }}">
                    @error('purchase_order_id')
                      <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                    <div class="form-text">
                      <i class="ri-information-line me-1"></i>
                      Pilih purchase order yang terkait dengan asset ini (opsional)
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select class="form-select @error('status') is-invalid @enderror" 
                            id="status" name="status" required>
                      <option value="">Pilih Status</option>
                      <option value="active" {{ old('status', $asset->status) == 'active' ? 'selected' : '' }}>Active</option>
                      <option value="inactive" {{ old('status', $asset->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                      <option value="maintenance" {{ old('status', $asset->status) == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                      <option value="disposed" {{ old('status', $asset->status) == 'disposed' ? 'selected' : '' }}>Disposed</option>
                    </select>
                    @error('status')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="description" class="form-label">Deskripsi</label>
                    <textarea class="form-control @error('description') is-invalid @enderror"
                              id="description" name="description" rows="3">{{ old('description', $asset->description) }}</textarea>
                    @error('description')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="image" class="form-label">Foto Asset</label>
                    <input type="file" class="form-control @error('image') is-invalid @enderror"
                           id="image" name="image" accept="image/*">
                    <div class="form-text">Upload foto asset baru (JPG, PNG, GIF - Max: 2MB)</div>
                    @if($asset->image)
                      <div class="mt-2">
                        <small class="text-muted">Foto saat ini:</small><br>
                        <img src="{{ Storage::url($asset->image) }}" alt="Current image" style="max-width: 100px; height: auto; border-radius: 4px;">
                      </div>
                    @endif
                    @error('image')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>
            </div>

            <!-- Dynamic Fields Container -->
            <div class="dynamic-fields-container" id="dynamic-fields-container">
              <div class="loading-spinner" id="loading-spinner">
                <i class="ri-loader-4-line ri-spin ri-24px mb-2"></i>
                <p class="mb-0">Loading field configurations...</p>
              </div>
              
              <div class="text-center text-muted" id="no-fields-message" style="display: none;">
                <i class="ri-information-line ri-24px mb-2"></i>
                <p class="mb-0">Pilih kategori asset untuk menampilkan field tambahan</p>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('assets.show', $asset) }}" class="btn btn-outline-secondary">
                Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                Update Asset
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const categorySelect = document.getElementById('asset_category_id');
  const typeSelect = document.getElementById('asset_type_id');
  const dynamicFieldsContainer = document.getElementById('dynamic-fields-container');
  const loadingSpinner = document.getElementById('loading-spinner');
  const noFieldsMessage = document.getElementById('no-fields-message');
  
  // Existing dynamic fields data
  const existingDynamicFields = @json($asset->dynamic_fields ?? []);

  // Load dynamic fields when category changes
  categorySelect.addEventListener('change', function() {
    const categoryId = this.value;
    
    // Filter asset types based on category
    filterAssetTypes(categoryId);
    
    // Load dynamic fields
    loadDynamicFields(categoryId);
  });

  function filterAssetTypes(categoryId) {
    const options = typeSelect.querySelectorAll('option');
    
    options.forEach(option => {
      if (option.value === '') {
        option.style.display = 'block';
        return;
      }
      
      const optionCategory = option.dataset.category;
      if (!categoryId || optionCategory === categoryId) {
        option.style.display = 'block';
      } else {
        option.style.display = 'none';
        // Reset selection if hidden
        if (option.selected) {
          typeSelect.value = '';
        }
      }
    });
  }

  function loadDynamicFields(categoryId) {
    // Clear previous content
    const existingFields = dynamicFieldsContainer.querySelectorAll('.field-group-section:not(.core-fields)');
    existingFields.forEach(field => field.remove());
    
    if (!categoryId) {
      noFieldsMessage.style.display = 'block';
      loadingSpinner.style.display = 'none';
      return;
    }

    // Show loading
    loadingSpinner.style.display = 'block';
    noFieldsMessage.style.display = 'none';

    // Fetch field configurations
    fetch(`{{ route('assets.field-configurations') }}?category_id=${categoryId}`)
      .then(response => response.json())
      .then(data => {
        loadingSpinner.style.display = 'none';
        
        if (data.success && data.html) {
          dynamicFieldsContainer.insertAdjacentHTML('beforeend', data.html);
          
          // Populate existing values
          populateExistingValues();
          
          // Initialize any special field behaviors
          initializeDynamicFields();
        } else {
          noFieldsMessage.style.display = 'block';
        }
      })
      .catch(error => {
        console.error('Error loading field configurations:', error);
        loadingSpinner.style.display = 'none';
        dynamicFieldsContainer.insertAdjacentHTML('beforeend', `
          <div class="alert alert-danger">
            <i class="ri-error-warning-line me-2"></i>
            Error loading field configurations. Please try again.
          </div>
        `);
      });
  }

  function populateExistingValues() {
    // Populate existing dynamic field values
    Object.entries(existingDynamicFields).forEach(([fieldName, value]) => {
      const field = document.querySelector(`[name="dynamic_fields[${fieldName}]"]`);
      if (field) {
        if (field.type === 'checkbox') {
          // Handle checkbox arrays
          if (Array.isArray(value)) {
            value.forEach(val => {
              const checkbox = document.querySelector(`[name="dynamic_fields[${fieldName}][]"][value="${val}"]`);
              if (checkbox) checkbox.checked = true;
            });
          }
        } else if (field.type === 'radio') {
          // Handle radio buttons
          const radio = document.querySelector(`[name="dynamic_fields[${fieldName}]"][value="${value}"]`);
          if (radio) radio.checked = true;
        } else {
          // Handle other input types
          field.value = value;
        }
      }
    });
  }

  function initializeDynamicFields() {
    // Initialize dependent dropdowns
    const dependentFields = document.querySelectorAll('[data-parent-field]');
    dependentFields.forEach(field => {
      const parentFieldName = field.dataset.parentField;
      const parentField = document.querySelector(`[name="${parentFieldName}"]`);
      
      if (parentField) {
        parentField.addEventListener('change', function() {
          loadDependentOptions(field, this.value);
        });
        
        // Trigger initial load if parent has value
        if (parentField.value) {
          loadDependentOptions(field, parentField.value);
        }
      }
    });
  }

  function loadDependentOptions(field, parentValue) {
    const fieldId = field.dataset.fieldId;
    const currentValue = field.value; // Store current value
    
    if (!parentValue) {
      field.innerHTML = '<option value="">Pilih ' + field.dataset.label + '</option>';
      return;
    }

    // Show loading in select
    field.innerHTML = '<option value="">Loading...</option>';

    // Fetch dependent options
    fetch(`{{ route('master.asset-field-configurations.dependent-options') }}?field_id=${fieldId}&parent_value=${parentValue}`)
      .then(response => response.json())
      .then(data => {
        field.innerHTML = '<option value="">Pilih ' + field.dataset.label + '</option>';
        
        if (data.success && data.options) {
          Object.entries(data.options).forEach(([key, value]) => {
            const selected = key === currentValue ? 'selected' : '';
            field.innerHTML += `<option value="${key}" ${selected}>${value}</option>`;
          });
        }
      })
      .catch(error => {
        console.error('Error loading dependent options:', error);
        field.innerHTML = '<option value="">Error loading options</option>';
      });
  }

  // Initialize on page load
  if (categorySelect.value) {
    filterAssetTypes(categorySelect.value);
    loadDynamicFields(categorySelect.value);
  }

  // Initialize Supplier Lookup
  supplierLookup = new SupplierLookup({
    inputId: 'supplier_id',
    displayId: 'supplier_display',
    buttonId: 'supplier_lookup_btn',
    modalId: 'supplierLookupModal',
    apiUrl: '{{ route("api.suppliers.lookup") }}'
  });

  // Initialize PO Lookup
  window.poLookup = new POLookup({
    inputId: 'purchase_order_id',
    displayId: 'purchase_order_display',
    buttonId: 'po_lookup_btn',
    modalId: 'poLookupModal',
    apiUrl: '{{ route("api.purchase-orders.lookup") }}'
  });

  // PO Clear button functionality
  document.getElementById('po_clear').addEventListener('click', function() {
    document.getElementById('purchase_order_id').value = '';
    document.getElementById('purchase_order_display').value = '';
    this.style.display = 'none';
  });

  // Show clear button when PO is selected
  document.getElementById('purchase_order_display').addEventListener('input', function() {
    const clearBtn = document.getElementById('po_clear');
    if (this.value) {
      clearBtn.style.display = 'inline-block';
    } else {
      clearBtn.style.display = 'none';
    }
  });
});
</script>

<!-- Include Supplier Lookup Script -->
<script src="{{ asset('js/supplier-lookup.js') }}"></script>
<!-- Include PO Lookup Script -->
<script src="{{ asset('js/po-lookup.js') }}"></script>

@endsection
