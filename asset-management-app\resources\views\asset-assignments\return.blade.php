@extends('layouts.contentNavbarLayout')

@section('title', 'Return Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex align-items-center">
            <a href="{{ route('asset-assignments.show', $assetAssignment) }}" class="btn btn-outline-secondary btn-sm me-3">
              <i class="ri-arrow-left-line"></i>
            </a>
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-arrow-go-back-line me-2"></i>
                Return Asset
              </h5>
              <small class="text-muted">Kembalikan asset dari karyawan</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Current Assignment Info -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Assignment Saat Ini
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Asset</h6>
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-lg me-3">
                  <span class="avatar-initial rounded bg-label-primary">
                    <i class="ri-archive-line ri-24px"></i>
                  </span>
                </div>
                <div>
                  <h6 class="mb-0">{{ $assetAssignment->asset->asset_code }}</h6>
                  <p class="mb-0 text-muted">{{ $assetAssignment->asset->name }}</p>
                  <small class="text-muted">{{ $assetAssignment->asset->assetCategory->name ?? '-' }}</small>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Karyawan</h6>
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-lg me-3">
                  <span class="avatar-initial rounded bg-label-info">
                    {{ strtoupper(substr($assetAssignment->employee->full_name, 0, 2)) }}
                  </span>
                </div>
                <div>
                  <h6 class="mb-0">{{ $assetAssignment->employee->full_name }}</h6>
                  <p class="mb-0 text-muted">{{ $assetAssignment->employee->nik }}</p>
                  <small class="text-muted">{{ $assetAssignment->employee->position }}</small>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <small class="text-muted">Tanggal Assignment:</small>
              <div class="fw-bold">{{ $assetAssignment->assigned_at->format('d/m/Y H:i') }}</div>
            </div>
            <div class="col-md-6">
              <small class="text-muted">Durasi Assignment:</small>
              <div class="fw-bold">{{ $assetAssignment->duration }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Return Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-file-edit-line me-2"></i>
            Form Return Asset
          </h6>
        </div>
        <div class="card-body">
          <form action="{{ route('asset-assignments.return', $assetAssignment) }}" method="POST">
            @csrf
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">Tanggal Return <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('returned_at') is-invalid @enderror" 
                         name="returned_at" value="{{ old('returned_at', date('Y-m-d')) }}">
                  @error('returned_at')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Kondisi Asset Saat Return <span class="text-danger">*</span></label>
                  <select class="form-select @error('condition_when_returned') is-invalid @enderror" name="condition_when_returned">
                    <option value="">Pilih kondisi asset saat dikembalikan</option>
                    <option value="excellent" {{ old('condition_when_returned') === 'excellent' ? 'selected' : '' }}>Sangat Baik</option>
                    <option value="good" {{ old('condition_when_returned') === 'good' ? 'selected' : '' }}>Baik</option>
                    <option value="fair" {{ old('condition_when_returned') === 'fair' ? 'selected' : '' }}>Cukup</option>
                    <option value="poor" {{ old('condition_when_returned') === 'poor' ? 'selected' : '' }}>Buruk</option>
                  </select>
                  @error('condition_when_returned')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <div class="form-text">
                    <small>Kondisi saat assignment: 
                      <span class="badge bg-{{ $assetAssignment->condition_when_assigned === 'excellent' ? 'success' : ($assetAssignment->condition_when_assigned === 'good' ? 'info' : ($assetAssignment->condition_when_assigned === 'fair' ? 'warning' : 'danger')) }}">
                        {{ $assetAssignment->condition_when_assigned_text }}
                      </span>
                    </small>
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">Catatan Return</label>
                  <textarea class="form-control @error('return_notes') is-invalid @enderror" 
                            name="return_notes" rows="6" 
                            placeholder="Catatan kondisi asset saat dikembalikan, kerusakan yang ditemukan, atau informasi lainnya...">{{ old('return_notes') }}</textarea>
                  @error('return_notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Condition Comparison -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="alert alert-info">
                  <h6 class="alert-heading mb-2">
                    <i class="ri-information-line me-2"></i>
                    Perbandingan Kondisi
                  </h6>
                  <div class="row">
                    <div class="col-md-6">
                      <small class="text-muted">Kondisi Saat Assignment:</small>
                      <div>
                        <span class="badge bg-{{ $assetAssignment->condition_when_assigned === 'excellent' ? 'success' : ($assetAssignment->condition_when_assigned === 'good' ? 'info' : ($assetAssignment->condition_when_assigned === 'fair' ? 'warning' : 'danger')) }}">
                          {{ $assetAssignment->condition_when_assigned_text }}
                        </span>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <small class="text-muted">Kondisi Saat Return:</small>
                      <div id="returnConditionDisplay">
                        <span class="badge bg-secondary">Belum dipilih</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Warning for condition downgrade -->
            <div id="conditionWarning" class="row mt-3" style="display: none;">
              <div class="col-12">
                <div class="alert alert-warning">
                  <h6 class="alert-heading mb-2">
                    <i class="ri-alert-line me-2"></i>
                    Perhatian: Kondisi Asset Menurun
                  </h6>
                  <p class="mb-0">Kondisi asset saat dikembalikan lebih buruk dari saat di-assign. Pastikan untuk memberikan catatan yang detail mengenai kerusakan atau perubahan kondisi yang terjadi.</p>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-3">
                  <a href="{{ route('asset-assignments.show', $assetAssignment) }}" class="btn btn-outline-secondary">
                    <i class="ri-close-line me-2"></i>
                    Batal
                  </a>
                  <button type="submit" class="btn btn-warning">
                    <i class="ri-arrow-go-back-line me-2"></i>
                    Return Asset
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const conditionSelect = document.querySelector('select[name="condition_when_returned"]');
  const returnConditionDisplay = document.getElementById('returnConditionDisplay');
  const conditionWarning = document.getElementById('conditionWarning');
  
  const conditionValues = {
    'excellent': { text: 'Sangat Baik', class: 'success', value: 4 },
    'good': { text: 'Baik', class: 'info', value: 3 },
    'fair': { text: 'Cukup', class: 'warning', value: 2 },
    'poor': { text: 'Buruk', class: 'danger', value: 1 }
  };
  
  const assignedCondition = '{{ $assetAssignment->condition_when_assigned }}';
  const assignedConditionValue = conditionValues[assignedCondition]?.value || 0;

  conditionSelect.addEventListener('change', function() {
    const selectedCondition = this.value;
    
    if (selectedCondition) {
      const condition = conditionValues[selectedCondition];
      returnConditionDisplay.innerHTML = `<span class="badge bg-${condition.class}">${condition.text}</span>`;
      
      // Show warning if condition is worse
      if (condition.value < assignedConditionValue) {
        conditionWarning.style.display = 'block';
      } else {
        conditionWarning.style.display = 'none';
      }
    } else {
      returnConditionDisplay.innerHTML = '<span class="badge bg-secondary">Belum dipilih</span>';
      conditionWarning.style.display = 'none';
    }
  });
});
</script>

@endsection
