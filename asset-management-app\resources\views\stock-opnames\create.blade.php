@extends('layouts.contentNavbarLayout')

@section('title', 'Buat Stock Opname - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex align-items-center">
            <a href="{{ route('stock-opnames.index') }}" class="btn btn-outline-secondary btn-sm me-3">
              <i class="ri-arrow-left-line"></i>
            </a>
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-add-line me-2"></i>
                Buat Stock Opname Baru
              </h5>
              <small class="text-muted">Buat stock opname untuk audit asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Stock Opnames Info -->
  @if(isset($activeOpnames) && $activeOpnames->count() > 0)
    <div class="row mb-4">
      <div class="col-12">
        <div class="alert alert-info" role="alert">
          <div class="d-flex align-items-start">
            <i class="ri-information-line ri-24px me-3"></i>
            <div>
              <h6 class="alert-heading mb-2">Stock Opname Aktif</h6>
              <p class="mb-2">Saat ini terdapat {{ $activeOpnames->count() }} stock opname yang sedang berjalan:</p>

              @foreach($activeOpnames as $opname)
                <div class="border rounded p-2 mb-2 bg-light">
                  <div class="row align-items-center">
                    <div class="col-md-8">
                      <strong>{{ $opname->branch->name }}</strong> - {{ $opname->title }}
                      <br><span class="badge bg-primary">{{ $opname->opname_number }}</span>
                      <small class="text-muted d-block">Dimulai: {{ $opname->start_date->format('d/m/Y H:i') }}</small>
                    </div>
                    <div class="col-md-4 text-md-end">
                      <span class="badge bg-warning">
                        <i class="ri-lock-line me-1"></i>
                        Asset Dikunci
                      </span>
                    </div>
                  </div>
                </div>
              @endforeach

              <small class="text-muted">
                <i class="ri-lightbulb-line me-1"></i>
                Anda dapat membuat stock opname baru untuk cabang yang berbeda, namun tidak untuk cabang yang sedang aktif.
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  @endif

  <!-- Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-file-text-line me-2"></i>
            Informasi Stock Opname
          </h6>
        </div>
        <div class="card-body">
          <form method="POST" action="{{ route('stock-opnames.store') }}">
            @csrf
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="title" class="form-label">Judul Stock Opname <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('title') is-invalid @enderror" 
                         id="title" name="title" value="{{ old('title') }}" 
                         placeholder="Contoh: Stock Opname Bulanan Januari 2025" required>
                  @error('title')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="branch_id" class="form-label">Cabang <span class="text-danger">*</span></label>
                  <select class="form-select @error('branch_id') is-invalid @enderror"
                          id="branch_id" name="branch_id" required>
                    <option value="">Pilih Cabang</option>
                    @foreach($branches as $branch)
                      <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                        {{ $branch->name }} ({{ $branch->code }})
                      </option>
                    @endforeach
                  </select>
                  @error('branch_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label for="asset_category_id" class="form-label">Kategori Asset</label>
                  <select class="form-select @error('asset_category_id') is-invalid @enderror"
                          id="asset_category_id" name="asset_category_id">
                    <option value="">Semua Kategori</option>
                    @foreach($assetCategories as $category)
                      <option value="{{ $category->id }}" {{ old('asset_category_id') == $category->id ? 'selected' : '' }}>
                        {{ $category->name }}
                      </option>
                    @endforeach
                  </select>
                  @error('asset_category_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <div class="form-text">
                    <small class="text-muted">Kosongkan jika ingin stock opname semua kategori asset</small>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="mb-3">
                  <label for="description" class="form-label">Deskripsi</label>
                  <textarea class="form-control @error('description') is-invalid @enderror" 
                            id="description" name="description" rows="3" 
                            placeholder="Deskripsi detail tentang stock opname ini...">{{ old('description') }}</textarea>
                  @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="mb-3">
                  <label for="notes" class="form-label">Catatan</label>
                  <textarea class="form-control @error('notes') is-invalid @enderror" 
                            id="notes" name="notes" rows="2" 
                            placeholder="Catatan tambahan...">{{ old('notes') }}</textarea>
                  @error('notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Info Box -->
            <div class="alert alert-info d-flex align-items-start" role="alert">
              <i class="ri-information-line ri-24px me-3"></i>
              <div>
                <h6 class="alert-heading mb-1">Informasi Penting</h6>
                <ul class="mb-0 ps-3">
                  <li>Stock opname akan dibuat dalam status <strong>Draft</strong></li>
                  <li>Setelah dibuat, Anda dapat memulai stock opname untuk mengunci operasi asset</li>
                  <li>Semua asset di cabang yang dipilih akan dimasukkan dalam daftar stock opname</li>
                  <li>Hanya satu stock opname yang dapat aktif dalam satu waktu</li>
                  <li><strong>Form Hardcopy:</strong> Setelah stock opname dibuat, Anda dapat download form Excel sebagai backup jika sistem mengalami gangguan</li>
                </ul>
              </div>
            </div>

            <!-- Asset Count Preview -->
            <div class="card bg-light mb-4" id="assetCountPreview" style="display: none;">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <i class="ri-database-2-line ri-24px text-primary me-3"></i>
                  <div>
                    <h6 class="mb-1">Preview Asset</h6>
                    <p class="mb-0">
                      <span id="assetCount">0</span> asset akan dimasukkan dalam stock opname ini
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between">
              <a href="{{ route('stock-opnames.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>
                Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                Buat Stock Opname
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const branchSelect = document.getElementById('branch_id');
  const categorySelect = document.getElementById('asset_category_id');
  const assetCountPreview = document.getElementById('assetCountPreview');
  const assetCountSpan = document.getElementById('assetCount');
  const submitBtn = document.querySelector('button[type="submit"]');

  // Active branch IDs that are locked
  const activeBranchIds = @json(isset($activeOpnames) ? $activeOpnames->pluck('branch_id')->toArray() : []);

  // Initially disable submit button until valid selection is made
  submitBtn.disabled = true;

  function updateAssetPreview() {
    const branchId = branchSelect.value;
    const categoryId = categorySelect.value;

    // Check if selected branch is active
    if (branchId && activeBranchIds.includes(parseInt(branchId))) {
      // Show warning and disable submit
      assetCountPreview.style.display = 'block';
      assetCountPreview.className = 'card bg-danger mb-4';
      assetCountPreview.innerHTML = `
        <div class="card-body">
          <div class="d-flex align-items-center">
            <i class="ri-error-warning-line ri-24px text-white me-3"></i>
            <div class="text-white">
              <h6 class="mb-1 text-white">Cabang Tidak Tersedia</h6>
              <p class="mb-0">Cabang ini sedang memiliki stock opname aktif. Pilih cabang lain.</p>
            </div>
          </div>
        </div>
      `;
      submitBtn.disabled = true;
      return;
    }

    // Reset submit button
    submitBtn.disabled = false;

    if (branchId) {
      // Show loading
      assetCountPreview.style.display = 'block';
      assetCountPreview.className = 'card bg-light mb-4';
      assetCountPreview.innerHTML = `
        <div class="card-body">
          <div class="d-flex align-items-center">
            <i class="ri-database-2-line ri-24px text-primary me-3"></i>
            <div>
              <h6 class="mb-1">Preview Asset</h6>
              <p class="mb-0">
                <span id="assetCount">Loading...</span> asset akan dimasukkan dalam stock opname ini
              </p>
            </div>
          </div>
        </div>
      `;

      // Build URL with category filter if selected
      let url = `/api/branches/${branchId}/asset-count`;
      if (categoryId) {
        url += `?category_id=${categoryId}`;
      }

      // Fetch asset count for selected branch and category
      fetch(url)
        .then(response => response.json())
        .then(data => {
          const count = data.count || 0;

          if (count === 0) {
            // Show warning when no assets found
            assetCountPreview.className = 'card bg-warning mb-4';
            assetCountPreview.innerHTML = `
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <i class="ri-alert-line ri-24px text-white me-3"></i>
                  <div class="text-white">
                    <h6 class="mb-1 text-white">Tidak Ada Asset</h6>
                    <p class="mb-0">Tidak ada asset yang ditemukan pada cabang dan kategori yang dipilih. Stock opname tidak dapat dilanjutkan.</p>
                  </div>
                </div>
              </div>
            `;
            submitBtn.disabled = true;
          } else {
            // Show success when assets found
            assetCountPreview.className = 'card bg-success mb-4';
            assetCountPreview.innerHTML = `
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <i class="ri-database-2-line ri-24px text-white me-3"></i>
                  <div class="text-white">
                    <h6 class="mb-1 text-white">Preview Asset</h6>
                    <p class="mb-0">
                      <strong>${count}</strong> asset akan dimasukkan dalam stock opname ini
                    </p>
                  </div>
                </div>
              </div>
            `;
            submitBtn.disabled = false;
          }
        })
        .catch(error => {
          console.error('Error:', error);
          assetCountPreview.className = 'card bg-danger mb-4';
          assetCountPreview.innerHTML = `
            <div class="card-body">
              <div class="d-flex align-items-center">
                <i class="ri-error-warning-line ri-24px text-white me-3"></i>
                <div class="text-white">
                  <h6 class="mb-1 text-white">Error</h6>
                  <p class="mb-0">Gagal memuat jumlah asset. Silakan coba lagi.</p>
                </div>
              </div>
            </div>
          `;
          submitBtn.disabled = true;
        });
    } else {
      // Show message when no branch is selected
      assetCountPreview.style.display = 'block';
      assetCountPreview.className = 'card bg-light mb-4';
      assetCountPreview.innerHTML = `
        <div class="card-body">
          <div class="d-flex align-items-center">
            <i class="ri-information-line ri-24px text-muted me-3"></i>
            <div class="text-muted">
              <h6 class="mb-1">Pilih Cabang</h6>
              <p class="mb-0">Silakan pilih cabang untuk melihat preview asset yang akan di-stock opname.</p>
            </div>
          </div>
        </div>
      `;
      submitBtn.disabled = true;
    }
  };

  // Add event listeners for both branch and category changes
  branchSelect.addEventListener('change', updateAssetPreview);
  categorySelect.addEventListener('change', updateAssetPreview);

  // Initial call to show the preview state
  updateAssetPreview();
});
</script>

@if(session('error'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-danger show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-error-warning-line me-2"></i>
      <div class="me-auto fw-semibold">Error</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('error') }}
    </div>
  </div>
@endif

@endsection
