@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Approval Workflow - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Approval Workflows /</span> Edit Workflow
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Edit Approval Workflow - {{ $approvalWorkflow->name }}</h5>
          <a href="{{ route('master.approval-workflows.show', $approvalWorkflow) }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          @if($errors->any())
            <div class="alert alert-danger alert-dismissible" role="alert">
              <h6 class="alert-heading d-flex align-items-center">
                <i class="ri-error-warning-line me-2"></i>
                Terdapat Kesalahan Input
              </h6>
              <ul class="mb-0">
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          @endif

          <form action="{{ route('master.approval-workflows.update', $approvalWorkflow) }}" method="POST" id="workflowForm">
            @csrf
            @method('PUT')

            <h6 class="mb-3">Informasi Workflow</h6>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name">Nama Workflow <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror"
                       id="name" name="name" value="{{ old('name', $approvalWorkflow->name) }}"
                       placeholder="Contoh: Approval Permintaan Asset">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Workflow <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror"
                       id="code" name="code" value="{{ old('code', $approvalWorkflow->code) }}"
                       placeholder="Contoh: ASSET_REQUEST_APPROVAL">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="module">Module <span class="text-danger">*</span></label>
                <select class="form-select @error('module') is-invalid @enderror" id="module" name="module">
                  <option value="">Pilih Module</option>
                  <option value="asset_requests" {{ old('module', $approvalWorkflow->module) === 'asset_requests' ? 'selected' : '' }}>Asset Requests</option>
                  <option value="purchase_orders" {{ old('module', $approvalWorkflow->module) === 'purchase_orders' ? 'selected' : '' }}>Purchase Orders</option>
                  <option value="budget_requests" {{ old('module', $approvalWorkflow->module) === 'budget_requests' ? 'selected' : '' }}>Budget Requests</option>
                </select>
                @error('module')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="priority">Prioritas <span class="text-danger">*</span></label>
                <input type="number" class="form-control @error('priority') is-invalid @enderror"
                       id="priority" name="priority" value="{{ old('priority', $approvalWorkflow->priority) }}"
                       min="0" placeholder="100">
                @error('priority')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Semakin tinggi angka, semakin prioritas</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror"
                        id="description" name="description" rows="3"
                        placeholder="Deskripsi workflow approval">{{ old('description', $approvalWorkflow->description) }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                       {{ old('is_active', $approvalWorkflow->is_active) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Workflow Aktif
                </label>
              </div>
            </div>

            <hr class="my-4">
            <h6 class="mb-3">Kondisi Workflow</h6>
            <p class="text-muted mb-3">Tentukan kondisi kapan workflow ini akan digunakan</p>

            <div id="conditions-container">
              @if(old('conditions'))
                @foreach(old('conditions') as $index => $condition)
                <div class="condition-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Kondisi {{ $index + 1 }}</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-condition" {{ $index === 0 ? 'disabled' : '' }}>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>

                  <div class="row">
                    <div class="col-md-4">
                      <label class="form-label">Field <span class="text-danger">*</span></label>
                      <select class="form-select" name="conditions[{{ $index }}][field]">
                        <option value="">Pilih Field</option>
                        <option value="estimated_total" {{ ($condition['field'] ?? '') === 'estimated_total' ? 'selected' : '' }}>Total Estimasi</option>
                        <option value="quantity" {{ ($condition['field'] ?? '') === 'quantity' ? 'selected' : '' }}>Jumlah</option>
                        <option value="priority" {{ ($condition['field'] ?? '') === 'priority' ? 'selected' : '' }}>Prioritas</option>
                        <option value="category_id" {{ ($condition['field'] ?? '') === 'category_id' ? 'selected' : '' }}>Kategori</option>
                        <option value="branch_id" {{ ($condition['field'] ?? '') === 'branch_id' ? 'selected' : '' }}>Cabang</option>
                        <option value="division_id" {{ ($condition['field'] ?? '') === 'division_id' ? 'selected' : '' }}>Divisi</option>
                      </select>
                    </div>
                    <div class="col-md-3">
                      <label class="form-label">Operator <span class="text-danger">*</span></label>
                      <select class="form-select" name="conditions[{{ $index }}][operator]">
                        <option value="">Pilih Operator</option>
                        <option value="=" {{ ($condition['operator'] ?? '') === '=' ? 'selected' : '' }}>Sama dengan (=)</option>
                        <option value="!=" {{ ($condition['operator'] ?? '') === '!=' ? 'selected' : '' }}>Tidak sama dengan (!=)</option>
                        <option value=">" {{ ($condition['operator'] ?? '') === '>' ? 'selected' : '' }}>Lebih besar (>)</option>
                        <option value=">=" {{ ($condition['operator'] ?? '') === '>=' ? 'selected' : '' }}>Lebih besar sama dengan (>=)</option>
                        <option value="<" {{ ($condition['operator'] ?? '') === '<' ? 'selected' : '' }}>Lebih kecil (<)</option>
                        <option value="<=" {{ ($condition['operator'] ?? '') === '<=' ? 'selected' : '' }}>Lebih kecil sama dengan (<=)</option>
                      </select>
                    </div>
                    <div class="col-md-5">
                      <label class="form-label">Nilai <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="conditions[{{ $index }}][value]"
                             value="{{ $condition['value'] ?? '' }}" placeholder="Nilai kondisi">
                    </div>
                  </div>
                </div>
                @endforeach
              @elseif($approvalWorkflow->conditions && count($approvalWorkflow->conditions) > 0)
                @foreach($approvalWorkflow->conditions as $index => $condition)
                <div class="condition-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Kondisi {{ $index + 1 }}</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-condition" {{ $index === 0 ? 'disabled' : '' }}>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>

                  <div class="row">
                    <div class="col-md-4">
                      <label class="form-label">Field <span class="text-danger">*</span></label>
                      <select class="form-select" name="conditions[{{ $index }}][field]">
                        <option value="">Pilih Field</option>
                        <option value="estimated_total" {{ ($condition['field'] ?? '') === 'estimated_total' ? 'selected' : '' }}>Total Estimasi</option>
                        <option value="quantity" {{ ($condition['field'] ?? '') === 'quantity' ? 'selected' : '' }}>Jumlah</option>
                        <option value="priority" {{ ($condition['field'] ?? '') === 'priority' ? 'selected' : '' }}>Prioritas</option>
                        <option value="category_id" {{ ($condition['field'] ?? '') === 'category_id' ? 'selected' : '' }}>Kategori</option>
                        <option value="branch_id" {{ ($condition['field'] ?? '') === 'branch_id' ? 'selected' : '' }}>Cabang</option>
                        <option value="division_id" {{ ($condition['field'] ?? '') === 'division_id' ? 'selected' : '' }}>Divisi</option>
                      </select>
                    </div>
                    <div class="col-md-3">
                      <label class="form-label">Operator <span class="text-danger">*</span></label>
                      <select class="form-select" name="conditions[{{ $index }}][operator]">
                        <option value="">Pilih Operator</option>
                        <option value="=" {{ ($condition['operator'] ?? '') === '=' ? 'selected' : '' }}>Sama dengan (=)</option>
                        <option value="!=" {{ ($condition['operator'] ?? '') === '!=' ? 'selected' : '' }}>Tidak sama dengan (!=)</option>
                        <option value=">" {{ ($condition['operator'] ?? '') === '>' ? 'selected' : '' }}>Lebih besar (>)</option>
                        <option value=">=" {{ ($condition['operator'] ?? '') === '>=' ? 'selected' : '' }}>Lebih besar sama dengan (>=)</option>
                        <option value="<" {{ ($condition['operator'] ?? '') === '<' ? 'selected' : '' }}>Lebih kecil (<)</option>
                        <option value="<=" {{ ($condition['operator'] ?? '') === '<=' ? 'selected' : '' }}>Lebih kecil sama dengan (<=)</option>
                      </select>
                    </div>
                    <div class="col-md-5">
                      <label class="form-label">Nilai <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="conditions[{{ $index }}][value]"
                             value="{{ $condition['value'] ?? '' }}" placeholder="Nilai kondisi">
                    </div>
                  </div>
                </div>
                @endforeach
              @else
                <div class="condition-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Kondisi 1</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-condition" disabled>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>

                  <div class="row">
                    <div class="col-md-4">
                      <label class="form-label">Field <span class="text-danger">*</span></label>
                      <select class="form-select" name="conditions[0][field]">
                        <option value="">Pilih Field</option>
                        <option value="estimated_total" selected>Total Estimasi</option>
                        <option value="quantity">Jumlah</option>
                        <option value="priority">Prioritas</option>
                        <option value="category_id">Kategori</option>
                        <option value="branch_id">Cabang</option>
                        <option value="division_id">Divisi</option>
                      </select>
                    </div>
                    <div class="col-md-3">
                      <label class="form-label">Operator <span class="text-danger">*</span></label>
                      <select class="form-select" name="conditions[0][operator]">
                        <option value="">Pilih Operator</option>
                        <option value="=" >Sama dengan (=)</option>
                        <option value="!=" >Tidak sama dengan (!=)</option>
                        <option value=">" >Lebih besar (>)</option>
                        <option value=">=" selected>Lebih besar sama dengan (>=)</option>
                        <option value="<" >Lebih kecil (<)</option>
                        <option value="<=" >Lebih kecil sama dengan (<=)</option>
                      </select>
                    </div>
                    <div class="col-md-5">
                      <label class="form-label">Nilai <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="conditions[0][value]"
                             value="0" placeholder="Nilai kondisi">
                    </div>
                  </div>
                </div>
              @endif
            </div>

            <button type="button" class="btn btn-outline-primary mb-4" id="add-condition">
              <i class="ri-add-line me-1"></i>Tambah Kondisi
            </button>

            <hr class="my-4">
            <h6 class="mb-3">Level Approval</h6>
            <p class="text-muted mb-3">Tentukan level-level approval yang diperlukan</p>

            <div id="levels-container">
              @if(old('levels'))
                @foreach(old('levels') as $index => $level)
                <div class="level-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Level {{ $index + 1 }}</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-level" {{ $index === 0 ? 'disabled' : '' }}>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-6">
                      <label class="form-label">Nama Level <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="levels[{{ $index }}][level_name]"
                             value="{{ $level['level_name'] ?? '' }}" placeholder="Contoh: Supervisor">
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Tipe Approver <span class="text-danger">*</span></label>
                      <select class="form-select approver-type" name="levels[{{ $index }}][approver_type]" data-level-index="{{ $index }}">
                        <option value="">Pilih Tipe</option>
                        <option value="role" {{ ($level['approver_type'] ?? '') === 'role' ? 'selected' : '' }}>Role</option>
                        <option value="user" {{ ($level['approver_type'] ?? '') === 'user' ? 'selected' : '' }}>User Spesifik</option>
                        <option value="position" {{ ($level['approver_type'] ?? '') === 'position' ? 'selected' : '' }}>Jabatan</option>
                      </select>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-12">
                      <label class="form-label">Konfigurasi Approver <span class="text-danger">*</span></label>
                      <div class="approver-config">
                        @if(($level['approver_type'] ?? '') === 'role')
                          <select class="form-select" name="levels[{{ $index }}][approver_config][roles][]" multiple>
                            @foreach($roles as $role)
                              <option value="{{ $role->name }}"
                                {{ in_array($role->name, $level['approver_config']['roles'] ?? []) ? 'selected' : '' }}>
                                {{ $role->name }}
                              </option>
                            @endforeach
                          </select>
                          <div class="form-text">Pilih role yang dapat melakukan approval di level ini</div>
                        @elseif(($level['approver_type'] ?? '') === 'user')
                          <select class="form-select" name="levels[{{ $index }}][approver_config][users][]" multiple>
                            @foreach($users as $user)
                              <option value="{{ $user->id }}"
                                {{ in_array($user->id, $level['approver_config']['users'] ?? []) ? 'selected' : '' }}>
                                {{ $user->name }}
                              </option>
                            @endforeach
                          </select>
                          <div class="form-text">Pilih user spesifik yang dapat melakukan approval</div>
                        @else
                          <input type="text" class="form-control" name="levels[{{ $index }}][approver_config][positions]"
                                 value="{{ is_array($level['approver_config']['positions'] ?? []) ? implode(',', $level['approver_config']['positions']) : ($level['approver_config']['positions'] ?? '') }}"
                                 placeholder="Contoh: Manager,Supervisor">
                          <div class="form-text">Masukkan jabatan yang dapat melakukan approval (pisahkan dengan koma)</div>
                        @endif
                      </div>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-12">
                      <label class="form-label">Deskripsi</label>
                      <textarea class="form-control" name="levels[{{ $index }}][description]" rows="2"
                                placeholder="Deskripsi level approval">{{ $level['description'] ?? '' }}</textarea>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-4">
                      <label class="form-label">Timeout (Jam)</label>
                      <input type="number" class="form-control" name="levels[{{ $index }}][timeout_hours]"
                             value="{{ $level['timeout_hours'] ?? '' }}" min="1" placeholder="24">
                    </div>
                    <div class="col-md-4">
                      <label class="form-label">Aksi Timeout</label>
                      <select class="form-select" name="levels[{{ $index }}][timeout_action]">
                        <option value="approve" {{ ($level['timeout_action'] ?? '') === 'approve' ? 'selected' : '' }}>Auto Approve</option>
                        <option value="reject" {{ ($level['timeout_action'] ?? '') === 'reject' ? 'selected' : '' }}>Auto Reject</option>
                        <option value="escalate" {{ ($level['timeout_action'] ?? 'escalate') === 'escalate' ? 'selected' : '' }}>Escalate</option>
                      </select>
                    </div>
                    <div class="col-md-4">
                      <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" name="levels[{{ $index }}][is_required]" value="1"
                               {{ ($level['is_required'] ?? true) ? 'checked' : '' }}>
                        <label class="form-check-label">Level Wajib</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="levels[{{ $index }}][can_skip]" value="1"
                               {{ ($level['can_skip'] ?? false) ? 'checked' : '' }}>
                        <label class="form-check-label">Bisa Di-skip</label>
                      </div>
                    </div>
                  </div>
                </div>
                @endforeach
              @elseif($approvalWorkflow->levels && count($approvalWorkflow->levels) > 0)
                @foreach($approvalWorkflow->levels as $index => $level)
                <div class="level-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Level {{ $index + 1 }}</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-level" {{ $index === 0 ? 'disabled' : '' }}>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-6">
                      <label class="form-label">Nama Level <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="levels[{{ $index }}][level_name]"
                             value="{{ $level->level_name }}" placeholder="Contoh: Supervisor">
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Tipe Approver <span class="text-danger">*</span></label>
                      <select class="form-select approver-type" name="levels[{{ $index }}][approver_type]" data-level-index="{{ $index }}">
                        <option value="">Pilih Tipe</option>
                        <option value="role" {{ $level->approver_type === 'role' ? 'selected' : '' }}>Role</option>
                        <option value="user" {{ $level->approver_type === 'user' ? 'selected' : '' }}>User Spesifik</option>
                        <option value="position" {{ $level->approver_type === 'position' ? 'selected' : '' }}>Jabatan</option>
                      </select>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-12">
                      <label class="form-label">Konfigurasi Approver <span class="text-danger">*</span></label>
                      <div class="approver-config">
                        @if($level->approver_type === 'role')
                          <select class="form-select" name="levels[{{ $index }}][approver_config][roles][]" multiple>
                            @foreach($roles as $role)
                              <option value="{{ $role->name }}"
                                {{ in_array($role->name, $level->approver_config['roles'] ?? []) ? 'selected' : '' }}>
                                {{ $role->name }}
                              </option>
                            @endforeach
                          </select>
                          <div class="form-text">Pilih role yang dapat melakukan approval di level ini</div>
                        @elseif($level->approver_type === 'user')
                          <select class="form-select" name="levels[{{ $index }}][approver_config][users][]" multiple>
                            @foreach($users as $user)
                              <option value="{{ $user->id }}"
                                {{ in_array($user->id, $level->approver_config['users'] ?? []) ? 'selected' : '' }}>
                                {{ $user->name }}
                              </option>
                            @endforeach
                          </select>
                          <div class="form-text">Pilih user spesifik yang dapat melakukan approval</div>
                        @else
                          <input type="text" class="form-control" name="levels[{{ $index }}][approver_config][positions]"
                                 value="{{ is_array($level->approver_config['positions'] ?? []) ? implode(',', $level->approver_config['positions']) : ($level->approver_config['positions'] ?? '') }}"
                                 placeholder="Contoh: Manager,Supervisor">
                          <div class="form-text">Masukkan jabatan yang dapat melakukan approval (pisahkan dengan koma)</div>
                        @endif
                      </div>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-12">
                      <label class="form-label">Deskripsi</label>
                      <textarea class="form-control" name="levels[{{ $index }}][description]" rows="2"
                                placeholder="Deskripsi level approval">{{ $level->description }}</textarea>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-4">
                      <label class="form-label">Timeout (Jam)</label>
                      <input type="number" class="form-control" name="levels[{{ $index }}][timeout_hours]"
                             value="{{ $level->timeout_hours }}" min="1" placeholder="24">
                    </div>
                    <div class="col-md-4">
                      <label class="form-label">Aksi Timeout</label>
                      <select class="form-select" name="levels[{{ $index }}][timeout_action]">
                        <option value="approve" {{ $level->timeout_action === 'approve' ? 'selected' : '' }}>Auto Approve</option>
                        <option value="reject" {{ $level->timeout_action === 'reject' ? 'selected' : '' }}>Auto Reject</option>
                        <option value="escalate" {{ $level->timeout_action === 'escalate' ? 'selected' : '' }}>Escalate</option>
                      </select>
                    </div>
                    <div class="col-md-4">
                      <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" name="levels[{{ $index }}][is_required]" value="1"
                               {{ $level->is_required ? 'checked' : '' }}>
                        <label class="form-check-label">Level Wajib</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="levels[{{ $index }}][can_skip]" value="1"
                               {{ $level->can_skip ? 'checked' : '' }}>
                        <label class="form-check-label">Bisa Di-skip</label>
                      </div>
                    </div>
                  </div>
                </div>
                @endforeach
              @else
                <div class="level-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Level 1</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-level" disabled>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-6">
                      <label class="form-label">Nama Level <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="levels[0][level_name]"
                             placeholder="Contoh: Supervisor">
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Tipe Approver <span class="text-danger">*</span></label>
                      <select class="form-select approver-type" name="levels[0][approver_type]" data-level-index="0">
                        <option value="">Pilih Tipe</option>
                        <option value="role">Role</option>
                        <option value="user">User Spesifik</option>
                        <option value="position">Jabatan</option>
                      </select>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-12">
                      <label class="form-label">Konfigurasi Approver <span class="text-danger">*</span></label>
                      <div class="approver-config">
                        <div class="text-muted">Pilih tipe approver terlebih dahulu</div>
                      </div>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-12">
                      <label class="form-label">Deskripsi</label>
                      <textarea class="form-control" name="levels[0][description]" rows="2"
                                placeholder="Deskripsi level approval"></textarea>
                    </div>
                  </div>

                  <div class="row mb-3">
                    <div class="col-md-4">
                      <label class="form-label">Timeout (Jam)</label>
                      <input type="number" class="form-control" name="levels[0][timeout_hours]"
                             min="1" placeholder="24">
                    </div>
                    <div class="col-md-4">
                      <label class="form-label">Aksi Timeout</label>
                      <select class="form-select" name="levels[0][timeout_action]">
                        <option value="approve">Auto Approve</option>
                        <option value="reject">Auto Reject</option>
                        <option value="escalate" selected>Escalate</option>
                      </select>
                    </div>
                    <div class="col-md-4">
                      <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" name="levels[0][is_required]" value="1" checked>
                        <label class="form-check-label">Level Wajib</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="levels[0][can_skip]" value="1">
                        <label class="form-check-label">Bisa Di-skip</label>
                      </div>
                    </div>
                  </div>
                </div>
              @endif
            </div>

            <button type="button" class="btn btn-outline-primary mb-4" id="add-level">
              <i class="ri-add-line me-1"></i>Tambah Level
            </button>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.approval-workflows.show', $approvalWorkflow) }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Update Workflow
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Workflow</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Data Saat Ini:</h6>
            <p class="mb-2">Nama: <strong>{{ $approvalWorkflow->name }}</strong></p>
            <p class="mb-2">Kode: <strong>{{ $approvalWorkflow->code }}</strong></p>
            <p class="mb-2">Module: <strong>{{ ucfirst(str_replace('_', ' ', $approvalWorkflow->module)) }}</strong></p>
            <p class="mb-2">Prioritas: <strong>{{ $approvalWorkflow->priority }}</strong></p>
            <p class="mb-2">Level: <strong>{{ $approvalWorkflow->levels->count() }}</strong></p>
            <p class="mb-0">Status:
              <span class="badge bg-{{ $approvalWorkflow->is_active ? 'success' : 'secondary' }}">
                {{ $approvalWorkflow->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </p>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Panduan Edit</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Perubahan workflow akan mempengaruhi approval yang sedang berjalan</li>
              <li>Pastikan konfigurasi approver sudah benar</li>
              <li>Level approval akan diurutkan sesuai urutan input</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Tips Edit:</h6>
            <ul class="mb-0">
              <li>Gunakan prioritas tinggi untuk workflow utama</li>
              <li>Set timeout yang realistis</li>
              <li>Pastikan ada minimal 1 level approval</li>
              <li>Test workflow setelah perubahan</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let conditionIndex = {{ old('conditions') ? count(old('conditions')) : ($approvalWorkflow->conditions ? count($approvalWorkflow->conditions) : 1) }};
  let levelIndex = {{ old('levels') ? count(old('levels')) : ($approvalWorkflow->levels ? count($approvalWorkflow->levels) : 1) }};

  // Roles and Users data for dynamic generation
  const rolesData = @json($roles);
  const usersData = @json($users);

  // Add condition
  document.getElementById('add-condition').addEventListener('click', function() {
    const container = document.getElementById('conditions-container');
    const conditionHtml = `
      <div class="condition-row border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Kondisi ${conditionIndex + 1}</h6>
          <button type="button" class="btn btn-outline-danger btn-sm remove-condition">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>

        <div class="row">
          <div class="col-md-4">
            <label class="form-label">Field <span class="text-danger">*</span></label>
            <select class="form-select" name="conditions[${conditionIndex}][field]">
              <option value="">Pilih Field</option>
              <option value="estimated_total">Total Estimasi</option>
              <option value="quantity">Jumlah</option>
              <option value="priority">Prioritas</option>
              <option value="category_id">Kategori</option>
              <option value="branch_id">Cabang</option>
              <option value="division_id">Divisi</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Operator <span class="text-danger">*</span></label>
            <select class="form-select" name="conditions[${conditionIndex}][operator]">
              <option value="">Pilih Operator</option>
              <option value="=">Sama dengan (=)</option>
              <option value="!=">Tidak sama dengan (!=)</option>
              <option value=">">Lebih besar (>)</option>
              <option value=">=">Lebih besar sama dengan (>=)</option>
              <option value="<">Lebih kecil (<)</option>
              <option value="<=">Lebih kecil sama dengan (<=)</option>
            </select>
          </div>
          <div class="col-md-5">
            <label class="form-label">Nilai <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="conditions[${conditionIndex}][value]"
                   placeholder="Nilai kondisi">
          </div>
        </div>
      </div>
    `;
    container.insertAdjacentHTML('beforeend', conditionHtml);
    conditionIndex++;
    updateRemoveConditionButtons();
  });

  // Add level
  document.getElementById('add-level').addEventListener('click', function() {
    const container = document.getElementById('levels-container');
    const levelHtml = `
      <div class="level-row border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Level ${levelIndex + 1}</h6>
          <button type="button" class="btn btn-outline-danger btn-sm remove-level">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Nama Level <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="levels[${levelIndex}][level_name]"
                   placeholder="Contoh: Supervisor">
          </div>
          <div class="col-md-6">
            <label class="form-label">Tipe Approver <span class="text-danger">*</span></label>
            <select class="form-select approver-type" name="levels[${levelIndex}][approver_type]" data-level-index="${levelIndex}">
              <option value="">Pilih Tipe</option>
              <option value="role">Role</option>
              <option value="user">User Spesifik</option>
              <option value="position">Jabatan</option>
            </select>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-12">
            <label class="form-label">Konfigurasi Approver <span class="text-danger">*</span></label>
            <div class="approver-config">
              <div class="text-muted">Pilih tipe approver terlebih dahulu</div>
            </div>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-12">
            <label class="form-label">Deskripsi</label>
            <textarea class="form-control" name="levels[${levelIndex}][description]" rows="2"
                      placeholder="Deskripsi level approval"></textarea>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-4">
            <label class="form-label">Timeout (Jam)</label>
            <input type="number" class="form-control" name="levels[${levelIndex}][timeout_hours]"
                   min="1" placeholder="24">
          </div>
          <div class="col-md-4">
            <label class="form-label">Aksi Timeout</label>
            <select class="form-select" name="levels[${levelIndex}][timeout_action]">
              <option value="approve">Auto Approve</option>
              <option value="reject">Auto Reject</option>
              <option value="escalate" selected>Escalate</option>
            </select>
          </div>
          <div class="col-md-4">
            <div class="form-check mt-4">
              <input class="form-check-input" type="checkbox" name="levels[${levelIndex}][is_required]" value="1" checked>
              <label class="form-check-label">Level Wajib</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="levels[${levelIndex}][can_skip]" value="1">
              <label class="form-check-label">Bisa Di-skip</label>
            </div>
          </div>
        </div>
      </div>
    `;
    container.insertAdjacentHTML('beforeend', levelHtml);
    levelIndex++;
    updateRemoveLevelButtons();
    updateLevelNumbers();
    attachApproverTypeHandlers();
  });

  // Remove condition
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-condition')) {
      e.target.closest('.condition-row').remove();
      updateRemoveConditionButtons();
      updateConditionNumbers();
    }
  });

  // Remove level
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-level')) {
      e.target.closest('.level-row').remove();
      updateRemoveLevelButtons();
      updateLevelNumbers();
      reindexLevels();
    }
  });

  // Handle approver type change
  function attachApproverTypeHandlers() {
    document.querySelectorAll('.approver-type').forEach(select => {
      // Remove existing event listeners to prevent duplicates
      select.removeEventListener('change', handleApproverTypeChange);
      select.addEventListener('change', handleApproverTypeChange);
    });
  }

  function handleApproverTypeChange(event) {
    const select = event.target;
    const configDiv = select.closest('.level-row').querySelector('.approver-config');
    const levelIndex = select.getAttribute('data-level-index') || select.name.match(/\[(\d+)\]/)[1];

    if (select.value === 'role') {
      let roleOptions = '';
      rolesData.forEach(role => {
        roleOptions += `<option value="${role.name}">${role.name}</option>`;
      });

      configDiv.innerHTML = `
        <select class="form-select" name="levels[${levelIndex}][approver_config][roles][]" multiple>
          ${roleOptions}
        </select>
        <div class="form-text">Pilih role yang dapat melakukan approval di level ini</div>
      `;
    } else if (select.value === 'user') {
      let userOptions = '';
      usersData.forEach(user => {
        userOptions += `<option value="${user.id}">${user.name}</option>`;
      });

      configDiv.innerHTML = `
        <select class="form-select" name="levels[${levelIndex}][approver_config][users][]" multiple>
          ${userOptions}
        </select>
        <div class="form-text">Pilih user spesifik yang dapat melakukan approval</div>
      `;
    } else if (select.value === 'position') {
      configDiv.innerHTML = `
        <input type="text" class="form-control" name="levels[${levelIndex}][approver_config][positions]"
               placeholder="Contoh: Manager,Supervisor">
        <div class="form-text">Masukkan jabatan yang dapat melakukan approval (pisahkan dengan koma)</div>
      `;
    } else {
      configDiv.innerHTML = '<div class="text-muted">Pilih tipe approver terlebih dahulu</div>';
    }
  }

  function updateRemoveConditionButtons() {
    const conditions = document.querySelectorAll('.condition-row');
    conditions.forEach((condition, index) => {
      const removeBtn = condition.querySelector('.remove-condition');
      if (removeBtn) {
        removeBtn.disabled = conditions.length <= 1;
      }
    });
  }

  function updateRemoveLevelButtons() {
    const levels = document.querySelectorAll('.level-row');
    levels.forEach((level, index) => {
      const removeBtn = level.querySelector('.remove-level');
      if (removeBtn) {
        removeBtn.disabled = levels.length <= 1;
      }
    });
  }

  function updateConditionNumbers() {
    const conditions = document.querySelectorAll('.condition-row');
    conditions.forEach((condition, index) => {
      const header = condition.querySelector('h6');
      if (header) {
        header.textContent = `Kondisi ${index + 1}`;
      }
    });
  }

  function updateLevelNumbers() {
    const levels = document.querySelectorAll('.level-row');
    levels.forEach((level, index) => {
      const header = level.querySelector('h6');
      if (header) {
        header.textContent = `Level ${index + 1}`;
      }
    });
  }

  function reindexLevels() {
    const levels = document.querySelectorAll('.level-row');
    levels.forEach((level, index) => {
      // Update all name attributes to use correct index
      const inputs = level.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        if (input.name && input.name.includes('levels[')) {
          input.name = input.name.replace(/levels\[\d+\]/, `levels[${index}]`);
        }
      });

      // Update data-level-index attribute
      const approverTypeSelect = level.querySelector('.approver-type');
      if (approverTypeSelect) {
        approverTypeSelect.setAttribute('data-level-index', index);
      }
    });
  }

  // Initialize
  updateRemoveConditionButtons();
  updateRemoveLevelButtons();
  attachApproverTypeHandlers();
});
</script>
@endsection