<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AssetFieldConfiguration;
use App\Models\AssetCategory;

class AssetFieldConfigurationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AssetFieldConfiguration::with('assetCategory');

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('asset_category_id', $request->category_id);
        }

        // Filter by field group
        if ($request->filled('field_group')) {
            $query->where('field_group', $request->field_group);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('field_name', 'like', "%{$search}%")
                  ->orWhere('field_label', 'like', "%{$search}%");
            });
        }

        $fieldConfigurations = $query->ordered()->paginate(20);
        $categories = AssetCategory::active()->get();
        $fieldGroups = AssetFieldConfiguration::getFieldGroups();

        return view('master.asset-field-configurations.index', compact(
            'fieldConfigurations',
            'categories',
            'fieldGroups'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = AssetCategory::active()->get();
        $fieldTypes = AssetFieldConfiguration::getFieldTypes();
        $fieldGroups = AssetFieldConfiguration::getFieldGroups();
        $dataSourceTypes = AssetFieldConfiguration::getDataSourceTypes();
        $validationPatterns = AssetFieldConfiguration::getValidationPatterns();
        $availableTables = (new AssetFieldConfiguration())->getAvailableTables();
        $lookupCodes = \App\Models\Lookup::select('lookup_code')->distinct()->pluck('lookup_code', 'lookup_code');

        return view('master.asset-field-configurations.create', compact(
            'categories',
            'fieldTypes',
            'fieldGroups',
            'dataSourceTypes',
            'validationPatterns',
            'availableTables',
            'lookupCodes'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'field_name' => 'required|string|max:255|regex:/^[a-z_]+$/',
                'field_label' => 'required|string|max:255',
                'field_type' => 'required|string|in:' . implode(',', array_keys(AssetFieldConfiguration::getFieldTypes())),
                'field_group' => 'required|string|in:' . implode(',', array_keys(AssetFieldConfiguration::getFieldGroups())),
                'asset_category_id' => 'nullable|exists:asset_categories,id',
                'is_required' => 'boolean',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
                'help_text' => 'nullable|string',
                'field_options' => 'nullable|array',
                'validation_rules' => 'nullable|array',
                'data_source_type' => 'required|string|in:' . implode(',', array_keys(AssetFieldConfiguration::getDataSourceTypes())),
                'data_source_table' => 'nullable|string',
                'data_source_value_column' => 'nullable|string',
                'data_source_label_column' => 'nullable|string',
                'data_source_filter' => 'nullable|string',
                'enable_dependent' => 'boolean',
                'parent_field_id' => 'nullable|exists:asset_field_configurations,id',
                'parent_field_conditions' => 'nullable|string',
                'column_width' => 'integer|min:1|max:12',
                'css_class' => 'nullable|string',
                'validation_pattern' => 'nullable|string',
                'validation_message' => 'nullable|string',
                'input_mask' => 'nullable|string',
                'placeholder_text' => 'nullable|string',
                'min_length' => 'nullable|integer|min:0',
                'max_length' => 'nullable|integer|min:1',
            ], [
                'field_name.regex' => 'Field name harus menggunakan format snake_case (huruf kecil dan underscore).',
                'field_name.required' => 'Nama field wajib diisi.',
                'field_label.required' => 'Label field wajib diisi.',
                'field_type.required' => 'Tipe field wajib dipilih.',
                'field_group.required' => 'Grup field wajib dipilih.',
            ]);

            // Check for duplicate field name in same category
            $existingField = AssetFieldConfiguration::where('field_name', $validated['field_name'])
                ->where('asset_category_id', $validated['asset_category_id'])
                ->first();

            if ($existingField) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['field_name' => 'Field name sudah digunakan untuk kategori ini.']);
            }

            // Set default values
            $validated['is_required'] = $request->has('is_required');
            $validated['is_active'] = $request->has('is_active');
            $validated['enable_dependent'] = $request->has('enable_dependent');
            $validated['sort_order'] = $validated['sort_order'] ?? 0;
            $validated['column_width'] = $validated['column_width'] ?? 12;

            // Clear parent field data if dependent is not enabled
            if (!$validated['enable_dependent']) {
                $validated['parent_field_id'] = null;
                $validated['parent_field_conditions'] = null;
            } else {
                // Process parent field conditions
                if (!empty($validated['parent_field_conditions'])) {
                    $conditions = json_decode($validated['parent_field_conditions'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $validated['parent_field_conditions'] = $conditions;
                    } else {
                        $validated['parent_field_conditions'] = null;
                    }
                }
            }

            AssetFieldConfiguration::create($validated);

            return redirect()->route('master.asset-field-configurations.index')
                ->with('success', 'Konfigurasi field asset berhasil dibuat.');

        } catch (\Exception $e) {
            \Log::error('Error creating asset field configuration: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan saat membuat konfigurasi field.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetFieldConfiguration $assetFieldConfiguration)
    {
        return view('master.asset-field-configurations.show', compact('assetFieldConfiguration'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetFieldConfiguration $assetFieldConfiguration)
    {
        $categories = AssetCategory::active()->get();
        $fieldTypes = AssetFieldConfiguration::getFieldTypes();
        $fieldGroups = AssetFieldConfiguration::getFieldGroups();
        $dataSourceTypes = AssetFieldConfiguration::getDataSourceTypes();
        $availableTables = $assetFieldConfiguration->getAvailableTables();
        $lookupCodes = \App\Models\Lookup::select('lookup_code')->distinct()->pluck('lookup_code', 'lookup_code');

        return view('master.asset-field-configurations.edit', compact(
            'assetFieldConfiguration',
            'categories',
            'fieldTypes',
            'fieldGroups',
            'dataSourceTypes',
            'availableTables',
            'lookupCodes'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetFieldConfiguration $assetFieldConfiguration)
    {
        try {
            $validated = $request->validate([
                'field_name' => 'required|string|max:255|regex:/^[a-z_]+$/',
                'field_label' => 'required|string|max:255',
                'field_type' => 'required|string|in:' . implode(',', array_keys(AssetFieldConfiguration::getFieldTypes())),
                'field_group' => 'required|string|in:' . implode(',', array_keys(AssetFieldConfiguration::getFieldGroups())),
                'asset_category_id' => 'nullable|exists:asset_categories,id',
                'is_required' => 'boolean',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
                'help_text' => 'nullable|string',
                'field_options' => 'nullable|array',
                'validation_rules' => 'nullable|array',
            ]);

            // Check for duplicate field name in same category (excluding current record)
            $existingField = AssetFieldConfiguration::where('field_name', $validated['field_name'])
                ->where('asset_category_id', $validated['asset_category_id'])
                ->where('id', '!=', $assetFieldConfiguration->id)
                ->first();

            if ($existingField) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['field_name' => 'Field name sudah digunakan untuk kategori ini.']);
            }

            $assetFieldConfiguration->update($validated);

            return redirect()->route('master.asset-field-configurations.index')
                ->with('success', 'Konfigurasi field asset berhasil diperbarui.');

        } catch (\Exception $e) {
            \Log::error('Error updating asset field configuration: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan saat memperbarui konfigurasi field.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetFieldConfiguration $assetFieldConfiguration)
    {
        try {
            $assetFieldConfiguration->delete();

            return redirect()->route('master.asset-field-configurations.index')
                ->with('success', 'Konfigurasi field asset berhasil dihapus.');

        } catch (\Exception $e) {
            \Log::error('Error deleting asset field configuration: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat menghapus konfigurasi field.');
        }
    }

    /**
     * Get field configurations for specific category (AJAX)
     */
    public function getByCategory(Request $request)
    {
        $categoryId = $request->get('category_id');

        $fields = AssetFieldConfiguration::forCategory($categoryId)
            ->active()
            ->ordered()
            ->get();

        return response()->json($fields);
    }

    /**
     * Preview form with current field configurations
     */
    public function preview(Request $request)
    {
        $categoryId = $request->get('category_id');

        $fieldConfigurations = AssetFieldConfiguration::forCategory($categoryId)
            ->active()
            ->ordered()
            ->get()
            ->groupBy('field_group');

        return view('master.asset-field-configurations.preview', compact('fieldConfigurations', 'categoryId'));
    }

    /**
     * Get table columns for AJAX
     */
    public function getTableColumns(Request $request)
    {
        try {
            $tableName = $request->get('table');

            \Log::info('getTableColumns called', ['table' => $tableName]);

            if (!$tableName) {
                return response()->json(['error' => 'Table name is required'], 400);
            }

            $fieldConfig = new AssetFieldConfiguration();
            $columns = $fieldConfig->getTableColumns($tableName);

            \Log::info('Columns retrieved', ['table' => $tableName, 'columns' => $columns]);

            if (empty($columns)) {
                return response()->json(['error' => 'No columns found or table does not exist'], 404);
            }

            return response()->json([
                'success' => true,
                'columns' => $columns
            ]);

        } catch (\Exception $e) {
            \Log::error('Error in getTableColumns controller', [
                'table' => $request->get('table'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to load table columns: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * Get dependent options for child dropdown
     */
    public function getDependentOptions(Request $request)
    {
        try {
            $fieldId = $request->get('field_id');
            $parentValue = $request->get('parent_value');

            if (!$fieldId) {
                return response()->json(['error' => 'Field ID is required'], 400);
            }

            $field = AssetFieldConfiguration::find($fieldId);
            if (!$field) {
                return response()->json(['error' => 'Field not found'], 404);
            }

            $options = $field->getDependentOptions($parentValue);

            return response()->json([
                'success' => true,
                'options' => $options
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting dependent options', [
                'field_id' => $request->get('field_id'),
                'parent_value' => $request->get('parent_value'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to load dependent options: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test data source options
     */
    public function testDataSource(Request $request)
    {
        try {
            $dataSourceType = $request->get('data_source_type');
            $table = $request->get('table');
            $valueColumn = $request->get('value_column');
            $labelColumn = $request->get('label_column');
            $filter = $request->get('filter');
            $lookupCode = $request->get('lookup_code');

            $options = [];

            switch ($dataSourceType) {
                case 'database':
                    if ($table && $valueColumn && $labelColumn) {
                        $query = \DB::table($table);

                        if ($filter) {
                            $filters = json_decode($filter, true);
                            if (is_array($filters)) {
                                foreach ($filters as $column => $value) {
                                    $query->where($column, $value);
                                }
                            }
                        }

                        $results = $query->select([
                            $valueColumn . ' as value',
                            $labelColumn . ' as label'
                        ])->limit(10)->get();

                        foreach ($results as $result) {
                            $options[$result->value] = $result->label;
                        }
                    }
                    break;

                case 'lookup':
                    if ($lookupCode) {
                        $lookups = \App\Models\Lookup::where('lookup_code', $lookupCode)
                            ->where('is_active', true)
                            ->orderBy('sort_order')
                            ->limit(10)
                            ->get();

                        foreach ($lookups as $lookup) {
                            $options[$lookup->lookup_value] = $lookup->lookup_name;
                        }
                    }
                    break;
            }

            return response()->json([
                'success' => true,
                'options' => $options,
                'count' => count($options)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to test data source: ' . $e->getMessage()
            ], 500);
        }
    }
}
