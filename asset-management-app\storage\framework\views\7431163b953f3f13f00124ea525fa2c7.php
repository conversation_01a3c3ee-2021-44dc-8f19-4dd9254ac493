<?php $__env->startSection('title', 'Daftar Purchase Order - Asset Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi /</span> Purchase Order
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="<?php echo e(route('purchase-orders.create')); ?>" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Buat Purchase Order
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="<?php echo e(route('purchase-orders.index')); ?>">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="<?php echo e(request('search')); ?>" 
                   placeholder="Nomor PO, supplier, catatan...">
          </div>
          <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" name="status">
              <option value="">Semua Status</option>
              <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>Draft</option>
              <option value="submitted" <?php echo e(request('status') == 'submitted' ? 'selected' : ''); ?>>Diajukan</option>
              <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Disetujui</option>
              <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Ditolak</option>
              <option value="sent_to_supplier" <?php echo e(request('status') == 'sent_to_supplier' ? 'selected' : ''); ?>>Dikirim ke Supplier</option>
              <option value="partially_received" <?php echo e(request('status') == 'partially_received' ? 'selected' : ''); ?>>Diterima Sebagian</option>
              <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Selesai</option>
              <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Dibatalkan</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Supplier</label>
            <select class="form-select" name="supplier_id">
              <option value="">Semua Supplier</option>
              <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($supplier->id); ?>" <?php echo e(request('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                  <?php echo e($supplier->name); ?>

                </option>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Cabang</label>
            <select class="form-select" name="branch_id">
              <option value="">Semua Cabang</option>
              <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                  <?php echo e($branch->name); ?>

                </option>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Tanggal Dari</label>
            <input type="date" class="form-control" name="date_from" value="<?php echo e(request('date_from')); ?>">
          </div>
          <div class="col-md-2">
            <label class="form-label">Tanggal Sampai</label>
            <input type="date" class="form-control" name="date_to" value="<?php echo e(request('date_to')); ?>">
          </div>
          <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
              <i class="ri-search-line me-1"></i>Filter
            </button>
            <a href="<?php echo e(route('purchase-orders.index')); ?>" class="btn btn-outline-secondary">
              <i class="ri-refresh-line me-1"></i>Reset
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Purchase Orders Table -->
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">Daftar Purchase Order</h5>
    </div>
    <div class="card-body">
      <?php if($purchaseOrders->count() > 0): ?>
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Nomor PO</th>
                <th>Tanggal</th>
                <th>Supplier</th>
                <th>Cabang</th>
                <th>Total Item</th>
                <th>Total Amount</th>
                <th>Status</th>
                <th>Dibuat Oleh</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              <?php $__currentLoopData = $purchaseOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $po): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                  <td>
                    <strong><?php echo e($po->po_number); ?></strong>
                  </td>
                  <td><?php echo e($po->po_date->format('d/m/Y')); ?></td>
                  <td><?php echo e($po->supplier->name); ?></td>
                  <td><?php echo e($po->branch->name); ?></td>
                  <td>
                    <span class="badge bg-info"><?php echo e($po->total_items); ?> item</span>
                  </td>
                  <td>
                    <strong>Rp <?php echo e(number_format($po->total_amount, 0, ',', '.')); ?></strong>
                  </td>
                  <td>
                    <span class="badge bg-<?php echo e($po->status_badge); ?>"><?php echo e($po->status_label); ?></span>
                  </td>
                  <td><?php echo e($po->createdBy->name); ?></td>
                  <td>
                    <div class="dropdown">
                      <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        Aksi
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="<?php echo e(route('purchase-orders.show', $po)); ?>">
                          <i class="ri-eye-line me-1"></i>Lihat Detail
                        </a></li>
                        
                        <?php if($po->canBeEdited()): ?>
                          <li><a class="dropdown-item" href="<?php echo e(route('purchase-orders.edit', $po)); ?>">
                            <i class="ri-edit-line me-1"></i>Edit
                          </a></li>
                        <?php endif; ?>
                        
                        <li><a class="dropdown-item" href="<?php echo e(route('purchase-orders.print', $po)); ?>" target="_blank">
                          <i class="ri-printer-line me-1"></i>Print PDF
                        </a></li>
                        
                        <?php if($po->canBeSubmitted()): ?>
                          <li><hr class="dropdown-divider"></li>
                          <li>
                            <form action="<?php echo e(route('purchase-orders.submit', $po)); ?>" method="POST" class="d-inline">
                              <?php echo csrf_field(); ?>
                              <button type="submit" class="dropdown-item text-success" 
                                      onclick="return confirm('Yakin ingin mengajukan PO ini?')">
                                <i class="ri-send-plane-line me-1"></i>Ajukan
                              </button>
                            </form>
                          </li>
                        <?php endif; ?>
                        
                        <?php if($po->canBeEdited()): ?>
                          <li><hr class="dropdown-divider"></li>
                          <li>
                            <form action="<?php echo e(route('purchase-orders.destroy', $po)); ?>" method="POST" class="d-inline">
                              <?php echo csrf_field(); ?>
                              <?php echo method_field('DELETE'); ?>
                              <button type="submit" class="dropdown-item text-danger" 
                                      onclick="return confirm('Yakin ingin menghapus PO ini?')">
                                <i class="ri-delete-bin-line me-1"></i>Hapus
                              </button>
                            </form>
                          </li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </td>
                </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-4">
          <div>
            Menampilkan <?php echo e($purchaseOrders->firstItem()); ?> sampai <?php echo e($purchaseOrders->lastItem()); ?> 
            dari <?php echo e($purchaseOrders->total()); ?> data
          </div>
          <?php echo e($purchaseOrders->links()); ?>

        </div>
      <?php else: ?>
        <div class="text-center py-4">
          <i class="ri-file-list-3-line display-4 text-muted"></i>
          <h5 class="mt-3">Belum ada Purchase Order</h5>
          <p class="text-muted">Klik tombol "Buat Purchase Order" untuk membuat PO baru.</p>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/purchase-orders/index.blade.php ENDPATH**/ ?>