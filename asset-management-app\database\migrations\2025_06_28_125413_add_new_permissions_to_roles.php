<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Define new permissions with their modules
        $newPermissions = [
            // Stock Opname Permissions
            ['slug' => 'stock_opname_view', 'name' => 'Lihat Stock Opname', 'module' => 'stock_opname'],
            ['slug' => 'stock_opname_create', 'name' => 'Buat Stock Opname', 'module' => 'stock_opname'],
            ['slug' => 'stock_opname_edit', 'name' => 'Edit Stock Opname', 'module' => 'stock_opname'],
            ['slug' => 'stock_opname_delete', 'name' => 'Hapus Stock Opname', 'module' => 'stock_opname'],
            ['slug' => 'stock_opname_start', 'name' => 'Mulai Stock Opname', 'module' => 'stock_opname'],
            ['slug' => 'stock_opname_complete', 'name' => 'Selesaikan Stock Opname', 'module' => 'stock_opname'],
            ['slug' => 'stock_opname_scan', 'name' => 'Scan Asset Stock Opname', 'module' => 'stock_opname'],

            // Master Karyawan Permissions
            ['slug' => 'employee_view', 'name' => 'Lihat Master Karyawan', 'module' => 'employee'],
            ['slug' => 'employee_create', 'name' => 'Tambah Karyawan', 'module' => 'employee'],
            ['slug' => 'employee_edit', 'name' => 'Edit Karyawan', 'module' => 'employee'],
            ['slug' => 'employee_delete', 'name' => 'Hapus Karyawan', 'module' => 'employee'],
            ['slug' => 'employee_toggle_status', 'name' => 'Ubah Status Karyawan', 'module' => 'employee'],

            // Laporan Permissions
            ['slug' => 'report_view', 'name' => 'Lihat Laporan', 'module' => 'report'],
            ['slug' => 'report_asset', 'name' => 'Laporan Asset', 'module' => 'report'],
            ['slug' => 'report_stock_opname', 'name' => 'Laporan Stock Opname', 'module' => 'report'],
            ['slug' => 'report_export', 'name' => 'Export Laporan', 'module' => 'report'],
            ['slug' => 'report_print', 'name' => 'Print Laporan', 'module' => 'report'],

            // Asset Assignment Permissions
            ['slug' => 'asset_assign', 'name' => 'Assign Asset ke Karyawan', 'module' => 'asset'],
            ['slug' => 'asset_unassign', 'name' => 'Unassign Asset dari Karyawan', 'module' => 'asset'],
            ['slug' => 'asset_transfer', 'name' => 'Transfer Asset antar Karyawan', 'module' => 'asset'],

            // Advanced Permissions
            ['slug' => 'system_settings', 'name' => 'Pengaturan Sistem', 'module' => 'system'],
            ['slug' => 'audit_log', 'name' => 'Log Audit', 'module' => 'system'],
            ['slug' => 'backup_restore', 'name' => 'Backup & Restore', 'module' => 'system']
        ];

        // Create permissions if they don't exist
        foreach ($newPermissions as $permissionData) {
            \App\Models\Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                [
                    'name' => $permissionData['name'],
                    'module' => $permissionData['module'],
                    'description' => $permissionData['name']
                ]
            );
        }

        // Assign permissions to roles
        $roles = \App\Models\Role::all();

        foreach ($roles as $role) {
            if ($role->name === 'Super Admin') {
                // Super Admin gets all permissions
                $permissionSlugs = collect($newPermissions)->pluck('slug')->toArray();
                $this->assignPermissionsToRole($role, $permissionSlugs);
            } elseif ($role->name === 'Admin') {
                // Admin gets most permissions except system settings
                $permissionSlugs = collect($newPermissions)
                    ->reject(function($perm) {
                        return in_array($perm['slug'], ['system_settings', 'backup_restore']);
                    })
                    ->pluck('slug')
                    ->toArray();
                $this->assignPermissionsToRole($role, $permissionSlugs);
            } elseif ($role->name === 'Manager') {
                // Manager gets view and report permissions
                $managerPermissions = [
                    'stock_opname_view', 'stock_opname_scan',
                    'employee_view',
                    'report_view', 'report_asset', 'report_stock_opname', 'report_export', 'report_print',
                    'asset_assign', 'asset_unassign',
                    'audit_log'
                ];
                $this->assignPermissionsToRole($role, $managerPermissions);
            } elseif ($role->name === 'Staff') {
                // Staff gets basic permissions
                $staffPermissions = [
                    'stock_opname_view', 'stock_opname_scan',
                    'employee_view',
                    'report_view', 'report_asset'
                ];
                $this->assignPermissionsToRole($role, $staffPermissions);
            }
        }
    }

    private function assignPermissionsToRole($role, $permissionSlugs)
    {
        $permissions = \App\Models\Permission::whereIn('slug', $permissionSlugs)->get();
        foreach ($permissions as $permission) {
            if (!$role->hasPermission($permission)) {
                $role->givePermissionTo($permission);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the new permissions from all roles
        $permissionSlugs = [
            'stock_opname_view', 'stock_opname_create', 'stock_opname_edit', 'stock_opname_delete',
            'stock_opname_start', 'stock_opname_complete', 'stock_opname_scan',
            'employee_view', 'employee_create', 'employee_edit', 'employee_delete', 'employee_toggle_status',
            'report_view', 'report_asset', 'report_stock_opname', 'report_export', 'report_print',
            'asset_assign', 'asset_unassign', 'asset_transfer',
            'system_settings', 'audit_log', 'backup_restore'
        ];

        // Remove permissions from all roles
        $roles = \App\Models\Role::all();
        $permissions = \App\Models\Permission::whereIn('slug', $permissionSlugs)->get();

        foreach ($roles as $role) {
            foreach ($permissions as $permission) {
                $role->revokePermissionTo($permission);
            }
        }

        // Delete the permissions
        \App\Models\Permission::whereIn('slug', $permissionSlugs)->delete();
    }
};
