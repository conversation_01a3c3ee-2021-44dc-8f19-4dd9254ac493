@extends('layouts/contentNavbarLayout')

@section('title', 'Tambah Maintenance Asset')

@section('page-style')
<style>
.asset-row:hover {
  background-color: #f8f9fa;
  cursor: pointer;
}

.asset-row.selected {
  background-color: #e3f2fd;
}

.select-asset-btn:hover {
  transform: scale(1.05);
}

#clear_asset_btn {
  display: none;
}

.modal-xl {
  max-width: 1200px;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 10;
}
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="ri-tools-line me-2"></i>
            Tambah Maintenance Asset
          </h5>
        </div>
      </div>
    </div>
  </div>

  <!-- Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="POST" action="{{ route('asset-maintenances.store') }}">
            @csrf
            
            <div class="row">
              <!-- Asset Selection -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="asset_display" class="form-label">Asset <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <input type="text" class="form-control @error('asset_id') is-invalid @enderror"
                           id="asset_display" name="asset_display"
                           value="{{ old('asset_display') }}"
                           placeholder="Klik tombol lookup untuk pilih asset" readonly>
                    <button type="button" class="btn btn-outline-primary" id="asset_lookup_btn"
                            data-bs-toggle="modal" data-bs-target="#assetLookupModal">
                      <i class="ri-search-line"></i>
                      Lookup
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="clear_asset_btn"
                            onclick="clearAssetSelection()" title="Clear Selection">
                      <i class="ri-close-line"></i>
                    </button>
                  </div>
                  <input type="hidden" id="asset_id" name="asset_id" value="{{ old('asset_id') }}" required>
                  @error('asset_id')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                  @enderror
                  <small class="text-muted">Gunakan tombol Lookup untuk mencari dan memilih asset</small>
                </div>
              </div>

              <!-- Asset Information (Hidden by default) -->
              <div class="col-12" id="asset_info" style="display: none;">
                <div class="alert alert-success">
                  <h6 class="alert-heading">
                    <i class="ri-computer-line me-2"></i>
                    Informasi Asset
                  </h6>
                  <div id="asset_details">
                    <!-- Asset details will be populated here -->
                  </div>
                </div>
              </div>

              <!-- Maintenance Number Preview (Hidden by default) -->
              <div class="col-12" id="maintenance_number_preview" style="display: none;">
                <div class="alert alert-info">
                  <h6 class="alert-heading">
                    <i class="ri-file-text-line me-2"></i>
                    Preview Nomor Maintenance
                  </h6>
                  <div class="text-center">
                    <span class="badge bg-primary fs-6" id="preview_number">-</span>
                  </div>
                  <small class="text-muted mt-2 d-block">
                    <i class="ri-information-line me-1"></i>
                    Nomor ini akan digenerate otomatis berdasarkan cabang dan kategori asset
                  </small>
                </div>
              </div>

              <!-- Supplier Field -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="supplier_display" class="form-label">Supplier</label>
                  <div class="input-group">
                    <input type="text" class="form-control @error('supplier_id') is-invalid @enderror"
                           id="supplier_display" name="supplier_display"
                           placeholder="Klik tombol untuk memilih supplier..." readonly>
                    <button type="button" class="btn btn-outline-primary" id="supplier_lookup_btn">
                      <i class="ri-search-line me-1"></i>
                      Cari Supplier
                    </button>
                    <button type="button" class="btn btn-outline-danger" id="supplier_clear" style="display: none;">
                      <i class="ri-close-line me-1"></i>
                      Hapus
                    </button>
                  </div>
                  <input type="hidden" id="supplier_id" name="supplier_id" value="{{ old('supplier_id') }}">
                  @error('supplier_id')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                  @enderror
                  <div class="form-text">
                    <i class="ri-information-line me-1"></i>
                    Pilih supplier yang menangani maintenance ini (opsional)
                  </div>
                </div>
              </div>

              <!-- Supplier Contact Information (Hidden by default) -->
              <div class="col-12" id="supplier_contact_info" style="display: none;">
                <div class="alert alert-info">
                  <h6 class="alert-heading">
                    <i class="ri-phone-line me-2"></i>
                    Informasi Kontak Supplier
                  </h6>
                  <div id="supplier_contact_details">
                    <!-- Contact details will be populated here -->
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <!-- Maintenance Type -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="maintenance_type" class="form-label">Tipe Maintenance <span class="text-danger">*</span></label>
                  <select class="form-select @error('maintenance_type') is-invalid @enderror"
                          id="maintenance_type" name="maintenance_type" required>
                    <option value="">Pilih Tipe</option>
                    @foreach($maintenanceTypes as $type)
                      <option value="{{ $type->lookup_name }}" {{ old('maintenance_type') == $type->lookup_name ? 'selected' : '' }}>
                        {{ $type->lookup_name }}
                        @if($type->description)
                          <small class="text-muted"> - {{ $type->description }}</small>
                        @endif
                      </option>
                    @endforeach
                  </select>
                  @error('maintenance_type')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <small class="text-muted">Data diambil dari Master Lookup (MTC_TYPE)</small>
                </div>
              </div>

              <!-- Title -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="title" class="form-label">Judul Maintenance <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('title') is-invalid @enderror" 
                         id="title" name="title" value="{{ old('title') }}" 
                         placeholder="Contoh: Penggantian RAM Laptop" required>
                  @error('title')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Description -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="description" class="form-label">Deskripsi <span class="text-danger">*</span></label>
                  <textarea class="form-control @error('description') is-invalid @enderror" 
                            id="description" name="description" rows="3" 
                            placeholder="Jelaskan detail maintenance yang akan dilakukan..." required>{{ old('description') }}</textarea>
                  @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Problem Description -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="problem_description" class="form-label">Deskripsi Masalah</label>
                  <textarea class="form-control @error('problem_description') is-invalid @enderror" 
                            id="problem_description" name="problem_description" rows="3" 
                            placeholder="Jelaskan masalah yang ditemukan (opsional)...">{{ old('problem_description') }}</textarea>
                  @error('problem_description')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Priority -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="priority" class="form-label">Prioritas <span class="text-danger">*</span></label>
                  <select class="form-select @error('priority') is-invalid @enderror" 
                          id="priority" name="priority" required>
                    @foreach(\App\Models\AssetMaintenance::getPriorities() as $key => $value)
                      <option value="{{ $key }}" {{ old('priority', 'medium') == $key ? 'selected' : '' }}>
                        {{ $value }}
                      </option>
                    @endforeach
                  </select>
                  @error('priority')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Scheduled Date -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="scheduled_date" class="form-label">Tanggal Jadwal <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('scheduled_date') is-invalid @enderror" 
                         id="scheduled_date" name="scheduled_date" 
                         value="{{ old('scheduled_date', date('Y-m-d')) }}" 
                         min="{{ date('Y-m-d') }}" required>
                  @error('scheduled_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Estimated Cost -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="estimated_cost" class="form-label">Estimasi Biaya</label>
                  <div class="input-group">
                    <span class="input-group-text">Rp</span>
                    <input type="number" class="form-control @error('estimated_cost') is-invalid @enderror" 
                           id="estimated_cost" name="estimated_cost" 
                           value="{{ old('estimated_cost') }}" 
                           placeholder="0" min="0" step="1000">
                  </div>
                  @error('estimated_cost')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Assigned To -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="assigned_to" class="form-label">Ditugaskan Kepada</label>
                  <select class="form-select @error('assigned_to') is-invalid @enderror" 
                          id="assigned_to" name="assigned_to">
                    <option value="">Pilih Teknisi</option>
                    @foreach($users as $user)
                      <option value="{{ $user->id }}" {{ old('assigned_to') == $user->id ? 'selected' : '' }}>
                        {{ $user->name }}
                      </option>
                    @endforeach
                  </select>
                  @error('assigned_to')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>



              <!-- Notes -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="notes" class="form-label">Catatan Tambahan</label>
                  <textarea class="form-control @error('notes') is-invalid @enderror" 
                            id="notes" name="notes" rows="3" 
                            placeholder="Catatan tambahan (opsional)...">{{ old('notes') }}</textarea>
                  @error('notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('asset-maintenances.index') }}" class="btn btn-outline-secondary">
                <i class="ri-arrow-left-line me-1"></i>
                Kembali
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                Simpan Maintenance
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Asset Lookup Modal -->
<div class="modal fade" id="assetLookupModal" tabindex="-1" aria-labelledby="assetLookupModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="assetLookupModalLabel">
          <i class="ri-search-line me-2"></i>
          Pilih Asset untuk Maintenance
          <small class="text-muted">(Hanya Asset Aktif)</small>
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Search Filter -->
        <div class="row mb-3">
          <div class="col-md-4">
            <input type="text" class="form-control" id="asset_search"
                   placeholder="Cari kode asset, nama, atau brand...">
          </div>
          <div class="col-md-3">
            <select class="form-select" id="branch_filter">
              <option value="">Semua Cabang</option>
              @foreach($assets->groupBy('branch.name') as $branchName => $branchAssets)
                <option value="{{ $branchName }}">{{ $branchName }}</option>
              @endforeach
            </select>
          </div>
          <div class="col-md-3">
            <select class="form-select" id="category_filter">
              <option value="">Semua Kategori</option>
              @foreach($assets->groupBy('assetCategory.name') as $categoryName => $categoryAssets)
                @if($categoryName)
                  <option value="{{ $categoryName }}">{{ $categoryName }}</option>
                @endif
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <button type="button" class="btn btn-primary w-100" onclick="filterAssets()">
              <i class="ri-search-line me-1"></i>
              Filter
            </button>
          </div>
        </div>

        <!-- Asset Table -->
        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
          <table class="table table-hover table-sm">
            <thead class="table-light sticky-top">
              <tr>
                <th width="120">Kode Asset</th>
                <th>Nama Asset</th>
                <th>Brand/Model</th>
                <th>Kategori</th>
                <th>Cabang</th>
                <th width="80">Aksi</th>
              </tr>
            </thead>
            <tbody id="asset_table_body">
              @foreach($assets as $asset)
                <tr class="asset-row"
                    data-asset-id="{{ $asset->id }}"
                    data-asset-code="{{ $asset->asset_code }}"
                    data-asset-name="{{ $asset->name }}"
                    data-asset-brand="{{ $asset->brand }}"
                    data-asset-model="{{ $asset->model }}"
                    data-asset-category="{{ $asset->assetCategory->name ?? '' }}"
                    data-asset-branch="{{ $asset->branch->name }}"
                    data-asset-status="{{ $asset->status }}">
                  <td>
                    <strong>{{ $asset->asset_code }}</strong>
                  </td>
                  <td>{{ $asset->name }}</td>
                  <td>{{ $asset->brand }} {{ $asset->model }}</td>
                  <td>
                    @if($asset->assetCategory)
                      <span class="badge bg-info">{{ $asset->assetCategory->name }}</span>
                    @else
                      -
                    @endif
                  </td>
                  <td>{{ $asset->branch->name }}</td>
                  <td>
                    <button type="button" class="btn btn-sm btn-primary select-asset-btn"
                            onclick="selectAsset({{ $asset->id }}, '{{ $asset->asset_code }}', '{{ $asset->name }}', '{{ $asset->branch->name }}')">
                      <i class="ri-check-line"></i>
                      Pilih
                    </button>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>

        @if($assets->count() === 0)
          <div class="text-center py-4">
            <i class="ri-inbox-line ri-48px text-muted mb-3"></i>
            <h6 class="text-muted">Tidak ada asset aktif yang tersedia</h6>
            <p class="text-muted">Hanya asset dengan status "Active" yang dapat di-maintenance.<br>
            Pastikan ada asset aktif di cabang Anda.</p>
          </div>
        @endif
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="ri-close-line me-1"></i>
          Tutup
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Asset selection function
function selectAsset(assetId, assetCode, assetName, branchName) {
  // Set hidden input value
  document.getElementById('asset_id').value = assetId;

  // Set display text
  document.getElementById('asset_display').value = assetCode + ' - ' + assetName + ' (' + branchName + ')';

  // Close modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('assetLookupModal'));
  modal.hide();

  // Remove validation error if exists
  const assetDisplay = document.getElementById('asset_display');
  assetDisplay.classList.remove('is-invalid');

  // Show success feedback
  assetDisplay.classList.add('is-valid');
  setTimeout(() => {
    assetDisplay.classList.remove('is-valid');
  }, 2000);

  // Show clear button
  document.getElementById('clear_asset_btn').style.display = 'block';

  // Show asset information
  showAssetInfo(assetId);

  // Show maintenance number preview
  showMaintenanceNumberPreview(assetId);

  // Show success toast
  showToast('Asset berhasil dipilih: ' + assetCode, 'success');
}

// Function to show asset information
function showAssetInfo(assetId) {
  // Fetch detailed asset information including assignment
  fetch(`/api/assets/${assetId}/details`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const asset = data.data;
        let assetHtml = '<div class="row">';

        // Asset Code and Name
        assetHtml += `
          <div class="col-md-6">
            <strong>Kode Asset:</strong><br>
            <span class="text-primary">${asset.asset_code}</span>
          </div>
          <div class="col-md-6">
            <strong>Nama Asset:</strong><br>
            <span class="text-dark">${asset.name}</span>
          </div>
        `;

        // Brand and Model
        if (asset.brand || asset.model) {
          assetHtml += `
            <div class="col-md-6 mt-2">
              <strong>Brand/Model:</strong><br>
              <span class="text-muted">${asset.brand || ''} ${asset.model || ''}</span>
            </div>
          `;
        }

        // Category
        if (asset.asset_category) {
          assetHtml += `
            <div class="col-md-6 mt-2">
              <strong>Kategori:</strong><br>
              <span class="badge bg-info">${asset.asset_category.name}</span>
            </div>
          `;
        }

        // Branch
        if (asset.branch) {
          assetHtml += `
            <div class="col-md-6 mt-2">
              <strong>Cabang:</strong><br>
              <span class="text-muted">${asset.branch.name}</span>
            </div>
          `;
        }

        // Status
        let statusClass = 'bg-success';
        if (asset.status === 'inactive') statusClass = 'bg-secondary';
        else if (asset.status === 'maintenance') statusClass = 'bg-warning';
        else if (asset.status === 'disposed') statusClass = 'bg-danger';

        assetHtml += `
          <div class="col-md-6 mt-2">
            <strong>Status:</strong><br>
            <span class="badge ${statusClass}">${asset.status.charAt(0).toUpperCase() + asset.status.slice(1)}</span>
          </div>
        `;

        // Assignment Information
        if (asset.assigned_employee) {
          assetHtml += `
            <div class="col-12 mt-3">
              <div class="alert alert-warning">
                <h6 class="alert-heading mb-2">
                  <i class="ri-user-line me-2"></i>
                  Asset Sedang Di-assign
                </h6>
                <div class="row">
                  <div class="col-md-4">
                    <strong>NIK:</strong><br>
                    <span class="text-primary">${asset.assigned_employee.nik_karyawan}</span>
                  </div>
                  <div class="col-md-4">
                    <strong>Nama Karyawan:</strong><br>
                    <span class="text-dark">${asset.assigned_employee.nama_lengkap}</span>
                  </div>
                  <div class="col-md-4">
                    <strong>Jabatan:</strong><br>
                    <span class="text-muted">${asset.assigned_employee.jabatan || '-'}</span>
                  </div>
                  <div class="col-md-4 mt-2">
                    <strong>Divisi:</strong><br>
                    <span class="text-muted">${asset.assigned_employee.divisi || '-'}</span>
                  </div>
                  <div class="col-md-4 mt-2">
                    <strong>Cabang:</strong><br>
                    <span class="text-muted">${asset.assigned_employee.cabang || '-'}</span>
                  </div>
                  <div class="col-md-4 mt-2">
                    <strong>Tanggal Assign:</strong><br>
                    <span class="text-muted">${asset.assignment_date || '-'}</span>
                  </div>
                </div>
              </div>
            </div>
          `;
        } else {
          assetHtml += `
            <div class="col-12 mt-3">
              <div class="alert alert-info">
                <i class="ri-information-line me-2"></i>
                Asset ini belum di-assign ke karyawan manapun
              </div>
            </div>
          `;
        }

        assetHtml += '</div>';

        // Update asset info display
        document.getElementById('asset_details').innerHTML = assetHtml;
        document.getElementById('asset_info').style.display = 'block';
      }
    })
    .catch(error => {
      console.error('Error fetching asset details:', error);
      // Fallback to basic info from table data
      showBasicAssetInfo(assetId);
    });
}

// Fallback function for basic asset info
function showBasicAssetInfo(assetId) {
  const assetRow = document.querySelector(`tr[data-asset-id="${assetId}"]`);
  if (assetRow) {
    const assetCode = assetRow.dataset.assetCode;
    const assetName = assetRow.dataset.assetName;
    const assetBrand = assetRow.dataset.assetBrand;
    const assetModel = assetRow.dataset.assetModel;
    const assetCategory = assetRow.dataset.assetCategory;
    const assetBranch = assetRow.dataset.assetBranch;
    const assetStatus = assetRow.dataset.assetStatus;

    let assetHtml = '<div class="row">';

    assetHtml += `
      <div class="col-md-6">
        <strong>Kode Asset:</strong><br>
        <span class="text-primary">${assetCode}</span>
      </div>
      <div class="col-md-6">
        <strong>Nama Asset:</strong><br>
        <span class="text-dark">${assetName}</span>
      </div>
    `;

    if (assetBrand || assetModel) {
      assetHtml += `
        <div class="col-md-6 mt-2">
          <strong>Brand/Model:</strong><br>
          <span class="text-muted">${assetBrand} ${assetModel}</span>
        </div>
      `;
    }

    if (assetCategory) {
      assetHtml += `
        <div class="col-md-6 mt-2">
          <strong>Kategori:</strong><br>
          <span class="badge bg-info">${assetCategory}</span>
        </div>
      `;
    }

    assetHtml += `
      <div class="col-md-6 mt-2">
        <strong>Cabang:</strong><br>
        <span class="text-muted">${assetBranch}</span>
      </div>
    `;

    let statusClass = 'bg-success';
    if (assetStatus === 'inactive') statusClass = 'bg-secondary';
    else if (assetStatus === 'maintenance') statusClass = 'bg-warning';
    else if (assetStatus === 'disposed') statusClass = 'bg-danger';

    assetHtml += `
      <div class="col-md-6 mt-2">
        <strong>Status:</strong><br>
        <span class="badge ${statusClass}">${assetStatus.charAt(0).toUpperCase() + assetStatus.slice(1)}</span>
      </div>
    `;

    assetHtml += '</div>';

    document.getElementById('asset_details').innerHTML = assetHtml;
    document.getElementById('asset_info').style.display = 'block';
  }
}

// Function to hide asset information
function hideAssetInfo() {
  document.getElementById('asset_info').style.display = 'none';
  document.getElementById('asset_details').innerHTML = '';
}

// Function to show maintenance number preview
function showMaintenanceNumberPreview(assetId) {
  // Fetch maintenance number preview
  fetch(`/api/maintenance/preview-number/${assetId}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        document.getElementById('preview_number').textContent = data.number;
        document.getElementById('maintenance_number_preview').style.display = 'block';
      }
    })
    .catch(error => {
      console.error('Error fetching maintenance number preview:', error);
    });
}

// Function to hide maintenance number preview
function hideMaintenanceNumberPreview() {
  document.getElementById('maintenance_number_preview').style.display = 'none';
  document.getElementById('preview_number').textContent = '-';
}

// Filter assets function
function filterAssets() {
  const searchTerm = document.getElementById('asset_search').value.toLowerCase();
  const branchFilter = document.getElementById('branch_filter').value.toLowerCase();
  const categoryFilter = document.getElementById('category_filter').value.toLowerCase();

  const rows = document.querySelectorAll('.asset-row');

  rows.forEach(row => {
    const assetCode = row.dataset.assetCode.toLowerCase();
    const assetName = row.dataset.assetName.toLowerCase();
    const assetBrand = row.dataset.assetBrand.toLowerCase();
    const assetModel = row.dataset.assetModel.toLowerCase();
    const assetCategory = row.dataset.assetCategory.toLowerCase();
    const assetBranch = row.dataset.assetBranch.toLowerCase();

    const matchesSearch = !searchTerm ||
                         assetCode.includes(searchTerm) ||
                         assetName.includes(searchTerm) ||
                         assetBrand.includes(searchTerm) ||
                         assetModel.includes(searchTerm);

    const matchesBranch = !branchFilter || assetBranch.includes(branchFilter);
    const matchesCategory = !categoryFilter || assetCategory.includes(categoryFilter);

    if (matchesSearch && matchesBranch && matchesCategory) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

// Real-time search
document.getElementById('asset_search').addEventListener('input', filterAssets);
document.getElementById('branch_filter').addEventListener('change', filterAssets);
document.getElementById('category_filter').addEventListener('change', filterAssets);

// Clear selection function
function clearAssetSelection() {
  document.getElementById('asset_id').value = '';
  document.getElementById('asset_display').value = '';
  document.getElementById('asset_display').classList.remove('is-valid', 'is-invalid');
  document.getElementById('clear_asset_btn').style.display = 'none';
  hideAssetInfo();
  hideMaintenanceNumberPreview();
  showToast('Pilihan asset telah dihapus', 'info');
}

// Toast notification function
function showToast(message, type = 'info') {
  // Create toast element
  const toastHtml = `
    <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : (type === 'error' ? 'danger' : 'info')} border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          <i class="ri-${type === 'success' ? 'check' : (type === 'error' ? 'error-warning' : 'information')}-line me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  `;

  // Create toast container if not exists
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    document.body.appendChild(toastContainer);
  }

  // Add toast to container
  const toastElement = document.createElement('div');
  toastElement.innerHTML = toastHtml;
  toastContainer.appendChild(toastElement.firstElementChild);

  // Show toast
  const toast = new bootstrap.Toast(toastContainer.lastElementChild);
  toast.show();

  // Remove toast element after it's hidden
  toastContainer.lastElementChild.addEventListener('hidden.bs.toast', function() {
    this.remove();
  });
}

// Double click to select asset
document.addEventListener('DOMContentLoaded', function() {
  const assetRows = document.querySelectorAll('.asset-row');
  assetRows.forEach(row => {
    // Add cursor pointer style
    row.style.cursor = 'pointer';

    // Double click to select
    row.addEventListener('dblclick', function() {
      const assetId = this.dataset.assetId;
      const assetCode = this.dataset.assetCode;
      const assetName = this.dataset.assetName;
      const assetBranch = this.dataset.assetBranch;
      selectAsset(assetId, assetCode, assetName, assetBranch);
    });

    // Hover effects
    row.addEventListener('mouseover', function() {
      this.style.backgroundColor = '#f8f9fa';
    });

    row.addEventListener('mouseout', function() {
      this.style.backgroundColor = '';
    });
  });

  // Initialize Supplier Lookup
  supplierLookup = new SupplierLookup({
    inputId: 'supplier_id',
    displayId: 'supplier_display',
    buttonId: 'supplier_lookup_btn',
    modalId: 'supplierLookupModal',
    apiUrl: '{{ route("api.suppliers.lookup") }}',
    onSelect: function(supplier) {
      // Show supplier contact information
      showSupplierContactInfo(supplier);
    }
  });

  // Function to show supplier contact information
  function showSupplierContactInfo(supplier) {
    // Fetch detailed supplier information
    fetch(`{{ route("api.suppliers.lookup") }}/${supplier.id}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const supplierData = data.data;
          let contactHtml = '';

          // Build contact information HTML
          if (supplierData.phone || supplierData.email || supplierData.contact_person) {
            contactHtml += '<div class="row">';

            if (supplierData.phone) {
              contactHtml += `
                <div class="col-md-4">
                  <strong>Telepon:</strong><br>
                  <span class="text-primary">${supplierData.phone}</span>
                  <a href="tel:${supplierData.phone}" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="ri-phone-line"></i>
                  </a>
                </div>
              `;
            }

            if (supplierData.email) {
              contactHtml += `
                <div class="col-md-4">
                  <strong>Email:</strong><br>
                  <span class="text-primary">${supplierData.email}</span>
                  <a href="mailto:${supplierData.email}" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="ri-mail-line"></i>
                  </a>
                </div>
              `;
            }

            if (supplierData.contact_person) {
              contactHtml += `
                <div class="col-md-4">
                  <strong>Kontak Person:</strong><br>
                  <span class="text-primary">${supplierData.contact_person}</span>
                </div>
              `;
            }

            contactHtml += '</div>';

            // Add location if available
            if (supplierData.city || supplierData.province) {
              contactHtml += `
                <div class="row mt-2">
                  <div class="col-12">
                    <strong>Lokasi:</strong>
                    <span class="text-muted">
                      <i class="ri-map-pin-line me-1"></i>
                      ${supplierData.city || ''}${supplierData.city && supplierData.province ? ', ' : ''}${supplierData.province || ''}
                    </span>
                  </div>
                </div>
              `;
            }
          } else {
            contactHtml = '<p class="text-muted mb-0">Informasi kontak tidak tersedia</p>';
          }

          // Update contact info display
          document.getElementById('supplier_contact_details').innerHTML = contactHtml;
          document.getElementById('supplier_contact_info').style.display = 'block';
        }
      })
      .catch(error => {
        console.error('Error fetching supplier details:', error);
      });
  }

  // Function to hide supplier contact information
  function hideSupplierContactInfo() {
    document.getElementById('supplier_contact_info').style.display = 'none';
    document.getElementById('supplier_contact_details').innerHTML = '';
  }

  // Override clear function to hide contact info
  const originalClearSelection = supplierLookup.clearSelection;
  supplierLookup.clearSelection = function() {
    originalClearSelection.call(this);
    hideSupplierContactInfo();
  };
});
</script>

<!-- Include Supplier Lookup Script -->
<script src="{{ asset('js/supplier-lookup.js') }}"></script>

@endsection
