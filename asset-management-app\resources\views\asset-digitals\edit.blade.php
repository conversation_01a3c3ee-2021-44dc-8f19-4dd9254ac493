@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Asset Digital')

@section('content')
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Edit Asset Digital</h5>
        <div class="d-flex gap-2">
          <a href="{{ route('asset-digitals.show', $assetDigital) }}" class="btn btn-outline-info">
            <i class="ri-eye-line me-1"></i>Detail
          </a>
          <a href="{{ route('asset-digitals.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
      </div>
      
      <div class="card-body">
        <form action="{{ route('asset-digitals.update', $assetDigital) }}" method="POST">
          @csrf
          @method('PUT')
          
          <div class="row">
            <!-- Basic Information -->
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Dasar</h6>
              
              <div class="mb-3">
                <label class="form-label">Kode Asset</label>
                <input type="text" class="form-control" value="{{ $assetDigital->asset_code }}" readonly>
                <small class="text-muted">Kode asset tidak dapat diubah</small>
              </div>

              <div class="mb-3">
                <label class="form-label">Nama Asset Digital <span class="text-danger">*</span></label>
                <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" 
                       value="{{ old('name', $assetDigital->name) }}" placeholder="Contoh: Microsoft Office 365">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Tipe Lisensi <span class="text-danger">*</span></label>
                <select name="license_type" class="form-select @error('license_type') is-invalid @enderror">
                  <option value="">Pilih Tipe Lisensi</option>
                  @foreach($licenseTypes as $type)
                    <option value="{{ $type->lookup_name }}" {{ old('license_type', $assetDigital->license_type) == $type->lookup_name ? 'selected' : '' }}>
                      {{ $type->lookup_name }}
                    </option>
                  @endforeach
                </select>
                @error('license_type')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">License Key</label>
                <input type="text" name="license_key" class="form-control @error('license_key') is-invalid @enderror" 
                       value="{{ old('license_key', $assetDigital->license_key) }}" placeholder="License key atau serial number">
                @error('license_key')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Vendor</label>
                <input type="text" name="vendor" class="form-control @error('vendor') is-invalid @enderror" 
                       value="{{ old('vendor', $assetDigital->vendor) }}" placeholder="Contoh: Microsoft, Adobe, Google">
                @error('vendor')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Versi</label>
                <input type="text" name="version" class="form-control @error('version') is-invalid @enderror" 
                       value="{{ old('version', $assetDigital->version) }}" placeholder="Contoh: 2023, v1.5.2">
                @error('version')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Deskripsi</label>
                <textarea name="description" class="form-control @error('description') is-invalid @enderror" 
                          rows="3" placeholder="Deskripsi singkat tentang asset digital">{{ old('description', $assetDigital->description) }}</textarea>
                @error('description')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <!-- Login & Access Information -->
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Login & Akses</h6>
              
              <div class="mb-3">
                <label class="form-label">Username</label>
                <input type="text" name="username" class="form-control @error('username') is-invalid @enderror" 
                       value="{{ old('username', $assetDigital->username) }}" placeholder="Username untuk login">
                @error('username')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Password</label>
                <input type="password" name="password" class="form-control @error('password') is-invalid @enderror" 
                       placeholder="Kosongkan jika tidak ingin mengubah password">
                <small class="text-muted">Kosongkan jika tidak ingin mengubah password</small>
                @error('password')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Login URL</label>
                <input type="url" name="login_url" class="form-control @error('login_url') is-invalid @enderror" 
                       value="{{ old('login_url', $assetDigital->login_url) }}" placeholder="https://example.com/login">
                @error('login_url')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Max Users</label>
                    <input type="number" name="max_users" class="form-control @error('max_users') is-invalid @enderror" 
                           value="{{ old('max_users', $assetDigital->max_users) }}" min="1" placeholder="Maksimal pengguna">
                    @error('max_users')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Current Users</label>
                    <input type="number" name="current_users" class="form-control @error('current_users') is-invalid @enderror" 
                           value="{{ old('current_users', $assetDigital->current_users) }}" min="0" placeholder="Pengguna saat ini">
                    @error('current_users')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label class="form-label">Status <span class="text-danger">*</span></label>
                <select name="status" class="form-select @error('status') is-invalid @enderror">
                  <option value="">Pilih Status</option>
                  <option value="active" {{ old('status', $assetDigital->status) == 'active' ? 'selected' : '' }}>Active</option>
                  <option value="inactive" {{ old('status', $assetDigital->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                  <option value="expired" {{ old('status', $assetDigital->status) == 'expired' ? 'selected' : '' }}>Expired</option>
                  <option value="suspended" {{ old('status', $assetDigital->status) == 'suspended' ? 'selected' : '' }}>Suspended</option>
                </select>
                @error('status')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>

          <hr class="my-4">

          <div class="row">
            <!-- Purchase & Financial Information -->
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Pembelian</h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Tanggal Pembelian</label>
                    <input type="date" name="purchase_date" class="form-control @error('purchase_date') is-invalid @enderror" 
                           value="{{ old('purchase_date', $assetDigital->purchase_date?->format('Y-m-d')) }}">
                    @error('purchase_date')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Tanggal Kadaluarsa</label>
                    <input type="date" name="expiry_date" class="form-control @error('expiry_date') is-invalid @enderror" 
                           value="{{ old('expiry_date', $assetDigital->expiry_date?->format('Y-m-d')) }}">
                    @error('expiry_date')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label class="form-label">Harga Pembelian</label>
                <div class="input-group">
                  <span class="input-group-text">Rp</span>
                  <input type="number" name="purchase_price" class="form-control @error('purchase_price') is-invalid @enderror" 
                         value="{{ old('purchase_price', $assetDigital->purchase_price) }}" min="0" step="0.01" placeholder="0.00">
                </div>
                @error('purchase_price')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <!-- Assignment & Branch Information -->
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Assignment</h6>
              
              <div class="mb-3">
                <label class="form-label">Cabang <span class="text-danger">*</span></label>
                <select name="branch_id" class="form-select @error('branch_id') is-invalid @enderror">
                  <option value="">Pilih Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ old('branch_id', $assetDigital->branch_id) == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }} ({{ $branch->code }})
                    </option>
                  @endforeach
                </select>
                @error('branch_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Assigned To</label>
                <select name="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                  <option value="">Pilih Karyawan (Opsional)</option>
                  @foreach($employees as $employee)
                    <option value="{{ $employee->id }}" {{ old('assigned_to', $assetDigital->assigned_to) == $employee->id ? 'selected' : '' }}>
                      {{ $employee->name }} ({{ $employee->nik }})
                    </option>
                  @endforeach
                </select>
                @error('assigned_to')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label class="form-label">Catatan</label>
                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" 
                          rows="3" placeholder="Catatan tambahan">{{ old('notes', $assetDigital->notes) }}</textarea>
                @error('notes')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-end gap-2 mt-4">
            <a href="{{ route('asset-digitals.show', $assetDigital) }}" class="btn btn-outline-secondary">
              <i class="ri-close-line me-1"></i>Batal
            </a>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line me-1"></i>Update Asset Digital
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
