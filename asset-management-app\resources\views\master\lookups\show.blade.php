@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Lookup - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Lookup /</span> Detail Lookup
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ $lookup->lookup_name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.lookups.edit', $lookup) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.lookups.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <!-- Status Badge -->
          <div class="mb-4">
            <span class="badge bg-primary fs-6 me-2">{{ $lookup->lookup_code }}</span>
            @if($lookup->category)
              <span class="badge bg-info fs-6 me-2">{{ $lookup->category }}</span>
            @endif
            <span class="badge bg-{{ $lookup->is_active ? 'success' : 'secondary' }} fs-6 me-2">
              {{ $lookup->is_active ? 'Aktif' : 'Non-Aktif' }}
            </span>
            <span class="badge bg-secondary fs-6">Urutan: {{ $lookup->sort_order }}</span>
          </div>

          <!-- Lookup Information -->
          <h6 class="mb-3">Informasi Lookup</h6>
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Lookup</label>
                <p class="fw-bold">{{ $lookup->lookup_code }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Lookup</label>
                <p class="fw-bold">{{ $lookup->lookup_name }}</p>
              </div>
            </div>
          </div>

          @if($lookup->description)
          <div class="mb-4">
            <label class="form-label text-muted">Deskripsi</label>
            <p>{{ $lookup->description }}</p>
          </div>
          @endif

          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kategori</label>
                <p>
                  @if($lookup->category)
                    <span class="badge bg-info">{{ $lookup->category }}</span>
                  @else
                    <span class="text-muted">-</span>
                  @endif
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Urutan Tampilan</label>
                <p><span class="badge bg-secondary">{{ $lookup->sort_order }}</span></p>
              </div>
            </div>
          </div>

          <!-- Metadata -->
          @if($lookup->metadata && count($lookup->metadata) > 0)
          <hr class="my-4">
          <h6 class="mb-3">Metadata</h6>
          <div class="table-responsive">
            <table class="table table-sm table-bordered">
              <thead>
                <tr>
                  <th>Key</th>
                  <th>Value</th>
                  <th>Type</th>
                </tr>
              </thead>
              <tbody>
                @foreach($lookup->metadata as $key => $value)
                <tr>
                  <td><strong>{{ $key }}</strong></td>
                  <td>
                    @if(is_array($value))
                      <span class="badge bg-warning">Array</span>
                      <small class="d-block text-muted">{{ json_encode($value) }}</small>
                    @elseif(is_bool($value))
                      <span class="badge bg-{{ $value ? 'success' : 'danger' }}">
                        {{ $value ? 'true' : 'false' }}
                      </span>
                    @elseif(is_numeric($value))
                      <span class="badge bg-info">{{ $value }}</span>
                    @else
                      {{ $value }}
                    @endif
                  </td>
                  <td>
                    <span class="badge bg-secondary">
                      @if(is_array($value))
                        array
                      @elseif(is_bool($value))
                        boolean
                      @elseif(is_numeric($value))
                        number
                      @else
                        string
                      @endif
                    </span>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
          @endif

          <!-- Related Lookups -->
          @if($relatedLookups->count() > 0)
          <hr class="my-4">
          <h6 class="mb-3">Lookup Terkait (Kode: {{ $lookup->lookup_code }})</h6>
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Nama</th>
                  <th>Kategori</th>
                  <th>Urutan</th>
                  <th>Status</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                @foreach($relatedLookups as $related)
                <tr class="{{ $related->id === $lookup->id ? 'table-primary' : '' }}">
                  <td>
                    @if($related->id === $lookup->id)
                      <strong>{{ $related->lookup_name }}</strong> <span class="badge bg-primary">Current</span>
                    @else
                      {{ $related->lookup_name }}
                    @endif
                  </td>
                  <td>
                    @if($related->category)
                      <span class="badge bg-info">{{ $related->category }}</span>
                    @else
                      <span class="text-muted">-</span>
                    @endif
                  </td>
                  <td><span class="badge bg-secondary">{{ $related->sort_order }}</span></td>
                  <td>
                    <span class="badge bg-{{ $related->is_active ? 'success' : 'secondary' }}">
                      {{ $related->is_active ? 'Aktif' : 'Non-Aktif' }}
                    </span>
                  </td>
                  <td>
                    @if($related->id !== $lookup->id)
                      <a href="{{ route('master.lookups.show', $related) }}" class="btn btn-sm btn-outline-primary">
                        <i class="ri-eye-line"></i>
                      </a>
                    @endif
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
          @endif
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Detail</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ strtoupper(substr($lookup->lookup_code, 0, 2)) }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $lookup->lookup_name }}</h6>
              <small class="text-muted">{{ $lookup->lookup_code }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>Kode: <strong>{{ $lookup->lookup_code }}</strong></li>
              <li>Kategori: <strong>{{ $lookup->category ?: 'Tidak ada' }}</strong></li>
              <li>Urutan: <strong>{{ $lookup->sort_order }}</strong></li>
              <li>Metadata: <strong>{{ $lookup->metadata ? count($lookup->metadata) : 0 }} item</strong></li>
              <li>Related: <strong>{{ $relatedLookups->count() }} lookup</strong></li>
              <li>Dibuat: {{ $lookup->created_at->diffForHumans() }}</li>
              <li>Diupdate: {{ $lookup->updated_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.lookups.edit', $lookup) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Lookup
            </a>
            
            @if($lookup->is_active)
              <form action="{{ route('master.lookups.update', $lookup) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="lookup_code" value="{{ $lookup->lookup_code }}">
                <input type="hidden" name="lookup_name" value="{{ $lookup->lookup_name }}">
                <input type="hidden" name="description" value="{{ $lookup->description }}">
                <input type="hidden" name="category" value="{{ $lookup->category }}">
                <input type="hidden" name="sort_order" value="{{ $lookup->sort_order }}">
                <input type="hidden" name="is_active" value="0">
                <button type="submit" class="btn btn-warning w-100" onclick="return confirm('Yakin ingin menonaktifkan lookup ini?')">
                  <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
                </button>
              </form>
            @else
              <form action="{{ route('master.lookups.update', $lookup) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="lookup_code" value="{{ $lookup->lookup_code }}">
                <input type="hidden" name="lookup_name" value="{{ $lookup->lookup_name }}">
                <input type="hidden" name="description" value="{{ $lookup->description }}">
                <input type="hidden" name="category" value="{{ $lookup->category }}">
                <input type="hidden" name="sort_order" value="{{ $lookup->sort_order }}">
                <input type="hidden" name="is_active" value="1">
                <button type="submit" class="btn btn-success w-100" onclick="return confirm('Yakin ingin mengaktifkan lookup ini?')">
                  <i class="ri-play-circle-line me-1"></i>Aktifkan
                </button>
              </form>
            @endif
            
            <form action="{{ route('master.lookups.destroy', $lookup) }}" method="POST">
              @csrf
              @method('DELETE')
              <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Yakin ingin menghapus lookup ini?\n\nTindakan ini tidak dapat dibatalkan!')">
                <i class="ri-delete-bin-line me-1"></i>Hapus Lookup
              </button>
            </form>
          </div>
        </div>
      </div>

      @if($lookup->metadata && count($lookup->metadata) > 0)
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Metadata Preview</h6>
        </div>
        <div class="card-body">
          @foreach($lookup->metadata as $key => $value)
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="fw-bold">{{ $key }}:</span>
              <span class="text-muted">
                @if(is_array($value))
                  [Array]
                @elseif(is_bool($value))
                  {{ $value ? 'true' : 'false' }}
                @else
                  {{ Str::limit($value, 20) }}
                @endif
              </span>
            </div>
          @endforeach
        </div>
      </div>
      @endif
    </div>
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
