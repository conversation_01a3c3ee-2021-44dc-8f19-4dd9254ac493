<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_opname_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stock_opname_id')->constrained('stock_opnames')->onDelete('cascade');
            $table->foreignId('asset_id')->constrained('assets');
            $table->string('asset_code');
            $table->string('asset_name');
            $table->enum('expected_status', ['active', 'inactive', 'maintenance', 'disposed']);
            $table->enum('found_status', ['found', 'not_found', 'damaged', 'missing'])->nullable();
            $table->enum('physical_condition', ['excellent', 'good', 'fair', 'poor', 'damaged'])->nullable();
            $table->text('condition_notes')->nullable();
            $table->string('location_found')->nullable();
            $table->boolean('location_match')->nullable(); // Does location match expected?
            $table->json('dynamic_fields_expected')->nullable(); // Expected dynamic field values
            $table->json('dynamic_fields_found')->nullable(); // Found dynamic field values
            $table->foreignId('scanned_by')->nullable()->constrained('users');
            $table->datetime('scanned_at')->nullable();
            $table->text('scanner_notes')->nullable();
            $table->boolean('has_discrepancy')->default(false);
            $table->json('discrepancy_details')->nullable();
            $table->timestamps();

            $table->unique(['stock_opname_id', 'asset_id']);
            $table->index(['stock_opname_id', 'found_status']);
            $table->index(['asset_id', 'scanned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_opname_details');
    }
};
