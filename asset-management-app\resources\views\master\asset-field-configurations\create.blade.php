@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Konfigurasi Field Asset - Asset Management System')

@section('content')
<style>
.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-section h6 {
  color: #566a7f;
  margin-bottom: 1rem;
  font-weight: 600;
}

.field-preview {
  border: 2px dashed #e7e7ff;
  border-radius: 8px;
  padding: 1rem;
  background: #fafbff;
}

.validation-rules-container {
  display: none;
}

.field-options-container {
  display: none;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-add-line me-2"></i>
              Tambah Konfigurasi Field Asset
            </h5>
            <small class="text-muted">Buat field baru untuk form input asset</small>
          </div>
          <div>
            <a href="{{ route('master.asset-field-configurations.index') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <form method="POST" action="{{ route('master.asset-field-configurations.store') }}">
    @csrf
    
    <div class="row">
      <!-- Form Configuration -->
      <div class="col-md-8">
        <div class="card">
          <div class="card-body">
            <!-- Basic Information -->
            <div class="form-section">
              <h6><i class="ri-information-line me-2"></i>Informasi Dasar</h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_name" class="form-label">Field Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('field_name') is-invalid @enderror" 
                           id="field_name" name="field_name" value="{{ old('field_name') }}" 
                           placeholder="contoh: processor_type" required>
                    <div class="form-text">Gunakan format snake_case (huruf kecil dan underscore)</div>
                    @error('field_name')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_label" class="form-label">Field Label <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('field_label') is-invalid @enderror" 
                           id="field_label" name="field_label" value="{{ old('field_label') }}" 
                           placeholder="contoh: Tipe Processor" required>
                    <div class="form-text">Label yang akan ditampilkan di form</div>
                    @error('field_label')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_type" class="form-label">Field Type <span class="text-danger">*</span></label>
                    <select class="form-select @error('field_type') is-invalid @enderror" 
                            id="field_type" name="field_type" required>
                      <option value="">Pilih Tipe Field</option>
                      @foreach($fieldTypes as $key => $type)
                        <option value="{{ $key }}" {{ old('field_type') == $key ? 'selected' : '' }}>
                          {{ $type }}
                        </option>
                      @endforeach
                    </select>
                    @error('field_type')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_group" class="form-label">Field Group <span class="text-danger">*</span></label>
                    <select class="form-select @error('field_group') is-invalid @enderror" 
                            id="field_group" name="field_group" required>
                      <option value="">Pilih Grup Field</option>
                      @foreach($fieldGroups as $key => $group)
                        <option value="{{ $key }}" {{ old('field_group') == $key ? 'selected' : '' }}>
                          {{ $group }}
                        </option>
                      @endforeach
                    </select>
                    @error('field_group')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>
            </div>

            <!-- Configuration -->
            <div class="form-section">
              <h6><i class="ri-settings-4-line me-2"></i>Konfigurasi</h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="asset_category_id" class="form-label">Kategori Asset</label>
                    <select class="form-select @error('asset_category_id') is-invalid @enderror" 
                            id="asset_category_id" name="asset_category_id">
                      <option value="">Global (Semua Kategori)</option>
                      @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ old('asset_category_id') == $category->id ? 'selected' : '' }}>
                          {{ $category->name }}
                        </option>
                      @endforeach
                    </select>
                    <div class="form-text">Kosongkan untuk field global</div>
                    @error('asset_category_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="sort_order" class="form-label">Urutan</label>
                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                           id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                    <div class="form-text">Urutan tampil field (0 = paling atas)</div>
                    @error('sort_order')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="help_text" class="form-label">Help Text</label>
                <textarea class="form-control @error('help_text') is-invalid @enderror" 
                          id="help_text" name="help_text" rows="2" 
                          placeholder="Teks bantuan untuk user...">{{ old('help_text') }}</textarea>
                @error('help_text')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_required" name="is_required" value="1" 
                           {{ old('is_required') ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_required">
                      Field Wajib Diisi
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                           {{ old('is_active', true) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                      Field Aktif
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Validation & Format Configuration (for text fields) -->
            <div class="form-section validation-section" style="display: none;">
              <h6><i class="ri-shield-check-line me-2"></i>Validation & Format Configuration</h6>

              <div class="mb-3">
                <label for="validation_pattern_preset" class="form-label">Validation Pattern</label>
                <select class="form-select" id="validation_pattern_preset">
                  <option value="">Select Validation Pattern</option>
                  @foreach($validationPatterns as $key => $pattern)
                    <option value="{{ $key }}"
                            data-pattern="{{ $pattern['pattern'] }}"
                            data-message="{{ $pattern['message'] }}"
                            data-mask="{{ $pattern['mask'] }}"
                            data-placeholder="{{ $pattern['placeholder'] }}">
                      {{ $pattern['name'] }}
                    </option>
                  @endforeach
                </select>
                <div class="form-text">Choose a predefined pattern or select "Custom Pattern" for manual configuration</div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="validation_pattern" class="form-label">Regex Pattern</label>
                    <input type="text" class="form-control @error('validation_pattern') is-invalid @enderror"
                           id="validation_pattern" name="validation_pattern" value="{{ old('validation_pattern') }}"
                           placeholder="^[a-zA-Z0-9]+$">
                    <div class="form-text">Regular expression pattern for validation</div>
                    @error('validation_pattern')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="validation_message" class="form-label">Error Message</label>
                    <input type="text" class="form-control @error('validation_message') is-invalid @enderror"
                           id="validation_message" name="validation_message" value="{{ old('validation_message') }}"
                           placeholder="Please enter a valid value">
                    <div class="form-text">Custom error message when validation fails</div>
                    @error('validation_message')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="input_mask" class="form-label">Input Mask</label>
                    <input type="text" class="form-control @error('input_mask') is-invalid @enderror"
                           id="input_mask" name="input_mask" value="{{ old('input_mask') }}"
                           placeholder="999-999-9999">
                    <div class="form-text">Input mask for formatting (9=digit, A=letter, *=alphanumeric)</div>
                    @error('input_mask')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="placeholder_text" class="form-label">Placeholder Text</label>
                    <input type="text" class="form-control @error('placeholder_text') is-invalid @enderror"
                           id="placeholder_text" name="placeholder_text" value="{{ old('placeholder_text') }}"
                           placeholder="Enter placeholder text">
                    <div class="form-text">Placeholder text shown in the input field</div>
                    @error('placeholder_text')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="min_length" class="form-label">Minimum Length</label>
                    <input type="number" class="form-control @error('min_length') is-invalid @enderror"
                           id="min_length" name="min_length" value="{{ old('min_length') }}"
                           min="0" placeholder="0">
                    <div class="form-text">Minimum number of characters required</div>
                    @error('min_length')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="max_length" class="form-label">Maximum Length</label>
                    <input type="number" class="form-control @error('max_length') is-invalid @enderror"
                           id="max_length" name="max_length" value="{{ old('max_length') }}"
                           min="1" placeholder="255">
                    <div class="form-text">Maximum number of characters allowed</div>
                    @error('max_length')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <button type="button" class="btn btn-outline-info btn-sm" id="test-pattern-btn">
                  <i class="ri-test-tube-line me-1"></i>
                  Test Pattern
                </button>
                <div id="pattern-test-result" class="mt-2"></div>
              </div>
            </div>

            <!-- Dependent Dropdown Configuration (only for select fields) -->
            <div class="form-section dependent-dropdown-section" style="display: none;">
              <h6><i class="ri-git-branch-line me-2"></i>Dependent Dropdown Configuration</h6>

              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="enable_dependent" name="enable_dependent" value="1">
                  <label class="form-check-label" for="enable_dependent">
                    <strong>Enable Dependent Dropdown</strong>
                  </label>
                </div>
                <div class="form-text">Centang jika dropdown ini bergantung pada dropdown lain</div>
              </div>

              <div id="dependent-config" style="display: none;">
                <div class="mb-3">
                  <label for="parent_field_id" class="form-label">Parent Field</label>
                  <select class="form-select" id="parent_field_id" name="parent_field_id">
                    <option value="">Pilih Parent Field</option>
                    @foreach(\App\Models\AssetFieldConfiguration::where('field_type', 'select')->active()->get() as $parentField)
                      <option value="{{ $parentField->id }}">{{ $parentField->field_label }} ({{ $parentField->field_name }})</option>
                    @endforeach
                  </select>
                  <div class="form-text">Pilih field dropdown yang akan menjadi parent</div>
                </div>

                <div id="parent-conditions">
                  <div class="mb-3">
                    <label class="form-label">Parent Field Conditions</label>
                    <div class="alert alert-info">
                      <small>
                        <strong>Untuk Database:</strong> Gunakan format JSON seperti: <code>[{"column": "parent_id", "operator": "=", "value": "{parent_value}"}]</code><br>
                        <strong>Untuk Lookup:</strong> Akan menggunakan lookup code yang disesuaikan dengan parent value
                      </small>
                    </div>
                    <textarea class="form-control" id="parent_field_conditions" name="parent_field_conditions" rows="3"
                              placeholder='[{"column": "category_id", "operator": "=", "value": "{parent_value}"}]'></textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- Data Source Configuration (for select, radio, checkbox fields) -->
            <div class="form-section data-source-container" style="display: none;">
              <h6><i class="ri-database-2-line me-2"></i>Konfigurasi Data Source</h6>

              <div class="mb-3">
                <label for="data_source_type" class="form-label">Tipe Data Source</label>
                <select class="form-select" id="data_source_type" name="data_source_type">
                  @foreach($dataSourceTypes as $key => $type)
                    <option value="{{ $key }}" {{ old('data_source_type', 'manual') == $key ? 'selected' : '' }}>
                      {{ $type }}
                    </option>
                  @endforeach
                </select>
              </div>

              <!-- Manual Options -->
              <div id="manual-options" class="data-source-section">
                <div class="mb-3">
                  <label class="form-label">Manual Options</label>
                  <div id="options-container">
                    <div class="option-item mb-2">
                      <div class="input-group">
                        <input type="text" class="form-control" name="field_options[options][key][]" placeholder="Key">
                        <input type="text" class="form-control" name="field_options[options][value][]" placeholder="Value">
                        <button type="button" class="btn btn-outline-danger remove-option">
                          <i class="ri-delete-bin-line"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <button type="button" class="btn btn-sm btn-outline-primary" id="add-option">
                    <i class="ri-add-line me-1"></i>Tambah Option
                  </button>
                </div>
              </div>

              <!-- Database Options -->
              <div id="database-options" class="data-source-section" style="display: none;">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="data_source_table" class="form-label">Database Table</label>
                      <select class="form-select" id="data_source_table" name="data_source_table">
                        <option value="">Pilih Table</option>
                        @foreach($availableTables as $table => $label)
                          <option value="{{ $table }}">{{ $label }}</option>
                        @endforeach
                      </select>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="data_source_filter" class="form-label">Filter (JSON)</label>
                      <input type="text" class="form-control" id="data_source_filter" name="data_source_filter"
                             placeholder='{"status": "active"}'>
                      <div class="form-text">Optional: JSON filter untuk query</div>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="data_source_value_column" class="form-label">Value Column</label>
                      <select class="form-select" id="data_source_value_column" name="data_source_value_column">
                        <option value="">Pilih Value Column</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="data_source_label_column" class="form-label">Label Column</label>
                      <select class="form-select" id="data_source_label_column" name="data_source_label_column">
                        <option value="">Pilih Label Column</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="mb-3">
                  <button type="button" class="btn btn-outline-info btn-sm" id="test-database-btn">
                    <i class="ri-test-tube-line me-1"></i>
                    Test Database Connection
                  </button>
                  <div id="database-test-result" class="mt-2"></div>
                </div>
              </div>

              <!-- Lookup Options -->
              <div id="lookup-options" class="data-source-section" style="display: none;">
                <div class="mb-3">
                  <label for="lookup_code" class="form-label">Lookup Code</label>
                  <select class="form-select" id="lookup_code" name="data_source_filter">
                    <option value="">Pilih Lookup Code</option>
                    @foreach($lookupCodes as $code)
                      <option value="{{ $code }}">{{ $code }}</option>
                    @endforeach
                  </select>
                  <div class="form-text">Data akan diambil dari Master Lookup berdasarkan kode ini</div>
                </div>
                <div class="mb-3">
                  <button type="button" class="btn btn-outline-info btn-sm" id="test-lookup-btn">
                    <i class="ri-test-tube-line me-1"></i>
                    Test Lookup Data
                  </button>
                  <div id="lookup-test-result" class="mt-2"></div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.asset-field-configurations.index') }}" class="btn btn-outline-secondary">
                Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                Simpan Field
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview -->
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-eye-line me-2"></i>
              Preview Field
            </h6>
          </div>
          <div class="card-body">
            <div class="field-preview" id="field-preview">
              <p class="text-muted text-center">
                <i class="ri-eye-off-line ri-24px d-block mb-2"></i>
                Isi form untuk melihat preview
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const fieldTypeSelect = document.getElementById('field_type');
  const fieldLabelInput = document.getElementById('field_label');
  const fieldNameInput = document.getElementById('field_name');
  const helpTextInput = document.getElementById('help_text');
  const isRequiredCheck = document.getElementById('is_required');
  const previewContainer = document.getElementById('field-preview');
  const dataSourceContainer = document.querySelector('.data-source-container');
  const dependentDropdownSection = document.querySelector('.dependent-dropdown-section');
  const validationSection = document.querySelector('.validation-section');
  const dataSourceTypeSelect = document.getElementById('data_source_type');
  const dataSourceTableSelect = document.getElementById('data_source_table');
  const enableDependentCheck = document.getElementById('enable_dependent');
  const parentFieldSelect = document.getElementById('parent_field_id');
  const validationPatternPreset = document.getElementById('validation_pattern_preset');

  // Auto-generate field name from label
  fieldLabelInput.addEventListener('input', function() {
    if (!fieldNameInput.value) {
      const fieldName = this.value.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_');
      fieldNameInput.value = fieldName;
    }
    updatePreview();
  });

  // Show/hide containers based on field type
  fieldTypeSelect.addEventListener('change', function() {
    const fieldType = this.value;
    const needsDataSource = ['select', 'radio', 'checkbox'].includes(fieldType);
    const isSelect = fieldType === 'select';
    const needsValidation = ['text', 'email', 'url', 'tel', 'number'].includes(fieldType);

    // Show/hide data source container
    dataSourceContainer.style.display = needsDataSource ? 'block' : 'none';

    // Show/hide dependent dropdown section (only for select fields)
    dependentDropdownSection.style.display = isSelect ? 'block' : 'none';

    // Show/hide validation section (for text-based fields)
    validationSection.style.display = needsValidation ? 'block' : 'none';

    // Reset dependent dropdown if not select field
    if (!isSelect) {
      enableDependentCheck.checked = false;
      document.getElementById('dependent-config').style.display = 'none';
    }

    updatePreview();
  });

  // Show/hide dependent config when checkbox is toggled
  enableDependentCheck.addEventListener('change', function() {
    const dependentConfig = document.getElementById('dependent-config');
    dependentConfig.style.display = this.checked ? 'block' : 'none';

    // Reset parent field selection when disabled
    if (!this.checked) {
      parentFieldSelect.value = '';
    }
  });

  // Show/hide data source sections
  dataSourceTypeSelect.addEventListener('change', function() {
    const sections = document.querySelectorAll('.data-source-section');
    sections.forEach(section => section.style.display = 'none');

    const selectedSection = document.getElementById(this.value + '-options');
    if (selectedSection) {
      selectedSection.style.display = 'block';
    }
  });

  // Load table columns when table is selected
  dataSourceTableSelect.addEventListener('change', function() {
    const tableName = this.value;
    const valueSelect = document.getElementById('data_source_value_column');
    const labelSelect = document.getElementById('data_source_label_column');

    // Clear existing options
    valueSelect.innerHTML = '<option value="">Pilih Value Column</option>';
    labelSelect.innerHTML = '<option value="">Pilih Label Column</option>';

    if (!tableName) return;

    // Show loading state
    valueSelect.innerHTML = '<option value="">Loading...</option>';
    labelSelect.innerHTML = '<option value="">Loading...</option>';

    fetch(`{{ route('master.asset-field-configurations.table-columns') }}?table=${encodeURIComponent(tableName)}`, {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        credentials: 'same-origin'
      })
      .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('Received data:', data);

        // Clear loading state
        valueSelect.innerHTML = '<option value="">Pilih Value Column</option>';
        labelSelect.innerHTML = '<option value="">Pilih Label Column</option>';

        if (data.error) {
          console.error('Server error:', data.error);
          valueSelect.innerHTML = '<option value="">Error loading columns</option>';
          labelSelect.innerHTML = '<option value="">Error loading columns</option>';
          alert('Error: ' + data.error);
          return;
        }

        if (data.success && data.columns) {
          console.log('Using data.columns:', data.columns);
          // Add new options
          Object.entries(data.columns).forEach(([key, value]) => {
            valueSelect.innerHTML += `<option value="${key}">${value}</option>`;
            labelSelect.innerHTML += `<option value="${key}">${value}</option>`;
          });
        } else {
          console.log('Using direct data:', data);
          // Fallback for direct columns response
          Object.entries(data).forEach(([key, value]) => {
            if (typeof value === 'string') {
              valueSelect.innerHTML += `<option value="${key}">${value}</option>`;
              labelSelect.innerHTML += `<option value="${key}">${value}</option>`;
            }
          });
        }
      })
      .catch(error => {
        console.error('Error loading table columns:', error);
        valueSelect.innerHTML = '<option value="">Error loading columns</option>';
        labelSelect.innerHTML = '<option value="">Error loading columns</option>';
        alert('Error loading table columns: ' + error.message);
      });
  });

  // Update preview on any change
  [fieldNameInput, fieldTypeSelect, helpTextInput, isRequiredCheck].forEach(element => {
    element.addEventListener('input', updatePreview);
    element.addEventListener('change', updatePreview);
  });

  function updatePreview() {
    const fieldType = fieldTypeSelect.value;
    const fieldLabel = fieldLabelInput.value;
    const helpText = helpTextInput.value;
    const isRequired = isRequiredCheck.checked;

    if (!fieldType || !fieldLabel) {
      previewContainer.innerHTML = `
        <p class="text-muted text-center">
          <i class="ri-eye-off-line ri-24px d-block mb-2"></i>
          Isi form untuk melihat preview
        </p>
      `;
      return;
    }

    let fieldHtml = '';
    const requiredMark = isRequired ? '<span class="text-danger">*</span>' : '';

    switch (fieldType) {
      case 'text':
      case 'email':
      case 'url':
      case 'tel':
        fieldHtml = `<input type="${fieldType}" class="form-control" placeholder="Masukkan ${fieldLabel.toLowerCase()}">`;
        break;
      case 'number':
        fieldHtml = `<input type="number" class="form-control" placeholder="Masukkan ${fieldLabel.toLowerCase()}">`;
        break;
      case 'date':
        fieldHtml = `<input type="date" class="form-control">`;
        break;
      case 'textarea':
        fieldHtml = `<textarea class="form-control" rows="3" placeholder="Masukkan ${fieldLabel.toLowerCase()}"></textarea>`;
        break;
      case 'select':
        fieldHtml = `
          <select class="form-select">
            <option value="">Pilih ${fieldLabel}</option>
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </select>
        `;
        break;
      case 'file':
        fieldHtml = `<input type="file" class="form-control">`;
        break;
      default:
        fieldHtml = `<input type="text" class="form-control" placeholder="Masukkan ${fieldLabel.toLowerCase()}">`;
    }

    previewContainer.innerHTML = `
      <div class="mb-3">
        <label class="form-label">${fieldLabel} ${requiredMark}</label>
        ${fieldHtml}
        ${helpText ? `<div class="form-text">${helpText}</div>` : ''}
      </div>
    `;
  }

  // Add option functionality
  document.getElementById('add-option').addEventListener('click', function() {
    const container = document.getElementById('options-container');
    const newOption = document.createElement('div');
    newOption.className = 'option-item mb-2';
    newOption.innerHTML = `
      <div class="input-group">
        <input type="text" class="form-control" name="field_options[options][key][]" placeholder="Key">
        <input type="text" class="form-control" name="field_options[options][value][]" placeholder="Value">
        <button type="button" class="btn btn-outline-danger remove-option">
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>
    `;
    container.appendChild(newOption);
  });

  // Remove option functionality
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-option')) {
      e.target.closest('.option-item').remove();
    }
  });

  // Test database connection
  document.getElementById('test-database-btn').addEventListener('click', function() {
    const table = document.getElementById('data_source_table').value;
    const valueColumn = document.getElementById('data_source_value_column').value;
    const labelColumn = document.getElementById('data_source_label_column').value;
    const filter = document.getElementById('data_source_filter').value;

    if (!table || !valueColumn || !labelColumn) {
      alert('Please fill in table, value column, and label column first');
      return;
    }

    const resultDiv = document.getElementById('database-test-result');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="ri-loader-4-line ri-spin me-1"></i> Testing connection...</div>';

    const params = new URLSearchParams({
      data_source_type: 'database',
      table: table,
      value_column: valueColumn,
      label_column: labelColumn,
      filter: filter
    });

    fetch(`{{ route('master.asset-field-configurations.test-data-source') }}?${params}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          let html = `<div class="alert alert-success">
            <strong>Success!</strong> Found ${data.count} records.<br>
            <small>Sample data:</small>
            <ul class="mb-0 mt-1">`;

          let count = 0;
          for (const [key, value] of Object.entries(data.options)) {
            if (count >= 5) break;
            html += `<li><code>${key}</code> → ${value}</li>`;
            count++;
          }

          if (data.count > 5) {
            html += `<li><em>... and ${data.count - 5} more</em></li>`;
          }

          html += '</ul></div>';
          resultDiv.innerHTML = html;
        } else {
          resultDiv.innerHTML = `<div class="alert alert-danger"><strong>Error:</strong> ${data.error}</div>`;
        }
      })
      .catch(error => {
        resultDiv.innerHTML = `<div class="alert alert-danger"><strong>Error:</strong> ${error.message}</div>`;
      });
  });

  // Handle validation pattern preset selection
  validationPatternPreset.addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
      document.getElementById('validation_pattern').value = selectedOption.dataset.pattern || '';
      document.getElementById('validation_message').value = selectedOption.dataset.message || '';
      document.getElementById('input_mask').value = selectedOption.dataset.mask || '';
      document.getElementById('placeholder_text').value = selectedOption.dataset.placeholder || '';
    }
  });

  // Test validation pattern
  document.getElementById('test-pattern-btn').addEventListener('click', function() {
    const pattern = document.getElementById('validation_pattern').value;
    const message = document.getElementById('validation_message').value;
    const resultDiv = document.getElementById('pattern-test-result');

    if (!pattern) {
      resultDiv.innerHTML = '<div class="alert alert-warning">Please enter a regex pattern first</div>';
      return;
    }

    // Show test input
    resultDiv.innerHTML = `
      <div class="alert alert-info">
        <strong>Test your pattern:</strong><br>
        <div class="input-group mt-2">
          <input type="text" class="form-control" id="test-input" placeholder="Enter test value">
          <button type="button" class="btn btn-outline-primary" id="validate-test">Test</button>
        </div>
        <div id="test-result" class="mt-2"></div>
      </div>
    `;

    // Add test functionality
    document.getElementById('validate-test').addEventListener('click', function() {
      const testValue = document.getElementById('test-input').value;
      const testResult = document.getElementById('test-result');

      try {
        const regex = new RegExp(pattern);
        const isValid = regex.test(testValue);

        if (isValid) {
          testResult.innerHTML = '<div class="text-success"><i class="ri-check-line me-1"></i>Valid! Pattern matches.</div>';
        } else {
          testResult.innerHTML = `<div class="text-danger"><i class="ri-close-line me-1"></i>Invalid! ${message || 'Pattern does not match.'}</div>`;
        }
      } catch (error) {
        testResult.innerHTML = `<div class="text-danger"><i class="ri-error-warning-line me-1"></i>Invalid regex pattern: ${error.message}</div>`;
      }
    });

    // Test on input
    document.getElementById('test-input').addEventListener('input', function() {
      document.getElementById('validate-test').click();
    });
  });

  // Test lookup data
  document.getElementById('test-lookup-btn').addEventListener('click', function() {
    const lookupCode = document.getElementById('lookup_code').value;

    if (!lookupCode) {
      alert('Please select a lookup code first');
      return;
    }

    const resultDiv = document.getElementById('lookup-test-result');
    resultDiv.innerHTML = '<div class="alert alert-info"><i class="ri-loader-4-line ri-spin me-1"></i> Testing lookup...</div>';

    const params = new URLSearchParams({
      data_source_type: 'lookup',
      lookup_code: lookupCode
    });

    fetch(`{{ route('master.asset-field-configurations.test-data-source') }}?${params}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          let html = `<div class="alert alert-success">
            <strong>Success!</strong> Found ${data.count} records.<br>
            <small>Sample data:</small>
            <ul class="mb-0 mt-1">`;

          let count = 0;
          for (const [key, value] of Object.entries(data.options)) {
            if (count >= 5) break;
            html += `<li><code>${key}</code> → ${value}</li>`;
            count++;
          }

          if (data.count > 5) {
            html += `<li><em>... and ${data.count - 5} more</em></li>`;
          }

          html += '</ul></div>';
          resultDiv.innerHTML = html;
        } else {
          resultDiv.innerHTML = `<div class="alert alert-danger"><strong>Error:</strong> ${data.error}</div>`;
        }
      })
      .catch(error => {
        resultDiv.innerHTML = `<div class="alert alert-danger"><strong>Error:</strong> ${error.message}</div>`;
      });
  });
});
</script>

@endsection
