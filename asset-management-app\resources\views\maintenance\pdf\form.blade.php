<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Form Maintenance Asset - {{ $maintenance->maintenance_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 10px;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        .info-label {
            width: 150px;
            font-weight: bold;
        }
        .info-value {
            flex: 1;
            border-bottom: 1px dotted #666;
            padding-bottom: 2px;
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            background-color: #f0f0f0;
            padding: 8px;
            margin: 20px 0 10px 0;
            border: 1px solid #ccc;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #666;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 200px;
            text-align: center;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            height: 60px;
            margin-bottom: 5px;
        }
        .qr-section {
            position: absolute;
            top: 20px;
            right: 20px;
            text-align: center;
            border: 2px solid #000;
            padding: 10px;
            background-color: #fff;
            width: 150px;
            height: 160px;
        }
        .qr-code {
            margin-bottom: 5px;
            display: block;
            width: 100px;
            height: 100px;
            margin: 0 auto;
        }
        .qr-code svg {
            width: 100px;
            height: 100px;
            display: block;
            margin: 0 auto;
        }
        .qr-code div {
            width: 100px;
            height: 100px;
            margin: 0 auto;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        .status-scheduled { background-color: #007bff; }
        .status-in_progress { background-color: #ffc107; color: #000; }
        .status-completed { background-color: #28a745; }
        .status-cancelled { background-color: #dc3545; }
    </style>
</head>
<body>
    <!-- QR Code Section -->
    <div class="qr-section">
        <div class="qr-code">
            {!! $qrCode !!}
        </div>
        <div style="font-size: 9px; font-weight: bold; margin-top: 5px;">
            Scan untuk tracking
        </div>
        <div style="font-size: 7px; margin-top: 3px; word-break: break-all;">
            {{ $qrCodeUrl }}
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $company->company_name ?? 'PT. NAMA PERUSAHAAN' }}</div>
        <div>{{ $company->address ?? 'Alamat Perusahaan' }}</div>
        <div>Telp: {{ $company->phone ?? '-' }} | Email: {{ $company->email ?? '-' }}</div>
        <div class="document-title">FORM MAINTENANCE ASSET</div>
    </div>

    <!-- Document Information -->
    <div class="info-section">
        <div class="info-row">
            <div class="info-label">Nomor Maintenance:</div>
            <div class="info-value">{{ $maintenance->maintenance_number }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Tanggal Dibuat:</div>
            <div class="info-value">{{ $maintenance->created_at->format('d/m/Y') }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Status:</div>
            <div class="info-value">
                <span class="status-badge status-{{ $maintenance->status }}">
                    {{ $maintenance->status_text }}
                </span>
            </div>
        </div>
    </div>

    <!-- Asset Information -->
    <div class="section-title">INFORMASI ASSET</div>
    <table class="table">
        <tr>
            <th width="25%">Kode Asset</th>
            <td>{{ $maintenance->asset->asset_code }}</td>
            <th width="25%">Nama Asset</th>
            <td>{{ $maintenance->asset->name }}</td>
        </tr>
        <tr>
            <th>Kategori</th>
            <td>{{ $maintenance->asset->assetCategory->name ?? '-' }}</td>
            <th>Cabang</th>
            <td>{{ $maintenance->asset->branch->name ?? '-' }}</td>
        </tr>
        <tr>
            <th>Brand/Model</th>
            <td>{{ $maintenance->asset->brand }} {{ $maintenance->asset->model }}</td>
            <th>Serial Number</th>
            <td>{{ $maintenance->asset->serial_number ?? '-' }}</td>
        </tr>
        <tr>
            <th>Status Asset</th>
            <td>{{ ucfirst($maintenance->asset->status) }}</td>
            <th>Lokasi</th>
            <td>{{ $maintenance->asset->location ?? '-' }}</td>
        </tr>
    </table>

    <!-- Employee Assignment Information -->
    @if($maintenance->asset->assignedEmployee)
    <div class="section-title">INFORMASI KARYAWAN YANG MENGGUNAKAN ASSET</div>
    <table class="table">
        <tr>
            <th width="25%">NIK</th>
            <td>{{ $maintenance->asset->assignedEmployee->nik ?? '-' }}</td>
            <th width="25%">Nama Lengkap</th>
            <td>{{ $maintenance->asset->assignedEmployee->full_name ?? '-' }}</td>
        </tr>
        <tr>
            <th>Jabatan</th>
            <td>{{ $maintenance->asset->assignedEmployee->position ?? '-' }}</td>
            <th>Divisi</th>
            <td>{{ $maintenance->asset->assignedEmployee->division->name ?? '-' }}</td>
        </tr>
        <tr>
            <th>Cabang</th>
            <td>{{ $maintenance->asset->assignedEmployee->branch->name ?? '-' }}</td>
            <th>Departemen</th>
            <td>{{ $maintenance->asset->assignedEmployee->department ?? '-' }}</td>
        </tr>
    </table>
    @endif

    <!-- Maintenance Details -->
    <div class="section-title">DETAIL MAINTENANCE</div>
    <table class="table">
        <tr>
            <th width="25%">Judul Maintenance</th>
            <td colspan="3">{{ $maintenance->title }}</td>
        </tr>
        <tr>
            <th>Tipe Maintenance</th>
            <td>{{ $maintenance->maintenance_type_text }}</td>
            <th width="25%">Prioritas</th>
            <td>{{ $maintenance->priority_text }}</td>
        </tr>
        <tr>
            <th>Tanggal Jadwal</th>
            <td>{{ $maintenance->scheduled_date ? $maintenance->scheduled_date->format('d/m/Y') : '-' }}</td>
            <th>Estimasi Biaya</th>
            <td>Rp {{ number_format($maintenance->estimated_cost ?? 0, 0, ',', '.') }}</td>
        </tr>
        @if($maintenance->started_date)
        <tr>
            <th>Tanggal Mulai</th>
            <td>{{ $maintenance->started_date->format('d/m/Y') }}</td>
            <th>Tanggal Selesai</th>
            <td>{{ $maintenance->completed_date ? $maintenance->completed_date->format('d/m/Y') : '-' }}</td>
        </tr>
        @endif
        @if($maintenance->actual_cost)
        <tr>
            <th>Biaya Aktual</th>
            <td colspan="3">Rp {{ number_format($maintenance->actual_cost, 0, ',', '.') }}</td>
        </tr>
        @endif
    </table>

    <!-- Problem & Solution Description -->
    <div class="section-title">DESKRIPSI MASALAH</div>
    <div style="border: 1px solid #666; padding: 10px; min-height: 60px; margin-bottom: 20px;">
        {{ $maintenance->problem_description ?? $maintenance->description ?? '-' }}
    </div>

    @if($maintenance->solution_description)
    <div class="section-title">SOLUSI YANG DILAKUKAN</div>
    <div style="border: 1px solid #666; padding: 10px; min-height: 60px; margin-bottom: 20px;">
        {{ $maintenance->solution_description }}
    </div>
    @endif

    <!-- Supplier Information -->
    @if($maintenance->supplier)
    <div class="section-title">INFORMASI SUPPLIER</div>
    <table class="table">
        <tr>
            <th width="25%">Kode Supplier</th>
            <td>{{ $maintenance->supplier->supplier_code }}</td>
            <th width="25%">Nama Supplier</th>
            <td>{{ $maintenance->supplier->name }}</td>
        </tr>
        <tr>
            <th>Perusahaan</th>
            <td>{{ $maintenance->supplier->company_name ?? '-' }}</td>
            <th>Kontak</th>
            <td>{{ $maintenance->supplier->phone ?? '-' }}</td>
        </tr>
    </table>
    @endif

    <!-- Notes -->
    @if($maintenance->notes)
    <div class="section-title">CATATAN TAMBAHAN</div>
    <div style="border: 1px solid #666; padding: 10px; min-height: 40px; margin-bottom: 20px;">
        {{ $maintenance->notes }}
    </div>
    @endif

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div>Diminta Oleh:</div>
            <div class="signature-line"></div>
            <div>{{ $maintenance->requestedBy->name ?? '-' }}</div>
            <div>Tanggal: ___________</div>
        </div>
        
        <div class="signature-box">
            <div>Ditugaskan Kepada:</div>
            <div class="signature-line"></div>
            <div>{{ $maintenance->assignedTo->name ?? '-' }}</div>
            <div>Tanggal: ___________</div>
        </div>
        
        <div class="signature-box">
            <div>Disetujui Oleh:</div>
            <div class="signature-line"></div>
            <div>{{ $maintenance->approvedBy->name ?? '-' }}</div>
            <div>Tanggal: ___________</div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Dicetak pada: {{ $printDate }} oleh {{ $printedBy }}<br>
        Dokumen ini digenerate otomatis oleh sistem Asset Management
    </div>
</body>
</html>
