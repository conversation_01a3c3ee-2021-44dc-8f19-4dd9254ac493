@extends('layouts.contentNavbarLayout')

@section('title', 'Daftar Permintaan Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi /</span> Daftar Permintaan Asset
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('request-assets.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Buat Permin<PERSON>an
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('request-assets.index') }}">
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label"><PERSON><PERSON><PERSON></label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                   placeholder="Nomor, tujuan, atau nama pemohon...">
          </div>
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <select class="form-select" name="status">
              <option value="">Semua Status</option>
              <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
              <option value="submitted" {{ request('status') === 'submitted' ? 'selected' : '' }}>Diajukan</option>
              <option value="reviewed" {{ request('status') === 'reviewed' ? 'selected' : '' }}>Dalam Review</option>
              <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
              <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
              <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Divisi</label>
            <select class="form-select" name="division_id">
              <option value="">Semua Divisi</option>
              @foreach($divisions as $division)
                <option value="{{ $division->id }}" {{ request('division_id') == $division->id ? 'selected' : '' }}>
                  {{ $division->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('request-assets.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Requests Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Permintaan ({{ $requests->total() }} permintaan)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nomor Permintaan</th>
            <th>Pemohon</th>
            <th>Kategori & Item</th>
            <th>Tanggal</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($requests as $request)
          <tr>
            <td>
              <div>
                <strong>{{ $request->request_number }}</strong>
                <br><small class="text-muted">{{ $request->total_items }} item</small>
                @if($request->estimated_total)
                  <br><small class="text-primary">Rp {{ number_format($request->estimated_total, 0, ',', '.') }}</small>
                @endif
              </div>
            </td>
            <td>
              <div>
                <strong>{{ $request->requestedByUser->name }}</strong>
                <br><small class="text-muted">{{ $request->position }}</small>
                <br><small class="text-muted">{{ $request->division->name }}</small>
              </div>
            </td>
            <td>
              <div>
                <span class="badge bg-info mb-1">{{ $request->request_category }}</span>
                <br><span class="badge bg-secondary">{{ $request->item_type }}</span>
                @if($request->purpose)
                  <br><small class="text-muted">{{ Str::limit($request->purpose, 50) }}</small>
                @endif
              </div>
            </td>
            <td>
              <div>
                <strong>{{ $request->request_date->format('d/m/Y') }}</strong>
                <br><small class="text-muted">Dibutuhkan:</small>
                <br><small class="text-muted">{{ $request->needed_date->format('d/m/Y') }}</small>
              </div>
            </td>
            <td>
              <span class="badge bg-{{ $request->status_badge }}">
                {{ $request->status_label }}
              </span>
              @if(in_array($request->status, ['approved', 'rejected']) && $request->approvedByUser)
                <br><small class="text-muted">oleh {{ $request->approvedByUser->name }}</small>
                <br><small class="text-muted">{{ $request->approved_at->format('d/m/Y') }}</small>
              @endif
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('request-assets.show', $request) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>

                  @if(in_array($request->status, ['draft', 'submitted', 'reviewed', 'approved', 'completed']))
                    <a class="dropdown-item" href="{{ route('request-assets.print', $request) }}" target="_blank">
                      <i class="ri-printer-line me-1"></i> Cetak PDF
                    </a>
                  @endif
                  
                  @if($request->canBeEdited())
                    <a class="dropdown-item" href="{{ route('request-assets.edit', $request) }}">
                      <i class="ri-pencil-line me-1"></i> Edit
                    </a>
                  @endif
                  
                  @if($request->canBeSubmitted())
                    <form action="{{ route('request-assets.submit', $request) }}" method="POST" class="d-inline">
                      @csrf
                      <button type="submit" class="dropdown-item text-success" onclick="return confirm('Yakin ingin mengajukan permintaan ini?')">
                        <i class="ri-send-plane-line me-1"></i> Ajukan
                      </button>
                    </form>
                  @endif
                  
                  @php
                    try {
                      $approvalService = app(\App\Services\ApprovalService::class);
                      $canApprove = $approvalService->canUserApproveRequest($request, auth()->user());
                    } catch (\Exception $e) {
                      $canApprove = false;
                    }
                  @endphp

                  @if($canApprove)
                    <div class="dropdown-divider"></div>
                    <button type="button" class="dropdown-item text-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ $request->id }}">
                      <i class="ri-check-line me-1"></i> Setujui
                    </button>
                    <button type="button" class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ $request->id }}">
                      <i class="ri-close-line me-1"></i> Tolak
                    </button>
                  @endif
                  
                  @if($request->status === 'draft')
                    <div class="dropdown-divider"></div>
                    <form action="{{ route('request-assets.destroy', $request) }}" method="POST" class="d-inline">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus permintaan ini?')">
                        <i class="ri-delete-bin-line me-1"></i> Hapus
                      </button>
                    </form>
                  @endif
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="6" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-file-list-3-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada permintaan asset</h6>
                <p class="text-muted mb-3">Belum ada permintaan asset yang dibuat atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('request-assets.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Buat Permintaan Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($requests->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $requests->firstItem() }} - {{ $requests->lastItem() }} dari {{ $requests->total() }} data
        </div>
        {{ $requests->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

<!-- Approve Modals -->
@foreach($requests as $request)
@php
  try {
    $approvalService = app(\App\Services\ApprovalService::class);
    $canApprove = $approvalService->canUserApproveRequest($request, auth()->user());
  } catch (\Exception $e) {
    $canApprove = false;
  }
@endphp
@if($canApprove)
<div class="modal fade" id="approveModal{{ $request->id }}" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Setujui Permintaan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('request-assets.approve', $request) }}" method="POST">
        @csrf
        <div class="modal-body">
          <p>Yakin ingin menyetujui permintaan <strong>{{ $request->request_number }}</strong>?</p>
          
          <div class="mb-3">
            <label class="form-label">Catatan Persetujuan (Opsional)</label>
            <textarea class="form-control" name="approval_notes" rows="3" 
                      placeholder="Catatan atau instruksi tambahan..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-success">
            <i class="ri-check-line me-1"></i>Setujui
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="modal fade" id="rejectModal{{ $request->id }}" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Tolak Permintaan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('request-assets.reject', $request) }}" method="POST">
        @csrf
        <div class="modal-body">
          <p>Yakin ingin menolak permintaan <strong>{{ $request->request_number }}</strong>?</p>
          
          <div class="mb-3">
            <label class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
            <textarea class="form-control" name="approval_notes" rows="3" required
                      placeholder="Jelaskan alasan penolakan..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-danger">
            <i class="ri-close-line me-1"></i>Tolak
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
@endif
@endforeach

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
