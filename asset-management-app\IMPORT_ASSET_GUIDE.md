# Panduan Import Data Asset Excel

## Deskripsi
Fitur import data asset memungkinkan pengguna untuk mengunggah data asset dalam jumlah besar menggunakan file Excel. Sistem akan memvalidasi data dan memberikan laporan error jika ada data yang tidak sesuai.

## Persyaratan
- User harus memiliki permission `assets.import`
- File harus berformat Excel (.xlsx)
- Maksimal ukuran file: 10MB
- Data harus sesuai dengan format template yang disediakan

## Cara Menggunakan

### 1. Download Template
1. Login ke aplikasi dengan user yang memiliki permission import
2. Masuk ke menu **Lihat Data Asset**
3. Klik tombol **Import Excel** (hanya muncul jika user memiliki permission)
4. Di halaman import, klik **Download Template Excel**
5. Template akan otomatis terdownload dengan nama `Template_Import_Asset_[tanggal-waktu].xlsx`

### 2. Mengisi Template
Template terdiri dari 3 sheet:

#### Sheet 1: Template Import Asset
Kolom yang harus diisi:
- **asset_code**: Kode asset (opsional, akan auto-generate jika kosong)
- **name**: Nama asset (wajib)
- **category_code**: Kode kategori asset (wajib, lihat sheet referensi)
- **type_code**: Kode tipe asset (wajib, lihat sheet referensi)
- **brand**: Merek asset (opsional)
- **model**: Model asset (opsional)
- **serial_number**: Nomor seri (opsional)
- **purchase_date**: Tanggal pembelian format dd/mm/yyyy (opsional)
- **purchase_price**: Harga pembelian dalam rupiah (opsional)
- **supplier_code**: Kode supplier (opsional, lihat sheet referensi)
- **branch_code**: Kode cabang (wajib, lihat sheet referensi)
- **condition**: Kondisi asset (Baik/Rusak Ringan/Rusak Berat)
- **status**: Status asset (Active/Inactive/Maintenance/Disposed)
- **location**: Lokasi asset (opsional)
- **assigned_employee_nik**: NIK karyawan yang ditugaskan (opsional)
- **notes**: Catatan tambahan (opsional)

#### Sheet 2: Petunjuk Pengisian
Berisi penjelasan detail untuk setiap kolom dan format yang harus digunakan.

#### Sheet 3: Data Referensi
Berisi data master yang dapat digunakan sebagai referensi:
- Kategori Asset (kode dan nama)
- Tipe Asset (kode dan nama)
- Cabang (kode dan nama)
- Supplier (kode dan nama)
- Karyawan (NIK dan nama)

### 3. Upload File
1. Setelah mengisi template, simpan file Excel
2. Kembali ke halaman import
3. Klik **Choose File** dan pilih file Excel yang sudah diisi
4. Klik **Import Data**
5. Sistem akan memproses dan menampilkan hasil import

## Validasi Data

### Validasi Header
- Sistem akan memeriksa apakah header kolom sesuai dengan template
- Jika ada kolom yang hilang atau salah, import akan dibatalkan

### Validasi Data
- **Nama asset**: Tidak boleh kosong
- **Kategori**: Harus ada di master kategori
- **Tipe**: Harus ada di master tipe
- **Cabang**: Harus ada di master cabang
- **Supplier**: Jika diisi, harus ada di master supplier
- **Karyawan**: Jika diisi, NIK harus ada di master karyawan
- **Tanggal**: Format harus dd/mm/yyyy
- **Harga**: Harus berupa angka
- **Kondisi**: Harus salah satu dari: Baik, Rusak Ringan, Rusak Berat
- **Status**: Harus salah satu dari: Active, Inactive, Maintenance, Disposed

### Auto-Generate Asset Code
Jika kolom `asset_code` dikosongkan, sistem akan otomatis generate kode dengan format:
`[CATEGORY_CODE]-[BRANCH_CODE]-[YEAR]-[AUTO_NUMBER]`

Contoh: `LAPTOP-JKT-2025-001`

## Hasil Import

### Sukses
- Menampilkan jumlah data yang berhasil diimport
- Data langsung tersimpan di database
- Asset yang memiliki karyawan akan otomatis ter-assign

### Error
- Menampilkan daftar error per baris
- Data tidak akan tersimpan jika ada error
- User harus memperbaiki error dan upload ulang

## Tips Penggunaan

1. **Backup Data**: Selalu backup database sebelum import data besar
2. **Test Import**: Coba import dengan sedikit data dulu untuk memastikan format benar
3. **Validasi Manual**: Periksa data referensi di sheet 3 sebelum mengisi template
4. **Format Tanggal**: Pastikan format tanggal konsisten (dd/mm/yyyy)
5. **Kode Unik**: Jika mengisi asset_code manual, pastikan tidak duplikat
6. **Employee Assignment**: Asset yang di-assign ke karyawan akan otomatis mengubah status assignment

## Troubleshooting

### Error "Permission Denied"
- Pastikan user memiliki permission `assets.import`
- Contact administrator untuk menambahkan permission

### Error "Invalid File Format"
- Pastikan file berformat .xlsx
- Jangan mengubah struktur template
- Pastikan file tidak corrupt

### Error "File Too Large"
- Maksimal ukuran file 10MB
- Bagi data menjadi beberapa file jika terlalu besar

### Error "Invalid Reference Data"
- Periksa kembali kode kategori, tipe, cabang, supplier, dan NIK karyawan
- Pastikan data referensi ada di master data
- Gunakan data dari sheet "Data Referensi" sebagai acuan

## Permissions Required
- `assets.import`: Untuk mengakses fitur import
- `assets.create`: Untuk membuat asset baru
- `assets.view`: Untuk melihat hasil import

## File Locations
- Controller: `app/Http/Controllers/AssetImportController.php`
- View: `resources/views/assets/import.blade.php`
- Routes: Defined in `routes/web.php`
- Permissions: Seeded via `AssetPermissionSeeder.php`
