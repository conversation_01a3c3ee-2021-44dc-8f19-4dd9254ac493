<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->string('contract_number')->unique();
            $table->string('contract_name');
            $table->text('description')->nullable();
            $table->foreignId('supplier_id')->constrained('suppliers')->onDelete('cascade');
            $table->foreignId('division_id')->nullable()->constrained('divisions')->onDelete('set null');
            $table->date('contract_date');
            $table->date('start_date');
            $table->date('end_date');
            $table->decimal('contract_value', 15, 2)->nullable();
            $table->enum('contract_type', ['service', 'supply', 'maintenance', 'lease', 'other'])->default('service');
            $table->enum('status', ['draft', 'active', 'expired', 'terminated', 'renewed'])->default('draft');
            $table->text('terms_conditions')->nullable();
            $table->string('contract_file')->nullable(); // File upload path
            $table->json('attachments')->nullable(); // Additional files
            $table->text('notes')->nullable();
            $table->boolean('auto_renewal')->default(false);
            $table->integer('renewal_period_months')->nullable(); // For auto renewal
            $table->integer('notification_days')->default(30); // Days before expiry to notify
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['supplier_id', 'status']);
            $table->index(['end_date', 'status']);
            $table->index(['division_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
