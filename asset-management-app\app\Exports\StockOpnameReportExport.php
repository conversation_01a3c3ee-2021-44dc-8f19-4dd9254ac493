<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use App\Models\StockOpname;
use App\Models\StockOpnameDetail;
use App\Helpers\BranchHelper;

class StockOpnameReportExport implements WithMultipleSheets
{
    protected $filters;
    protected $stockOpnameId;

    public function __construct($filters = [], $stockOpnameId = null)
    {
        $this->filters = $filters;
        $this->stockOpnameId = $stockOpnameId;
    }

    public function sheets(): array
    {
        $sheets = [];

        if ($this->stockOpnameId) {
            // Single stock opname detailed report
            $stockOpname = StockOpname::with(['branch', 'creator'])->find($this->stockOpnameId);

            $sheets[] = new StockOpnameSummarySheet($stockOpname);
            $sheets[] = new StockOpnameAllAssetsSheet($stockOpname);
            $sheets[] = new StockOpnameNotScannedSheet($stockOpname);
            $sheets[] = new StockOpnameScannedSheet($stockOpname);
            $sheets[] = new StockOpnameFoundSheet($stockOpname);
            $sheets[] = new StockOpnameMissingSheet($stockOpname);
            $sheets[] = new StockOpnameDamagedSheet($stockOpname);
            $sheets[] = new StockOpnameDiscrepancySheet($stockOpname);
            $sheets[] = new StockOpnameConditionAnalysisSheet($stockOpname);
            $sheets[] = new StockOpnameCategoryAnalysisSheet($stockOpname);
        } else {
            // Multiple stock opnames summary report
            $sheets[] = new StockOpnameListSheet($this->filters);
            $sheets[] = new StockOpnameSummaryStatsSheet($this->filters);
        }

        return $sheets;
    }
}

// Summary sheet for single stock opname
class StockOpnameSummarySheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return collect([$this->stockOpname]);
    }

    public function headings(): array
    {
        return [
            'Nomor Opname',
            'Judul',
            'Cabang',
            'Status',
            'Dibuat Oleh',
            'Tanggal Dibuat',
            'Tanggal Mulai',
            'Tanggal Selesai',
            'Durasi',
            'Total Asset',
            'Asset Di-scan',
            'Asset Belum Di-scan',
            'Asset Ditemukan',
            'Asset Hilang',
            'Asset Rusak',
            'Ketidaksesuaian',
            'Kondisi Excellent',
            'Kondisi Good',
            'Kondisi Fair',
            'Kondisi Poor',
            'Progress (%)',
            'Total Nilai Asset (Rp)',
            'Deskripsi',
            'Catatan'
        ];
    }

    public function map($stockOpname): array
    {
        $notScannedCount = $stockOpname->total_assets - $stockOpname->scanned_assets;

        // Get detailed counts
        $details = \App\Models\StockOpnameDetail::where('stock_opname_id', $stockOpname->id);
        $damagedCount = $details->where('found_status', 'damaged')->count();
        $discrepancyCount = $details->where('has_discrepancy', true)->count();
        $excellentCount = $details->where('physical_condition', 'excellent')->count();
        $goodCount = $details->where('physical_condition', 'good')->count();
        $fairCount = $details->where('physical_condition', 'fair')->count();
        $poorCount = $details->where('physical_condition', 'poor')->count();

        // Calculate duration
        $duration = '-';
        if ($stockOpname->start_date && $stockOpname->end_date) {
            $duration = $stockOpname->start_date->diffInDays($stockOpname->end_date) . ' hari';
        } elseif ($stockOpname->start_date) {
            $duration = $stockOpname->start_date->diffInDays(now()) . ' hari (berjalan)';
        }

        // Calculate total asset value
        $totalValue = \App\Models\StockOpnameDetail::where('stock_opname_id', $stockOpname->id)
            ->join('assets', 'stock_opname_details.asset_id', '=', 'assets.id')
            ->sum('assets.purchase_price');

        return [
            $stockOpname->opname_number,
            $stockOpname->title,
            $stockOpname->branch->name,
            $this->getStatusText($stockOpname->status),
            $stockOpname->creator->name,
            $stockOpname->created_at->format('d/m/Y H:i'),
            $stockOpname->start_date ? $stockOpname->start_date->format('d/m/Y H:i') : '-',
            $stockOpname->end_date ? $stockOpname->end_date->format('d/m/Y H:i') : '-',
            $duration,
            $stockOpname->total_assets,
            $stockOpname->scanned_assets,
            $notScannedCount,
            $stockOpname->found_assets,
            $stockOpname->missing_assets,
            $damagedCount,
            $discrepancyCount,
            $excellentCount,
            $goodCount,
            $fairCount,
            $poorCount,
            $stockOpname->total_assets > 0 ? round(($stockOpname->scanned_assets / $stockOpname->total_assets) * 100, 2) : 0,
            number_format($totalValue, 0, ',', '.'),
            $stockOpname->description ?: '-',
            $stockOpname->notes ?: '-'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E3F2FD']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ],
            2 => [
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ]
        ];
    }

    public function title(): string
    {
        return 'Ringkasan Stock Opname';
    }

    private function getStatusText($status)
    {
        $statuses = [
            'draft' => 'Draft',
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan'
        ];
        return $statuses[$status] ?? $status;
    }
}

// Detail sheet for single stock opname
class StockOpnameDetailSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->with(['asset.assetCategory', 'scanner'])
            ->orderByRaw("CASE
                WHEN scanned_at IS NULL THEN 1
                WHEN found_status = 'not_found' THEN 2
                WHEN has_discrepancy = 1 THEN 3
                ELSE 4
            END")
            ->orderBy('asset_code')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Status Awal',
            'Status Scan',
            'Kondisi Fisik',
            'Lokasi',
            'Catatan Kondisi',
            'Waktu Scan',
            'Scanner',
            'Prioritas',
            'Keterangan'
        ];
    }

    public function map($detail): array
    {
        // Determine priority and status
        $priority = 'Normal';
        $statusScan = 'Belum di-scan';

        if (!$detail->scanned_at) {
            $priority = 'URGENT - Belum di-scan';
            $statusScan = 'BELUM DI-SCAN';
        } elseif ($detail->found_status === 'not_found') {
            $priority = 'HIGH - Asset Hilang';
            $statusScan = 'HILANG';
        } elseif ($detail->has_discrepancy) {
            $priority = 'MEDIUM - Ada Ketidaksesuaian';
            $statusScan = 'ADA MASALAH';
        } else {
            $statusScan = 'SELESAI';
        }

        return [
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            ucfirst($detail->expected_status),
            $statusScan,
            $detail->physical_condition ? ucfirst($detail->physical_condition) : '-',
            $detail->location_found ?: ($detail->asset->location ?? '-'),
            $detail->condition_notes ?: '-',
            $detail->scanned_at ? $detail->scanned_at->format('d/m/Y H:i') : '-',
            $detail->scanner->name ?? '-',
            $priority,
            $detail->getDiscrepancySummary() ?: ($detail->scanned_at ? '-' : 'Perlu dilakukan scanning')
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $styles = [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F5E8']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];

        // Apply conditional formatting based on data
        $data = $this->collection();
        $row = 2; // Start from row 2 (after header)

        foreach ($data as $detail) {
            if (!$detail->scanned_at) {
                // Red background for urgent (not scanned)
                $styles[$row] = [
                    'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFEBEE']],
                    'font' => ['color' => ['rgb' => 'C62828']],
                    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
                ];
            } elseif ($detail->found_status === 'not_found') {
                // Orange background for high priority (missing)
                $styles[$row] = [
                    'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFF3E0']],
                    'font' => ['color' => ['rgb' => 'E65100']],
                    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
                ];
            } elseif ($detail->has_discrepancy) {
                // Yellow background for medium priority (discrepancy)
                $styles[$row] = [
                    'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFFDE7']],
                    'font' => ['color' => ['rgb' => 'F57F17']],
                    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
                ];
            } else {
                // Green background for normal (completed)
                $styles[$row] = [
                    'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F1F8E9']],
                    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
                ];
            }
            $row++;
        }

        return $styles;
    }

    public function title(): string
    {
        return 'Detail Asset';
    }
}

// Discrepancy sheet for single stock opname
class StockOpnameDiscrepancySheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->where('has_discrepancy', true)
            ->with(['asset.assetCategory', 'scanner'])
            ->get();
    }

    public function headings(): array
    {
        return [
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Jenis Ketidaksesuaian',
            'Detail Ketidaksesuaian',
            'Status Awal',
            'Status Ditemukan',
            'Kondisi Fisik',
            'Waktu Scan',
            'Scanner',
            'Catatan'
        ];
    }

    public function map($detail): array
    {
        return [
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            $this->getDiscrepancyType($detail),
            $detail->getDiscrepancySummary() ?: '-',
            ucfirst($detail->expected_status),
            $detail->found_status ? ucfirst($detail->found_status) : '-',
            $detail->physical_condition ? ucfirst($detail->physical_condition) : '-',
            $detail->scanned_at ? $detail->scanned_at->format('d/m/Y H:i') : '-',
            $detail->scanner->name ?? '-',
            $detail->condition_notes ?: '-'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFF3E0']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Ketidaksesuaian';
    }

    private function getDiscrepancyType($detail)
    {
        if (!$detail->has_discrepancy || !$detail->discrepancy_details) {
            return '-';
        }

        $types = [];
        foreach ($detail->discrepancy_details as $type => $details) {
            switch ($type) {
                case 'status':
                    $types[] = 'Status Tidak Sesuai';
                    break;
                case 'missing':
                    $types[] = 'Asset Hilang';
                    break;
                case 'condition':
                    $types[] = 'Kondisi Buruk';
                    break;
                case 'dynamic_fields':
                    $types[] = 'Field Tidak Sesuai';
                    break;
            }
        }
        return implode(', ', $types);
    }
}

// List sheet for multiple stock opnames
class StockOpnameListSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $filters;

    public function __construct($filters)
    {
        $this->filters = $filters;
    }

    public function collection()
    {
        $query = StockOpname::with(['branch', 'creator']);

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = \App\Helpers\BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        // Apply filters
        if (!empty($this->filters['branch_id'])) {
            $query->where('branch_id', $this->filters['branch_id']);
        }

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('created_at', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('created_at', '<=', $this->filters['date_to']);
        }

        if (!empty($this->filters['start_date_from'])) {
            $query->whereDate('start_date', '>=', $this->filters['start_date_from']);
        }

        if (!empty($this->filters['start_date_to'])) {
            $query->whereDate('start_date', '<=', $this->filters['start_date_to']);
        }

        return $query->latest()->get();
    }

    public function headings(): array
    {
        return [
            'Nomor Opname',
            'Judul',
            'Cabang',
            'Status',
            'Dibuat Oleh',
            'Tanggal Dibuat',
            'Tanggal Mulai',
            'Tanggal Selesai',
            'Total Asset',
            'Asset Di-scan',
            'Asset Ditemukan',
            'Asset Hilang',
            'Progress (%)',
            'Durasi (Hari)',
            'Deskripsi'
        ];
    }

    public function map($stockOpname): array
    {
        $duration = '-';
        if ($stockOpname->start_date && $stockOpname->end_date) {
            $duration = $stockOpname->start_date->diffInDays($stockOpname->end_date);
        }

        return [
            $stockOpname->opname_number,
            $stockOpname->title,
            $stockOpname->branch->name,
            $this->getStatusText($stockOpname->status),
            $stockOpname->creator->name,
            $stockOpname->created_at->format('d/m/Y H:i'),
            $stockOpname->start_date ? $stockOpname->start_date->format('d/m/Y H:i') : '-',
            $stockOpname->end_date ? $stockOpname->end_date->format('d/m/Y H:i') : '-',
            $stockOpname->total_assets,
            $stockOpname->scanned_assets,
            $stockOpname->found_assets,
            $stockOpname->missing_assets,
            $stockOpname->total_assets > 0 ? round(($stockOpname->scanned_assets / $stockOpname->total_assets) * 100, 2) : 0,
            $duration,
            $stockOpname->description ?: '-'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E3F2FD']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Daftar Stock Opname';
    }

    private function getStatusText($status)
    {
        $statuses = [
            'draft' => 'Draft',
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan'
        ];
        return $statuses[$status] ?? $status;
    }
}

// Summary statistics sheet
class StockOpnameSummaryStatsSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $filters;

    public function __construct($filters)
    {
        $this->filters = $filters;
    }

    public function collection()
    {
        $query = StockOpname::query();

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = \App\Helpers\BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        // Apply filters (same as StockOpnameListSheet)
        if (!empty($this->filters['branch_id'])) {
            $query->where('branch_id', $this->filters['branch_id']);
        }

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('created_at', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('created_at', '<=', $this->filters['date_to']);
        }

        $stockOpnames = $query->get();

        // Calculate summary statistics
        $summary = [
            'Total Stock Opname' => $stockOpnames->count(),
            'Stock Opname Selesai' => $stockOpnames->where('status', 'completed')->count(),
            'Stock Opname Berjalan' => $stockOpnames->where('status', 'in_progress')->count(),
            'Stock Opname Dibatalkan' => $stockOpnames->where('status', 'cancelled')->count(),
            'Total Asset Diaudit' => $stockOpnames->sum('total_assets'),
            'Total Asset Di-scan' => $stockOpnames->sum('scanned_assets'),
            'Total Asset Ditemukan' => $stockOpnames->sum('found_assets'),
            'Total Asset Hilang' => $stockOpnames->sum('missing_assets'),
            'Tingkat Penyelesaian (%)' => $stockOpnames->count() > 0 ?
                round(($stockOpnames->where('status', 'completed')->count() / $stockOpnames->count()) * 100, 2) : 0,
            'Tingkat Scanning (%)' => $stockOpnames->sum('total_assets') > 0 ?
                round(($stockOpnames->sum('scanned_assets') / $stockOpnames->sum('total_assets')) * 100, 2) : 0,
        ];

        return collect($summary)->map(function ($value, $key) {
            return (object) ['metric' => $key, 'value' => $value];
        });
    }

    public function headings(): array
    {
        return [
            'Metrik',
            'Nilai'
        ];
    }

    public function map($item): array
    {
        return [
            $item->metric,
            $item->value
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F3E5F5']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Ringkasan Statistik';
    }
}

// Not Scanned Assets Sheet
class StockOpnameNotScannedSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->whereNull('scanned_at')
            ->with(['asset.assetCategory', 'asset.assetType'])
            ->orderBy('asset_code')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Tipe Asset',
            'Status Awal',
            'Lokasi Terakhir',
            'Kondisi Terakhir',
            'Tanggal Dibuat',
            'Keterangan'
        ];
    }

    public function map($detail): array
    {
        return [
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            $detail->asset->assetType->name ?? '-',
            ucfirst($detail->expected_status),
            $detail->asset->location ?? '-',
            $detail->asset->condition ?? '-',
            $detail->asset->created_at ? $detail->asset->created_at->format('d/m/Y') : '-',
            'Belum di-scan - Perlu dilakukan scanning'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFF3CD']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Asset Belum Di-scan';
    }
}

// Found Assets Sheet
class StockOpnameFoundSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->where('found_status', 'found')
            ->with(['asset.assetCategory', 'scanner'])
            ->orderBy('scanned_at', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Kondisi Fisik',
            'Lokasi Ditemukan',
            'Waktu Scan',
            'Scanner',
            'Catatan Kondisi',
            'Status'
        ];
    }

    public function map($detail): array
    {
        return [
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            ucfirst($detail->physical_condition),
            $detail->location_found ?: '-',
            $detail->scanned_at->format('d/m/Y H:i'),
            $detail->scanner->name ?? '-',
            $detail->condition_notes ?: '-',
            'Ditemukan'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'D4EDDA']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Asset Ditemukan';
    }
}

// Missing Assets Sheet
class StockOpnameMissingSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->where('found_status', 'not_found')
            ->with(['asset.assetCategory', 'scanner'])
            ->orderBy('scanned_at', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Status Terakhir',
            'Lokasi Terakhir',
            'Waktu Scan',
            'Scanner',
            'Catatan',
            'Tindak Lanjut'
        ];
    }

    public function map($detail): array
    {
        return [
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            ucfirst($detail->expected_status),
            $detail->asset->location ?? '-',
            $detail->scanned_at ? $detail->scanned_at->format('d/m/Y H:i') : '-',
            $detail->scanner->name ?? '-',
            $detail->condition_notes ?: 'Asset tidak ditemukan saat stock opname',
            'Perlu investigasi dan pencarian asset'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F8D7DA']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Asset Hilang';
    }
}

// All Assets Sheet - Complete overview
class StockOpnameAllAssetsSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->with(['asset.assetCategory', 'asset.assetType', 'asset.branch', 'scanner'])
            ->orderByRaw("CASE
                WHEN scanned_at IS NULL THEN 1
                WHEN found_status = 'not_found' THEN 2
                WHEN found_status = 'damaged' THEN 3
                WHEN has_discrepancy = 1 THEN 4
                ELSE 5
            END")
            ->orderBy('asset_code')
            ->get();
    }

    public function headings(): array
    {
        return [
            'No',
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Tipe Asset',
            'Cabang',
            'Status Awal',
            'Status Scan',
            'Status Ditemukan',
            'Kondisi Fisik',
            'Lokasi Awal',
            'Lokasi Ditemukan',
            'Tanggal Scan',
            'Waktu Scan',
            'Scanner',
            'Catatan Kondisi',
            'Ketidaksesuaian',
            'Detail Ketidaksesuaian',
            'Prioritas',
            'Tindak Lanjut',
            'Nilai Asset',
            'Umur Asset (Bulan)'
        ];
    }

    public function map($detail): array
    {
        static $no = 0;
        $no++;

        // Determine status and priority
        $statusScan = 'Belum di-scan';
        $priority = 'URGENT';
        $tindakLanjut = 'Segera lakukan scanning';

        if ($detail->scanned_at) {
            $statusScan = 'Sudah di-scan';
            if ($detail->found_status === 'not_found') {
                $priority = 'HIGH';
                $tindakLanjut = 'Investigasi dan pencarian asset';
            } elseif ($detail->found_status === 'damaged') {
                $priority = 'HIGH';
                $tindakLanjut = 'Evaluasi perbaikan atau penghapusan';
            } elseif ($detail->has_discrepancy) {
                $priority = 'MEDIUM';
                $tindakLanjut = 'Review dan verifikasi data';
            } else {
                $priority = 'NORMAL';
                $tindakLanjut = 'Tidak ada tindakan diperlukan';
            }
        }

        // Calculate asset age
        $assetAge = $detail->asset->created_at ?
            $detail->asset->created_at->diffInMonths(now()) : 0;

        return [
            $no,
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            $detail->asset->assetType->name ?? '-',
            $detail->asset->branch->name ?? '-',
            ucfirst($detail->expected_status),
            $statusScan,
            $detail->found_status ? ucfirst($detail->found_status) : '-',
            $detail->physical_condition ? ucfirst($detail->physical_condition) : '-',
            $detail->asset->location ?? '-',
            $detail->location_found ?: '-',
            $detail->scanned_at ? $detail->scanned_at->format('d/m/Y') : '-',
            $detail->scanned_at ? $detail->scanned_at->format('H:i:s') : '-',
            $detail->scanner->name ?? '-',
            $detail->condition_notes ?: '-',
            $detail->has_discrepancy ? 'Ya' : 'Tidak',
            $detail->getDiscrepancySummary() ?: '-',
            $priority,
            $tindakLanjut,
            $detail->asset->purchase_price ?? '-',
            $assetAge
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $styles = [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E3F2FD']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];

        // Apply conditional formatting
        $rowCount = $this->collection()->count();
        for ($row = 2; $row <= $rowCount + 1; $row++) {
            $styles[$row] = [
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ];
        }

        return $styles;
    }

    public function title(): string
    {
        return 'Semua Asset';
    }
}

// Scanned Assets Sheet
class StockOpnameScannedSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->whereNotNull('scanned_at')
            ->with(['asset.assetCategory', 'asset.assetType', 'scanner'])
            ->orderBy('scanned_at', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            'No',
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Tipe Asset',
            'Status Ditemukan',
            'Kondisi Fisik',
            'Lokasi Awal',
            'Lokasi Ditemukan',
            'Tanggal Scan',
            'Jam Scan',
            'Scanner',
            'Durasi Scan (menit)',
            'Catatan Kondisi',
            'Ada Ketidaksesuaian',
            'Detail Ketidaksesuaian',
            'Rekomendasi'
        ];
    }

    public function map($detail): array
    {
        static $no = 0;
        $no++;

        // Calculate scan duration (from stock opname start to scan time)
        $scanDuration = $this->stockOpname->start_date && $detail->scanned_at ?
            $this->stockOpname->start_date->diffInMinutes($detail->scanned_at) : 0;

        // Determine recommendation
        $rekomendasi = 'Asset dalam kondisi baik';
        if ($detail->found_status === 'not_found') {
            $rekomendasi = 'Perlu investigasi lebih lanjut';
        } elseif ($detail->found_status === 'damaged') {
            $rekomendasi = 'Perlu evaluasi perbaikan atau penghapusan';
        } elseif ($detail->has_discrepancy) {
            $rekomendasi = 'Perlu verifikasi dan update data';
        } elseif (in_array($detail->physical_condition, ['poor', 'damaged'])) {
            $rekomendasi = 'Perlu maintenance atau penggantian';
        }

        return [
            $no,
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            $detail->asset->assetType->name ?? '-',
            ucfirst($detail->found_status),
            ucfirst($detail->physical_condition),
            $detail->asset->location ?? '-',
            $detail->location_found ?: '-',
            $detail->scanned_at->format('d/m/Y'),
            $detail->scanned_at->format('H:i:s'),
            $detail->scanner->name ?? '-',
            $scanDuration,
            $detail->condition_notes ?: '-',
            $detail->has_discrepancy ? 'Ya' : 'Tidak',
            $detail->getDiscrepancySummary() ?: '-',
            $rekomendasi
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F5E8']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Asset Sudah Di-scan';
    }
}

// Damaged Assets Sheet
class StockOpnameDamagedSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->where(function($query) {
                $query->where('found_status', 'damaged')
                      ->orWhere('physical_condition', 'damaged')
                      ->orWhere('physical_condition', 'poor');
            })
            ->with(['asset.assetCategory', 'asset.assetType', 'scanner'])
            ->orderBy('scanned_at', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            'No',
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Tipe Asset',
            'Jenis Kerusakan',
            'Tingkat Kerusakan',
            'Kondisi Fisik',
            'Lokasi Ditemukan',
            'Tanggal Scan',
            'Scanner',
            'Deskripsi Kerusakan',
            'Estimasi Perbaikan',
            'Rekomendasi Tindakan',
            'Prioritas Perbaikan',
            'Nilai Asset',
            'Biaya Estimasi Perbaikan'
        ];
    }

    public function map($detail): array
    {
        static $no = 0;
        $no++;

        // Determine damage type and level
        $jenisKerusakan = 'Kerusakan Fisik';
        $tingkatKerusakan = 'Ringan';
        $estimasiPerbaikan = 'Dapat diperbaiki';
        $rekomendasiTindakan = 'Perbaikan';
        $prioritasPerbaikan = 'Medium';

        if ($detail->found_status === 'damaged') {
            $jenisKerusakan = 'Kerusakan Total';
            $tingkatKerusakan = 'Berat';
            $estimasiPerbaikan = 'Tidak dapat diperbaiki';
            $rekomendasiTindakan = 'Penghapusan asset';
            $prioritasPerbaikan = 'High';
        } elseif ($detail->physical_condition === 'damaged') {
            $tingkatKerusakan = 'Berat';
            $prioritasPerbaikan = 'High';
        } elseif ($detail->physical_condition === 'poor') {
            $tingkatKerusakan = 'Sedang';
            $prioritasPerbaikan = 'Medium';
        }

        // Estimate repair cost (example calculation)
        $assetValue = $detail->asset->purchase_price ?? 0;
        $estimasiBiayaPerbaikan = '-';
        if ($assetValue > 0) {
            if ($tingkatKerusakan === 'Ringan') {
                $estimasiBiayaPerbaikan = number_format($assetValue * 0.1, 0, ',', '.');
            } elseif ($tingkatKerusakan === 'Sedang') {
                $estimasiBiayaPerbaikan = number_format($assetValue * 0.3, 0, ',', '.');
            } elseif ($tingkatKerusakan === 'Berat') {
                $estimasiBiayaPerbaikan = number_format($assetValue * 0.7, 0, ',', '.');
            }
        }

        return [
            $no,
            $detail->asset_code,
            $detail->asset_name,
            $detail->asset->assetCategory->name ?? '-',
            $detail->asset->assetType->name ?? '-',
            $jenisKerusakan,
            $tingkatKerusakan,
            ucfirst($detail->physical_condition),
            $detail->location_found ?: '-',
            $detail->scanned_at ? $detail->scanned_at->format('d/m/Y') : '-',
            $detail->scanner->name ?? '-',
            $detail->condition_notes ?: 'Tidak ada deskripsi kerusakan',
            $estimasiPerbaikan,
            $rekomendasiTindakan,
            $prioritasPerbaikan,
            $assetValue ? number_format($assetValue, 0, ',', '.') : '-',
            $estimasiBiayaPerbaikan
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FFEBEE']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Asset Rusak';
    }
}

// Condition Analysis Sheet
class StockOpnameConditionAnalysisSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        // Get condition summary data
        $conditions = ['excellent', 'good', 'fair', 'poor', 'damaged'];
        $data = collect();

        foreach ($conditions as $condition) {
            $count = StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
                ->where('physical_condition', $condition)
                ->count();

            $assets = StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
                ->where('physical_condition', $condition)
                ->with(['asset.assetCategory'])
                ->get();

            $totalValue = $assets->sum(function($detail) {
                return $detail->asset->purchase_price ?? 0;
            });

            $data->push((object)[
                'condition' => $condition,
                'count' => $count,
                'percentage' => $this->stockOpname->scanned_assets > 0 ?
                    round(($count / $this->stockOpname->scanned_assets) * 100, 2) : 0,
                'total_value' => $totalValue,
                'avg_value' => $count > 0 ? $totalValue / $count : 0,
                'categories' => $assets->groupBy('asset.assetCategory.name')->map->count()
            ]);
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Kondisi Fisik',
            'Jumlah Asset',
            'Persentase (%)',
            'Total Nilai (Rp)',
            'Rata-rata Nilai (Rp)',
            'Kategori Terbanyak',
            'Jumlah Kategori',
            'Status Kondisi',
            'Rekomendasi',
            'Prioritas Tindakan'
        ];
    }

    public function map($item): array
    {
        $statusKondisi = 'Baik';
        $rekomendasi = 'Maintenance rutin';
        $prioritas = 'Low';

        switch ($item->condition) {
            case 'excellent':
                $statusKondisi = 'Sangat Baik';
                $rekomendasi = 'Pertahankan kondisi';
                $prioritas = 'Low';
                break;
            case 'good':
                $statusKondisi = 'Baik';
                $rekomendasi = 'Maintenance rutin';
                $prioritas = 'Low';
                break;
            case 'fair':
                $statusKondisi = 'Cukup';
                $rekomendasi = 'Maintenance preventif';
                $prioritas = 'Medium';
                break;
            case 'poor':
                $statusKondisi = 'Buruk';
                $rekomendasi = 'Perbaikan segera';
                $prioritas = 'High';
                break;
            case 'damaged':
                $statusKondisi = 'Rusak';
                $rekomendasi = 'Penggantian atau penghapusan';
                $prioritas = 'Critical';
                break;
        }

        $topCategory = $item->categories->sortDesc()->keys()->first() ?? '-';

        return [
            ucfirst($item->condition),
            $item->count,
            $item->percentage,
            number_format($item->total_value, 0, ',', '.'),
            number_format($item->avg_value, 0, ',', '.'),
            $topCategory,
            $item->categories->count(),
            $statusKondisi,
            $rekomendasi,
            $prioritas
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F3E5F5']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Analisis Kondisi';
    }
}

// Category Analysis Sheet
class StockOpnameCategoryAnalysisSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $stockOpname;

    public function __construct($stockOpname)
    {
        $this->stockOpname = $stockOpname;
    }

    public function collection()
    {
        return StockOpnameDetail::where('stock_opname_id', $this->stockOpname->id)
            ->with(['asset.assetCategory'])
            ->get()
            ->groupBy('asset.assetCategory.name')
            ->map(function ($items, $categoryName) {
                $total = $items->count();
                $scanned = $items->whereNotNull('scanned_at')->count();
                $notScanned = $total - $scanned;
                $found = $items->where('found_status', 'found')->count();
                $missing = $items->where('found_status', 'not_found')->count();
                $damaged = $items->where('found_status', 'damaged')->count();
                $discrepancy = $items->where('has_discrepancy', true)->count();

                $totalValue = $items->sum(function($detail) {
                    return $detail->asset->purchase_price ?? 0;
                });

                return (object)[
                    'category' => $categoryName ?: 'Tidak Berkategori',
                    'total' => $total,
                    'scanned' => $scanned,
                    'not_scanned' => $notScanned,
                    'found' => $found,
                    'missing' => $missing,
                    'damaged' => $damaged,
                    'discrepancy' => $discrepancy,
                    'scan_percentage' => $total > 0 ? round(($scanned / $total) * 100, 2) : 0,
                    'found_percentage' => $scanned > 0 ? round(($found / $scanned) * 100, 2) : 0,
                    'total_value' => $totalValue,
                    'avg_value' => $total > 0 ? $totalValue / $total : 0
                ];
            })
            ->sortByDesc('total')
            ->values();
    }

    public function headings(): array
    {
        return [
            'Kategori Asset',
            'Total Asset',
            'Sudah Di-scan',
            'Belum Di-scan',
            'Ditemukan',
            'Hilang',
            'Rusak',
            'Ketidaksesuaian',
            'Progress Scan (%)',
            'Tingkat Ditemukan (%)',
            'Total Nilai (Rp)',
            'Rata-rata Nilai (Rp)',
            'Status Kategori',
            'Prioritas',
            'Rekomendasi'
        ];
    }

    public function map($item): array
    {
        // Determine category status and priority
        $statusKategori = 'Normal';
        $prioritas = 'Low';
        $rekomendasi = 'Maintenance rutin';

        if ($item->not_scanned > 0) {
            $statusKategori = 'Perlu Scanning';
            $prioritas = 'High';
            $rekomendasi = 'Segera lakukan scanning asset yang belum di-scan';
        } elseif ($item->missing > 0) {
            $statusKategori = 'Ada Asset Hilang';
            $prioritas = 'Critical';
            $rekomendasi = 'Investigasi dan pencarian asset hilang';
        } elseif ($item->damaged > 0) {
            $statusKategori = 'Ada Asset Rusak';
            $prioritas = 'High';
            $rekomendasi = 'Evaluasi perbaikan atau penggantian asset rusak';
        } elseif ($item->discrepancy > 0) {
            $statusKategori = 'Ada Ketidaksesuaian';
            $prioritas = 'Medium';
            $rekomendasi = 'Review dan update data asset';
        }

        return [
            $item->category,
            $item->total,
            $item->scanned,
            $item->not_scanned,
            $item->found,
            $item->missing,
            $item->damaged,
            $item->discrepancy,
            $item->scan_percentage,
            $item->found_percentage,
            number_format($item->total_value, 0, ',', '.'),
            number_format($item->avg_value, 0, ',', '.'),
            $statusKategori,
            $prioritas,
            $rekomendasi
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true, 'size' => 11],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E1F5FE']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]
        ];
    }

    public function title(): string
    {
        return 'Analisis per Kategori';
    }
}
