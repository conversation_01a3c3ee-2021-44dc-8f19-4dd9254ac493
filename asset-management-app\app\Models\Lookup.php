<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lookup extends Model
{
    use HasFactory;

    protected $fillable = [
        'lookup_code',
        'lookup_name',
        'description',
        'category',
        'sort_order',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('lookup_code', $code);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('lookup_name');
    }

    // Static methods for easy lookup
    public static function getByCode($code, $active = true)
    {
        $query = self::where('lookup_code', $code);

        if ($active) {
            $query->where('is_active', true);
        }

        return $query->ordered()->get();
    }

    public static function getByCategory($category, $active = true)
    {
        $query = self::where('category', $category);

        if ($active) {
            $query->where('is_active', true);
        }

        return $query->ordered()->get();
    }

    public static function getCategories()
    {
        return self::whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->sort()
            ->values();
    }

    public static function getCodes()
    {
        return self::distinct()
            ->pluck('lookup_code')
            ->sort()
            ->values();
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->lookup_name . ($this->description ? ' - ' . $this->description : '');
    }

    public function getMetadataValue($key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    public function setMetadataValue($key, $value)
    {
        $metadata = $this->metadata ?? [];
        $metadata[$key] = $value;
        $this->metadata = $metadata;
        return $this;
    }
}
