@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Role - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Role /</span> Detail Role
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Detail Role: {{ $role->name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.roles.edit', $role) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.roles.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Role</label>
                <p class="fw-bold">{{ $role->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Slug</label>
                <p><span class="badge bg-info fs-6">{{ $role->slug }}</span></p>
              </div>
            </div>
          </div>

          @if($role->description)
          <div class="mb-3">
            <label class="form-label text-muted">Deskripsi</label>
            <p>{{ $role->description }}</p>
          </div>
          @endif

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $role->is_active ? 'success' : 'secondary' }} fs-6">
                    {{ $role->is_active ? 'Aktif' : 'Non-Aktif' }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Jumlah User</label>
                <p><span class="badge bg-secondary fs-6">{{ $role->users->count() }} user</span></p>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Dibuat</label>
                <p>{{ $role->created_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Terakhir Diupdate</label>
                <p>{{ $role->updated_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Permissions Card -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">Permissions ({{ $role->permissions->count() }} permission)</h6>
        </div>
        <div class="card-body">
          @if($permissionsByModule->count() > 0)
            @foreach($permissionsByModule as $module => $permissions)
            <div class="mb-4">
              <h6 class="text-primary mb-3">
                <i class="ri-folder-line me-1"></i>{{ ucfirst($module) }}
              </h6>
              <div class="row">
                @foreach($permissions as $permission)
                <div class="col-md-6 mb-2">
                  <div class="d-flex align-items-center">
                    <i class="ri-check-line text-success me-2"></i>
                    <span>{{ $permission->name }}</span>
                  </div>
                </div>
                @endforeach
              </div>
            </div>
            @endforeach
          @else
            <div class="text-center py-4">
              <i class="ri-shield-line display-4 text-muted mb-2"></i>
              <p class="text-muted">Tidak ada permission yang diberikan untuk role ini.</p>
            </div>
          @endif
        </div>
      </div>

      <!-- Users Card -->
      @if($role->users->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">User dengan Role Ini ({{ $role->users->count() }} user)</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Nama</th>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Cabang</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                @foreach($role->users as $user)
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-xs me-2">
                        <span class="avatar-initial rounded-circle bg-label-primary">
                          {{ strtoupper(substr($user->name, 0, 2)) }}
                        </span>
                      </div>
                      {{ $user->name }}
                    </div>
                  </td>
                  <td>{{ $user->username }}</td>
                  <td>{{ $user->email }}</td>
                  <td>{{ $user->branch->name }}</td>
                  <td>
                    <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }} badge-sm">
                      {{ $user->is_active ? 'Aktif' : 'Non-Aktif' }}
                    </span>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
      @endif
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Role</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-{{ $role->slug === 'super-admin' ? 'danger' : ($role->slug === 'admin' ? 'warning' : 'primary') }}">
                <i class="ri-shield-user-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $role->name }}</h6>
              <small class="text-muted">{{ $role->slug }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>{{ $role->permissions->count() }} Permission</li>
              <li>{{ $role->users->count() }} User Aktif</li>
              <li>Dibuat {{ $role->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.roles.edit', $role) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Role
            </a>
            
            @if($role->is_active)
              <button class="btn btn-warning" onclick="toggleStatus(false)">
                <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
              </button>
            @else
              <button class="btn btn-success" onclick="toggleStatus(true)">
                <i class="ri-play-circle-line me-1"></i>Aktifkan
              </button>
            @endif
            
            @if($role->slug !== 'super-admin' && $role->users->count() == 0)
            <form action="{{ route('master.roles.destroy', $role) }}" method="POST" id="deleteForm">
              @csrf
              @method('DELETE')
              <button type="button" class="btn btn-danger w-100" onclick="confirmDelete()">
                <i class="ri-delete-bin-line me-1"></i>Hapus Role
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStatus(status) {
  if (confirm('Yakin ingin mengubah status role ini?')) {
    // Create form to toggle status
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("master.roles.update", $role) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'is_active';
    statusField.value = status ? '1' : '0';
    
    // Copy other fields
    const nameField = document.createElement('input');
    nameField.type = 'hidden';
    nameField.name = 'name';
    nameField.value = '{{ $role->name }}';
    
    const slugField = document.createElement('input');
    slugField.type = 'hidden';
    slugField.name = 'slug';
    slugField.value = '{{ $role->slug }}';
    
    const descField = document.createElement('input');
    descField.type = 'hidden';
    descField.name = 'description';
    descField.value = '{{ $role->description }}';
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    form.appendChild(statusField);
    form.appendChild(nameField);
    form.appendChild(slugField);
    form.appendChild(descField);
    
    document.body.appendChild(form);
    form.submit();
  }
}

function confirmDelete() {
  if (confirm('Yakin ingin menghapus role ini?\n\nTindakan ini tidak dapat dibatalkan!')) {
    document.getElementById('deleteForm').submit();
  }
}
</script>
@endsection
