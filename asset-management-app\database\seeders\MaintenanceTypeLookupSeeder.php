<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MaintenanceTypeLookupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Delete existing MTC_TYPE data to avoid duplicates
        \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')->delete();

        // Create maintenance types that match database ENUM values
        $maintenanceTypes = [
            [
                'lookup_code' => 'MTC_TYPE',
                'lookup_name' => 'preventive',
                'description' => 'Maintenance Preventif - Perawatan rutin untuk mencegah kerusakan',
                'category' => 'Maintenance',
                'sort_order' => 1,
                'is_active' => true,
                'metadata' => ['color' => 'success', 'icon' => 'ri-shield-check-line']
            ],
            [
                'lookup_code' => 'MTC_TYPE',
                'lookup_name' => 'corrective',
                'description' => 'Maintenance Korektif - Perbaikan setelah terjadi kerusakan',
                'category' => 'Maintenance',
                'sort_order' => 2,
                'is_active' => true,
                'metadata' => ['color' => 'warning', 'icon' => 'ri-tools-line']
            ],
            [
                'lookup_code' => 'MTC_TYPE',
                'lookup_name' => 'emergency',
                'description' => 'Maintenance Darurat - Perbaikan mendesak untuk kerusakan kritis',
                'category' => 'Maintenance',
                'sort_order' => 3,
                'is_active' => true,
                'metadata' => ['color' => 'danger', 'icon' => 'ri-alarm-warning-line']
            ],
            [
                'lookup_code' => 'MTC_TYPE',
                'lookup_name' => 'upgrade',
                'description' => 'Upgrade/Peningkatan - Peningkatan kapasitas atau fitur asset',
                'category' => 'Maintenance',
                'sort_order' => 4,
                'is_active' => true,
                'metadata' => ['color' => 'info', 'icon' => 'ri-arrow-up-circle-line']
            ],
        ];

        foreach ($maintenanceTypes as $type) {
            \App\Models\Lookup::create($type);
        }
    }
}
