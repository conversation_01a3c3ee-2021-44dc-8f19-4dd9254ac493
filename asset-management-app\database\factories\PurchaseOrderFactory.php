<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\PurchaseOrder;
use App\Models\Supplier;
use App\Models\Branch;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PurchaseOrder>
 */
class PurchaseOrderFactory extends Factory
{
    protected $model = PurchaseOrder::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'po_number' => 'PO-' . $this->faker->unique()->numerify('####'),
            'po_date' => $this->faker->date(),
            'delivery_date' => $this->faker->dateTimeBetween('+1 week', '+1 month'),
            'supplier_id' => Supplier::factory(),
            'branch_id' => Branch::factory(),
            'created_by' => User::factory(),
            'status' => $this->faker->randomElement(['draft', 'submitted', 'approved', 'rejected', 'sent', 'received', 'completed', 'cancelled']),
            'subtotal_amount' => $this->faker->numberBetween(100000, 10000000),
            'tax_percentage' => $this->faker->numberBetween(0, 15),
            'tax_amount' => $this->faker->numberBetween(0, 1000000),
            'discount_percentage' => $this->faker->numberBetween(0, 10),
            'discount_amount' => $this->faker->numberBetween(0, 500000),
            'total_amount' => $this->faker->numberBetween(100000, 10000000),
            'notes' => $this->faker->optional()->sentence(),
            'terms_conditions' => $this->faker->optional()->paragraph(),
            'payment_terms' => $this->faker->optional()->sentence(),
            'delivery_address' => $this->faker->optional()->address(),
            'approval_notes' => $this->faker->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the purchase order is in draft status.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'approved_by' => null,
            'approved_at' => null,
            'approval_notes' => null,
        ]);
    }

    /**
     * Indicate that the purchase order is submitted.
     */
    public function submitted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'submitted',
            'approved_by' => null,
            'approved_at' => null,
            'approval_notes' => null,
        ]);
    }

    /**
     * Indicate that the purchase order is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'approved_by' => User::factory(),
            'approved_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'approval_notes' => $this->faker->optional()->sentence(),
        ]);
    }

    /**
     * Indicate that the purchase order is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'approved_by' => null,
            'approved_at' => null,
            'approval_notes' => $this->faker->sentence(),
        ]);
    }

    /**
     * Configure the model factory after creating.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (PurchaseOrder $purchaseOrder) {
            // Calculate totals after creation
            $purchaseOrder->calculateTotals();
        });
    }
}
