<?php

namespace App\Services;

use App\Models\ApprovalWorkflow;
use App\Models\ApprovalHistory;
use App\Models\RequestAsset;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApprovalService
{
    /**
     * Start approval process for asset request
     */
    public function startApprovalProcess(RequestAsset $requestAsset)
    {
        try {
            DB::beginTransaction();

            // Find applicable workflow
            $workflow = $this->findApplicableWorkflow($requestAsset);
            
            if (!$workflow) {
                throw new \Exception('No applicable approval workflow found');
            }

            // Create approval histories for all levels
            foreach ($workflow->levels as $level) {
                ApprovalHistory::create([
                    'approval_workflow_id' => $workflow->id,
                    'approval_level_id' => $level->id,
                    'approvable_type' => RequestAsset::class,
                    'approvable_id' => $requestAsset->id,
                    'approver_id' => null,
                    'status' => $level->level_order === 1 ? 'pending' : 'waiting',
                    'notes' => null,
                    'approved_at' => null,
                    'timeout_at' => $level->timeout_hours ? now()->addHours($level->timeout_hours) : null,
                    'metadata' => [
                        'level_order' => $level->level_order,
                        'level_name' => $level->level_name,
                        'approver_type' => $level->approver_type,
                        'approver_config' => $level->approver_config,
                    ],
                ]);
            }

            // Update request status
            $requestAsset->update(['status' => 'submitted']);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error starting approval process: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Process approval action
     */
    public function processApproval(RequestAsset $requestAsset, User $approver, string $action, string $notes = null)
    {
        try {
            DB::beginTransaction();

            // Get current pending approval
            $currentApproval = $requestAsset->currentApprovalHistory;
            
            if (!$currentApproval) {
                throw new \Exception('No pending approval found');
            }

            // Check if user can approve this level
            if (!$this->canUserApprove($currentApproval, $approver)) {
                throw new \Exception('User not authorized to approve this level');
            }

            // Update current approval
            $currentApproval->update([
                'approver_id' => $approver->id,
                'status' => $action,
                'notes' => $notes,
                'approved_at' => now(),
            ]);

            if ($action === 'approved') {
                // Move to next level or complete
                $this->moveToNextLevel($requestAsset);
            } else {
                // Reject - update request status
                $requestAsset->update([
                    'status' => 'rejected',
                    'approved_by' => $approver->id,
                    'approved_at' => now(),
                    'approval_notes' => $notes,
                ]);
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error processing approval: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'approver_id' => $approver->id,
                'action' => $action,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Find applicable workflow for request
     */
    private function findApplicableWorkflow(RequestAsset $requestAsset)
    {
        // For asset requests, always use the workflow regardless of conditions
        return ApprovalWorkflow::where('module', 'asset_requests')
            ->where('is_active', true)
            ->orderBy('priority', 'desc')
            ->first();
    }

    /**
     * Check if user can approve current level
     */
    private function canUserApprove(ApprovalHistory $approval, User $user)
    {
        // Super administrator can approve any level
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // For manual approval type, allow any user with role
        if (($approval->metadata['approver_type'] ?? '') === 'manual') {
            return $user->role !== null;
        }

        $config = $approval->metadata['approver_config'] ?? [];
        $type = $approval->metadata['approver_type'] ?? 'role';

        switch ($type) {
            case 'role':
                $allowedRoles = $config['roles'] ?? [];
                if (!$user->role) {
                    return false;
                }
                // Check both by name and slug for compatibility
                return in_array($user->role->name, $allowedRoles) ||
                       in_array($user->role->slug, $allowedRoles);

            case 'user':
                $allowedUsers = $config['users'] ?? [];
                return in_array($user->id, $allowedUsers);

            case 'position':
                $allowedPositions = $config['positions'] ?? [];
                return in_array($user->position, $allowedPositions);

            default:
                return false;
        }
    }

    /**
     * Move to next approval level
     */
    private function moveToNextLevel(RequestAsset $requestAsset)
    {
        // Get next pending approval
        $nextApproval = $requestAsset->approvalHistories()
            ->where('status', 'waiting')
            ->orderBy('id')
            ->first();

        if ($nextApproval) {
            // Move to next level
            $nextApproval->update(['status' => 'pending']);
            $requestAsset->update(['status' => 'reviewed']);
        } else {
            // All levels approved - complete the request
            $requestAsset->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => now(),
            ]);
        }
    }

    /**
     * Get approval timeline for request
     */
    public function getApprovalTimeline(RequestAsset $requestAsset)
    {
        $histories = $requestAsset->approvalHistories()
            ->with(['approver'])
            ->orderBy('created_at')
            ->get();

        return $histories->map(function ($history) {
            return [
                'level_name' => $history->metadata['level_name'] ?? 'Unknown Level',
                'level_order' => $history->metadata['level_order'] ?? 0,
                'status' => $history->status,
                'approver' => $history->approver,
                'notes' => $history->notes,
                'approved_at' => $history->approved_at ? \Carbon\Carbon::parse($history->approved_at) : null,
                'timeout_at' => $history->timeout_at ? \Carbon\Carbon::parse($history->timeout_at) : null,
                'created_at' => $history->created_at ? \Carbon\Carbon::parse($history->created_at) : null,
            ];
        });
    }

    /**
     * Check if user can approve current request
     */
    public function canUserApproveRequest(RequestAsset $requestAsset, User $user)
    {
        // Only allow approval for submitted/reviewed status
        if (!in_array($requestAsset->status, ['submitted', 'reviewed'])) {
            return false;
        }

        // Super admin can always approve
        if ($user->hasRole('super-admin')) {
            return true;
        }

        // For asset requests, allow users with specific roles to approve
        $allowedRoles = ['supervisor', 'manager', 'it_admin', 'hrd_admin', 'finance_admin', 'finance_manager'];
        if ($user->role && (in_array($user->role->slug, $allowedRoles) || in_array($user->role->name, $allowedRoles))) {
            return true;
        }

        $currentApproval = $requestAsset->currentApprovalHistory;

        if (!$currentApproval) {
            // If no approval history exists, allow any user with role to approve
            return $user->role !== null;
        }

        return $this->canUserApprove($currentApproval, $user);
    }

    /**
     * Manual approval for requests without workflow
     */
    public function manualApproval(RequestAsset $requestAsset, User $approver, string $action, string $notes = null)
    {
        try {
            DB::beginTransaction();

            // Log the action for debugging
            Log::info('Manual approval started', [
                'request_id' => $requestAsset->id,
                'current_status' => $requestAsset->status,
                'action' => $action,
                'approver_id' => $approver->id,
                'notes' => $notes
            ]);

            // Update request status directly
            if ($action === 'approved') {
                $updated = $requestAsset->update([
                    'status' => 'approved',
                    'approved_by' => $approver->id,
                    'approved_at' => now(),
                    'approval_notes' => $notes,
                ]);

                Log::info('Request updated to approved', [
                    'request_id' => $requestAsset->id,
                    'update_result' => $updated,
                    'new_status' => $requestAsset->fresh()->status
                ]);
            } else {
                $updated = $requestAsset->update([
                    'status' => 'rejected',
                    'approved_by' => $approver->id,
                    'approved_at' => now(),
                    'approval_notes' => $notes,
                ]);

                Log::info('Request updated to rejected', [
                    'request_id' => $requestAsset->id,
                    'update_result' => $updated,
                    'new_status' => $requestAsset->fresh()->status
                ]);
            }

            // Create approval history for tracking
            $history = ApprovalHistory::create([
                'approval_workflow_id' => null,
                'approval_level_id' => null,
                'approvable_type' => RequestAsset::class,
                'approvable_id' => $requestAsset->id,
                'approver_id' => $approver->id,
                'status' => $action,
                'notes' => $notes,
                'approved_at' => now(),
                'timeout_at' => null,
                'metadata' => [
                    'level_order' => 1,
                    'level_name' => 'Manual Approval',
                    'approver_type' => 'manual',
                    'approver_config' => [],
                ],
            ]);

            Log::info('Approval history created', [
                'history_id' => $history->id,
                'status' => $history->status
            ]);

            DB::commit();

            Log::info('Manual approval completed successfully', [
                'request_id' => $requestAsset->id,
                'final_status' => $requestAsset->fresh()->status
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error in manual approval: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'approver_id' => $approver->id,
                'action' => $action,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
