<?php $__env->startSection('title', 'Master <PERSON> - Asset Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-team-line me-2"></i>
                Master <PERSON>
              </h5>
              <small class="text-muted">Kelola data karyawan untuk assignment asset</small>
            </div>
            <div>
              <?php if(session('user_permissions') && in_array('employees.import', session('user_permissions'))): ?>
              <a href="<?php echo e(route('master.employees.import')); ?>" class="btn btn-success me-2">
                <i class="ri-upload-2-line me-1"></i>Import Excel
              </a>
              <?php endif; ?>
              <a href="<?php echo e(route('master.employees.create')); ?>" class="btn btn-primary">
                <i class="ri-add-line me-2"></i>
                Tambah Karyawan
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" action="<?php echo e(route('master.employees.index')); ?>" id="filterForm">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Pencarian</label>
                <input type="text" class="form-control" name="search" value="<?php echo e(request('search')); ?>" 
                       placeholder="NIK, Nama, Email, Telepon...">
              </div>
              <div class="col-md-2">
                <label class="form-label">Cabang</label>
                <select class="form-select" name="branch_id">
                  <option value="">Semua Cabang</option>
                  <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                      <?php echo e($branch->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Divisi</label>
                <select class="form-select" name="division_id">
                  <option value="">Semua Divisi</option>
                  <?php $__currentLoopData = $divisions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $division): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($division->id); ?>" <?php echo e(request('division_id') == $division->id ? 'selected' : ''); ?>>
                      <?php echo e($division->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Status</label>
                <select class="form-select" name="status">
                  <option value="">Semua Status</option>
                  <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Aktif</option>
                  <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Status Karyawan</label>
                <select class="form-select" name="employee_status">
                  <option value="">Semua</option>
                  <option value="permanent" <?php echo e(request('employee_status') === 'permanent' ? 'selected' : ''); ?>>Tetap</option>
                  <option value="contract" <?php echo e(request('employee_status') === 'contract' ? 'selected' : ''); ?>>Kontrak</option>
                  <option value="intern" <?php echo e(request('employee_status') === 'intern' ? 'selected' : ''); ?>>Magang</option>
                </select>
              </div>
              <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line"></i>
                  </button>
                  <a href="<?php echo e(route('master.employees.index')); ?>" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line"></i>
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Employees Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">
              Daftar Karyawan (<?php echo e($employees->total()); ?>)
            </h6>
          </div>
        </div>
        <div class="card-body">
          <?php if($employees->count() > 0): ?>
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>NIK</th>
                    <th>Nama Lengkap</th>
                    <th>Cabang</th>
                    <th>Divisi</th>
                    <th>Jabatan</th>
                    <th>Status Karyawan</th>
                    <th>Status</th>
                    <th>Asset Assigned</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  <?php $__currentLoopData = $employees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $employee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                      <td>
                        <span class="badge bg-primary"><?php echo e($employee->nik); ?></span>
                      </td>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar avatar-sm me-3">
                            <span class="avatar-initial rounded-circle bg-label-info">
                              <?php echo e(strtoupper(substr($employee->full_name, 0, 2))); ?>

                            </span>
                          </div>
                          <div>
                            <h6 class="mb-0"><?php echo e($employee->full_name); ?></h6>
                            <?php if($employee->email): ?>
                              <small class="text-muted"><?php echo e($employee->email); ?></small>
                            <?php endif; ?>
                          </div>
                        </div>
                      </td>
                      <td><?php echo e($employee->branch->name); ?></td>
                      <td><?php echo e($employee->division->name); ?></td>
                      <td><?php echo e($employee->position); ?></td>
                      <td>
                        <span class="badge bg-<?php echo e($employee->employee_status === 'permanent' ? 'success' : ($employee->employee_status === 'contract' ? 'warning' : 'info')); ?>">
                          <?php echo e($employee->employee_status_text); ?>

                        </span>
                      </td>
                      <td>
                        <div class="form-check form-switch">
                          <input class="form-check-input status-toggle" type="checkbox" 
                                 data-employee-id="<?php echo e($employee->id); ?>"
                                 <?php echo e($employee->is_active ? 'checked' : ''); ?>>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-info"><?php echo e($employee->getAssignedAssetsCount()); ?></span>
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="ri-more-2-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="<?php echo e(route('master.employees.show', $employee)); ?>">
                                <i class="ri-eye-line me-2"></i>
                                Detail
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="<?php echo e(route('master.employees.edit', $employee)); ?>">
                                <i class="ri-edit-line me-2"></i>
                                Edit
                              </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                              <form action="<?php echo e(route('master.employees.destroy', $employee)); ?>" method="POST" 
                                    onsubmit="return confirm('Yakin ingin menghapus karyawan ini?')" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="dropdown-item text-danger">
                                  <i class="ri-delete-bin-line me-2"></i>
                                  Hapus
                                </button>
                              </form>
                            </li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
              <div>
                <small class="text-muted">
                  Menampilkan <?php echo e($employees->firstItem()); ?> - <?php echo e($employees->lastItem()); ?> 
                  dari <?php echo e($employees->total()); ?> karyawan
                </small>
              </div>
              <div>
                <?php echo e($employees->links()); ?>

              </div>
            </div>
          <?php else: ?>
            <div class="text-center py-5">
              <i class="ri-team-line ri-48px text-muted mb-3"></i>
              <h6 class="text-muted">Tidak ada data karyawan</h6>
              <p class="text-muted">Belum ada karyawan yang terdaftar atau sesuai dengan filter.</p>
              <a href="<?php echo e(route('master.employees.create')); ?>" class="btn btn-primary">
                <i class="ri-add-line me-2"></i>
                Tambah Karyawan Pertama
              </a>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Status toggle functionality
  document.querySelectorAll('.status-toggle').forEach(function(toggle) {
    toggle.addEventListener('change', function() {
      const employeeId = this.dataset.employeeId;
      const isActive = this.checked;
      
      fetch(`/master/employees/${employeeId}/toggle-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ is_active: isActive })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message
          console.log(data.message);
        } else {
          // Revert toggle if failed
          this.checked = !isActive;
          alert('Gagal mengubah status karyawan');
        }
      })
      .catch(error => {
        // Revert toggle if error
        this.checked = !isActive;
        alert('Terjadi kesalahan saat mengubah status');
      });
    });
  });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/master/employees/index.blade.php ENDPATH**/ ?>