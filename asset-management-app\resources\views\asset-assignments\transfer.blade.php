@extends('layouts.contentNavbarLayout')

@section('title', 'Transfer Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex align-items-center">
            <a href="{{ route('asset-assignments.show', $assetAssignment) }}" class="btn btn-outline-secondary btn-sm me-3">
              <i class="ri-arrow-left-line"></i>
            </a>
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-exchange-line me-2"></i>
                Transfer Asset
              </h5>
              <small class="text-muted">Transfer asset ke karyawan lain</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Current Assignment Info -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Assignment Saat Ini
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <h6 class="text-muted mb-3">Asset</h6>
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-lg me-3">
                  <span class="avatar-initial rounded bg-label-primary">
                    <i class="ri-archive-line ri-24px"></i>
                  </span>
                </div>
                <div>
                  <h6 class="mb-0">{{ $assetAssignment->asset->asset_code }}</h6>
                  <p class="mb-0 text-muted">{{ $assetAssignment->asset->name }}</p>
                  <small class="text-muted">{{ $assetAssignment->asset->assetCategory->name ?? '-' }}</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <h6 class="text-muted mb-3">Karyawan Saat Ini</h6>
              <div class="d-flex align-items-center mb-3">
                <div class="avatar avatar-lg me-3">
                  <span class="avatar-initial rounded bg-label-info">
                    {{ strtoupper(substr($assetAssignment->employee->full_name, 0, 2)) }}
                  </span>
                </div>
                <div>
                  <h6 class="mb-0">{{ $assetAssignment->employee->full_name }}</h6>
                  <p class="mb-0 text-muted">{{ $assetAssignment->employee->nik }}</p>
                  <small class="text-muted">{{ $assetAssignment->employee->position }}</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <h6 class="text-muted mb-3">Info Assignment</h6>
              <div>
                <small class="text-muted">Tanggal Assignment:</small>
                <div class="fw-bold">{{ $assetAssignment->assigned_at->format('d/m/Y H:i') }}</div>
                <small class="text-muted">Durasi:</small>
                <div class="fw-bold">{{ $assetAssignment->duration }}</div>
                <small class="text-muted">Kondisi:</small>
                <div>
                  <span class="badge bg-{{ $assetAssignment->condition_when_assigned === 'excellent' ? 'success' : ($assetAssignment->condition_when_assigned === 'good' ? 'info' : ($assetAssignment->condition_when_assigned === 'fair' ? 'warning' : 'danger')) }}">
                    {{ $assetAssignment->condition_when_assigned_text }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Transfer Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-file-edit-line me-2"></i>
            Form Transfer Asset
          </h6>
        </div>
        <div class="card-body">
          <form action="{{ route('asset-assignments.transfer', $assetAssignment) }}" method="POST">
            @csrf
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">Karyawan Baru <span class="text-danger">*</span></label>
                  <select class="form-select @error('new_employee_id') is-invalid @enderror" name="new_employee_id" id="newEmployeeSelect">
                    <option value="">Pilih karyawan yang akan menerima asset</option>
                    @foreach($employees as $employee)
                      <option value="{{ $employee->id }}" 
                              data-nik="{{ $employee->nik }}"
                              data-name="{{ $employee->full_name }}"
                              data-position="{{ $employee->position }}"
                              data-department="{{ $employee->department }}"
                              data-branch="{{ $employee->branch->name }}"
                              data-division="{{ $employee->division->name }}"
                              {{ old('new_employee_id') == $employee->id ? 'selected' : '' }}>
                        {{ $employee->nik }} - {{ $employee->full_name }} ({{ $employee->branch->name }})
                      </option>
                    @endforeach
                  </select>
                  @error('new_employee_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <!-- New Employee Info Display -->
                <div id="newEmployeeInfo" class="mb-3" style="display: none;">
                  <div class="alert alert-success">
                    <h6 class="alert-heading mb-2">Informasi Karyawan Baru</h6>
                    <div class="row">
                      <div class="col-6">
                        <small class="text-muted">NIK:</small>
                        <div id="newEmployeeNik" class="fw-bold"></div>
                      </div>
                      <div class="col-6">
                        <small class="text-muted">Nama:</small>
                        <div id="newEmployeeName" class="fw-bold"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Jabatan:</small>
                        <div id="newEmployeePosition"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Bagian:</small>
                        <div id="newEmployeeDepartment"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Cabang:</small>
                        <div id="newEmployeeBranch"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Divisi:</small>
                        <div id="newEmployeeDivision"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">Tanggal Transfer <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('transfer_date') is-invalid @enderror" 
                         name="transfer_date" value="{{ old('transfer_date', date('Y-m-d')) }}">
                  @error('transfer_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">Catatan Transfer</label>
                  <textarea class="form-control @error('transfer_notes') is-invalid @enderror" 
                            name="transfer_notes" rows="8" 
                            placeholder="Alasan transfer, kondisi asset, atau informasi lainnya yang perlu dicatat...">{{ old('transfer_notes') }}</textarea>
                  @error('transfer_notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Transfer Summary -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="alert alert-info">
                  <h6 class="alert-heading mb-3">
                    <i class="ri-exchange-line me-2"></i>
                    Ringkasan Transfer
                  </h6>
                  <div class="row">
                    <div class="col-md-5">
                      <div class="text-center">
                        <h6 class="text-muted mb-2">Dari</h6>
                        <div class="d-flex align-items-center justify-content-center">
                          <div class="avatar avatar-md me-3">
                            <span class="avatar-initial rounded bg-label-info">
                              {{ strtoupper(substr($assetAssignment->employee->full_name, 0, 2)) }}
                            </span>
                          </div>
                          <div class="text-start">
                            <div class="fw-bold">{{ $assetAssignment->employee->full_name }}</div>
                            <small class="text-muted">{{ $assetAssignment->employee->nik }}</small>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-2">
                      <div class="text-center">
                        <div class="avatar avatar-md mx-auto">
                          <span class="avatar-initial rounded bg-label-primary">
                            <i class="ri-arrow-right-line ri-20px"></i>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-5">
                      <div class="text-center">
                        <h6 class="text-muted mb-2">Ke</h6>
                        <div id="transferToDisplay" class="d-flex align-items-center justify-content-center">
                          <div class="text-muted">
                            <i class="ri-user-line me-2"></i>
                            Pilih karyawan baru
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-3">
                  <a href="{{ route('asset-assignments.show', $assetAssignment) }}" class="btn btn-outline-secondary">
                    <i class="ri-close-line me-2"></i>
                    Batal
                  </a>
                  <button type="submit" class="btn btn-info">
                    <i class="ri-exchange-line me-2"></i>
                    Transfer Asset
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const newEmployeeSelect = document.getElementById('newEmployeeSelect');
  const newEmployeeInfo = document.getElementById('newEmployeeInfo');
  const transferToDisplay = document.getElementById('transferToDisplay');

  newEmployeeSelect.addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    
    if (this.value) {
      // Update employee info display
      document.getElementById('newEmployeeNik').textContent = selectedOption.dataset.nik;
      document.getElementById('newEmployeeName').textContent = selectedOption.dataset.name;
      document.getElementById('newEmployeePosition').textContent = selectedOption.dataset.position;
      document.getElementById('newEmployeeDepartment').textContent = selectedOption.dataset.department || '-';
      document.getElementById('newEmployeeBranch').textContent = selectedOption.dataset.branch;
      document.getElementById('newEmployeeDivision').textContent = selectedOption.dataset.division;
      newEmployeeInfo.style.display = 'block';
      
      // Update transfer summary
      const initials = selectedOption.dataset.name.substring(0, 2).toUpperCase();
      transferToDisplay.innerHTML = `
        <div class="d-flex align-items-center justify-content-center">
          <div class="avatar avatar-md me-3">
            <span class="avatar-initial rounded bg-label-success">
              ${initials}
            </span>
          </div>
          <div class="text-start">
            <div class="fw-bold">${selectedOption.dataset.name}</div>
            <small class="text-muted">${selectedOption.dataset.nik}</small>
          </div>
        </div>
      `;
    } else {
      newEmployeeInfo.style.display = 'none';
      transferToDisplay.innerHTML = `
        <div class="text-muted">
          <i class="ri-user-line me-2"></i>
          Pilih karyawan baru
        </div>
      `;
    }
  });

  // Trigger change event if there's a pre-selected value
  if (newEmployeeSelect.value) {
    newEmployeeSelect.dispatchEvent(new Event('change'));
  }
});
</script>

@endsection
