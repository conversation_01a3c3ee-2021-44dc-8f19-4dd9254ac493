<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Asset;
use App\Models\QrLabelConfiguration;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Barryvdh\DomPDF\Facade\Pdf;

class QrLabelController extends Controller
{
    /**
     * Show QR label configuration page
     */
    public function index()
    {
        $configurations = QrLabelConfiguration::active()->get();
        $predefinedSizes = QrLabelConfiguration::getPredefinedSizes();

        return view('qr-labels.index', compact('configurations', 'predefinedSizes'));
    }

    /**
     * Store new QR label configuration
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'width' => 'required|numeric|min:10|max:200',
            'height' => 'required|numeric|min:10|max:200',
            'qr_size' => 'required|numeric|min:5|max:100',
            'margin_top' => 'required|numeric|min:0|max:50',
            'margin_bottom' => 'required|numeric|min:0|max:50',
            'margin_left' => 'required|numeric|min:0|max:50',
            'margin_right' => 'required|numeric|min:0|max:50',
            'font_family' => 'required|string',
            'font_size_title' => 'required|integer|min:6|max:24',
            'font_size_content' => 'required|integer|min:6|max:20',
            'show_asset_name' => 'boolean',
            'show_asset_code' => 'boolean',
            'show_category' => 'boolean',
            'show_branch' => 'boolean',
            'show_location' => 'boolean',
            'is_default' => 'boolean'
        ]);

        $configuration = QrLabelConfiguration::create($validated);

        if ($request->is_default) {
            $configuration->setAsDefault();
        }

        return redirect()->route('qr-labels.index')
            ->with('success', 'Konfigurasi label QR berhasil dibuat.');
    }

    /**
     * Generate QR code for single asset
     */
    public function generateSingle(Asset $asset, Request $request)
    {
        $configId = $request->get('config_id');
        $config = $configId ? QrLabelConfiguration::find($configId) : QrLabelConfiguration::getDefault();

        if (!$config) {
            return redirect()->back()->with('error', 'Konfigurasi label tidak ditemukan.');
        }

        $qrUrl = route('assets.qr-view', $asset->id);

        // Generate QR Code as SVG with precise sizing
        // Convert mm to pixels for 72 DPI (PDF standard): 1mm = 2.834645669 pixels
        $qrSizePixels = $config->qr_size * 2.834645669;
        $qrCodeSvg = QrCode::size($qrSizePixels)
                           ->format('svg')
                           ->errorCorrection('M')
                           ->margin(0)
                           ->generate($qrUrl);

        // Convert SVG to base64 for embedding in PDF
        $qrCodeBase64 = 'data:image/svg+xml;base64,' . base64_encode($qrCodeSvg);

        // Create exact paper size in points (1mm = 2.834645669 points)
        $widthPoints = $config->width * 2.834645669;
        $heightPoints = $config->height * 2.834645669;

        $pdf = Pdf::loadView('qr-labels.single-exact', compact('asset', 'config', 'qrCodeBase64'))
                  ->setPaper([$widthPoints, $heightPoints])
                  ->setOptions([
                      'isHtml5ParserEnabled' => true,
                      'isRemoteEnabled' => true,
                      'defaultFont' => 'DejaVu Sans',
                      'dpi' => 72,
                      'margin_top' => 0,
                      'margin_right' => 0,
                      'margin_bottom' => 0,
                      'margin_left' => 0,
                      'enable_font_subsetting' => false,
                      'chroot' => public_path(),
                  ]);

        return $pdf->stream("qr-label-{$asset->asset_code}.pdf");
    }

    /**
     * Generate QR codes for multiple assets
     */
    public function generateMultiple(Request $request)
    {
        $assetIds = json_decode($request->get('asset_ids', '[]'));
        $configId = $request->get('config_id');

        if (empty($assetIds)) {
            return redirect()->back()->with('error', 'Pilih minimal satu asset.');
        }

        $config = $configId ? QrLabelConfiguration::find($configId) : QrLabelConfiguration::getDefault();
        $assets = Asset::whereIn('id', $assetIds)->with(['assetCategory', 'branch'])->get();

        $labelsData = [];
        $qrSize = $config->qr_size * 3.78; // Convert mm to pixels

        foreach ($assets as $asset) {
            $qrUrl = route('assets.qr-view', $asset->id);
            $qrCodeSvg = QrCode::size($qrSize)
                               ->format('svg')
                               ->errorCorrection('M')
                               ->margin(1)
                               ->generate($qrUrl);

            // Convert SVG to base64 for embedding in PDF
            $qrCodeBase64 = 'data:image/svg+xml;base64,' . base64_encode($qrCodeSvg);

            $labelsData[] = [
                'asset' => $asset,
                'qrCodeBase64' => $qrCodeBase64
            ];
        }

        $pdf = Pdf::loadView('qr-labels.multiple', compact('labelsData', 'config'))
                  ->setPaper('A4', 'portrait')
                  ->setOptions([
                      'isHtml5ParserEnabled' => true,
                      'isRemoteEnabled' => true,
                      'defaultFont' => 'Arial'
                  ]);

        return $pdf->stream("qr-labels-multiple.pdf");
    }

    /**
     * Preview QR label
     */
    public function preview(Request $request)
    {
        $configId = $request->get('config_id');
        $config = $configId ? QrLabelConfiguration::find($configId) : QrLabelConfiguration::getDefault();

        // Use sample data for preview
        $sampleAsset = (object) [
            'asset_code' => 'SAMPLE-001',
            'name' => 'Sample Asset Name',
            'assetCategory' => (object) ['name' => 'Sample Category'],
            'branch' => (object) ['name' => 'Sample Branch'],
            'location' => 'Sample Location'
        ];

        $qrUrl = url('/sample-qr');
        $qrSize = $config->qr_size * 3.78;
        $qrCodeSvg = QrCode::size($qrSize)
                           ->format('svg')
                           ->errorCorrection('M')
                           ->margin(1)
                           ->generate($qrUrl);

        // Convert SVG to base64 for display
        $qrCodeBase64 = 'data:image/svg+xml;base64,' . base64_encode($qrCodeSvg);

        return view('qr-labels.preview', compact('sampleAsset', 'config', 'qrCodeBase64'));
    }

    /**
     * Delete configuration
     */
    public function destroy(QrLabelConfiguration $qrLabel)
    {
        if ($qrLabel->is_default) {
            return redirect()->back()->with('error', 'Tidak dapat menghapus konfigurasi default.');
        }

        $qrLabel->delete();

        return redirect()->route('qr-labels.index')
            ->with('success', 'Konfigurasi label berhasil dihapus.');
    }
}
