<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get role and branch IDs
        $superAdminRole = DB::table('roles')->where('slug', 'super-admin')->first();
        $adminRole = DB::table('roles')->where('slug', 'admin')->first();
        $managerRole = DB::table('roles')->where('slug', 'manager')->first();
        $hqBranch = DB::table('branches')->where('code', 'HQ')->first();
        $jktBranch = DB::table('branches')->where('code', 'JKT-E')->first();

        $users = [
            [
                'name' => 'Super Administrator',
                'username' => 'superadmin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $superAdminRole->id,
                'branch_id' => $hqBranch->id,
                'phone' => '081234567890',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Admin User',
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $adminRole->id,
                'branch_id' => $hqBranch->id,
                'phone' => '081234567891',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Manager Jakarta Timur',
                'username' => 'manager_jkt',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole->id,
                'branch_id' => $jktBranch->id,
                'phone' => '081234567892',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($users as $user) {
            DB::table('users')->insert([
                'name' => $user['name'],
                'username' => $user['username'],
                'email' => $user['email'],
                'password' => $user['password'],
                'role_id' => $user['role_id'],
                'branch_id' => $user['branch_id'],
                'phone' => $user['phone'],
                'is_active' => $user['is_active'],
                'email_verified_at' => $user['email_verified_at'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
