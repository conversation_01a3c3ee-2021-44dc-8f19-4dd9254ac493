<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QR Label - {{ $asset->asset_code }}</title>
    <style>
        @page {
            margin: 0mm;
            padding: 0mm;
            size: {{ $config->width }}mm {{ $config->height }}mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            margin: 0;
            padding: 0;
        }

        body {
            margin: 0mm;
            padding: 0mm;
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            font-family: 'DejaVu Sans', Arial, sans-serif;
            background: white;
            overflow: hidden;
            position: relative;
        }
        
        .label-container {
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            border: 1px solid #000;
            background: white;
            position: absolute;
            top: 0mm;
            left: 0mm;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: {{ $config->margin_top }}mm {{ $config->margin_right }}mm {{ $config->margin_bottom }}mm {{ $config->margin_left }}mm;
            box-sizing: border-box;
        }
        
        .qr-container {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1mm;
        }

        .qr-container img {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            display: block;
        }

        .separator {
            width: 1px;
            height: {{ $config->qr_size }}mm;
            background-color: #000;
            margin: 0 1.5mm;
            flex-shrink: 0;
        }

        .text-container {
            flex: 1;
            height: {{ $config->qr_size }}mm;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 0.5mm;
        }
        
        .asset-name {
            font-size: {{ $config->font_size_title }}pt;
            font-weight: bold;
            color: #000;
            line-height: 1.1;
            margin-bottom: 1mm;
            word-wrap: break-word;
        }
        
        .asset-code {
            font-size: {{ $config->font_size_content }}pt;
            font-weight: bold;
            color: #000;
            line-height: 1.1;
            margin-bottom: 0.8mm;
            letter-spacing: 0.2px;
        }
        
        .asset-detail {
            font-size: {{ max($config->font_size_content - 1, 6) }}pt;
            color: #333;
            line-height: 1.1;
            margin-bottom: 0.5mm;
            word-wrap: break-word;
        }
        
        .asset-detail:last-child {
            margin-bottom: 0;
        }
        
        .asset-detail.category {
            font-style: italic;
            color: #666;
        }
        
        .asset-detail.branch {
            font-weight: 600;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="label-container">
        <div class="qr-container">
            <img src="{{ $qrCodeBase64 }}" alt="QR Code">
        </div>
        <div class="separator"></div>
        <div class="text-container">
            @if($config->show_asset_name)
                <div class="asset-name">{{ Str::limit($asset->name, 20) }}</div>
            @endif

            @if($config->show_asset_code)
                <div class="asset-code">{{ $asset->asset_code }}</div>
            @endif

            @if($config->show_category && $asset->assetCategory)
                <div class="asset-detail category">{{ Str::limit($asset->assetCategory->name, 18) }}</div>
            @endif

            @if($config->show_branch && $asset->branch)
                <div class="asset-detail branch">{{ Str::limit($asset->branch->name, 18) }}</div>
            @endif

            @if($config->show_location && isset($asset->dynamic_fields['location']))
                <div class="asset-detail">{{ Str::limit($asset->dynamic_fields['location'], 18) }}</div>
            @endif
        </div>
    </div>
</body>
</html>
