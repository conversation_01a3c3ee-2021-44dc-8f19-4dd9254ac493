@extends('layouts.blankLayout')

@section('title', 'Login - Asset Management System')

@section('page-style')
{{-- Page Css files --}}
<link rel="stylesheet" href="{{ asset('assets/vendor/css/pages/page-auth.css') }}">
<style>
  .authentication-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }
  .login-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    max-width: 420px;
    margin: 0 auto;
  }
  .form-control {
    border: 2px solid #e7e7ff;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 10px 14px;
    font-size: 14px;
  }
  .form-control:focus {
    border-color: #696cff;
    box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
  }
  .form-label {
    color: #566a7f;
    margin-bottom: 8px;
    font-size: 14px;
  }
  .input-group-text {
    border: 2px solid #e7e7ff;
    border-left: none;
    background: transparent;
    color: #a8aaae;
  }
  .form-control:focus + .input-group-text {
    border-color: #696cff;
  }
  .form-check-input:checked {
    background-color: #696cff;
    border-color: #696cff;
  }
  .btn-primary {
    background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
    border: none;
    border-radius: 8px;
    padding: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(105, 108, 255, 0.4);
  }
  .app-brand-logo {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
  }
</style>
@endsection

@section('content')
<div class="position-relative">
  <div class="authentication-wrapper authentication-basic container-p-y">
    <div class="authentication-inner py-4 mx-4">
      <!-- Login -->
      <div class="card login-card p-4">
        <!-- Logo -->
        <div class="app-brand justify-content-center mt-3">
          <a href="{{ url('/') }}" class="app-brand-link gap-2">
            <span class="app-brand-logo demo">
              <svg width="24" viewBox="0 0 25 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <defs>
                  <path d="M13.7918663,0.358365126 L3.39788168,7.44174259 C0.566865006,9.69408886 -0.379795268,12.4788597 0.557900856,15.7960551 C0.68998853,16.2305145 1.09562888,17.7872135 3.12357076,19.2293357 C3.8146334,19.7207684 5.32369333,20.3834223 7.65075054,21.2172976 L7.59773219,21.2525164 L2.63468769,24.5493413 C0.445452254,26.3002124 0.0884951797,28.5083815 1.56381646,31.1738486 C2.83770406,32.8170431 5.20850219,33.2640127 7.09180128,32.5391577 C8.347334,32.0559211 11.4559176,30.0011079 16.4175519,26.3747182 C18.0338572,24.4997857 18.6973423,22.4544883 18.4080071,20.2388261 C17.963753,17.5346866 16.1776345,15.5799961 13.0496516,14.3747546 L10.9194936,13.4715819 L18.6192054,7.984237 L13.7918663,0.358365126 Z" id="path-1"></path>
                  <path d="M5.47320593,6.00457225 C4.05321814,8.216144 4.36334763,10.0722806 6.40359441,11.5729822 C8.61520715,12.571656 10.0999176,13.2171421 10.8577257,13.5094407 L15.5088241,14.433041 L18.6192054,7.984237 C15.5364148,3.11535317 13.9273018,0.573395879 13.7918663,0.358365126 C13.5790555,0.511491653 10.8061687,2.3935607 5.47320593,6.00457225 Z" id="path-3"></path>
                  <path d="M7.50063644,21.2294429 L12.3234468,23.3159332 C14.1688022,24.7579751 14.397098,26.4880487 13.008334,28.506154 C11.6195701,30.5242593 10.3099883,31.790241 9.07958868,32.3040991 C5.78142938,33.4346997 4.13234973,34 4.13234973,34 C4.13234973,34 2.75489982,33.0538207 2.37032616e-14,31.1614621 C-0.55822714,27.8186216 -0.55822714,26.0572515 -4.05231404e-15,25.8773518 C0.83734071,25.6075023 2.77988457,22.8248993 3.3049379,22.52991 C3.65497346,22.3332504 5.05353963,21.8997614 7.50063644,21.2294429 Z" id="path-4"></path>
                  <path d="M20.6,7.13333333 L25.6,13.8 C26.2627417,14.6836556 26.0836556,15.9372583 25.2,16.6 C24.8538077,16.8596443 24.4327404,17 24,17 L14,17 C12.8954305,17 12,16.1045695 12,15 C12,14.5672596 12.1403557,14.1461923 12.4,13.8 L17.4,7.13333333 C18.0627417,6.24967773 19.3163444,6.07059163 20.2,6.73333333 C20.3516113,6.84704183 20.4862915,6.981722 20.6,7.13333333 Z" id="path-5"></path>
                </defs>
                <g id="g-app-brand" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                  <g id="Brand-Logo" transform="translate(-27.000000, -15.000000)">
                    <g id="Icon" transform="translate(27.000000, 15.000000)">
                      <g id="Mask" transform="translate(0.000000, 8.000000)">
                        <mask id="mask-2" fill="white">
                          <use xlink:href="#path-1"></use>
                        </mask>
                        <use fill="#696cff" xlink:href="#path-1"></use>
                        <g id="Path-3" mask="url(#mask-2)">
                          <use fill="#696cff" xlink:href="#path-3"></use>
                          <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-3"></use>
                        </g>
                        <g id="Path-4" mask="url(#mask-2)">
                          <use fill="#696cff" xlink:href="#path-4"></use>
                          <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-4"></use>
                        </g>
                      </g>
                      <g id="Triangle" transform="translate(19.000000, 11.000000) rotate(-300.000000) translate(-19.000000, -11.000000) ">
                        <use fill="#696cff" xlink:href="#path-5"></use>
                        <use fill-opacity="0.2" fill="#FFFFFF" xlink:href="#path-5"></use>
                      </g>
                    </g>
                  </g>
                </g>
              </svg>
            </span>
            <span class="app-brand-text demo text-heading fw-semibold">Asset Management</span>
          </a>
        </div>
        <!-- /Logo -->

        <div class="card-body mt-1">
          <h5 class="mb-1 text-center">Selamat Datang! 👋</h5>
          <p class="mb-4 text-center text-muted">Silakan masuk ke akun Anda untuk memulai mengelola aset</p>

          <form id="formAuthentication" class="mb-4" action="{{ route('login') }}" method="POST">
            @csrf

            <!-- Username Field -->
            <div class="mb-3">
              <label for="username" class="form-label fw-medium">Username</label>
              <input type="text" class="form-control @error('username') is-invalid @enderror"
                     id="username" name="username" placeholder="Masukkan username Anda"
                     value="{{ old('username') }}" autofocus>
              @error('username')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <!-- Password Field -->
            <div class="mb-3">
              <label for="password" class="form-label fw-medium">Password</label>
              <div class="form-password-toggle">
                <div class="input-group input-group-merge">
                  <input type="password" id="password"
                         class="form-control @error('password') is-invalid @enderror"
                         name="password"
                         placeholder="Masukkan password Anda"
                         aria-describedby="password" />
                  <span class="input-group-text cursor-pointer">
                    <i class="ri-eye-off-line ri-20px"></i>
                  </span>
                </div>
                @error('password')
                  <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="mb-3 d-flex justify-content-between align-items-center">
              <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="remember-me" name="remember">
                <label class="form-check-label" for="remember-me">
                  Ingat saya
                </label>
              </div>
              <a href="javascript:void(0);" class="text-muted">
                <small>Lupa Password?</small>
              </a>
            </div>

            <!-- Login Button -->
            <div class="mb-3">
              <button class="btn btn-primary d-grid w-100" type="submit">
                <span class="d-flex align-items-center justify-content-center">
                  <i class="ri-login-box-line ri-18px me-2"></i>
                  Masuk
                </span>
              </button>
            </div>
          </form>

          <!-- Footer Info -->
          <div class="text-center">
            <p class="mb-2">
              <span class="text-muted">Belum punya akun?</span>
              <a href="javascript:void(0);" class="text-primary fw-medium">
                <span>Hubungi Administrator</span>
              </a>
            </p>

            <!-- System Info -->
            <div class="mt-3 pt-2 border-top">
              <small class="text-muted d-block mb-1">
                <i class="ri-shield-check-line me-1"></i>
                Sistem Manajemen Aset Perusahaan
              </small>
              <small class="text-muted">
                <i class="ri-time-line me-1"></i>
                {{ date('Y') }} - Secure & Reliable
              </small>
            </div>
          </div>
        </div>
      </div>
      <!-- /Login -->

      <!-- Background Elements -->
      <img src="{{ asset('assets/img/illustrations/tree-3.png') }}" alt="auth-tree"
           class="authentication-image-object-left d-none d-lg-block" style="opacity: 0.6;">
      <img src="{{ asset('assets/img/illustrations/auth-basic-mask-light.png') }}"
           class="authentication-image d-none d-lg-block" height="172" alt="triangle-bg" style="opacity: 0.8;">
      <img src="{{ asset('assets/img/illustrations/tree.png') }}" alt="auth-tree"
           class="authentication-image-object-right d-none d-lg-block" style="opacity: 0.6;">
    </div>
  </div>
</div>
@endsection

@section('page-script')
{{-- Page js files --}}
<script src="{{ asset('assets/js/pages-auth.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading animation to login button
    const loginForm = document.getElementById('formAuthentication');
    const loginButton = loginForm.querySelector('button[type="submit"]');
    const originalButtonText = loginButton.innerHTML;

    loginForm.addEventListener('submit', function() {
        loginButton.disabled = true;
        loginButton.innerHTML = `
            <span class="d-flex align-items-center justify-content-center">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Memproses...
            </span>
        `;
    });

    // Add floating animation to card
    const loginCard = document.querySelector('.login-card');
    loginCard.style.animation = 'fadeInUp 0.6s ease-out';

    // Add focus effects to form inputs
    const formInputs = document.querySelectorAll('.form-control');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.2s ease';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .form-control:focus {
            border-color: #696cff !important;
            box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25) !important;
        }

        .authentication-image-object-left,
        .authentication-image-object-right {
            animation: float 6s ease-in-out infinite;
        }

        .authentication-image-object-right {
            animation-delay: -3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>
@endsection
