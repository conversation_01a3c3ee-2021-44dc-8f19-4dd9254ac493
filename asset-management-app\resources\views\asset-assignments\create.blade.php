@extends('layouts.contentNavbarLayout')

@section('title', 'Assign Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex align-items-center">
            <a href="{{ route('asset-assignments.index') }}" class="btn btn-outline-secondary btn-sm me-3">
              <i class="ri-arrow-left-line"></i>
            </a>
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-user-add-line me-2"></i>
                Assign Asset ke Karyawan
              </h5>
              <small class="text-muted">Assign asset ke karyawan untuk penggunaan</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">Informasi Assignment</h6>
        </div>
        <div class="card-body">
          <form action="{{ route('asset-assignments.store') }}" method="POST">
            @csrf
            
            <div class="row">
              <!-- Asset Selection -->
              <div class="col-md-6">
                <h6 class="text-muted mb-3">Pilih Asset</h6>
                
                <div class="mb-3">
                  <label class="form-label">Asset <span class="text-danger">*</span></label>
                  @if($assets->count() > 0)
                    <!-- Search input for assets -->
                    <input type="text" class="form-control mb-2" id="assetSearch" placeholder="Ketik untuk mencari asset...">

                    <div id="assetSelectContainer">
                    <select class="form-select @error('asset_id') is-invalid @enderror" name="asset_id" id="assetSelect">
                      <option value="">Pilih asset yang akan di-assign</option>
                      @foreach($assets as $asset)
                        <option value="{{ $asset->id }}"
                                data-code="{{ $asset->asset_code }}"
                                data-name="{{ $asset->name }}"
                                data-category="{{ $asset->assetCategory->name ?? '-' }}"
                                data-branch="{{ $asset->branch->name }}"
                                {{ old('asset_id', $selectedAsset?->id) == $asset->id ? 'selected' : '' }}>
                          {{ $asset->asset_code }} - {{ $asset->name }} ({{ $asset->branch->name }}) - Status: Aktif
                        </option>
                      @endforeach
                    </select>
                    </div>
                  @else
                    <div class="alert alert-warning">
                      <i class="ri-alert-line me-2"></i>
                      <strong>Tidak ada asset yang tersedia untuk di-assign.</strong>
                      <br>Pastikan ada asset dengan status <strong>Aktif</strong> dan <strong>belum di-assign</strong> ke karyawan lain.
                      <br><a href="{{ route('assets.create') }}" class="alert-link">Tambah Asset Baru</a> atau periksa status asset yang ada.
                    </div>
                    <input type="hidden" name="asset_id" value="">
                  @endif
                  @error('asset_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <div class="form-text">
                    <i class="ri-information-line me-1"></i>
                    Hanya asset dengan status <strong>Aktif</strong> dan <strong>belum di-assign</strong> yang dapat dipilih
                  </div>
                </div>

                <!-- Asset Info Display -->
                <div id="assetInfo" class="mb-3" style="display: none;">
                  <div class="alert alert-info">
                    <h6 class="alert-heading mb-2">Informasi Asset</h6>
                    <div class="row">
                      <div class="col-6">
                        <small class="text-muted">Kode Asset:</small>
                        <div id="assetCode" class="fw-bold"></div>
                      </div>
                      <div class="col-6">
                        <small class="text-muted">Nama Asset:</small>
                        <div id="assetName" class="fw-bold"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Kategori:</small>
                        <div id="assetCategory"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Cabang:</small>
                        <div id="assetBranch"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">Kondisi Asset Saat Assignment <span class="text-danger">*</span></label>
                  <select class="form-select @error('condition_when_assigned') is-invalid @enderror" name="condition_when_assigned">
                    <option value="">Pilih kondisi asset</option>
                    <option value="excellent" {{ old('condition_when_assigned') === 'excellent' ? 'selected' : '' }}>Sangat Baik</option>
                    <option value="good" {{ old('condition_when_assigned') === 'good' ? 'selected' : '' }}>Baik</option>
                    <option value="fair" {{ old('condition_when_assigned') === 'fair' ? 'selected' : '' }}>Cukup</option>
                    <option value="poor" {{ old('condition_when_assigned') === 'poor' ? 'selected' : '' }}>Buruk</option>
                  </select>
                  @error('condition_when_assigned')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Tanggal Assignment <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('assigned_at') is-invalid @enderror" 
                         name="assigned_at" value="{{ old('assigned_at', date('Y-m-d')) }}">
                  @error('assigned_at')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
          
              <!-- Employee Selection -->
              <div class="col-md-6">
                <h6 class="text-muted mb-3">Pilih Karyawan</h6>
                
                <div class="mb-3">
                  <label class="form-label">Karyawan <span class="text-danger">*</span></label>
                  @if($employees->count() > 0)
                    <!-- Search input for employees -->
                    <input type="text" class="form-control mb-2" id="employeeSearch" placeholder="Ketik untuk mencari karyawan...">

                    <div id="employeeSelectContainer">
                    <select class="form-select @error('employee_id') is-invalid @enderror" name="employee_id" id="employeeSelect">
                      <option value="">Pilih karyawan yang akan menerima asset</option>
                      @foreach($employees as $employee)
                        <option value="{{ $employee->id }}"
                                data-nik="{{ $employee->nik }}"
                                data-name="{{ $employee->full_name }}"
                                data-position="{{ $employee->position }}"
                                data-department="{{ $employee->department }}"
                                data-branch="{{ $employee->branch->name }}"
                                data-division="{{ $employee->division->name }}"
                                {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                          {{ $employee->nik }} - {{ $employee->full_name }} ({{ $employee->branch->name }})
                        </option>
                      @endforeach
                    </select>
                    </div>
                  @else
                    <div class="alert alert-warning">
                      <i class="ri-alert-line me-2"></i>
                      <strong>Tidak ada karyawan aktif yang tersedia.</strong>
                      <br>Pastikan ada karyawan dengan status <strong>Aktif</strong> di sistem.
                      <br><a href="{{ route('master.employees.create') }}" class="alert-link">Tambah Karyawan Baru</a> atau aktifkan karyawan yang ada.
                    </div>
                    <input type="hidden" name="employee_id" value="">
                  @endif
                  @error('employee_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <!-- Employee Info Display -->
                <div id="employeeInfo" class="mb-3" style="display: none;">
                  <div class="alert alert-success">
                    <h6 class="alert-heading mb-2">Informasi Karyawan</h6>
                    <div class="row">
                      <div class="col-6">
                        <small class="text-muted">NIK:</small>
                        <div id="employeeNik" class="fw-bold"></div>
                      </div>
                      <div class="col-6">
                        <small class="text-muted">Nama:</small>
                        <div id="employeeName" class="fw-bold"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Jabatan:</small>
                        <div id="employeePosition"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Bagian:</small>
                        <div id="employeeDepartment"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Cabang:</small>
                        <div id="employeeBranch"></div>
                      </div>
                      <div class="col-6 mt-2">
                        <small class="text-muted">Divisi:</small>
                        <div id="employeeDivision"></div>
                      </div>
                    </div>
                  </div>
                </div>
    <div class="mb-3">
                  <label class="form-label">Catatan Assignment</label>
                  <textarea class="form-control @error('assignment_notes') is-invalid @enderror" 
                            name="assignment_notes" rows="4" 
                            placeholder="Catatan tambahan untuk assignment ini (opsional)">{{ old('assignment_notes') }}</textarea>
                  @error('assignment_notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
                
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-3">
                  <a href="{{ route('asset-assignments.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-close-line me-2"></i>
                    Batal
                  </a>
                  <button type="submit" class="btn btn-primary" {{ ($assets->count() == 0 || $employees->count() == 0) ? 'disabled' : '' }}>
                    <i class="ri-save-line me-2"></i>
                    Assign Asset
                  </button>
                  @if($assets->count() == 0 || $employees->count() == 0)
                    <small class="text-muted d-block mt-2">
                      <i class="ri-information-line me-1"></i>
                      Tombol tidak aktif karena tidak ada asset atau karyawan yang tersedia
                    </small>
                  @endif
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const assetSelect = document.getElementById('assetSelect');
  const employeeSelect = document.getElementById('employeeSelect');
  const assetInfo = document.getElementById('assetInfo');
  const employeeInfo = document.getElementById('employeeInfo');
  const assetSearch = document.getElementById('assetSearch');
  const employeeSearch = document.getElementById('employeeSearch');

  // Search functionality for assets
  if (assetSearch && assetSelect) {
    assetSearch.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      const options = assetSelect.options;

      for (let i = 0; i < options.length; i++) {
        const option = options[i];
        const text = option.textContent.toLowerCase();

        if (searchTerm === '' || text.includes(searchTerm)) {
          option.style.display = '';
        } else {
          option.style.display = 'none';
        }
      }
    });
  }

  // Search functionality for employees
  if (employeeSearch && employeeSelect) {
    employeeSearch.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      const options = employeeSelect.options;

      for (let i = 0; i < options.length; i++) {
        const option = options[i];
        const text = option.textContent.toLowerCase();

        if (searchTerm === '' || text.includes(searchTerm)) {
          option.style.display = '';
        } else {
          option.style.display = 'none';
        }
      }
    });
  }

  // Asset selection handler
  if (assetSelect) {
    assetSelect.addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];

      if (this.value) {
        document.getElementById('assetCode').textContent = selectedOption.dataset.code;
        document.getElementById('assetName').textContent = selectedOption.dataset.name;
        document.getElementById('assetCategory').textContent = selectedOption.dataset.category;
        document.getElementById('assetBranch').textContent = selectedOption.dataset.branch;
        assetInfo.style.display = 'block';

        // Check for existing assignments when both asset and employee are selected
        checkExistingAssignment();
      } else {
        assetInfo.style.display = 'none';
        hideAssignmentWarning();
      }
    });
  }

  // Employee selection handler
  if (employeeSelect) {
    employeeSelect.addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];

      if (this.value) {
        document.getElementById('employeeNik').textContent = selectedOption.dataset.nik;
        document.getElementById('employeeName').textContent = selectedOption.dataset.name;
        document.getElementById('employeePosition').textContent = selectedOption.dataset.position;
        document.getElementById('employeeDepartment').textContent = selectedOption.dataset.department || '-';
        document.getElementById('employeeBranch').textContent = selectedOption.dataset.branch;
        document.getElementById('employeeDivision').textContent = selectedOption.dataset.division;
        employeeInfo.style.display = 'block';

        // Check for existing assignments when both asset and employee are selected
        checkExistingAssignment();
      } else {
        employeeInfo.style.display = 'none';
        hideAssignmentWarning();
      }
    });
  }

  // Function to check existing assignment
  function checkExistingAssignment() {
    const assetId = assetSelect ? assetSelect.value : null;
    const employeeId = employeeSelect ? employeeSelect.value : null;

    if (assetId && employeeId) {
      // Show loading indicator
      showAssignmentCheck('Mengecek assignment yang sudah ada...');

      fetch(`/asset-assignments/check-existing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
          asset_id: assetId,
          employee_id: employeeId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.has_existing) {
          showAssignmentWarning(data.message, data.existing_assignment);
        } else {
          hideAssignmentWarning();
        }
      })
      .catch(error => {
        console.error('Error checking existing assignment:', error);
        hideAssignmentWarning();
      });
    }
  }

  function showAssignmentCheck(message) {
    let warningDiv = document.getElementById('assignmentWarning');
    if (!warningDiv) {
      warningDiv = document.createElement('div');
      warningDiv.id = 'assignmentWarning';
      warningDiv.className = 'alert alert-danger mt-3';
      employeeInfo.parentNode.insertBefore(warningDiv, employeeInfo.nextSibling);
    }
    warningDiv.innerHTML = `
      <div class="d-flex align-items-center">
        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
        <span>${message}</span>
      </div>
    `;
    warningDiv.style.display = 'block';
  }

  function showAssignmentWarning(message, existingAssignment) {
    let warningDiv = document.getElementById('assignmentWarning');
    if (!warningDiv) {
      warningDiv = document.createElement('div');
      warningDiv.id = 'assignmentWarning';
      warningDiv.className = 'alert alert-warning mt-3';
      employeeInfo.parentNode.insertBefore(warningDiv, employeeInfo.nextSibling);
    }

    warningDiv.innerHTML = `
      <h6 class="alert-heading mb-2">
        <i class="ri-alert-line me-2"></i>
        Peringatan: Asset Kategori Sama Sudah Ada
      </h6>
      <p class="mb-2">${message}</p>
      <div class="row">
        <div class="col-md-6">
          <small class="text-muted">Asset yang sudah dimiliki:</small>
          <div class="fw-bold">${existingAssignment.asset_code}</div>
        </div>
        <div class="col-md-6">
          <small class="text-muted">Tanggal Assignment:</small>
          <div class="fw-bold">${existingAssignment.assigned_date}</div>
        </div>
      </div>
      <hr class="my-2">
      <small class="text-muted">
        <i class="ri-information-line me-1"></i>
        Anda masih dapat melanjutkan assignment ini jika diperlukan.
      </small>
    `;
    warningDiv.style.display = 'block';
  }

  function hideAssignmentWarning() {
    const warningDiv = document.getElementById('assignmentWarning');
    if (warningDiv) {
      warningDiv.style.display = 'none';
    }
  }

  // Trigger change events if there are pre-selected values
  if (assetSelect && assetSelect.value) {
    assetSelect.dispatchEvent(new Event('change'));
  }
  if (employeeSelect && employeeSelect.value) {
    employeeSelect.dispatchEvent(new Event('change'));
  }
});
</script>

@endsection
