@extends('layouts.contentNavbarLayout')

@section('title', 'Laporan Stock Opname - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-file-chart-line me-2"></i>
                Laporan Stock Opname
              </h5>
              <small class="text-muted">Laporan dan analisis hasil stock opname asset</small>
            </div>
            <div>
              <button type="button" class="btn btn-success" id="exportExcelBtn">
                <i class="ri-file-excel-2-line me-2"></i>
                Export Excel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Summary Statistics -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-primary">
                <i class="ri-file-list-3-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $summary['total_opnames'] }}</h5>
              <small class="text-muted">Total Stock Opname</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-success">
                <i class="ri-check-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $summary['completed_opnames'] }}</h5>
              <small class="text-muted">Selesai</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-info">
                <i class="ri-database-2-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ number_format($summary['total_assets_audited']) }}</h5>
              <small class="text-muted">Total Asset Diaudit</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-warning">
                <i class="ri-error-warning-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $summary['total_assets_missing'] }}</h5>
              <small class="text-muted">Asset Hilang</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-filter-3-line me-2"></i>
            Filter Laporan
          </h6>
        </div>
        <div class="card-body">
          <form method="GET" action="{{ route('reports.stock-opnames') }}" id="filterForm">
            <div class="row">
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="branch_id" class="form-label">Cabang</label>
                  <select class="form-select" id="branch_id" name="branch_id">
                    <option value="">Semua Cabang</option>
                    @foreach($branches as $branch)
                      <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                        {{ $branch->name }}
                      </option>
                    @endforeach
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="status" class="form-label">Status</label>
                  <select class="form-select" id="status" name="status">
                    <option value="">Semua Status</option>
                    @foreach($statuses as $key => $label)
                      <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                        {{ $label }}
                      </option>
                    @endforeach
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="date_from" class="form-label">Tanggal Dibuat Dari</label>
                  <input type="date" class="form-control" id="date_from" name="date_from" 
                         value="{{ request('date_from') }}">
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="date_to" class="form-label">Tanggal Dibuat Sampai</label>
                  <input type="date" class="form-control" id="date_to" name="date_to" 
                         value="{{ request('date_to') }}">
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="start_date_from" class="form-label">Tanggal Mulai Dari</label>
                  <input type="date" class="form-control" id="start_date_from" name="start_date_from" 
                         value="{{ request('start_date_from') }}">
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="mb-3">
                  <label for="start_date_to" class="form-label">Tanggal Mulai Sampai</label>
                  <input type="date" class="form-control" id="start_date_to" name="start_date_to" 
                         value="{{ request('start_date_to') }}">
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">&nbsp;</label>
                  <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                      <i class="ri-search-line me-2"></i>
                      Filter
                    </button>
                    <a href="{{ route('reports.stock-opnames') }}" class="btn btn-outline-secondary">
                      <i class="ri-refresh-line me-2"></i>
                      Reset
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Stock Opnames Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-table-line me-2"></i>
            Daftar Stock Opname
          </h6>
        </div>
        <div class="card-body">
          @if($stockOpnames->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Nomor Opname</th>
                    <th>Judul</th>
                    <th>Cabang</th>
                    <th>Status</th>
                    <th>Progress</th>
                    <th>Asset Hilang</th>
                    <th>Tanggal</th>
                    <th>Durasi</th>
                    <th width="150">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($stockOpnames as $opname)
                    <tr>
                      <td>
                        <span class="badge bg-primary">{{ $opname->opname_number }}</span>
                      </td>
                      <td>
                        <div>
                          <strong>{{ $opname->title }}</strong>
                          <br><small class="text-muted">{{ $opname->creator->name }}</small>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ $opname->branch->name }}</span>
                      </td>
                      <td>
                        @switch($opname->status)
                          @case('draft')
                            <span class="badge bg-secondary">Draft</span>
                            @break
                          @case('in_progress')
                            <span class="badge bg-warning">Berjalan</span>
                            @break
                          @case('completed')
                            <span class="badge bg-success">Selesai</span>
                            @break
                          @case('cancelled')
                            <span class="badge bg-danger">Dibatalkan</span>
                            @break
                        @endswitch
                      </td>
                      <td>
                        @if($opname->total_assets > 0)
                          @php
                            $progress = round(($opname->scanned_assets / $opname->total_assets) * 100, 1);
                          @endphp
                          <div class="d-flex align-items-center">
                            <div class="progress flex-grow-1 me-2" style="height: 6px;">
                              <div class="progress-bar" role="progressbar" 
                                   style="width: {{ $progress }}%"></div>
                            </div>
                            <small>{{ $progress }}%</small>
                          </div>
                          <small class="text-muted">{{ $opname->scanned_assets }}/{{ $opname->total_assets }}</small>
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>
                        @if($opname->missing_assets > 0)
                          <span class="badge bg-danger">{{ $opname->missing_assets }}</span>
                        @else
                          <span class="badge bg-success">0</span>
                        @endif
                      </td>
                      <td>
                        <div>
                          <small class="text-muted">Dibuat:</small>
                          <br>{{ $opname->created_at->format('d/m/Y') }}
                        </div>
                        @if($opname->start_date)
                          <div class="mt-1">
                            <small class="text-muted">Mulai:</small>
                            <br>{{ $opname->start_date->format('d/m/Y') }}
                          </div>
                        @endif
                      </td>
                      <td>
                        @if($opname->start_date && $opname->end_date)
                          {{ $opname->start_date->diffInDays($opname->end_date) }} hari
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                  data-bs-toggle="dropdown">
                            <i class="ri-more-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="{{ route('reports.stock-opnames.show', $opname) }}">
                                <i class="ri-eye-line me-2"></i>
                                Lihat Detail
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="{{ route('stock-opnames.show', $opname) }}">
                                <i class="ri-settings-4-line me-2"></i>
                                Kelola
                              </a>
                            </li>
                            @if($opname->status === 'completed')
                              <li>
                                <a class="dropdown-item export-detail-btn" 
                                   href="{{ route('reports.stock-opnames.export-detail', $opname) }}">
                                  <i class="ri-file-excel-2-line me-2"></i>
                                  Export Excel
                                </a>
                              </li>
                            @endif
                          </ul>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
              {{ $stockOpnames->links() }}
            </div>
          @else
            <!-- Empty State -->
            <div class="text-center py-5">
              <i class="ri-file-chart-line ri-48px text-muted mb-3"></i>
              <h5 class="text-muted">Tidak Ada Data</h5>
              <p class="text-muted mb-4">Tidak ada stock opname yang sesuai dengan filter yang dipilih.</p>
              <a href="{{ route('reports.stock-opnames') }}" class="btn btn-outline-primary">
                <i class="ri-refresh-line me-1"></i>
                Reset Filter
              </a>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Export Excel functionality
  document.getElementById('exportExcelBtn').addEventListener('click', function() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    window.location.href = `{{ route('reports.stock-opnames.export') }}?${params.toString()}`;
  });
});
</script>

@if(session('success'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-check-line me-2"></i>
      <div class="me-auto fw-semibold">Success</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('success') }}
    </div>
  </div>
@endif

@endsection
