@extends('layouts.contentNavbarLayout')

@section('title', 'Edit User - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master User /</span> Edit User
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Edit User</h5>
          <a href="{{ route('master.users.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.users.update', $user) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name"><PERSON><PERSON> <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name', $user->name) }}" 
                       placeholder="Contoh: John Doe">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="username">Username <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('username') is-invalid @enderror" 
                       id="username" name="username" value="{{ old('username', $user->username) }}" 
                       placeholder="Contoh: johndoe">
                @error('username')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Username harus unik dan akan digunakan untuk login</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="email">Email <span class="text-danger">*</span></label>
              <input type="email" class="form-control @error('email') is-invalid @enderror" 
                     id="email" name="email" value="{{ old('email', $user->email) }}" 
                     placeholder="Contoh: <EMAIL>">
              @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="password">Password Baru</label>
                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                       id="password" name="password" placeholder="Kosongkan jika tidak ingin mengubah">
                @error('password')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kosongkan jika tidak ingin mengubah password</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="password_confirmation">Konfirmasi Password</label>
                <input type="password" class="form-control" 
                       id="password_confirmation" name="password_confirmation" 
                       placeholder="Ulangi password baru">
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-4">
                <label class="form-label" for="role_id">Role <span class="text-danger">*</span></label>
                <select class="form-select @error('role_id') is-invalid @enderror" id="role_id" name="role_id">
                  <option value="">Pilih Role</option>
                  @foreach($roles as $role)
                    <option value="{{ $role->id }}" {{ old('role_id', $user->role_id) == $role->id ? 'selected' : '' }}>
                      {{ $role->name }}
                    </option>
                  @endforeach
                </select>
                @error('role_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="branch_id">Cabang <span class="text-danger">*</span></label>
                <select class="form-select @error('branch_id') is-invalid @enderror" id="branch_id" name="branch_id">
                  <option value="">Pilih Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ old('branch_id', $user->branch_id) == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }} ({{ $branch->code }})
                    </option>
                  @endforeach
                </select>
                @error('branch_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="division_id">Divisi</label>
                <select class="form-select @error('division_id') is-invalid @enderror" id="division_id" name="division_id">
                  <option value="">Pilih Divisi (Opsional)</option>
                  @foreach($divisions as $division)
                    <option value="{{ $division->id }}" {{ old('division_id', $user->division_id) == $division->id ? 'selected' : '' }}>
                      {{ $division->name }} ({{ $division->code }})
                    </option>
                  @endforeach
                </select>
                @error('division_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="phone">Nomor Telepon</label>
              <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                     id="phone" name="phone" value="{{ old('phone', $user->phone) }}" 
                     placeholder="Contoh: 08123456789">
              @error('phone')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', $user->is_active) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.users.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Update
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi User</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ strtoupper(substr($user->name, 0, 2)) }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $user->name }}</h6>
              <small class="text-muted">{{ $user->username }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Data Saat Ini:</h6>
            <p class="mb-2">Email: <strong>{{ $user->email }}</strong></p>
            <p class="mb-2">Role: <strong>{{ $user->role->name }}</strong></p>
            <p class="mb-2">Cabang: <strong>{{ $user->branch->name }}</strong></p>
            <p class="mb-2">Divisi: <strong>{{ $user->division ? $user->division->name : 'Belum ditentukan' }}</strong></p>
            <p class="mb-2">Status:
              <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                {{ $user->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </p>
            <p class="mb-0">Dibuat: {{ $user->created_at->format('d/m/Y') }}</p>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Peringatan:</h6>
            <ul class="mb-0">
              <li>Hati-hati mengubah role dan cabang user</li>
              <li>Password akan dienkripsi otomatis</li>
              <li>Username dan email harus tetap unik</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Format phone number
  const phoneInput = document.getElementById('phone');
  phoneInput.addEventListener('input', function() {
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });

  // Password confirmation validation
  const passwordInput = document.getElementById('password');
  const confirmPasswordInput = document.getElementById('password_confirmation');
  
  confirmPasswordInput.addEventListener('input', function() {
    if (passwordInput.value && this.value && this.value !== passwordInput.value) {
      this.setCustomValidity('Password tidak cocok');
    } else {
      this.setCustomValidity('');
    }
  });
});
</script>
@endsection
