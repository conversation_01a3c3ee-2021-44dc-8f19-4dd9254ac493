<?php

namespace App\Http\Controllers;

use App\Models\RequestAsset;
use App\Models\Lookup;
use App\Models\Division;
use App\Models\ApprovalHistory;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Services\ApprovalService;
use App\Services\NotificationService;

class RequestAssetController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = RequestAsset::with(['requestedByUser', 'division', 'approvedByUser']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('division_id')) {
            $query->where('division_id', $request->division_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('request_number', 'like', "%{$search}%")
                  ->orWhere('purpose', 'like', "%{$search}%")
                  ->orWhereHas('requestedByUser', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(15);
        $divisions = Division::active()->get();

        return view('request-assets.index', compact('requests', 'divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Lookup::getByCode('KATEGORI_REQ');
        $positions = Lookup::getByCode('JABATAN');
        $divisions = Division::active()->get();
        $itemTypes = Lookup::getByCode('ITEM_REQ');

        return view('request-assets.create', compact('categories', 'positions', 'divisions', 'itemTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Debug: Log all incoming data
        \Log::info('Store method called', [
            'all_data' => $request->all(),
            'user_id' => auth()->id(),
            'user_branch' => auth()->user()->branch_id ?? 'null'
        ]);

        try {
            // Simplified validation first
            $validated = $request->validate([
                'needed_date' => 'required|date',
                'request_category' => 'required|string',
                'position' => 'required|string',
                'division_id' => 'required|integer',
                'item_type' => 'required|string',
                'purpose' => 'required|string|max:1000',
                'items' => 'required|array|min:1',
                'items.*.name' => 'required|string|max:255',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.description' => 'nullable|string|max:500',
                'estimated_total' => 'nullable|numeric|min:0',
                'notes' => 'nullable|string|max:1000',
            ]);

            \Log::info('Validation passed', ['validated' => $validated]);

            // Get user's branch ID
            $user = auth()->user();
            $branchId = $user->branch_id ?? 1;

            // Simple request number generation
            $requestNumber = 'REQ-' . date('Ym') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

            // Prepare minimal data
            $data = [
                'request_number' => $requestNumber,
                'request_date' => now()->toDateString(),
                'needed_date' => $validated['needed_date'],
                'request_category' => $validated['request_category'],
                'requested_by' => auth()->id(),
                'position' => $validated['position'],
                'division_id' => (int)$validated['division_id'],
                'item_type' => $validated['item_type'],
                'purpose' => $validated['purpose'],
                'items' => json_encode($validated['items']),
                'status' => 'draft',
                'estimated_total' => $validated['estimated_total'] ?? null,
                'notes' => $validated['notes'] ?? null,
            ];

            \Log::info('Prepared data for creation', ['data' => $data]);

            // Create using DB query builder first to test
            $id = \DB::table('request_assets')->insertGetId($data + [
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            \Log::info('Record created with ID', ['id' => $id]);

            // Get the created record
            $requestAsset = RequestAsset::find($id);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('success', "Permintaan asset berhasil dibuat dengan nomor {$requestNumber}.");

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation failed', ['errors' => $e->errors()]);
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Terdapat kesalahan dalam data yang diinput. Silakan periksa kembali.');

        } catch (\Exception $e) {
            \Log::error('Error creating asset request: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan sistem: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(RequestAsset $requestAsset, ApprovalService $approvalService)
    {
        try {
            $requestAsset->load(['requestedByUser', 'division', 'approvedByUser']);

            // Get approval timeline
            $approvalTimeline = $approvalService->getApprovalTimeline($requestAsset);

            // Check if current user can approve
            $canApprove = $approvalService->canUserApproveRequest($requestAsset, auth()->user());

            return view('request-assets.show', compact('requestAsset', 'approvalTimeline', 'canApprove'));

        } catch (\Exception $e) {
            \Log::error('Error showing request asset: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback values
            $approvalTimeline = collect();
            $canApprove = false;

            return view('request-assets.show', compact('requestAsset', 'approvalTimeline', 'canApprove'))
                ->with('error', 'Terjadi kesalahan saat memuat data approval. Silakan refresh halaman.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RequestAsset $requestAsset)
    {
        // Check if request can be edited
        if (!$requestAsset->canBeEdited()) {
            return redirect()->route('request-assets.show', $requestAsset)
                ->with('error', 'Permintaan ini tidak dapat diedit karena sudah diproses.');
        }

        $categories = Lookup::getByCode('KATEGORI_REQ');
        $positions = Lookup::getByCode('JABATAN');
        $divisions = Division::active()->get();
        $itemTypes = Lookup::getByCode('ITEM_REQ');

        return view('request-assets.edit', compact('requestAsset', 'categories', 'positions', 'divisions', 'itemTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RequestAsset $requestAsset)
    {
        try {
            // Check if request can be edited
            if (!$requestAsset->canBeEdited()) {
                return redirect()->route('request-assets.show', $requestAsset)
                    ->with('error', 'Permintaan ini tidak dapat diedit karena sudah diproses.');
            }

            $validated = $request->validate([
                'needed_date' => 'required|date|after:today',
                'request_category' => 'required|string',
                'position' => 'required|string',
                'division_id' => 'required|exists:divisions,id',
                'item_type' => 'required|string',
                'purpose' => 'required|string|max:1000',
                'items' => 'required|array|min:1',
                'items.*.name' => 'required|string|max:255',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.description' => 'nullable|string|max:500',
                'estimated_total' => 'nullable|numeric|min:0',
                'notes' => 'nullable|string|max:1000',
            ]);

            $requestAsset->update($validated);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('success', 'Permintaan asset berhasil diperbarui.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Terdapat kesalahan dalam data yang diinput. Silakan periksa kembali.');

        } catch (\Exception $e) {
            \Log::error('Error updating asset request: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'request_data' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan sistem. Silakan coba lagi atau hubungi administrator.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RequestAsset $requestAsset)
    {
        try {
            // Check if request can be deleted (only draft status)
            if ($requestAsset->status !== 'draft') {
                return redirect()->route('request-assets.index')
                    ->with('error', 'Hanya permintaan dengan status draft yang dapat dihapus.');
            }

            $requestNumber = $requestAsset->request_number;
            $requestAsset->delete();

            return redirect()->route('request-assets.index')
                ->with('success', "Permintaan asset {$requestNumber} berhasil dihapus.");

        } catch (\Exception $e) {
            \Log::error('Error deleting asset request: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('request-assets.index')
                ->with('error', 'Terjadi kesalahan saat menghapus permintaan. Silakan coba lagi.');
        }
    }

    /**
     * Submit request for approval
     */
    public function submit(RequestAsset $requestAsset, ApprovalService $approvalService, NotificationService $notificationService)
    {
        try {
            if (!$requestAsset->canBeSubmitted()) {
                return redirect()->route('request-assets.show', $requestAsset)
                    ->with('error', 'Permintaan ini tidak dapat diajukan.');
            }

            // Start approval process
            $approvalService->startApprovalProcess($requestAsset);

            // Create notifications for approvers
            $notificationService->createRequestSubmittedNotification($requestAsset);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('success', 'Permintaan asset berhasil diajukan untuk persetujuan dan masuk ke workflow approval.');

        } catch (\Exception $e) {
            \Log::error('Error submitting asset request: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'user_id' => auth()->id(),
            ]);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('error', 'Terjadi kesalahan saat mengajukan permintaan: ' . $e->getMessage());
        }
    }

    /**
     * Approve request
     */
    public function approve(Request $request, RequestAsset $requestAsset, ApprovalService $approvalService, NotificationService $notificationService)
    {
        try {
            \Log::info('Approve request started', [
                'request_id' => $requestAsset->id,
                'current_status' => $requestAsset->status,
                'user_id' => auth()->id(),
                'user_role' => auth()->user()->role ? auth()->user()->role->slug : 'no_role'
            ]);

            $validated = $request->validate([
                'approval_notes' => 'nullable|string|max:1000',
            ]);

            // Check if user can approve
            $canApprove = $approvalService->canUserApproveRequest($requestAsset, auth()->user());
            \Log::info('Can user approve check', [
                'request_id' => $requestAsset->id,
                'can_approve' => $canApprove,
                'user_id' => auth()->id()
            ]);

            if (!$canApprove) {
                return redirect()->route('request-assets.show', $requestAsset)
                    ->with('error', 'Anda tidak memiliki wewenang untuk menyetujui permintaan ini.');
            }

            // Simple direct approval - bypass complex workflow for now
            \Log::info('Direct approval update');

            $updateResult = $requestAsset->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => now(),
                'approval_notes' => $validated['approval_notes'] ?? null,
            ]);

            // Create approval history for timeline
            ApprovalHistory::create([
                'approval_workflow_id' => null,
                'approval_level_id' => null,
                'approvable_type' => RequestAsset::class,
                'approvable_id' => $requestAsset->id,
                'approver_id' => auth()->id(),
                'status' => 'approved',
                'notes' => $validated['approval_notes'] ?? null,
                'approved_at' => now(),
                'timeout_at' => null,
                'metadata' => [
                    'level_order' => 1,
                    'level_name' => 'Direct Approval',
                    'approver_type' => 'direct',
                    'approver_config' => [],
                ],
            ]);

            // Create notification for requester
            $notificationService->createRequestApprovedNotification($requestAsset, auth()->user());

            \Log::info('Direct update result', [
                'request_id' => $requestAsset->id,
                'update_result' => $updateResult,
                'status_before' => $requestAsset->getOriginal('status'),
                'status_after' => $requestAsset->status
            ]);

            // Refresh the model to get updated status
            $requestAsset->refresh();
            \Log::info('Approval completed', [
                'request_id' => $requestAsset->id,
                'final_status' => $requestAsset->status
            ]);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('success', 'Permintaan asset berhasil disetujui.');

        } catch (\Exception $e) {
            \Log::error('Error approving asset request: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('error', 'Terjadi kesalahan saat menyetujui permintaan: ' . $e->getMessage());
        }
    }

    /**
     * Reject request
     */
    public function reject(Request $request, RequestAsset $requestAsset, ApprovalService $approvalService, NotificationService $notificationService)
    {
        try {
            \Log::info('Reject request started', [
                'request_id' => $requestAsset->id,
                'current_status' => $requestAsset->status,
                'user_id' => auth()->id()
            ]);

            $validated = $request->validate([
                'approval_notes' => 'required|string|max:1000',
            ], [
                'approval_notes.required' => 'Alasan penolakan wajib diisi.',
            ]);

            // Check if user can approve/reject
            if (!$approvalService->canUserApproveRequest($requestAsset, auth()->user())) {
                return redirect()->route('request-assets.show', $requestAsset)
                    ->with('error', 'Anda tidak memiliki wewenang untuk menolak permintaan ini.');
            }

            // Simple direct rejection - bypass complex workflow for now
            \Log::info('Direct rejection update');

            $updateResult = $requestAsset->update([
                'status' => 'rejected',
                'approved_by' => auth()->id(),
                'approved_at' => now(),
                'approval_notes' => $validated['approval_notes'],
            ]);

            // Create approval history for timeline
            ApprovalHistory::create([
                'approval_workflow_id' => null,
                'approval_level_id' => null,
                'approvable_type' => RequestAsset::class,
                'approvable_id' => $requestAsset->id,
                'approver_id' => auth()->id(),
                'status' => 'rejected',
                'notes' => $validated['approval_notes'],
                'approved_at' => now(),
                'timeout_at' => null,
                'metadata' => [
                    'level_order' => 1,
                    'level_name' => 'Direct Rejection',
                    'approver_type' => 'direct',
                    'approver_config' => [],
                ],
            ]);

            // Create notification for requester
            $notificationService->createRequestRejectedNotification($requestAsset, auth()->user(), $validated['approval_notes']);

            \Log::info('Direct update result', [
                'request_id' => $requestAsset->id,
                'update_result' => $updateResult,
                'status_before' => $requestAsset->getOriginal('status'),
                'status_after' => $requestAsset->status
            ]);

            // Refresh the model to get updated status
            $requestAsset->refresh();
            \Log::info('Rejection completed', [
                'request_id' => $requestAsset->id,
                'final_status' => $requestAsset->status
            ]);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('success', 'Permintaan asset berhasil ditolak.');

        } catch (\Exception $e) {
            \Log::error('Error rejecting asset request: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('error', 'Terjadi kesalahan saat menolak permintaan: ' . $e->getMessage());
        }
    }

    /**
     * Preview document number for AJAX
     */
    public function previewDocumentNumber()
    {
        try {
            $user = auth()->user();
            $branchId = $user->branch_id ?? 1;

            // Get document number configuration
            $docNumber = \App\Models\DocumentNumber::where('document_type', 'request_asset')
                ->where('branch_id', $branchId)
                ->where('is_active', true)
                ->first();

            if (!$docNumber) {
                // Create temporary preview
                $companyCode = \App\Models\CompanySetting::getCompanyCode();
                $branch = \App\Models\Branch::find($branchId);
                $branchCode = $branch ? $branch->code : 'BR01';
                $year = date('Y');
                $month = date('m');

                $preview = "{$companyCode}-{$branchCode}-REQ-{$year}{$month}-0001";
            } else {
                // Generate preview using existing configuration
                $preview = $docNumber->generateDocumentNumber();
            }

            return response()->json([
                'success' => true,
                'preview' => $preview,
                'format' => '{company_code}-{branch_code}-REQ-{year}{month}-{number}'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat preview nomor dokumen'
            ], 500);
        }
    }

    /**
     * Print request asset as PDF
     */
    public function printPdf(RequestAsset $requestAsset)
    {
        try {
            // Check if request can be printed
            if (!in_array($requestAsset->status, ['draft', 'submitted', 'reviewed', 'approved', 'completed'])) {
                return redirect()->route('request-assets.show', $requestAsset)
                    ->with('error', 'Permintaan dengan status ditolak tidak dapat dicetak.');
            }

            // Load relationships
            $requestAsset->load(['requestedByUser', 'division', 'approvedByUser']);

            // Get company and branch information
            $companySetting = \App\Models\CompanySetting::first();
            $branch = $requestAsset->requestedByUser->branch ?? null;

            // Prepare data for PDF
            $data = [
                'requestAsset' => $requestAsset,
                'companySetting' => $companySetting,
                'branch' => $branch,
                'printDate' => now()->format('d/m/Y H:i'),
            ];

            // Generate PDF
            $pdf = Pdf::loadView('request-assets.pdf', $data);
            $pdf->setPaper('A4', 'portrait');

            // Set filename
            $filename = "Permintaan_Asset_{$requestAsset->request_number}.pdf";

            return $pdf->download($filename);

        } catch (\Exception $e) {
            \Log::error('Error generating PDF: ' . $e->getMessage(), [
                'request_id' => $requestAsset->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('request-assets.show', $requestAsset)
                ->with('error', 'Terjadi kesalahan saat membuat PDF. Silakan coba lagi.');
        }
    }
}
