<?php

namespace App\Http\Controllers;

use App\Models\AssetDigital;
use App\Models\Branch;
use App\Models\Employee;
use App\Models\Lookup;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AssetDigitalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AssetDigital::with(['branch', 'assignedTo', 'createdBy', 'supplier']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('license_type')) {
            $query->where('license_type', $request->license_type);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('asset_code', 'like', "%{$search}%")
                  ->orWhere('vendor', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%")
                        ->orWhere('company_name', 'like', "%{$search}%");
                  });
            });
        }

        // Branch isolation for non-super-admin users
        if (!auth()->user()->isSuperAdmin()) {
            $query->where('branch_id', auth()->user()->branch_id);
        }

        $assetDigitals = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $branches = auth()->user()->isSuperAdmin() ? Branch::active()->get() : collect([auth()->user()->branch]);
        $licenseTypes = Lookup::getByCode('LIC_TYPE');
        $statuses = ['active', 'inactive', 'expired', 'suspended'];

        return view('asset-digitals.index', compact('assetDigitals', 'branches', 'licenseTypes', 'statuses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = auth()->user()->isSuperAdmin() ? Branch::active()->get() : collect([auth()->user()->branch]);
        $employees = Employee::where('branch_id', auth()->user()->branch_id)->active()->get();
        $licenseTypes = Lookup::getByCode('LIC_TYPE');

        return view('asset-digitals.create', compact('branches', 'employees', 'licenseTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'license_type' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $exists = Lookup::where('lookup_code', 'LIC_TYPE')
                        ->where('lookup_name', $value)
                        ->where('is_active', true)
                        ->exists();
                    if (!$exists) {
                        $fail('Tipe lisensi yang dipilih tidak valid.');
                    }
                }
            ],
            'license_key' => 'nullable|string|max:255',
            'username' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
            'login_url' => 'nullable|url|max:255',
            'description' => 'nullable|string',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'version' => 'nullable|string|max:255',
            'purchase_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:purchase_date',
            'purchase_price' => 'nullable|numeric|min:0',
            'max_users' => 'nullable|integer|min:1',
            'current_users' => 'nullable|integer|min:0',
            'status' => 'required|in:active,inactive,expired,suspended',
            'branch_id' => 'required|exists:branches,id',
            'assigned_to' => 'nullable|exists:employees,id',
            'notes' => 'nullable|string',
        ]);

        // Generate asset code
        $validated['asset_code'] = AssetDigital::generateAssetCode($validated['branch_id']);
        $validated['created_by'] = Auth::id();

        // Validate current_users doesn't exceed max_users
        if (isset($validated['max_users']) && isset($validated['current_users'])) {
            if ($validated['current_users'] > $validated['max_users']) {
                return redirect()->back()
                    ->withErrors(['current_users' => 'Current users cannot exceed maximum users.'])
                    ->withInput();
            }
        }

        try {
            $assetDigital = AssetDigital::create($validated);

            return redirect()->route('asset-digitals.show', $assetDigital)
                ->with('success', "Asset Digital berhasil dibuat dengan kode {$assetDigital->asset_code}.");
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Terjadi kesalahan saat menyimpan data.'])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetDigital $assetDigital)
    {
        // Check branch access
        if (!auth()->user()->isSuperAdmin() && $assetDigital->branch_id !== auth()->user()->branch_id) {
            abort(403, 'Unauthorized access.');
        }

        $assetDigital->load(['branch', 'assignedTo', 'createdBy', 'supplier']);

        return view('asset-digitals.show', compact('assetDigital'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetDigital $assetDigital)
    {
        // Check branch access
        if (!auth()->user()->isSuperAdmin() && $assetDigital->branch_id !== auth()->user()->branch_id) {
            abort(403, 'Unauthorized access.');
        }

        $branches = auth()->user()->isSuperAdmin() ? Branch::active()->get() : collect([auth()->user()->branch]);
        $employees = Employee::where('branch_id', $assetDigital->branch_id)->active()->get();
        $licenseTypes = Lookup::getByCode('LIC_TYPE');

        return view('asset-digitals.edit', compact('assetDigital', 'branches', 'employees', 'licenseTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetDigital $assetDigital)
    {
        // Check branch access
        if (!auth()->user()->isSuperAdmin() && $assetDigital->branch_id !== auth()->user()->branch_id) {
            abort(403, 'Unauthorized access.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'license_type' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $exists = Lookup::where('lookup_code', 'LIC_TYPE')
                        ->where('lookup_name', $value)
                        ->where('is_active', true)
                        ->exists();
                    if (!$exists) {
                        $fail('Tipe lisensi yang dipilih tidak valid.');
                    }
                }
            ],
            'license_key' => 'nullable|string|max:255',
            'username' => 'nullable|string|max:255',
            'password' => 'nullable|string|max:255',
            'login_url' => 'nullable|url|max:255',
            'description' => 'nullable|string',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'version' => 'nullable|string|max:255',
            'purchase_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:purchase_date',
            'purchase_price' => 'nullable|numeric|min:0',
            'max_users' => 'nullable|integer|min:1',
            'current_users' => 'nullable|integer|min:0',
            'status' => 'required|in:active,inactive,expired,suspended',
            'branch_id' => 'required|exists:branches,id',
            'assigned_to' => 'nullable|exists:employees,id',
            'notes' => 'nullable|string',
        ]);

        // Validate current_users doesn't exceed max_users
        if (isset($validated['max_users']) && isset($validated['current_users'])) {
            if ($validated['current_users'] > $validated['max_users']) {
                return redirect()->back()
                    ->withErrors(['current_users' => 'Current users cannot exceed maximum users.'])
                    ->withInput();
            }
        }

        // Remove password from validated data if it's empty (don't update password)
        if (empty($validated['password'])) {
            unset($validated['password']);
        }

        try {
            $assetDigital->update($validated);

            return redirect()->route('asset-digitals.show', $assetDigital)
                ->with('success', 'Asset Digital berhasil diperbarui.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Terjadi kesalahan saat memperbarui data.'])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetDigital $assetDigital)
    {
        // Check branch access
        if (!auth()->user()->isSuperAdmin() && $assetDigital->branch_id !== auth()->user()->branch_id) {
            abort(403, 'Unauthorized access.');
        }

        try {
            $assetCode = $assetDigital->asset_code;
            $assetDigital->delete();

            return redirect()->route('asset-digitals.index')
                ->with('success', "Asset Digital {$assetCode} berhasil dihapus.");
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat menghapus data.');
        }
    }
}
