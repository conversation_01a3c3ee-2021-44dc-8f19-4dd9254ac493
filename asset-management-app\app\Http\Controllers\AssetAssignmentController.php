<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Asset;
use App\Models\Employee;
use App\Models\AssetAssignment;
use App\Helpers\BranchHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class AssetAssignmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AssetAssignment::with(['asset.assetCategory', 'employee.branch', 'assignedBy']);

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereHas('employee', function($q) use ($accessibleBranchIds) {
                    $q->whereIn('branch_id', $accessibleBranchIds);
                });
            }
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('asset', function($assetQuery) use ($search) {
                    $assetQuery->where('asset_code', 'like', "%{$search}%")
                             ->orWhere('name', 'like', "%{$search}%");
                })
                ->orWhereHas('employee', function($empQuery) use ($search) {
                    $empQuery->where('nik', 'like', "%{$search}%")
                            ->orWhere('full_name', 'like', "%{$search}%");
                });
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        if ($request->filled('branch_id')) {
            $query->whereHas('employee', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        $assignments = $query->orderBy('assigned_at', 'desc')->paginate(15);

        // Get filter options
        $employees = Employee::active()->with('branch')->orderBy('full_name')->get();
        $branches = BranchHelper::getAccessibleBranches();

        return view('asset-assignments.index', compact('assignments', 'employees', 'branches'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // Get available assets (active status and not assigned)
        $assetsQuery = Asset::where('status', 'active')
                           ->whereNull('assigned_to');

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $assetsQuery->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        $assets = $assetsQuery->with(['assetCategory', 'branch'])->orderBy('asset_code')->get();

        // Get active employees
        $employeesQuery = Employee::active()->with('branch');

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $employeesQuery->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        $employees = $employeesQuery->orderBy('full_name')->get();

        // Pre-select asset if provided
        $selectedAsset = null;
        if ($request->filled('asset_id')) {
            $selectedAsset = Asset::find($request->asset_id);
        }

        return view('asset-assignments.create', compact('assets', 'employees', 'selectedAsset'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'asset_id' => 'required|exists:assets,id',
            'employee_id' => 'required|exists:employees,id',
            'assigned_at' => 'required|date',
            'condition_when_assigned' => 'required|in:excellent,good,fair,poor',
            'assignment_notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if asset exists and is active
        $asset = Asset::find($request->asset_id);
        if (!$asset) {
            return redirect()->back()
                ->with('error', 'Asset tidak ditemukan.')
                ->withInput();
        }

        if ($asset->status !== 'active') {
            return redirect()->back()
                ->with('error', 'Hanya asset dengan status aktif yang dapat di-assign.')
                ->withInput();
        }

        if ($asset->assigned_to) {
            return redirect()->back()
                ->with('error', 'Asset sudah di-assign ke karyawan lain.')
                ->withInput();
        }

        // Check employee access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $employee = Employee::find($request->employee_id);
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($employee->branch_id)) {
                return redirect()->back()
                    ->with('error', 'Anda tidak memiliki akses ke karyawan tersebut.')
                    ->withInput();
            }
        }

        // Check if employee already has asset with same category
        $existingAssignment = AssetAssignment::where('employee_id', $request->employee_id)
            ->where('status', 'active')
            ->whereHas('asset', function($query) use ($asset) {
                $query->where('asset_category_id', $asset->asset_category_id);
            })
            ->with(['asset.assetCategory'])
            ->first();

        if ($existingAssignment) {
            $categoryName = $existingAssignment->asset->assetCategory->name ?? 'Unknown';
            $assetCode = $existingAssignment->asset->asset_code;
            $assignedDate = $existingAssignment->assigned_at->format('d/m/Y');

            return redirect()->back()
                ->with('error', "Karyawan ini sudah memiliki asset dengan kategori '{$categoryName}'. Asset: {$assetCode}, Tanggal Assignment: {$assignedDate}")
                ->withInput();
        }

        DB::transaction(function() use ($request, $asset) {
            // Create assignment record
            AssetAssignment::create([
                'asset_id' => $request->asset_id,
                'employee_id' => $request->employee_id,
                'assigned_by' => auth()->id(),
                'assigned_at' => $request->assigned_at,
                'status' => 'active',
                'assignment_notes' => $request->assignment_notes,
                'condition_when_assigned' => $request->condition_when_assigned
            ]);

            // Update asset
            $asset->update([
                'assigned_to' => $request->employee_id,
                'assigned_at' => $request->assigned_at,
                'assigned_by' => auth()->id(),
                'assignment_notes' => $request->assignment_notes,
                'assignment_status' => 'assigned'
            ]);
        });

        return redirect()->route('asset-assignments.index')
            ->with('success', 'Asset berhasil di-assign ke karyawan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetAssignment $assetAssignment)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetAssignment->employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data assignment ini.');
            }
        }

        $assetAssignment->load([
            'asset.assetCategory',
            'asset.branch',
            'employee.branch',
            'employee.division',
            'assignedBy',
            'returnedBy'
        ]);

        return view('asset-assignments.show', compact('assetAssignment'));
    }

    /**
     * Print asset assignment receipt (Tanda Terima Asset)
     */
    public function printReceipt(AssetAssignment $assetAssignment)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetAssignment->employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data assignment ini.');
            }
        }

        $assetAssignment->load([
            'asset.assetCategory',
            'asset.assetType',
            'asset.branch',
            'employee.branch',
            'employee.division',
            'assignedBy'
        ]);

        // Generate receipt number
        $receiptNumber = 'TR-' . $assetAssignment->asset->branch->code . '-' .
                        str_pad($assetAssignment->id, 4, '0', STR_PAD_LEFT) . '-' .
                        date('Y', strtotime($assetAssignment->assigned_at));

        return view('asset-assignments.print-receipt', compact('assetAssignment', 'receiptNumber'));
    }

    /**
     * Download asset assignment receipt as PDF
     */
    public function downloadReceiptPdf(AssetAssignment $assetAssignment)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetAssignment->employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data assignment ini.');
            }
        }

        $assetAssignment->load([
            'asset.assetCategory',
            'asset.assetType',
            'asset.branch',
            'employee.branch',
            'employee.division',
            'assignedBy'
        ]);

        // Generate receipt number
        $receiptNumber = 'TR-' . $assetAssignment->asset->branch->code . '-' .
                        str_pad($assetAssignment->id, 4, '0', STR_PAD_LEFT) . '-' .
                        date('Y', strtotime($assetAssignment->assigned_at));

        // Return compact PDF view optimized for browser PDF generation
        return view('asset-assignments.pdf-receipt-compact', compact('assetAssignment', 'receiptNumber'));
    }

    /**
     * Return asset form
     */
    public function returnForm(AssetAssignment $assetAssignment)
    {
        // Check if assignment is active
        if ($assetAssignment->status !== 'active') {
            return redirect()->back()
                ->with('error', 'Assignment ini sudah tidak aktif.');
        }

        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetAssignment->employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses untuk mengembalikan asset ini.');
            }
        }

        $assetAssignment->load(['asset', 'employee']);

        return view('asset-assignments.return', compact('assetAssignment'));
    }

    /**
     * Process asset return
     */
    public function returnAsset(Request $request, AssetAssignment $assetAssignment)
    {
        $validator = Validator::make($request->all(), [
            'returned_at' => 'required|date',
            'condition_when_returned' => 'required|in:excellent,good,fair,poor',
            'return_notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if assignment is active
        if ($assetAssignment->status !== 'active') {
            return redirect()->back()
                ->with('error', 'Assignment ini sudah tidak aktif.');
        }

        DB::transaction(function() use ($request, $assetAssignment) {
            // Update assignment
            $assetAssignment->update([
                'status' => 'returned',
                'returned_at' => $request->returned_at,
                'returned_by' => auth()->id(),
                'return_notes' => $request->return_notes,
                'condition_when_returned' => $request->condition_when_returned
            ]);

            // Update asset
            $assetAssignment->asset->update([
                'assigned_to' => null,
                'assigned_at' => null,
                'assignment_status' => 'returned'
            ]);
        });

        return redirect()->route('asset-assignments.index')
            ->with('success', 'Asset berhasil dikembalikan.');
    }

    /**
     * Transfer asset to another employee
     */
    public function transferForm(AssetAssignment $assetAssignment)
    {
        // Check if assignment is active
        if ($assetAssignment->status !== 'active') {
            return redirect()->back()
                ->with('error', 'Assignment ini sudah tidak aktif.');
        }

        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetAssignment->employee->branch_id)) {
                abort(403, 'Anda tidak memiliki akses untuk transfer asset ini.');
            }
        }

        // Get active employees (exclude current assignee)
        $employeesQuery = Employee::active()
                                ->where('id', '!=', $assetAssignment->employee_id)
                                ->with('branch');

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $employeesQuery->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        $employees = $employeesQuery->orderBy('full_name')->get();
        $assetAssignment->load(['asset', 'employee']);

        return view('asset-assignments.transfer', compact('assetAssignment', 'employees'));
    }

    /**
     * Process asset transfer
     */
    public function transferAsset(Request $request, AssetAssignment $assetAssignment)
    {
        $validator = Validator::make($request->all(), [
            'new_employee_id' => 'required|exists:employees,id',
            'transfer_date' => 'required|date',
            'transfer_notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if assignment is active
        if ($assetAssignment->status !== 'active') {
            return redirect()->back()
                ->with('error', 'Assignment ini sudah tidak aktif.');
        }

        DB::transaction(function() use ($request, $assetAssignment) {
            // Mark current assignment as transferred
            $assetAssignment->update([
                'status' => 'transferred',
                'returned_at' => $request->transfer_date,
                'returned_by' => auth()->id(),
                'return_notes' => 'Transferred to: ' . Employee::find($request->new_employee_id)->full_name . '. ' . $request->transfer_notes
            ]);

            // Create new assignment
            AssetAssignment::create([
                'asset_id' => $assetAssignment->asset_id,
                'employee_id' => $request->new_employee_id,
                'assigned_by' => auth()->id(),
                'assigned_at' => $request->transfer_date,
                'status' => 'active',
                'assignment_notes' => 'Transferred from: ' . $assetAssignment->employee->full_name . '. ' . $request->transfer_notes,
                'condition_when_assigned' => $assetAssignment->condition_when_assigned
            ]);

            // Update asset
            $assetAssignment->asset->update([
                'assigned_to' => $request->new_employee_id,
                'assigned_at' => $request->transfer_date,
                'assigned_by' => auth()->id(),
                'assignment_notes' => 'Transferred from: ' . $assetAssignment->employee->full_name,
                'assignment_status' => 'assigned'
            ]);
        });

        return redirect()->route('asset-assignments.index')
            ->with('success', 'Asset berhasil di-transfer ke karyawan baru.');
    }

    /**
     * Check existing assignment for AJAX request
     */
    public function checkExistingAssignment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'asset_id' => 'required|exists:assets,id',
            'employee_id' => 'required|exists:employees,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'has_existing' => false,
                'message' => 'Invalid data'
            ]);
        }

        $asset = Asset::find($request->asset_id);

        // Check if employee already has asset with same category
        $existingAssignment = AssetAssignment::where('employee_id', $request->employee_id)
            ->where('status', 'active')
            ->whereHas('asset', function($query) use ($asset) {
                $query->where('asset_category_id', $asset->asset_category_id);
            })
            ->with(['asset.assetCategory'])
            ->first();

        if ($existingAssignment) {
            $categoryName = $existingAssignment->asset->assetCategory->name ?? 'Unknown';
            $assetCode = $existingAssignment->asset->asset_code;
            $assignedDate = $existingAssignment->assigned_at->format('d/m/Y');

            return response()->json([
                'has_existing' => true,
                'message' => "Karyawan ini sudah memiliki asset dengan kategori '{$categoryName}'.",
                'existing_assignment' => [
                    'asset_code' => $assetCode,
                    'assigned_date' => $assignedDate,
                    'category_name' => $categoryName
                ]
            ]);
        }

        return response()->json([
            'has_existing' => false,
            'message' => 'No existing assignment found'
        ]);
    }
}
