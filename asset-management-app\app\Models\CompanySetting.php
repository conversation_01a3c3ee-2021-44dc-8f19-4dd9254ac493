<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_name',
        'company_code',
        'address',
        'phone',
        'email',
        'website',
        'logo',
        'asset_usage_agreement',
    ];

    // Helper methods
    public static function getCompanyCode()
    {
        $setting = self::first();
        return $setting ? $setting->company_code : 'COMP';
    }

    public static function getCompanyName()
    {
        $setting = self::first();
        return $setting ? $setting->company_name : 'Company Name';
    }

    public static function getAssetUsageAgreement()
    {
        $setting = self::first();
        return $setting && $setting->asset_usage_agreement
            ? $setting->asset_usage_agreement
            : 'Dengan menerima asset ini, saya berkomitmen untuk menggunakan asset dengan baik dan bertanggung jawab atas keamanan serta kondisi asset yang diberikan. Saya akan melaporkan jika terjadi kerusakan atau kehilangan asset kepada pihak IT/HRD-GA.';
    }
}
