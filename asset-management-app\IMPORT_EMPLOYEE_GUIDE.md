# Panduan Import Data Karyawan

## Deskripsi
Fitur import data karyawan memungkinkan admin untuk mengimpor data karyawan dalam jumlah besar menggunakan file Excel. Sistem akan memvalidasi data dan memberikan laporan hasil import yang detail.

## Akses <PERSON>tur
- **URL**: `/employees/import`
- **Permission**: `employees.import`
- **Role**: Super Admin, Admin
- **Menu**: Master Data → Karyawan → Tombol "Import Excel"

## Cara Penggunaan

### 1. Download Template Excel
1. Akses halaman import karyawan
2. Klik tombol "Download Template Excel"
3. Template akan berisi 3 sheet:
   - **Template Import Karyawan**: Sheet untuk mengisi data
   - **Petunjuk Pengisian**: Panduan lengkap pengisian
   - **Data Referensi**: Daftar kode cabang dan divisi

### 2. Isi Data di Template
Isi data karyawan sesuai format yang telah disediakan:

#### Kolom Wajib (*)
- **nik**: NIK ka<PERSON>wan (16 digit, unik)
- **full_name**: <PERSON>a lengkap karyawan
- **branch_code**: Kode cabang (sesuai data master)
- **division_code**: Kode divisi (sesuai data master)
- **position**: Jabatan karyawan
- **employee_status**: Status karyawan (permanent/contract/intern)

#### Kolom Opsional
- **department**: Bagian/departemen
- **email**: Email karyawan (harus unik jika diisi)
- **phone**: Nomor telepon
- **join_date**: Tanggal bergabung (format: dd/mm/yyyy)
- **birth_date**: Tanggal lahir (format: dd/mm/yyyy)
- **gender**: Jenis kelamin (male/female)
- **address**: Alamat lengkap
- **is_active**: Status aktif (true/false, default: true)
- **notes**: Catatan tambahan

### 3. Upload File Excel
1. Pilih file Excel yang sudah diisi
2. Klik tombol "Import Data Karyawan"
3. Sistem akan memproses dan menampilkan hasil

## Validasi Data

### Validasi Header
- Header harus sesuai dengan template
- Urutan kolom harus benar
- Tidak boleh ada kolom yang hilang

### Validasi Data
1. **NIK**: 
   - Harus 16 digit
   - Harus berupa angka
   - Harus unik dalam sistem
   
2. **Email**:
   - Format email yang valid
   - Harus unik jika diisi
   
3. **Tanggal**:
   - Format: dd/mm/yyyy
   - Tanggal lahir harus sebelum hari ini
   
4. **Referensi Data**:
   - Kode cabang harus ada di master cabang
   - Kode divisi harus ada di master divisi
   
5. **Enum Values**:
   - Gender: male atau female
   - Employee Status: permanent, contract, atau intern
   - Is Active: true atau false

### Batasan Import
- Maksimal 1000 baris data per import
- Ukuran file maksimal 10MB
- Format file: .xlsx atau .xls

## Hasil Import
Setelah proses import selesai, sistem akan menampilkan:
- Jumlah data yang berhasil diimport
- Jumlah data yang gagal
- Detail error untuk setiap baris yang gagal

## Contoh Data Valid
```
NIK: 1234567890123456
Nama: John Doe
Kode Cabang: JKT
Kode Divisi: IT
Departemen: Software Development
Jabatan: Software Engineer
Email: <EMAIL>
Telepon: 081234567890
Tanggal Bergabung: 01/01/2024
Tanggal Lahir: 15/06/1990
Jenis Kelamin: male
Alamat: Jl. Sudirman No. 123, Jakarta
Status Karyawan: permanent
Status Aktif: true
Catatan: Karyawan baru
```

## Error Handling
Sistem akan menangani berbagai jenis error:
- Format file tidak valid
- Header tidak sesuai template
- Data tidak valid (NIK duplikat, email duplikat, dll)
- Referensi data tidak ditemukan
- Format tanggal salah

## Tips Penggunaan
1. **Persiapan Data**: Pastikan data sudah lengkap dan valid sebelum import
2. **Backup**: Lakukan backup database sebelum import data besar
3. **Batch Import**: Untuk data besar, bagi menjadi beberapa batch (maksimal 1000 per batch)
4. **Validasi Manual**: Periksa hasil import dan lakukan koreksi manual jika diperlukan

## Troubleshooting

### Error "NIK sudah ada"
- Periksa apakah NIK sudah ada di database
- Pastikan NIK unik dalam file Excel

### Error "Email sudah ada"
- Periksa apakah email sudah digunakan karyawan lain
- Kosongkan kolom email jika tidak diperlukan

### Error "Kode cabang/divisi tidak ditemukan"
- Periksa data master cabang dan divisi
- Pastikan kode yang digunakan sesuai dengan data master

### Error "Format tanggal tidak valid"
- Gunakan format dd/mm/yyyy
- Contoh: 15/06/1990

## Keamanan
- Fitur hanya dapat diakses oleh user dengan permission `employees.import`
- Semua aktivitas import dicatat dalam log sistem
- Data divalidasi sepenuhnya sebelum disimpan ke database
- Menggunakan database transaction untuk memastikan konsistensi data
