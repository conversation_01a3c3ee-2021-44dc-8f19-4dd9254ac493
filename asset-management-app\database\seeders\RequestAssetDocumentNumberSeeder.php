<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RequestAssetDocumentNumberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get branches for document number configuration
        $branches = \App\Models\Branch::all();

        if ($branches->isEmpty()) {
            $this->command->warn('No branches found. Please run BranchSeeder first.');
            return;
        }

        foreach ($branches as $branch) {
            // Create document number configuration for request asset
            \App\Models\DocumentNumber::create([
                'document_type' => 'request_asset',
                'format' => '{company_code}-{branch_code}-REQ-{year}{month}-{number}',
                'current_number' => 0,
                'year' => date('Y'),
                'branch_id' => $branch->id,
                'asset_category_id' => null, // Request asset tidak terikat kategori spesifik
                'asset_type_id' => null, // Request asset tidak terikat jenis asset spesifik
                'description' => "Nomor dokumen permintaan asset untuk cabang {$branch->name}",
                'is_active' => true,
            ]);
        }

        $this->command->info('Request Asset document number configurations created successfully.');
    }
}
