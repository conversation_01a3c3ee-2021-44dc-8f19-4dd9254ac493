@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Divisi - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Divisi /</span> Detail Divisi
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Detail Divisi: {{ $division->name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.divisions.edit', $division) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.divisions.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Ke<PERSON>li
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Divisi</label>
                <p class="fw-bold">{{ $division->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Divisi</label>
                <p><span class="badge bg-primary fs-6">{{ $division->code }}</span></p>
              </div>
            </div>
          </div>

          @if($division->description)
          <div class="mb-3">
            <label class="form-label text-muted">Deskripsi</label>
            <p>{{ $division->description }}</p>
          </div>
          @endif

          <hr class="my-4">
          <h6 class="mb-3">Informasi Kepala Divisi</h6>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Kepala Divisi</label>
                <p>{{ $division->head_name ?: '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Email</label>
                <p>{{ $division->head_email ?: '-' }}</p>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Telepon</label>
                <p>{{ $division->head_phone ?: '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $division->is_active ? 'success' : 'secondary' }} fs-6">
                    {{ $division->is_active ? 'Aktif' : 'Non-Aktif' }}
                  </span>
                </p>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Dibuat</label>
                <p>{{ $division->created_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Terakhir Diupdate</label>
                <p>{{ $division->updated_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Card -->
      @if($division->users->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">User di Divisi Ini ({{ $division->users->count() }} user)</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Nama</th>
                  <th>Username</th>
                  <th>Role</th>
                  <th>Cabang</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                @foreach($division->users as $user)
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-xs me-2">
                        <span class="avatar-initial rounded-circle bg-label-primary">
                          {{ strtoupper(substr($user->name, 0, 2)) }}
                        </span>
                      </div>
                      {{ $user->name }}
                    </div>
                  </td>
                  <td>{{ $user->username }}</td>
                  <td>
                    <span class="badge bg-{{ $user->role->slug === 'super-admin' ? 'danger' : ($user->role->slug === 'admin' ? 'warning' : 'primary') }} badge-sm">
                      {{ $user->role->name }}
                    </span>
                  </td>
                  <td>{{ $user->branch->name }}</td>
                  <td>
                    <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }} badge-sm">
                      {{ $user->is_active ? 'Aktif' : 'Non-Aktif' }}
                    </span>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
      @endif
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Divisi</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ strtoupper(substr($division->code, 0, 2)) }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $division->name }}</h6>
              <small class="text-muted">{{ $division->code }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>{{ $division->users->count() }} User Total</li>
              <li>{{ $division->users->where('is_active', true)->count() }} User Aktif</li>
              <li>Dibuat {{ $division->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.divisions.edit', $division) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Divisi
            </a>
            
            @if($division->is_active)
              <button class="btn btn-warning" onclick="toggleStatus(false)">
                <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
              </button>
            @else
              <button class="btn btn-success" onclick="toggleStatus(true)">
                <i class="ri-play-circle-line me-1"></i>Aktifkan
              </button>
            @endif
            
            @if($division->users->count() == 0)
            <form action="{{ route('master.divisions.destroy', $division) }}" method="POST" id="deleteForm">
              @csrf
              @method('DELETE')
              <button type="button" class="btn btn-danger w-100" onclick="confirmDelete()">
                <i class="ri-delete-bin-line me-1"></i>Hapus Divisi
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStatus(status) {
  if (confirm('Yakin ingin mengubah status divisi ini?')) {
    // Create form to toggle status
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("master.divisions.update", $division) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'is_active';
    statusField.value = status ? '1' : '0';
    
    // Copy other fields
    const fields = ['name', 'code', 'description', 'head_name', 'head_email', 'head_phone'];
    fields.forEach(field => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = field;
      input.value = divisionData[field];
      form.appendChild(input);
    });
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    form.appendChild(statusField);
    
    document.body.appendChild(form);
    form.submit();
  }
}

function confirmDelete() {
  if (confirm('Yakin ingin menghapus divisi ini?\n\nTindakan ini tidak dapat dibatalkan!')) {
    document.getElementById('deleteForm').submit();
  }
}
</script>
@endsection
