<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ApprovalWorkflowSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get role IDs
        $adminRole = \App\Models\Role::where('slug', 'admin')->first();
        $managerRole = \App\Models\Role::where('slug', 'manager')->first();
        $superAdminRole = \App\Models\Role::where('slug', 'super-admin')->first();

        // Workflow 1: Standard Asset Request (Low Value)
        $workflow1 = \App\Models\ApprovalWorkflow::create([
            'name' => 'Permohonan Asset Standar',
            'code' => 'ASSET_STANDARD',
            'description' => 'Workflow untuk permohonan asset dengan nilai rendah hingga sedang (< Rp 10 juta)',
            'module' => 'asset_requests',
            'priority' => 1,
            'is_active' => true,
            'conditions' => [
                [
                    'field' => 'estimated_price',
                    'operator' => 'less_than',
                    'value' => 10000000
                ]
            ]
        ]);

        // Level 1: Supervisor Review
        \App\Models\ApprovalLevel::create([
            'approval_workflow_id' => $workflow1->id,
            'level_order' => 1,
            'level_name' => 'Review Supervisor',
            'description' => 'Review oleh supervisor langsung',
            'approver_type' => 'role',
            'approver_config' => [
                'role_ids' => [$adminRole?->id]
            ],
            'is_required' => true,
            'can_skip' => false,
            'timeout_hours' => 24,
            'timeout_action' => 'escalate',
            'is_active' => true,
        ]);

        // Level 2: Manager Approval
        \App\Models\ApprovalLevel::create([
            'approval_workflow_id' => $workflow1->id,
            'level_order' => 2,
            'level_name' => 'Persetujuan Manager',
            'description' => 'Persetujuan final oleh manager',
            'approver_type' => 'role',
            'approver_config' => [
                'role_ids' => [$managerRole?->id]
            ],
            'is_required' => true,
            'can_skip' => false,
            'timeout_hours' => 48,
            'timeout_action' => 'escalate',
            'is_active' => true,
        ]);

        // Workflow 2: High Value Asset Request
        $workflow2 = \App\Models\ApprovalWorkflow::create([
            'name' => 'Permohonan Asset Nilai Tinggi',
            'code' => 'ASSET_HIGH_VALUE',
            'description' => 'Workflow untuk permohonan asset dengan nilai tinggi (>= Rp 10 juta)',
            'module' => 'asset_requests',
            'priority' => 2,
            'is_active' => true,
            'conditions' => [
                [
                    'field' => 'estimated_price',
                    'operator' => 'greater_than_or_equal',
                    'value' => 10000000
                ]
            ]
        ]);

        // Level 1: Supervisor Review
        \App\Models\ApprovalLevel::create([
            'approval_workflow_id' => $workflow2->id,
            'level_order' => 1,
            'level_name' => 'Review Supervisor',
            'description' => 'Review awal oleh supervisor',
            'approver_type' => 'role',
            'approver_config' => [
                'role_ids' => [$adminRole?->id]
            ],
            'is_required' => true,
            'can_skip' => false,
            'timeout_hours' => 24,
            'timeout_action' => 'escalate',
            'is_active' => true,
        ]);

        // Level 2: Manager Approval
        \App\Models\ApprovalLevel::create([
            'approval_workflow_id' => $workflow2->id,
            'level_order' => 2,
            'level_name' => 'Persetujuan Manager',
            'description' => 'Persetujuan oleh manager divisi',
            'approver_type' => 'role',
            'approver_config' => [
                'role_ids' => [$managerRole?->id]
            ],
            'is_required' => true,
            'can_skip' => false,
            'timeout_hours' => 48,
            'timeout_action' => 'escalate',
            'is_active' => true,
        ]);

        // Level 3: Director Approval
        \App\Models\ApprovalLevel::create([
            'approval_workflow_id' => $workflow2->id,
            'level_order' => 3,
            'level_name' => 'Persetujuan Direktur',
            'description' => 'Persetujuan final oleh direktur',
            'approver_type' => 'role',
            'approver_config' => [
                'role_ids' => [$superAdminRole?->id]
            ],
            'is_required' => true,
            'can_skip' => false,
            'timeout_hours' => 72,
            'timeout_action' => 'escalate',
            'is_active' => true,
        ]);

        // Workflow 3: Urgent Asset Request
        $workflow3 = \App\Models\ApprovalWorkflow::create([
            'name' => 'Permohonan Asset Mendesak',
            'code' => 'ASSET_URGENT',
            'description' => 'Workflow untuk permohonan asset dengan prioritas mendesak',
            'module' => 'asset_requests',
            'priority' => 10,
            'is_active' => true,
            'conditions' => [
                [
                    'field' => 'priority',
                    'operator' => 'equals',
                    'value' => 'urgent'
                ]
            ]
        ]);

        // Level 1: Direct Manager Approval (Skip Supervisor)
        \App\Models\ApprovalLevel::create([
            'approval_workflow_id' => $workflow3->id,
            'level_order' => 1,
            'level_name' => 'Persetujuan Manager Langsung',
            'description' => 'Persetujuan langsung oleh manager untuk kasus mendesak',
            'approver_type' => 'role',
            'approver_config' => [
                'role_ids' => [$managerRole?->id, $superAdminRole?->id]
            ],
            'is_required' => true,
            'can_skip' => false,
            'timeout_hours' => 12,
            'timeout_action' => 'escalate',
            'is_active' => true,
        ]);

        // Workflow 4: IT Category Specific
        $itCategory = \App\Models\AssetCategory::where('code', 'PC-LAPTOP')->first();
        if ($itCategory) {
            $workflow4 = \App\Models\ApprovalWorkflow::create([
                'name' => 'Permohonan Asset IT',
                'code' => 'ASSET_IT_SPECIFIC',
                'description' => 'Workflow khusus untuk permohonan asset kategori IT/Komputer',
                'module' => 'asset_requests',
                'priority' => 5,
                'is_active' => true,
                'conditions' => [
                    [
                        'field' => 'category_id',
                        'operator' => 'equals',
                        'value' => $itCategory->id
                    ]
                ]
            ]);

            // Level 1: IT Admin Review
            \App\Models\ApprovalLevel::create([
                'approval_workflow_id' => $workflow4->id,
                'level_order' => 1,
                'level_name' => 'Review IT Admin',
                'description' => 'Review teknis oleh IT Admin',
                'approver_type' => 'role',
                'approver_config' => [
                    'role_ids' => [$adminRole?->id]
                ],
                'is_required' => true,
                'can_skip' => false,
                'timeout_hours' => 24,
                'timeout_action' => 'escalate',
                'is_active' => true,
            ]);

            // Level 2: Manager Approval
            \App\Models\ApprovalLevel::create([
                'approval_workflow_id' => $workflow4->id,
                'level_order' => 2,
                'level_name' => 'Persetujuan Manager',
                'description' => 'Persetujuan budget oleh manager',
                'approver_type' => 'role',
                'approver_config' => [
                    'role_ids' => [$managerRole?->id]
                ],
                'is_required' => true,
                'can_skip' => false,
                'timeout_hours' => 48,
                'timeout_action' => 'approve',
                'is_active' => true,
            ]);
        }
    }
}
