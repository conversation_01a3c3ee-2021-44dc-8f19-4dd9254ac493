<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('asset_id')->constrained('assets')->onDelete('cascade');
            $table->foreignId('employee_id')->constrained('employees');
            $table->foreignId('assigned_by')->constrained('users');
            $table->datetime('assigned_at');
            $table->datetime('returned_at')->nullable();
            $table->foreignId('returned_by')->nullable()->constrained('users');
            $table->enum('status', ['active', 'returned', 'transferred'])->default('active');
            $table->text('assignment_notes')->nullable();
            $table->text('return_notes')->nullable();
            $table->enum('condition_when_assigned', ['excellent', 'good', 'fair', 'poor'])->nullable();
            $table->enum('condition_when_returned', ['excellent', 'good', 'fair', 'poor'])->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['asset_id', 'status']);
            $table->index(['employee_id', 'status']);
            $table->index(['assigned_at']);
            $table->index(['returned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_assignments');
    }
};
