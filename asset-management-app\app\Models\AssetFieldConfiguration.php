<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetFieldConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'field_name',
        'field_label',
        'field_type',
        'field_options',
        'is_required',
        'is_active',
        'sort_order',
        'field_group',
        'asset_category_id',
        'help_text',
        'validation_pattern',
        'validation_message',
        'input_mask',
        'placeholder_text',
        'min_length',
        'max_length',
        'validation_rules',
        'data_source_type',
        'data_source_table',
        'data_source_value_column',
        'data_source_label_column',
        'data_source_filter',
        'enable_dependent',
        'parent_field_id',
        'parent_field_conditions',
        'is_conditional',
        'conditional_logic',
        'column_width',
        'css_class',
    ];

    protected $casts = [
        'field_options' => 'array',
        'validation_rules' => 'array',
        'parent_field_conditions' => 'array',
        'conditional_logic' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
        'is_conditional' => 'boolean',
        'enable_dependent' => 'boolean',
    ];

    // Relationships
    public function assetCategory()
    {
        return $this->belongsTo(AssetCategory::class);
    }

    public function parentField()
    {
        return $this->belongsTo(AssetFieldConfiguration::class, 'parent_field_id');
    }

    public function childFields()
    {
        return $this->hasMany(AssetFieldConfiguration::class, 'parent_field_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForCategory($query, $categoryId)
    {
        return $query->where(function ($q) use ($categoryId) {
            $q->where('asset_category_id', $categoryId)
              ->orWhereNull('asset_category_id'); // Global fields
        });
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('field_label');
    }

    public function scopeByGroup($query, $group = null)
    {
        if ($group) {
            return $query->where('field_group', $group);
        }
        return $query;
    }

    // Helper methods
    public function getFieldTypeDisplayAttribute()
    {
        $types = [
            'text' => 'Text',
            'number' => 'Number',
            'email' => 'Email',
            'date' => 'Date',
            'datetime' => 'Date Time',
            'select' => 'Dropdown',
            'radio' => 'Radio Button',
            'checkbox' => 'Checkbox',
            'textarea' => 'Textarea',
            'file' => 'File Upload',
            'url' => 'URL',
            'tel' => 'Phone Number',
        ];

        return $types[$this->field_type] ?? ucfirst($this->field_type);
    }

    public function getValidationRulesStringAttribute()
    {
        if (!$this->validation_rules) {
            return '';
        }

        $rules = [];
        foreach ($this->validation_rules as $rule => $value) {
            if ($value === true) {
                $rules[] = $rule;
            } elseif ($value !== false && $value !== null) {
                $rules[] = "{$rule}:{$value}";
            }
        }

        return implode('|', $rules);
    }

    // Static methods
    public static function getFieldTypes()
    {
        return [
            'text' => 'Text',
            'number' => 'Number',
            'email' => 'Email',
            'date' => 'Date',
            'datetime' => 'Date Time',
            'select' => 'Dropdown',
            'radio' => 'Radio Button',
            'checkbox' => 'Checkbox',
            'textarea' => 'Textarea',
            'file' => 'File Upload',
            'url' => 'URL',
            'tel' => 'Phone Number',
        ];
    }

    public static function getFieldGroups()
    {
        try {
            $lookups = \App\Models\Lookup::where('lookup_code', 'FIELD_GROUP')
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->get();

            $groups = [];
            foreach ($lookups as $lookup) {
                $value = $lookup->metadata['value'] ?? strtolower(str_replace(' ', '_', $lookup->lookup_name));
                $groups[$value] = $lookup->lookup_name;
            }

            return $groups;
        } catch (\Exception $e) {
            // Fallback to hardcoded values if lookup fails
            return [
                'basic_info' => 'Basic Information',
                'technical_specs' => 'Technical Specifications',
                'purchase_info' => 'Purchase Information',
                'maintenance' => 'Maintenance',
                'location' => 'Location & Assignment',
                'custom' => 'Custom Fields',
            ];
        }
    }

    public static function getDataSourceTypes()
    {
        return [
            'manual' => 'Manual Input',
            'database' => 'Database Table',
            'lookup' => 'Master Lookup',
        ];
    }

    public static function getValidationPatterns()
    {
        return [
            'none' => [
                'name' => 'No Validation',
                'pattern' => '',
                'message' => '',
                'mask' => '',
                'placeholder' => ''
            ],
            'alphanumeric' => [
                'name' => 'Alphanumeric Only',
                'pattern' => '^[a-zA-Z0-9]+$',
                'message' => 'Only letters and numbers are allowed',
                'mask' => '',
                'placeholder' => 'Enter alphanumeric text'
            ],
            'letters_only' => [
                'name' => 'Letters Only',
                'pattern' => '^[a-zA-Z\s]+$',
                'message' => 'Only letters and spaces are allowed',
                'mask' => '',
                'placeholder' => 'Enter letters only'
            ],
            'numbers_only' => [
                'name' => 'Numbers Only',
                'pattern' => '^[0-9]+$',
                'message' => 'Only numbers are allowed',
                'mask' => '',
                'placeholder' => 'Enter numbers only'
            ],
            'phone_number' => [
                'name' => 'Phone Number',
                'pattern' => '^(\+62|62|0)[0-9]{9,12}$',
                'message' => 'Please enter a valid Indonesian phone number',
                'mask' => '+62-999-9999-9999',
                'placeholder' => '+62-812-3456-7890'
            ],
            'email' => [
                'name' => 'Email Address',
                'pattern' => '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                'message' => 'Please enter a valid email address',
                'mask' => '',
                'placeholder' => '<EMAIL>'
            ],
            'asset_code' => [
                'name' => 'Asset Code',
                'pattern' => '^[A-Z]{2,4}-[0-9]{4,6}$',
                'message' => 'Asset code format: ABC-1234',
                'mask' => 'AAA-9999',
                'placeholder' => 'ABC-1234'
            ],
            'serial_number' => [
                'name' => 'Serial Number',
                'pattern' => '^[A-Z0-9]{8,20}$',
                'message' => 'Serial number must be 8-20 alphanumeric characters',
                'mask' => '',
                'placeholder' => 'ABC123DEF456'
            ],
            'ip_address' => [
                'name' => 'IP Address',
                'pattern' => '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
                'message' => 'Please enter a valid IP address',
                'mask' => '999.999.999.999',
                'placeholder' => '***********'
            ],
            'mac_address' => [
                'name' => 'MAC Address',
                'pattern' => '^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$',
                'message' => 'Please enter a valid MAC address',
                'mask' => 'AA:AA:AA:AA:AA:AA',
                'placeholder' => '00:1B:44:11:3A:B7'
            ],
            'license_plate' => [
                'name' => 'License Plate (Indonesia)',
                'pattern' => '^[A-Z]{1,2}\s[0-9]{1,4}\s[A-Z]{1,3}$',
                'message' => 'License plate format: B 1234 ABC',
                'mask' => 'A 9999 AAA',
                'placeholder' => 'B 1234 ABC'
            ],
            'nik' => [
                'name' => 'NIK (Indonesia)',
                'pattern' => '^[0-9]{16}$',
                'message' => 'NIK must be exactly 16 digits',
                'mask' => '9999999999999999',
                'placeholder' => '1234567890123456'
            ],
            'custom' => [
                'name' => 'Custom Pattern',
                'pattern' => '',
                'message' => 'Please enter a valid value',
                'mask' => '',
                'placeholder' => ''
            ]
        ];
    }

    public function getFieldOptions()
    {
        switch ($this->data_source_type) {
            case 'database':
                return $this->getDatabaseOptions();
            case 'lookup':
                return $this->getLookupOptions();
            case 'manual':
            default:
                return $this->field_options['options'] ?? [];
        }
    }

    private function getDatabaseOptions()
    {
        if (!$this->data_source_table || !$this->data_source_value_column || !$this->data_source_label_column) {
            return [];
        }

        try {
            $query = \DB::table($this->data_source_table);

            // Apply filter if exists
            if ($this->data_source_filter) {
                $filters = json_decode($this->data_source_filter, true);
                if (is_array($filters)) {
                    foreach ($filters as $column => $value) {
                        $query->where($column, $value);
                    }
                }
            }

            $results = $query->select([
                $this->data_source_value_column . ' as value',
                $this->data_source_label_column . ' as label'
            ])->get();

            $options = [];
            foreach ($results as $result) {
                $options[$result->value] = $result->label;
            }

            return $options;
        } catch (\Exception $e) {
            \Log::error('Error getting database options for field: ' . $this->field_name, [
                'error' => $e->getMessage(),
                'table' => $this->data_source_table,
                'value_column' => $this->data_source_value_column,
                'label_column' => $this->data_source_label_column,
            ]);
            return [];
        }
    }

    private function getLookupOptions()
    {
        if (!$this->data_source_filter) {
            return [];
        }

        try {
            $lookupCode = $this->data_source_filter;
            $lookups = \App\Models\Lookup::where('lookup_code', $lookupCode)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->get();

            $options = [];
            foreach ($lookups as $lookup) {
                $options[$lookup->lookup_value] = $lookup->lookup_name;
            }

            return $options;
        } catch (\Exception $e) {
            \Log::error('Error getting lookup options for field: ' . $this->field_name, [
                'error' => $e->getMessage(),
                'lookup_code' => $this->data_source_filter,
            ]);
            return [];
        }
    }

    public function getAvailableTables()
    {
        try {
            $tables = \DB::select('SHOW TABLES');
            $tableNames = [];

            foreach ($tables as $table) {
                $tableName = array_values((array) $table)[0];
                $tableNames[$tableName] = ucwords(str_replace('_', ' ', $tableName));
            }

            return $tableNames;
        } catch (\Exception $e) {
            return [];
        }
    }

    public function getTableColumns($tableName)
    {
        try {
            // Sanitize table name to prevent SQL injection
            $tableName = preg_replace('/[^a-zA-Z0-9_]/', '', $tableName);

            if (empty($tableName)) {
                return [];
            }

            // Use schema builder for better compatibility
            $columns = \Schema::getColumnListing($tableName);
            $columnNames = [];

            foreach ($columns as $column) {
                $columnNames[$column] = ucwords(str_replace('_', ' ', $column));
            }

            return $columnNames;
        } catch (\Exception $e) {
            \Log::error('Error getting table columns', [
                'table' => $tableName,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    // Dependent Dropdown Methods
    public function getDependentOptions($parentValue = null)
    {
        if (!$this->parent_field_id || !$parentValue) {
            return $this->getFieldOptions();
        }

        // Get options based on parent value
        switch ($this->data_source_type) {
            case 'database':
                return $this->getDependentDatabaseOptions($parentValue);
            case 'lookup':
                return $this->getDependentLookupOptions($parentValue);
            case 'manual':
            default:
                return $this->getDependentManualOptions($parentValue);
        }
    }

    private function getDependentDatabaseOptions($parentValue)
    {
        if (!$this->data_source_table || !$this->data_source_value_column || !$this->data_source_label_column) {
            return [];
        }

        try {
            $query = \DB::table($this->data_source_table);

            // Add parent field condition
            if ($this->parent_field_conditions) {
                foreach ($this->parent_field_conditions as $condition) {
                    if (isset($condition['column']) && isset($condition['operator'])) {
                        $value = $condition['value'] === '{parent_value}' ? $parentValue : $condition['value'];
                        $query->where($condition['column'], $condition['operator'], $value);
                    }
                }
            }

            // Apply additional filters
            if ($this->data_source_filter) {
                $filters = json_decode($this->data_source_filter, true);
                if (is_array($filters)) {
                    foreach ($filters as $column => $value) {
                        $query->where($column, $value);
                    }
                }
            }

            $results = $query->select([
                $this->data_source_value_column . ' as value',
                $this->data_source_label_column . ' as label'
            ])->get();

            $options = [];
            foreach ($results as $result) {
                $options[$result->value] = $result->label;
            }

            return $options;
        } catch (\Exception $e) {
            \Log::error('Error getting dependent database options', [
                'field' => $this->field_name,
                'parent_value' => $parentValue,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    private function getDependentLookupOptions($parentValue)
    {
        try {
            $lookupCode = $this->data_source_filter;

            // If parent conditions exist, modify lookup code based on parent value
            if ($this->parent_field_conditions) {
                foreach ($this->parent_field_conditions as $condition) {
                    if ($condition['value'] === '{parent_value}') {
                        $lookupCode = str_replace('{parent_value}', $parentValue, $lookupCode);
                    }
                }
            }

            $lookups = \App\Models\Lookup::where('lookup_code', $lookupCode)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->get();

            $options = [];
            foreach ($lookups as $lookup) {
                $options[$lookup->lookup_value] = $lookup->lookup_name;
            }

            return $options;
        } catch (\Exception $e) {
            \Log::error('Error getting dependent lookup options', [
                'field' => $this->field_name,
                'parent_value' => $parentValue,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    private function getDependentManualOptions($parentValue)
    {
        if (!$this->field_options || !isset($this->field_options['dependent_options'])) {
            return $this->field_options['options'] ?? [];
        }

        $dependentOptions = $this->field_options['dependent_options'];
        return $dependentOptions[$parentValue] ?? [];
    }

    public function isDependent()
    {
        return $this->enable_dependent && !is_null($this->parent_field_id);
    }

    public function hasChildren()
    {
        return $this->childFields()->exists();
    }

    public function getColumnClass()
    {
        return 'col-md-' . $this->column_width;
    }

    public function shouldShow($formData = [])
    {
        if (!$this->is_conditional || !$this->conditional_logic) {
            return true;
        }

        $logic = $this->conditional_logic;
        $operator = $logic['operator'] ?? 'and'; // 'and' or 'or'
        $rules = $logic['rules'] ?? [];

        $results = [];
        foreach ($rules as $rule) {
            $fieldName = $rule['field'] ?? '';
            $condition = $rule['condition'] ?? 'equals';
            $value = $rule['value'] ?? '';

            $fieldValue = $formData[$fieldName] ?? '';

            switch ($condition) {
                case 'equals':
                    $results[] = $fieldValue == $value;
                    break;
                case 'not_equals':
                    $results[] = $fieldValue != $value;
                    break;
                case 'contains':
                    $results[] = str_contains($fieldValue, $value);
                    break;
                case 'not_contains':
                    $results[] = !str_contains($fieldValue, $value);
                    break;
                case 'empty':
                    $results[] = empty($fieldValue);
                    break;
                case 'not_empty':
                    $results[] = !empty($fieldValue);
                    break;
                default:
                    $results[] = true;
            }
        }

        if ($operator === 'or') {
            return in_array(true, $results);
        } else {
            return !in_array(false, $results);
        }
    }
}
