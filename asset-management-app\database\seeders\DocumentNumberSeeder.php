<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DocumentNumberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get branches
        $jakartaBranch = \App\Models\Branch::where('code', 'JKT')->first();
        $surabayaBranch = \App\Models\Branch::where('code', 'SBY')->first();

        // Get categories and asset types
        $pcLaptopCategory = \App\Models\AssetCategory::where('code', '511')->first();
        $furnitureCategory = \App\Models\AssetCategory::where('code', '512')->first();

        $laptopType = \App\Models\AssetType::where('code', '01')->where('asset_category_id', $pcLaptopCategory?->id)->first();
        $desktopType = \App\Models\AssetType::where('code', '02')->where('asset_category_id', $pcLaptopCategory?->id)->first();
        $deskType = \App\Models\AssetType::where('code', '01')->where('asset_category_id', $furnitureCategory?->id)->first();

        $documentNumbers = [
            // Asset numbers for Jakarta branch
            [
                'document_type' => 'asset',
                'format' => '{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}',
                'current_number' => 3,
                'year' => 2025,
                'branch_id' => $jakartaBranch?->id,
                'asset_category_id' => $pcLaptopCategory?->id,
                'asset_type_id' => $laptopType?->id,
                'description' => 'Nomor asset untuk Laptop di Jakarta',
                'is_active' => true,
            ],
            [
                'document_type' => 'asset',
                'format' => '{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}',
                'current_number' => 5,
                'year' => 2025,
                'branch_id' => $jakartaBranch?->id,
                'asset_category_id' => $pcLaptopCategory?->id,
                'asset_type_id' => $desktopType?->id,
                'description' => 'Nomor asset untuk Desktop di Jakarta',
                'is_active' => true,
            ],

            // Asset numbers for Surabaya branch
            [
                'document_type' => 'asset',
                'format' => '{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}',
                'current_number' => 1,
                'year' => 2025,
                'branch_id' => $surabayaBranch?->id,
                'asset_category_id' => $pcLaptopCategory?->id,
                'asset_type_id' => $laptopType?->id,
                'description' => 'Nomor asset untuk Laptop di Surabaya',
                'is_active' => true,
            ],

            // Furniture for Jakarta
            [
                'document_type' => 'asset',
                'format' => '{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}',
                'current_number' => 12,
                'year' => 2025,
                'branch_id' => $jakartaBranch?->id,
                'asset_category_id' => $furnitureCategory?->id,
                'asset_type_id' => $deskType?->id,
                'description' => 'Nomor asset untuk Meja di Jakarta',
                'is_active' => true,
            ],

            // Maintenance Numbers
            [
                'document_type' => 'maintenance',
                'format' => '{company_code}-{branch_code}-MTC-{category_code}-{year}{month}-{number}',
                'current_number' => 0,
                'year' => 2025,
                'branch_id' => $jakartaBranch?->id,
                'asset_category_id' => $computerCategory?->id,
                'asset_type_id' => null,
                'description' => 'Nomor maintenance untuk Computer di Jakarta',
                'is_active' => true,
            ],
            [
                'document_type' => 'maintenance',
                'format' => '{company_code}-{branch_code}-MTC-{category_code}-{year}{month}-{number}',
                'current_number' => 0,
                'year' => 2025,
                'branch_id' => $jakartaBranch?->id,
                'asset_category_id' => $furnitureCategory?->id,
                'asset_type_id' => null,
                'description' => 'Nomor maintenance untuk Furniture di Jakarta',
                'is_active' => true,
            ],
        ];

        foreach ($documentNumbers as $docNumber) {
            if ($docNumber['branch_id'] && $docNumber['asset_category_id'] && $docNumber['asset_type_id']) {
                \App\Models\DocumentNumber::create($docNumber);
            }
        }
    }
}
