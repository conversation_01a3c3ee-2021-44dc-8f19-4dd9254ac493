<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetMaintenance extends Model
{
    use HasFactory;

    protected $fillable = [
        'maintenance_number',
        'asset_id',
        'supplier_id',
        'maintenance_type',
        'title',
        'description',
        'problem_description',
        'solution_description',
        'estimated_cost',
        'actual_cost',
        'scheduled_date',
        'started_date',
        'completed_date',
        'priority',
        'status',
        'previous_asset_status',
        'notes',
        'attachments',
        'requested_by',
        'assigned_to',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'scheduled_date' => 'date',
        'started_date' => 'date',
        'completed_date' => 'date',
        'approved_at' => 'datetime',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'attachments' => 'array',
    ];

    // Relationships
    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Accessors
    public function getMaintenanceTypeTextAttribute()
    {
        // Return the maintenance_type as is since it's now stored as lookup_name
        return $this->maintenance_type;
    }

    public function getPriorityTextAttribute()
    {
        return match($this->priority) {
            'low' => 'Rendah',
            'medium' => 'Sedang',
            'high' => 'Tinggi',
            'critical' => 'Kritis',
            default => ucfirst($this->priority)
        };
    }

    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'scheduled' => 'Terjadwal',
            'in_progress' => 'Sedang Berlangsung',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
            default => ucfirst($this->status)
        };
    }

    public function getPriorityBadgeClassAttribute()
    {
        return match($this->priority) {
            'low' => 'bg-success',
            'medium' => 'bg-warning',
            'high' => 'bg-danger',
            'critical' => 'bg-dark',
            default => 'bg-secondary'
        };
    }

    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'scheduled' => 'bg-info',
            'in_progress' => 'bg-warning',
            'completed' => 'bg-success',
            'cancelled' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByMaintenanceType($query, $type)
    {
        return $query->where('maintenance_type', $type);
    }

    public function scopeScheduledBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('scheduled_date', [$startDate, $endDate]);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'scheduled')
                    ->where('scheduled_date', '<', now()->toDateString());
    }

    public function scopeUpcoming($query, $days = 7)
    {
        return $query->where('status', 'scheduled')
                    ->whereBetween('scheduled_date', [
                        now()->toDateString(),
                        now()->addDays($days)->toDateString()
                    ]);
    }

    // Static methods
    public static function generateMaintenanceNumber($assetId = null)
    {
        try {
            // Get asset information for branch and category
            $asset = null;
            $branchId = 1; // Default branch
            $categoryId = null;

            if ($assetId) {
                $asset = \App\Models\Asset::with(['branch', 'assetCategory'])->find($assetId);
                if ($asset) {
                    $branchId = $asset->branch_id ?? 1;
                    $categoryId = $asset->asset_category_id;
                }
            }

            // Use DocumentNumber system
            return \App\Models\DocumentNumber::getMaintenanceNumber($branchId, $categoryId);
        } catch (\Exception $e) {
            \Log::error('Error generating maintenance number: ' . $e->getMessage(), [
                'asset_id' => $assetId,
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback to old system
            $prefix = 'MNT';
            $year = date('Y');
            $month = date('m');

            $lastMaintenance = self::where('maintenance_number', 'like', $prefix . '-' . $year . $month . '%')
                                  ->orderBy('maintenance_number', 'desc')
                                  ->first();

            if ($lastMaintenance) {
                $lastNumber = (int) substr($lastMaintenance->maintenance_number, -4);
                $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
            } else {
                $newNumber = '0001';
            }

            return $prefix . '-' . $year . $month . '-' . $newNumber;
        }
    }

    public static function getMaintenanceTypes()
    {
        // Get maintenance types from lookup table
        return \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')
                                ->where('is_active', true)
                                ->orderBy('sort_order')
                                ->pluck('lookup_name', 'lookup_name')
                                ->toArray();
    }

    public static function getPriorities()
    {
        return [
            'low' => 'Rendah',
            'medium' => 'Sedang',
            'high' => 'Tinggi',
            'critical' => 'Kritis',
        ];
    }

    public static function getStatuses()
    {
        return [
            'scheduled' => 'Terjadwal',
            'in_progress' => 'Sedang Berlangsung',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
        ];
    }

    // Business logic methods
    public function canBeStarted()
    {
        return $this->status === 'scheduled';
    }

    public function canBeCompleted()
    {
        return $this->status === 'in_progress';
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['scheduled', 'in_progress']);
    }

    public function isOverdue()
    {
        return $this->status === 'scheduled' && $this->scheduled_date < now()->toDateString();
    }

    public function getDurationInDays()
    {
        if ($this->started_date && $this->completed_date) {
            return $this->completed_date->diffInDays($this->started_date);
        }
        return null;
    }
}
