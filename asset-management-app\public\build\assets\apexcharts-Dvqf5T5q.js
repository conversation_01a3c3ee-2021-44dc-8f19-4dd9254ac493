import{g as zt}from"./_commonjsHelpers-Cpj98o6Y.js";var Te={exports:{}};/*!
 * ApexCharts v3.28.3
 * (c) 2018-2022 ApexCharts
 * Released under the MIT License.
 */(function(pe,qe){function Ie(v,e){var t=Object.keys(v);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(v);e&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(v,a).enumerable})),t.push.apply(t,i)}return t}function F(v){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Ie(Object(t),!0).forEach(function(i){de(v,i,t[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(v,Object.getOwnPropertyDescriptors(t)):Ie(Object(t)).forEach(function(i){Object.defineProperty(v,i,Object.getOwnPropertyDescriptor(t,i))})}return v}function N(v){return(N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(v)}function X(v,e){if(!(v instanceof e))throw new TypeError("Cannot call a class as a function")}function Me(v,e){for(var t=0;t<e.length;t++){var i=e[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(v,i.key,i)}}function E(v,e,t){return e&&Me(v.prototype,e),t&&Me(v,t),v}function de(v,e,t){return e in v?Object.defineProperty(v,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):v[e]=t,v}function se(v,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");v.prototype=Object.create(e&&e.prototype,{constructor:{value:v,writable:!0,configurable:!0}}),e&&ze(v,e)}function xe(v){return(xe=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(v)}function ze(v,e){return(ze=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(v,e)}function Ze(v,e){if(e&&(typeof e=="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(v)}function re(v){var e=function(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}();return function(){var t,i=xe(v);if(e){var a=xe(this).constructor;t=Reflect.construct(i,arguments,a)}else t=i.apply(this,arguments);return Ze(this,t)}}function U(v){return function(e){if(Array.isArray(e))return be(e)}(v)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(v)||function(e,t){if(e){if(typeof e=="string")return be(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);if(i==="Object"&&e.constructor&&(i=e.constructor.name),i==="Map"||i==="Set")return Array.from(e);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return be(e,t)}}(v)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function be(v,e){(e==null||e>v.length)&&(e=v.length);for(var t=0,i=new Array(e);t<e;t++)i[t]=v[t];return i}var L=function(){function v(){X(this,v)}return E(v,[{key:"shadeRGBColor",value:function(e,t){var i=t.split(","),a=e<0?0:255,s=e<0?-1*e:e,n=parseInt(i[0].slice(4),10),r=parseInt(i[1],10),o=parseInt(i[2],10);return"rgb("+(Math.round((a-n)*s)+n)+","+(Math.round((a-r)*s)+r)+","+(Math.round((a-o)*s)+o)+")"}},{key:"shadeHexColor",value:function(e,t){var i=parseInt(t.slice(1),16),a=e<0?0:255,s=e<0?-1*e:e,n=i>>16,r=i>>8&255,o=255&i;return"#"+(16777216+65536*(Math.round((a-n)*s)+n)+256*(Math.round((a-r)*s)+r)+(Math.round((a-o)*s)+o)).toString(16).slice(1)}},{key:"shadeColor",value:function(e,t){return v.isColorHex(t)?this.shadeHexColor(e,t):this.shadeRGBColor(e,t)}}],[{key:"bind",value:function(e,t){return function(){return e.apply(t,arguments)}}},{key:"isObject",value:function(e){return e&&N(e)==="object"&&!Array.isArray(e)&&e!=null}},{key:"listToArray",value:function(e){var t,i=[];for(t=0;t<e.length;t++)i[t]=e[t];return i}},{key:"extend",value:function(e,t){var i=this;typeof Object.assign!="function"&&(Object.assign=function(s){if(s==null)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(s),r=1;r<arguments.length;r++){var o=arguments[r];if(o!=null)for(var h in o)o.hasOwnProperty(h)&&(n[h]=o[h])}return n});var a=Object.assign({},e);return this.isObject(e)&&this.isObject(t)&&Object.keys(t).forEach(function(s){i.isObject(t[s])&&s in e?a[s]=i.extend(e[s],t[s]):Object.assign(a,de({},s,t[s]))}),a}},{key:"extendArray",value:function(e,t){var i=[];return e.map(function(a){i.push(v.extend(t,a))}),e=i}},{key:"monthMod",value:function(e){return e%12}},{key:"clone",value:function(e){if(Object.prototype.toString.call(e)==="[object Array]"){for(var t=[],i=0;i<e.length;i++)t[i]=this.clone(e[i]);return t}if(Object.prototype.toString.call(e)==="[object Null]")return null;if(Object.prototype.toString.call(e)==="[object Date]")return e;if(N(e)==="object"){var a={};for(var s in e)e.hasOwnProperty(s)&&(a[s]=this.clone(e[s]));return a}return e}},{key:"log10",value:function(e){return Math.log(e)/Math.LN10}},{key:"roundToBase10",value:function(e){return Math.pow(10,Math.floor(Math.log10(e)))}},{key:"roundToBase",value:function(e,t){return Math.pow(t,Math.floor(Math.log(e)/Math.log(t)))}},{key:"parseNumber",value:function(e){return e===null?e:parseFloat(e)}},{key:"randomId",value:function(){return(Math.random()+1).toString(36).substring(4)}},{key:"noExponents",value:function(e){var t=String(e).split(/[eE]/);if(t.length===1)return t[0];var i="",a=e<0?"-":"",s=t[0].replace(".",""),n=Number(t[1])+1;if(n<0){for(i=a+"0.";n++;)i+="0";return i+s.replace(/^-/,"")}for(n-=s.length;n--;)i+="0";return s+i}},{key:"getDimensions",value:function(e){var t=getComputedStyle(e,null),i=e.clientHeight,a=e.clientWidth;return i-=parseFloat(t.paddingTop)+parseFloat(t.paddingBottom),[a-=parseFloat(t.paddingLeft)+parseFloat(t.paddingRight),i]}},{key:"getBoundingClientRect",value:function(e){var t=e.getBoundingClientRect();return{top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:e.clientWidth,height:e.clientHeight,x:t.left,y:t.top}}},{key:"getLargestStringFromArr",value:function(e){return e.reduce(function(t,i){return Array.isArray(i)&&(i=i.reduce(function(a,s){return a.length>s.length?a:s})),t.length>i.length?t:i},0)}},{key:"hexToRgba",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"#999999",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:.6;e.substring(0,1)!=="#"&&(e="#999999");var i=e.replace("#","");i=i.match(new RegExp("(.{"+i.length/3+"})","g"));for(var a=0;a<i.length;a++)i[a]=parseInt(i[a].length===1?i[a]+i[a]:i[a],16);return t!==void 0&&i.push(t),"rgba("+i.join(",")+")"}},{key:"getOpacityFromRGBA",value:function(e){return parseFloat(e.replace(/^.*,(.+)\)/,"$1"))}},{key:"rgb2hex",value:function(e){return(e=e.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i))&&e.length===4?"#"+("0"+parseInt(e[1],10).toString(16)).slice(-2)+("0"+parseInt(e[2],10).toString(16)).slice(-2)+("0"+parseInt(e[3],10).toString(16)).slice(-2):""}},{key:"isColorHex",value:function(e){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)|(^#[0-9A-F]{8}$)/i.test(e)}},{key:"getPolygonPos",value:function(e,t){for(var i=[],a=2*Math.PI/t,s=0;s<t;s++){var n={};n.x=e*Math.sin(s*a),n.y=-e*Math.cos(s*a),i.push(n)}return i}},{key:"polarToCartesian",value:function(e,t,i,a){var s=(a-90)*Math.PI/180;return{x:e+i*Math.cos(s),y:t+i*Math.sin(s)}}},{key:"escapeString",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"x",i=e.toString().slice();return i=i.replace(/[` ~!@#$%^&*()_|+\-=?;:'",.<>{}[\]\\/]/gi,t)}},{key:"negToZero",value:function(e){return e<0?0:e}},{key:"moveIndexInArray",value:function(e,t,i){if(i>=e.length)for(var a=i-e.length+1;a--;)e.push(void 0);return e.splice(i,0,e.splice(t,1)[0]),e}},{key:"extractNumber",value:function(e){return parseFloat(e.replace(/[^\d.]*/g,""))}},{key:"findAncestor",value:function(e,t){for(;(e=e.parentElement)&&!e.classList.contains(t););return e}},{key:"setELstyles",value:function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e.style.key=t[i])}},{key:"isNumber",value:function(e){return!isNaN(e)&&parseFloat(Number(e))===e&&!isNaN(parseInt(e,10))}},{key:"isFloat",value:function(e){return Number(e)===e&&e%1!=0}},{key:"isSafari",value:function(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}},{key:"isFirefox",value:function(){return navigator.userAgent.toLowerCase().indexOf("firefox")>-1}},{key:"isIE11",value:function(){if(window.navigator.userAgent.indexOf("MSIE")!==-1||window.navigator.appVersion.indexOf("Trident/")>-1)return!0}},{key:"isIE",value:function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);if(e.indexOf("Trident/")>0){var i=e.indexOf("rv:");return parseInt(e.substring(i+3,e.indexOf(".",i)),10)}var a=e.indexOf("Edge/");return a>0&&parseInt(e.substring(a+5,e.indexOf(".",a)),10)}}]),v}(),K=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.setEasingFunctions()}return E(v,[{key:"setEasingFunctions",value:function(){var e;if(!this.w.globals.easing){switch(this.w.config.chart.animations.easing){case"linear":e="-";break;case"easein":e="<";break;case"easeout":e=">";break;case"easeinout":e="<>";break;case"swing":e=function(t){var i=1.70158;return(t-=1)*t*((i+1)*t+i)+1};break;case"bounce":e=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375};break;case"elastic":e=function(t){return t===!!t?t:Math.pow(2,-10*t)*Math.sin((t-.075)*(2*Math.PI)/.3)+1};break;default:e="<>"}this.w.globals.easing=e}}},{key:"animateLine",value:function(e,t,i,a){e.attr(t).animate(a).attr(i)}},{key:"animateMarker",value:function(e,t,i,a,s,n){t||(t=0),e.attr({r:t,width:t,height:t}).animate(a,s).attr({r:i,width:i.width,height:i.height}).afterAll(function(){n()})}},{key:"animateCircle",value:function(e,t,i,a,s){e.attr({r:t.r,cx:t.cx,cy:t.cy}).animate(a,s).attr({r:i.r,cx:i.cx,cy:i.cy})}},{key:"animateRect",value:function(e,t,i,a,s){e.attr(t).animate(a).attr(i).afterAll(function(){return s()})}},{key:"animatePathsGradually",value:function(e){var t=e.el,i=e.realIndex,a=e.j,s=e.fill,n=e.pathFrom,r=e.pathTo,o=e.speed,h=e.delay,c=this.w,d=0;c.config.chart.animations.animateGradually.enabled&&(d=c.config.chart.animations.animateGradually.delay),c.config.chart.animations.dynamicAnimation.enabled&&c.globals.dataChanged&&c.config.chart.type!=="bar"&&(d=0),this.morphSVG(t,i,a,c.config.chart.type!=="line"||c.globals.comboCharts?s:"stroke",n,r,o,h*d)}},{key:"showDelayedElements",value:function(){this.w.globals.delayedElements.forEach(function(e){e.el.classList.remove("apexcharts-element-hidden")})}},{key:"animationCompleted",value:function(e){var t=this.w;t.globals.animationEnded||(t.globals.animationEnded=!0,this.showDelayedElements(),typeof t.config.chart.events.animationEnd=="function"&&t.config.chart.events.animationEnd(this.ctx,{el:e,w:t}))}},{key:"morphSVG",value:function(e,t,i,a,s,n,r,o){var h=this,c=this.w;s||(s=e.attr("pathFrom")),n||(n=e.attr("pathTo"));var d=function(g){return c.config.chart.type==="radar"&&(r=1),"M 0 ".concat(c.globals.gridHeight)};(!s||s.indexOf("undefined")>-1||s.indexOf("NaN")>-1)&&(s=d()),(!n||n.indexOf("undefined")>-1||n.indexOf("NaN")>-1)&&(n=d()),c.globals.shouldAnimate||(r=1),e.plot(s).animate(1,c.globals.easing,o).plot(s).animate(r,c.globals.easing,o).plot(n).afterAll(function(){L.isNumber(i)?i===c.globals.series[c.globals.maxValsInArrayIndex].length-2&&c.globals.shouldAnimate&&h.animationCompleted(e):a!=="none"&&c.globals.shouldAnimate&&(!c.globals.comboCharts&&t===c.globals.series.length-1||c.globals.comboCharts)&&h.animationCompleted(e),h.showDelayedElements()})}}]),v}(),O=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"getDefaultFilter",value:function(e,t){var i=this.w;e.unfilter(!0),new window.SVG.Filter().size("120%","180%","-5%","-40%"),i.config.states.normal.filter!=="none"?this.applyFilter(e,t,i.config.states.normal.filter.type,i.config.states.normal.filter.value):i.config.chart.dropShadow.enabled&&this.dropShadow(e,i.config.chart.dropShadow,t)}},{key:"addNormalFilter",value:function(e,t){var i=this.w;i.config.chart.dropShadow.enabled&&!e.node.classList.contains("apexcharts-marker")&&this.dropShadow(e,i.config.chart.dropShadow,t)}},{key:"addLightenFilter",value:function(e,t,i){var a=this,s=this.w,n=i.intensity;e.unfilter(!0),new window.SVG.Filter,e.filter(function(r){var o=s.config.chart.dropShadow;(o.enabled?a.addShadow(r,t,o):r).componentTransfer({rgb:{type:"linear",slope:1.5,intercept:n}})}),e.filterer.node.setAttribute("filterUnits","userSpaceOnUse"),this._scaleFilterSize(e.filterer.node)}},{key:"addDarkenFilter",value:function(e,t,i){var a=this,s=this.w,n=i.intensity;e.unfilter(!0),new window.SVG.Filter,e.filter(function(r){var o=s.config.chart.dropShadow;(o.enabled?a.addShadow(r,t,o):r).componentTransfer({rgb:{type:"linear",slope:n}})}),e.filterer.node.setAttribute("filterUnits","userSpaceOnUse"),this._scaleFilterSize(e.filterer.node)}},{key:"applyFilter",value:function(e,t,i){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:.5;switch(i){case"none":this.addNormalFilter(e,t);break;case"lighten":this.addLightenFilter(e,t,{intensity:a});break;case"darken":this.addDarkenFilter(e,t,{intensity:a})}}},{key:"addShadow",value:function(e,t,i){var a=i.blur,s=i.top,n=i.left,r=i.color,o=i.opacity,h=e.flood(Array.isArray(r)?r[t]:r,o).composite(e.sourceAlpha,"in").offset(n,s).gaussianBlur(a).merge(e.source);return e.blend(e.source,h)}},{key:"dropShadow",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,a=t.top,s=t.left,n=t.blur,r=t.color,o=t.opacity,h=t.noUserSpaceOnUse,c=this.w;return e.unfilter(!0),L.isIE()&&c.config.chart.type==="radialBar"||(r=Array.isArray(r)?r[i]:r,e.filter(function(d){var g=null;g=L.isSafari()||L.isFirefox()||L.isIE()?d.flood(r,o).composite(d.sourceAlpha,"in").offset(s,a).gaussianBlur(n):d.flood(r,o).composite(d.sourceAlpha,"in").offset(s,a).gaussianBlur(n).merge(d.source),d.blend(d.source,g)}),h||e.filterer.node.setAttribute("filterUnits","userSpaceOnUse"),this._scaleFilterSize(e.filterer.node)),e}},{key:"setSelectionFilter",value:function(e,t,i){var a=this.w;if(a.globals.selectedDataPoints[t]!==void 0&&a.globals.selectedDataPoints[t].indexOf(i)>-1){e.node.setAttribute("selected",!0);var s=a.config.states.active.filter;s!=="none"&&this.applyFilter(e,t,s.type,s.value)}}},{key:"_scaleFilterSize",value:function(e){(function(t){for(var i in t)t.hasOwnProperty(i)&&e.setAttribute(i,t[i])})({width:"200%",height:"200%",x:"-50%",y:"-50%"})}}]),v}(),I=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"drawLine",value:function(e,t,i,a){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"#a8a8a8",n=arguments.length>5&&arguments[5]!==void 0?arguments[5]:0,r=arguments.length>6&&arguments[6]!==void 0?arguments[6]:null,o=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"butt",h=this.w,c=h.globals.dom.Paper.line().attr({x1:e,y1:t,x2:i,y2:a,stroke:s,"stroke-dasharray":n,"stroke-width":r,"stroke-linecap":o});return c}},{key:"drawRect",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,n=arguments.length>5&&arguments[5]!==void 0?arguments[5]:"#fefefe",r=arguments.length>6&&arguments[6]!==void 0?arguments[6]:1,o=arguments.length>7&&arguments[7]!==void 0?arguments[7]:null,h=arguments.length>8&&arguments[8]!==void 0?arguments[8]:null,c=arguments.length>9&&arguments[9]!==void 0?arguments[9]:0,d=this.w,g=d.globals.dom.Paper.rect();return g.attr({x:e,y:t,width:i>0?i:0,height:a>0?a:0,rx:s,ry:s,opacity:r,"stroke-width":o!==null?o:0,stroke:h!==null?h:"none","stroke-dasharray":c}),g.node.setAttribute("fill",n),g}},{key:"drawPolygon",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"#e1e1e1",i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"none",s=this.w,n=s.globals.dom.Paper.polygon(e).attr({fill:a,stroke:t,"stroke-width":i});return n}},{key:"drawCircle",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,i=this.w;e<0&&(e=0);var a=i.globals.dom.Paper.circle(2*e);return t!==null&&a.attr(t),a}},{key:"drawPath",value:function(e){var t=e.d,i=t===void 0?"":t,a=e.stroke,s=a===void 0?"#a8a8a8":a,n=e.strokeWidth,r=n===void 0?1:n,o=e.fill,h=e.fillOpacity,c=h===void 0?1:h,d=e.strokeOpacity,g=d===void 0?1:d,f=e.classes,p=e.strokeLinecap,x=p===void 0?null:p,b=e.strokeDashArray,y=b===void 0?0:b,k=this.w;return x===null&&(x=k.config.stroke.lineCap),(i.indexOf("undefined")>-1||i.indexOf("NaN")>-1)&&(i="M 0 ".concat(k.globals.gridHeight)),k.globals.dom.Paper.path(i).attr({fill:o,"fill-opacity":c,stroke:s,"stroke-opacity":g,"stroke-linecap":x,"stroke-width":r,"stroke-dasharray":y,class:f})}},{key:"group",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,t=this.w,i=t.globals.dom.Paper.group();return e!==null&&i.attr(e),i}},{key:"move",value:function(e,t){var i=["M",e,t].join(" ");return i}},{key:"line",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=null;return i===null?a=["L",e,t].join(" "):i==="H"?a=["H",e].join(" "):i==="V"&&(a=["V",t].join(" ")),a}},{key:"curve",value:function(e,t,i,a,s,n){var r=["C",e,t,i,a,s,n].join(" ");return r}},{key:"quadraticCurve",value:function(e,t,i,a){return["Q",e,t,i,a].join(" ")}},{key:"arc",value:function(e,t,i,a,s,n,r){var o=arguments.length>7&&arguments[7]!==void 0&&arguments[7],h="A";o&&(h="a");var c=[h,e,t,i,a,s,n,r].join(" ");return c}},{key:"renderPaths",value:function(e){var t,i=e.j,a=e.realIndex,s=e.pathFrom,n=e.pathTo,r=e.stroke,o=e.strokeWidth,h=e.strokeLinecap,c=e.fill,d=e.animationDelay,g=e.initialSpeed,f=e.dataChangeSpeed,p=e.className,x=e.shouldClipToGrid,b=x===void 0||x,y=e.bindEventsOnPaths,k=y===void 0||y,A=e.drawShadow,l=A===void 0||A,u=this.w,m=new O(this.ctx),w=new K(this.ctx),S=this.w.config.chart.animations.enabled,P=S&&this.w.config.chart.animations.dynamicAnimation.enabled,C=!!(S&&!u.globals.resized||P&&u.globals.dataChanged&&u.globals.shouldAnimate);C?t=s:(t=n,u.globals.animationEnded=!0);var M=u.config.stroke.dashArray,T=0;T=Array.isArray(M)?M[a]:u.config.stroke.dashArray;var z=this.drawPath({d:t,stroke:r,strokeWidth:o,fill:c,fillOpacity:1,classes:p,strokeLinecap:h,strokeDashArray:T});if(z.attr("index",a),b&&z.attr({"clip-path":"url(#gridRectMask".concat(u.globals.cuid,")")}),u.config.states.normal.filter.type!=="none")m.getDefaultFilter(z,a);else if(u.config.chart.dropShadow.enabled&&l&&(!u.config.chart.dropShadow.enabledOnSeries||u.config.chart.dropShadow.enabledOnSeries&&u.config.chart.dropShadow.enabledOnSeries.indexOf(a)!==-1)){var Y=u.config.chart.dropShadow;m.dropShadow(z,Y,a)}k&&(z.node.addEventListener("mouseenter",this.pathMouseEnter.bind(this,z)),z.node.addEventListener("mouseleave",this.pathMouseLeave.bind(this,z)),z.node.addEventListener("mousedown",this.pathMouseDown.bind(this,z))),z.attr({pathTo:n,pathFrom:s});var H={el:z,j:i,realIndex:a,pathFrom:s,pathTo:n,fill:c,strokeWidth:o,delay:d};return!S||u.globals.resized||u.globals.dataChanged?!u.globals.resized&&u.globals.dataChanged||w.showDelayedElements():w.animatePathsGradually(F(F({},H),{},{speed:g})),u.globals.dataChanged&&P&&C&&w.animatePathsGradually(F(F({},H),{},{speed:f})),z}},{key:"drawPattern",value:function(e,t,i){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"#a8a8a8",s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,n=this.w,r=n.globals.dom.Paper.pattern(t,i,function(o){e==="horizontalLines"?o.line(0,0,i,0).stroke({color:a,width:s+1}):e==="verticalLines"?o.line(0,0,0,t).stroke({color:a,width:s+1}):e==="slantedLines"?o.line(0,0,t,i).stroke({color:a,width:s}):e==="squares"?o.rect(t,i).fill("none").stroke({color:a,width:s}):e==="circles"&&o.circle(t).fill("none").stroke({color:a,width:s})});return r}},{key:"drawGradient",value:function(e,t,i,a,s){var n,r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:null,o=arguments.length>6&&arguments[6]!==void 0?arguments[6]:null,h=arguments.length>7&&arguments[7]!==void 0?arguments[7]:null,c=arguments.length>8&&arguments[8]!==void 0?arguments[8]:0,d=this.w;t.length<9&&t.indexOf("#")===0&&(t=L.hexToRgba(t,a)),i.length<9&&i.indexOf("#")===0&&(i=L.hexToRgba(i,s));var g=0,f=1,p=1,x=null;o!==null&&(g=o[0]!==void 0?o[0]/100:0,f=o[1]!==void 0?o[1]/100:1,p=o[2]!==void 0?o[2]/100:1,x=o[3]!==void 0?o[3]/100:null);var b=!(d.config.chart.type!=="donut"&&d.config.chart.type!=="pie"&&d.config.chart.type!=="polarArea"&&d.config.chart.type!=="bubble");if(n=h===null||h.length===0?d.globals.dom.Paper.gradient(b?"radial":"linear",function(A){A.at(g,t,a),A.at(f,i,s),A.at(p,i,s),x!==null&&A.at(x,t,a)}):d.globals.dom.Paper.gradient(b?"radial":"linear",function(A){(Array.isArray(h[c])?h[c]:h).forEach(function(l){A.at(l.offset/100,l.color,l.opacity)})}),b){var y=d.globals.gridWidth/2,k=d.globals.gridHeight/2;d.config.chart.type!=="bubble"?n.attr({gradientUnits:"userSpaceOnUse",cx:y,cy:k,r}):n.attr({cx:.5,cy:.5,r:.8,fx:.2,fy:.2})}else e==="vertical"?n.from(0,0).to(0,1):e==="diagonal"?n.from(0,0).to(1,1):e==="horizontal"?n.from(0,1).to(1,1):e==="diagonal2"&&n.from(1,0).to(0,1);return n}},{key:"drawText",value:function(e){var t,i=e.x,a=e.y,s=e.text,n=e.textAnchor,r=e.fontSize,o=e.fontFamily,h=e.fontWeight,c=e.foreColor,d=e.opacity,g=e.cssClass,f=g===void 0?"":g,p=e.isPlainText,x=p===void 0||p,b=this.w;return s===void 0&&(s=""),n||(n="start"),c&&c.length||(c=b.config.chart.foreColor),o=o||b.config.chart.fontFamily,h=h||"regular",(t=Array.isArray(s)?b.globals.dom.Paper.text(function(y){for(var k=0;k<s.length;k++)k===0?y.tspan(s[k]):y.tspan(s[k]).newLine()}):x?b.globals.dom.Paper.plain(s):b.globals.dom.Paper.text(function(y){return y.tspan(s)})).attr({x:i,y:a,"text-anchor":n,"dominant-baseline":"auto","font-size":r,"font-family":o,"font-weight":h,fill:c,class:"apexcharts-text "+f}),t.node.style.fontFamily=o,t.node.style.opacity=d,t}},{key:"drawMarker",value:function(e,t,i){e=e||0;var a=i.pSize||0,s=null;if(i.shape==="square"||i.shape==="rect"){var n=i.pRadius===void 0?a/2:i.pRadius;t!==null&&a||(a=0,n=0);var r=1.2*a+n,o=this.drawRect(r,r,r,r,n);o.attr({x:e-r/2,y:t-r/2,cx:e,cy:t,class:i.class?i.class:"",fill:i.pointFillColor,"fill-opacity":i.pointFillOpacity?i.pointFillOpacity:1,stroke:i.pointStrokeColor,"stroke-width":i.pointStrokeWidth?i.pointStrokeWidth:0,"stroke-opacity":i.pointStrokeOpacity?i.pointStrokeOpacity:1}),s=o}else i.shape!=="circle"&&i.shape||(L.isNumber(t)||(a=0,t=0),s=this.drawCircle(a,{cx:e,cy:t,class:i.class?i.class:"",stroke:i.pointStrokeColor,fill:i.pointFillColor,"fill-opacity":i.pointFillOpacity?i.pointFillOpacity:1,"stroke-width":i.pointStrokeWidth?i.pointStrokeWidth:0,"stroke-opacity":i.pointStrokeOpacity?i.pointStrokeOpacity:1}));return s}},{key:"pathMouseEnter",value:function(e,t){var i=this.w,a=new O(this.ctx),s=parseInt(e.node.getAttribute("index"),10),n=parseInt(e.node.getAttribute("j"),10);if(typeof i.config.chart.events.dataPointMouseEnter=="function"&&i.config.chart.events.dataPointMouseEnter(t,this.ctx,{seriesIndex:s,dataPointIndex:n,w:i}),this.ctx.events.fireEvent("dataPointMouseEnter",[t,this.ctx,{seriesIndex:s,dataPointIndex:n,w:i}]),(i.config.states.active.filter.type==="none"||e.node.getAttribute("selected")!=="true")&&i.config.states.hover.filter.type!=="none"&&i.config.states.active.filter.type!=="none"&&!i.globals.isTouchDevice){var r=i.config.states.hover.filter;a.applyFilter(e,s,r.type,r.value)}}},{key:"pathMouseLeave",value:function(e,t){var i=this.w,a=new O(this.ctx),s=parseInt(e.node.getAttribute("index"),10),n=parseInt(e.node.getAttribute("j"),10);typeof i.config.chart.events.dataPointMouseLeave=="function"&&i.config.chart.events.dataPointMouseLeave(t,this.ctx,{seriesIndex:s,dataPointIndex:n,w:i}),this.ctx.events.fireEvent("dataPointMouseLeave",[t,this.ctx,{seriesIndex:s,dataPointIndex:n,w:i}]),i.config.states.active.filter.type!=="none"&&e.node.getAttribute("selected")==="true"||i.config.states.hover.filter.type!=="none"&&a.getDefaultFilter(e,s)}},{key:"pathMouseDown",value:function(e,t){var i=this.w,a=new O(this.ctx),s=parseInt(e.node.getAttribute("index"),10),n=parseInt(e.node.getAttribute("j"),10),r="false";if(e.node.getAttribute("selected")==="true"){if(e.node.setAttribute("selected","false"),i.globals.selectedDataPoints[s].indexOf(n)>-1){var o=i.globals.selectedDataPoints[s].indexOf(n);i.globals.selectedDataPoints[s].splice(o,1)}}else{if(!i.config.states.active.allowMultipleDataPointsSelection&&i.globals.selectedDataPoints.length>0){i.globals.selectedDataPoints=[];var h=i.globals.dom.Paper.select(".apexcharts-series path").members,c=i.globals.dom.Paper.select(".apexcharts-series circle, .apexcharts-series rect").members,d=function(f){Array.prototype.forEach.call(f,function(p){p.node.setAttribute("selected","false"),a.getDefaultFilter(p,s)})};d(h),d(c)}e.node.setAttribute("selected","true"),r="true",i.globals.selectedDataPoints[s]===void 0&&(i.globals.selectedDataPoints[s]=[]),i.globals.selectedDataPoints[s].push(n)}if(r==="true"){var g=i.config.states.active.filter;g!=="none"&&a.applyFilter(e,s,g.type,g.value)}else i.config.states.active.filter.type!=="none"&&a.getDefaultFilter(e,s);typeof i.config.chart.events.dataPointSelection=="function"&&i.config.chart.events.dataPointSelection(t,this.ctx,{selectedDataPoints:i.globals.selectedDataPoints,seriesIndex:s,dataPointIndex:n,w:i}),t&&this.ctx.events.fireEvent("dataPointSelection",[t,this.ctx,{selectedDataPoints:i.globals.selectedDataPoints,seriesIndex:s,dataPointIndex:n,w:i}])}},{key:"rotateAroundCenter",value:function(e){var t=e.getBBox();return{x:t.x+t.width/2,y:t.y+t.height/2}}},{key:"getTextRects",value:function(e,t,i,a){var s=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],n=this.w,r=this.drawText({x:-200,y:-200,text:e,textAnchor:"start",fontSize:t,fontFamily:i,foreColor:"#fff",opacity:0});a&&r.attr("transform",a),n.globals.dom.Paper.add(r);var o=r.bbox();return s||(o=r.node.getBoundingClientRect()),r.remove(),{width:o.width,height:o.height}}},{key:"placeTextWithEllipsis",value:function(e,t,i){if(typeof e.getComputedTextLength=="function"&&(e.textContent=t,t.length>0&&e.getComputedTextLength()>=i/1.1)){for(var a=t.length-3;a>0;a-=3)if(e.getSubStringLength(0,a)<=i/1.1)return void(e.textContent=t.substring(0,a)+"...");e.textContent="."}}}],[{key:"setAttrs",value:function(e,t){for(var i in t)t.hasOwnProperty(i)&&e.setAttribute(i,t[i])}}]),v}(),$e=function(){function v(e){X(this,v),this.w=e.w,this.annoCtx=e}return E(v,[{key:"setOrientations",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,i=this.w;if(e.label.orientation==="vertical"){var a=t!==null?t:0,s=i.globals.dom.baseEl.querySelector(".apexcharts-xaxis-annotations .apexcharts-xaxis-annotation-label[rel='".concat(a,"']"));if(s!==null){var n=s.getBoundingClientRect();s.setAttribute("x",parseFloat(s.getAttribute("x"))-n.height+4),e.label.position==="top"?s.setAttribute("y",parseFloat(s.getAttribute("y"))+n.width):s.setAttribute("y",parseFloat(s.getAttribute("y"))-n.width);var r=this.annoCtx.graphics.rotateAroundCenter(s),o=r.x,h=r.y;s.setAttribute("transform","rotate(-90 ".concat(o," ").concat(h,")"))}}}},{key:"addBackgroundToAnno",value:function(e,t){var i=this.w;if(!e||!t.label.text||t.label.text&&!t.label.text.trim())return null;var a=i.globals.dom.baseEl.querySelector(".apexcharts-grid").getBoundingClientRect(),s=e.getBoundingClientRect(),n=t.label.style.padding.left,r=t.label.style.padding.right,o=t.label.style.padding.top,h=t.label.style.padding.bottom;t.label.orientation==="vertical"&&(o=t.label.style.padding.left,h=t.label.style.padding.right,n=t.label.style.padding.top,r=t.label.style.padding.bottom);var c=s.left-a.left-n,d=s.top-a.top-o,g=this.annoCtx.graphics.drawRect(c-i.globals.barPadForNumericAxis,d,s.width+n+r,s.height+o+h,t.label.borderRadius,t.label.style.background,1,t.label.borderWidth,t.label.borderColor,0);return t.id&&g.node.classList.add(t.id),g}},{key:"annotationsBackground",value:function(){var e=this,t=this.w,i=function(a,s,n){var r=t.globals.dom.baseEl.querySelector(".apexcharts-".concat(n,"-annotations .apexcharts-").concat(n,"-annotation-label[rel='").concat(s,"']"));if(r){var o=r.parentNode,h=e.addBackgroundToAnno(r,a);h&&o.insertBefore(h.node,r)}};t.config.annotations.xaxis.map(function(a,s){i(a,s,"xaxis")}),t.config.annotations.yaxis.map(function(a,s){i(a,s,"yaxis")}),t.config.annotations.points.map(function(a,s){i(a,s,"point")})}},{key:"getStringX",value:function(e){var t=this.w,i=e;t.config.xaxis.convertedCatToNumeric&&t.globals.categoryLabels.length&&(e=t.globals.categoryLabels.indexOf(e)+1);var a=t.globals.labels.indexOf(e),s=t.globals.dom.baseEl.querySelector(".apexcharts-xaxis-texts-g text:nth-child("+(a+1)+")");return s&&(i=parseFloat(s.getAttribute("x"))),i}}]),v}(),Je=function(){function v(e){X(this,v),this.w=e.w,this.annoCtx=e,this.invertAxis=this.annoCtx.invertAxis}return E(v,[{key:"addXaxisAnnotation",value:function(e,t,i){var a=this.w,s=this.invertAxis?a.globals.minY:a.globals.minX,n=this.invertAxis?a.globals.maxY:a.globals.maxX,r=this.invertAxis?a.globals.yRange[0]:a.globals.xRange,o=(e.x-s)/(r/a.globals.gridWidth);this.annoCtx.inversedReversedAxis&&(o=(n-e.x)/(r/a.globals.gridWidth));var h=e.label.text;a.config.xaxis.type!=="category"&&!a.config.xaxis.convertedCatToNumeric||this.invertAxis||a.globals.dataFormatXNumeric||(o=this.annoCtx.helpers.getStringX(e.x));var c=e.strokeDashArray;if(L.isNumber(o)){if(e.x2===null||e.x2===void 0){var d=this.annoCtx.graphics.drawLine(o+e.offsetX,0+e.offsetY,o+e.offsetX,a.globals.gridHeight+e.offsetY,e.borderColor,c,e.borderWidth);t.appendChild(d.node),e.id&&d.node.classList.add(e.id)}else{var g=(e.x2-s)/(r/a.globals.gridWidth);if(this.annoCtx.inversedReversedAxis&&(g=(n-e.x2)/(r/a.globals.gridWidth)),a.config.xaxis.type!=="category"&&!a.config.xaxis.convertedCatToNumeric||this.invertAxis||a.globals.dataFormatXNumeric||(g=this.annoCtx.helpers.getStringX(e.x2)),g<o){var f=o;o=g,g=f}var p=this.annoCtx.graphics.drawRect(o+e.offsetX,0+e.offsetY,g-o,a.globals.gridHeight+e.offsetY,0,e.fillColor,e.opacity,1,e.borderColor,c);p.node.classList.add("apexcharts-annotation-rect"),p.attr("clip-path","url(#gridRectMask".concat(a.globals.cuid,")")),t.appendChild(p.node),e.id&&p.node.classList.add(e.id)}var x=e.label.position==="top"?4:a.globals.gridHeight,b=this.annoCtx.graphics.getTextRects(h,parseFloat(e.label.style.fontSize)),y=this.annoCtx.graphics.drawText({x:o+e.label.offsetX,y:x+e.label.offsetY-(e.label.orientation==="vertical"?e.label.position==="top"?b.width/2-12:-b.width/2:0),text:h,textAnchor:e.label.textAnchor,fontSize:e.label.style.fontSize,fontFamily:e.label.style.fontFamily,fontWeight:e.label.style.fontWeight,foreColor:e.label.style.color,cssClass:"apexcharts-xaxis-annotation-label ".concat(e.label.style.cssClass," ").concat(e.id?e.id:"")});y.attr({rel:i}),t.appendChild(y.node),this.annoCtx.helpers.setOrientations(e,i)}}},{key:"drawXAxisAnnotations",value:function(){var e=this,t=this.w,i=this.annoCtx.graphics.group({class:"apexcharts-xaxis-annotations"});return t.config.annotations.xaxis.map(function(a,s){e.addXaxisAnnotation(a,i.node,s)}),i}}]),v}(),D=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"getStackedSeriesTotals",value:function(){var e=this.w,t=[];if(e.globals.series.length===0)return t;for(var i=0;i<e.globals.series[e.globals.maxValsInArrayIndex].length;i++){for(var a=0,s=0;s<e.globals.series.length;s++)e.globals.series[s][i]!==void 0&&(a+=e.globals.series[s][i]);t.push(a)}return e.globals.stackedSeriesTotals=t,t}},{key:"getSeriesTotalByIndex",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return e===null?this.w.config.series.reduce(function(t,i){return t+i},0):this.w.globals.series[e].reduce(function(t,i){return t+i},0)}},{key:"isSeriesNull",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return(e===null?this.w.config.series.filter(function(t){return t!==null}):this.w.config.series[e].data.filter(function(t){return t!==null})).length===0}},{key:"seriesHaveSameValues",value:function(e){return this.w.globals.series[e].every(function(t,i,a){return t===a[0]})}},{key:"getCategoryLabels",value:function(e){var t=this.w,i=e.slice();return t.config.xaxis.convertedCatToNumeric&&(i=e.map(function(a,s){return t.config.xaxis.labels.formatter(a-t.globals.minX+1)})),i}},{key:"getLargestSeries",value:function(){var e=this.w;e.globals.maxValsInArrayIndex=e.globals.series.map(function(t){return t.length}).indexOf(Math.max.apply(Math,e.globals.series.map(function(t){return t.length})))}},{key:"getLargestMarkerSize",value:function(){var e=this.w,t=0;return e.globals.markers.size.forEach(function(i){t=Math.max(t,i)}),e.globals.markers.largestSize=t,t}},{key:"getSeriesTotals",value:function(){var e=this.w;e.globals.seriesTotals=e.globals.series.map(function(t,i){var a=0;if(Array.isArray(t))for(var s=0;s<t.length;s++)a+=t[s];else a+=t;return a})}},{key:"getSeriesTotalsXRange",value:function(e,t){var i=this.w;return i.globals.series.map(function(a,s){for(var n=0,r=0;r<a.length;r++)i.globals.seriesX[s][r]>e&&i.globals.seriesX[s][r]<t&&(n+=a[r]);return n})}},{key:"getPercentSeries",value:function(){var e=this.w;e.globals.seriesPercent=e.globals.series.map(function(t,i){var a=[];if(Array.isArray(t))for(var s=0;s<t.length;s++){var n=e.globals.stackedSeriesTotals[s],r=0;n&&(r=100*t[s]/n),a.push(r)}else{var o=100*t/e.globals.seriesTotals.reduce(function(h,c){return h+c},0);a.push(o)}return a})}},{key:"getCalculatedRatios",value:function(){var e,t,i,a,s=this.w.globals,n=[],r=0,o=[],h=.1,c=0;if(s.yRange=[],s.isMultipleYAxis)for(var d=0;d<s.minYArr.length;d++)s.yRange.push(Math.abs(s.minYArr[d]-s.maxYArr[d])),o.push(0);else s.yRange.push(Math.abs(s.minY-s.maxY));s.xRange=Math.abs(s.maxX-s.minX),s.zRange=Math.abs(s.maxZ-s.minZ);for(var g=0;g<s.yRange.length;g++)n.push(s.yRange[g]/s.gridHeight);if(t=s.xRange/s.gridWidth,i=Math.abs(s.initialMaxX-s.initialMinX)/s.gridWidth,e=s.yRange/s.gridWidth,a=s.xRange/s.gridHeight,(r=s.zRange/s.gridHeight*16)||(r=1),s.minY!==Number.MIN_VALUE&&Math.abs(s.minY)!==0&&(s.hasNegs=!0),s.isMultipleYAxis){o=[];for(var f=0;f<n.length;f++)o.push(-s.minYArr[f]/n[f])}else o.push(-s.minY/n[0]),s.minY!==Number.MIN_VALUE&&Math.abs(s.minY)!==0&&(h=-s.minY/e,c=s.minX/t);return{yRatio:n,invertedYRatio:e,zRatio:r,xRatio:t,initialXRatio:i,invertedXRatio:a,baseLineInvertedY:h,baseLineY:o,baseLineX:c}}},{key:"getLogSeries",value:function(e){var t=this,i=this.w;return i.globals.seriesLog=e.map(function(a,s){return i.config.yaxis[s]&&i.config.yaxis[s].logarithmic?a.map(function(n){return n===null?null:t.getLogVal(n,s)}):a}),i.globals.invalidLogScale?e:i.globals.seriesLog}},{key:"getLogVal",value:function(e,t){var i=this.w;return(Math.log(e)-Math.log(i.globals.minYArr[t]))/(Math.log(i.globals.maxYArr[t])-Math.log(i.globals.minYArr[t]))}},{key:"getLogYRatios",value:function(e){var t=this,i=this.w,a=this.w.globals;return a.yLogRatio=e.slice(),a.logYRange=a.yRange.map(function(s,n){if(i.config.yaxis[n]&&t.w.config.yaxis[n].logarithmic){var r,o=-Number.MAX_VALUE,h=Number.MIN_VALUE;return a.seriesLog.forEach(function(c,d){c.forEach(function(g){i.config.yaxis[d]&&i.config.yaxis[d].logarithmic&&(o=Math.max(g,o),h=Math.min(g,h))})}),r=Math.pow(a.yRange[n],Math.abs(h-o)/a.yRange[n]),a.yLogRatio[n]=r/a.gridHeight,r}}),a.invalidLogScale?e.slice():a.yLogRatio}}],[{key:"checkComboSeries",value:function(e){var t=!1,i=0,a=0;return e.length&&e[0].type!==void 0&&e.forEach(function(s){s.type!=="bar"&&s.type!=="column"&&s.type!=="candlestick"&&s.type!=="boxPlot"||i++,s.type!==void 0&&a++}),a>1&&(t=!0),{comboBarCount:i,comboCharts:t}}},{key:"extendArrayProps",value:function(e,t,i){return t.yaxis&&(t=e.extendYAxis(t,i)),t.annotations&&(t.annotations.yaxis&&(t=e.extendYAxisAnnotations(t)),t.annotations.xaxis&&(t=e.extendXAxisAnnotations(t)),t.annotations.points&&(t=e.extendPointAnnotations(t))),t}}]),v}(),Qe=function(){function v(e){X(this,v),this.w=e.w,this.annoCtx=e}return E(v,[{key:"addYaxisAnnotation",value:function(e,t,i){var a,s=this.w,n=e.strokeDashArray,r=this._getY1Y2("y1",e),o=e.label.text;if(e.y2===null||e.y2===void 0){var h=this.annoCtx.graphics.drawLine(0+e.offsetX,r+e.offsetY,this._getYAxisAnnotationWidth(e),r+e.offsetY,e.borderColor,n,e.borderWidth);t.appendChild(h.node),e.id&&h.node.classList.add(e.id)}else{if((a=this._getY1Y2("y2",e))>r){var c=r;r=a,a=c}var d=this.annoCtx.graphics.drawRect(0+e.offsetX,a+e.offsetY,this._getYAxisAnnotationWidth(e),r-a,0,e.fillColor,e.opacity,1,e.borderColor,n);d.node.classList.add("apexcharts-annotation-rect"),d.attr("clip-path","url(#gridRectMask".concat(s.globals.cuid,")")),t.appendChild(d.node),e.id&&d.node.classList.add(e.id)}var g=e.label.position==="right"?s.globals.gridWidth:0,f=this.annoCtx.graphics.drawText({x:g+e.label.offsetX,y:(a||r)+e.label.offsetY-3,text:o,textAnchor:e.label.textAnchor,fontSize:e.label.style.fontSize,fontFamily:e.label.style.fontFamily,fontWeight:e.label.style.fontWeight,foreColor:e.label.style.color,cssClass:"apexcharts-yaxis-annotation-label ".concat(e.label.style.cssClass," ").concat(e.id?e.id:"")});f.attr({rel:i}),t.appendChild(f.node)}},{key:"_getY1Y2",value:function(e,t){var i,a=e==="y1"?t.y:t.y2,s=this.w;if(this.annoCtx.invertAxis){var n=s.globals.labels.indexOf(a);s.config.xaxis.convertedCatToNumeric&&(n=s.globals.categoryLabels.indexOf(a));var r=s.globals.dom.baseEl.querySelector(".apexcharts-yaxis-texts-g text:nth-child("+(n+1)+")");r&&(i=parseFloat(r.getAttribute("y")))}else{var o;s.config.yaxis[t.yAxisIndex].logarithmic?o=(a=new D(this.annoCtx.ctx).getLogVal(a,t.yAxisIndex))/s.globals.yLogRatio[t.yAxisIndex]:o=(a-s.globals.minYArr[t.yAxisIndex])/(s.globals.yRange[t.yAxisIndex]/s.globals.gridHeight),i=s.globals.gridHeight-o,s.config.yaxis[t.yAxisIndex]&&s.config.yaxis[t.yAxisIndex].reversed&&(i=o)}return i}},{key:"_getYAxisAnnotationWidth",value:function(e){var t=this.w;return t.globals.gridWidth,(e.width.indexOf("%")>-1?t.globals.gridWidth*parseInt(e.width,10)/100:parseInt(e.width,10))+e.offsetX}},{key:"drawYAxisAnnotations",value:function(){var e=this,t=this.w,i=this.annoCtx.graphics.group({class:"apexcharts-yaxis-annotations"});return t.config.annotations.yaxis.map(function(a,s){e.addYaxisAnnotation(a,i.node,s)}),i}}]),v}(),Ke=function(){function v(e){X(this,v),this.w=e.w,this.annoCtx=e}return E(v,[{key:"addPointAnnotation",value:function(e,t,i){var a=this.w,s=0,n=0,r=0;this.annoCtx.invertAxis&&console.warn("Point annotation is not supported in horizontal bar charts.");var o=parseFloat(e.y);if(typeof e.x=="string"){var h=a.globals.labels.indexOf(e.x);a.config.xaxis.convertedCatToNumeric&&(h=a.globals.categoryLabels.indexOf(e.x)),s=this.annoCtx.helpers.getStringX(e.x),e.y===null&&(o=a.globals.series[e.seriesIndex][h])}else s=(e.x-a.globals.minX)/(a.globals.xRange/a.globals.gridWidth);for(var c,d=[],g=0,f=0;f<=e.seriesIndex;f++){var p=a.config.yaxis[f].seriesName;if(p)for(var x=f+1;x<=e.seriesIndex;x++)a.config.yaxis[x].seriesName===p&&d.indexOf(p)===-1&&(g++,d.push(p))}if(a.config.yaxis[e.yAxisIndex].logarithmic)c=(o=new D(this.annoCtx.ctx).getLogVal(o,e.yAxisIndex))/a.globals.yLogRatio[e.yAxisIndex];else{var b=e.yAxisIndex+g;c=(o-a.globals.minYArr[b])/(a.globals.yRange[b]/a.globals.gridHeight)}if(n=a.globals.gridHeight-c-parseFloat(e.label.style.fontSize)-e.marker.size,r=a.globals.gridHeight-c,a.config.yaxis[e.yAxisIndex]&&a.config.yaxis[e.yAxisIndex].reversed&&(n=c+parseFloat(e.label.style.fontSize)+e.marker.size,r=c),L.isNumber(s)){var y={pSize:e.marker.size,pointStrokeWidth:e.marker.strokeWidth,pointFillColor:e.marker.fillColor,pointStrokeColor:e.marker.strokeColor,shape:e.marker.shape,pRadius:e.marker.radius,class:"apexcharts-point-annotation-marker ".concat(e.marker.cssClass," ").concat(e.id?e.id:"")},k=this.annoCtx.graphics.drawMarker(s+e.marker.offsetX,r+e.marker.offsetY,y);t.appendChild(k.node);var A=e.label.text?e.label.text:"",l=this.annoCtx.graphics.drawText({x:s+e.label.offsetX,y:n+e.label.offsetY,text:A,textAnchor:e.label.textAnchor,fontSize:e.label.style.fontSize,fontFamily:e.label.style.fontFamily,fontWeight:e.label.style.fontWeight,foreColor:e.label.style.color,cssClass:"apexcharts-point-annotation-label ".concat(e.label.style.cssClass," ").concat(e.id?e.id:"")});if(l.attr({rel:i}),t.appendChild(l.node),e.customSVG.SVG){var u=this.annoCtx.graphics.group({class:"apexcharts-point-annotations-custom-svg "+e.customSVG.cssClass});u.attr({transform:"translate(".concat(s+e.customSVG.offsetX,", ").concat(n+e.customSVG.offsetY,")")}),u.node.innerHTML=e.customSVG.SVG,t.appendChild(u.node)}if(e.image.path){var m=e.image.width?e.image.width:20,w=e.image.height?e.image.height:20;this.annoCtx.addImage({x:s+e.image.offsetX-m/2,y:n+e.image.offsetY-w/2,width:m,height:w,path:e.image.path,appendTo:".apexcharts-point-annotations"})}}}},{key:"drawPointAnnotations",value:function(){var e=this,t=this.w,i=this.annoCtx.graphics.group({class:"apexcharts-point-annotations"});return t.config.annotations.points.map(function(a,s){e.addPointAnnotation(a,i.node,s)}),i}}]),v}(),Xe={name:"en",options:{months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],toolbar:{exportToSVG:"Download SVG",exportToPNG:"Download PNG",exportToCSV:"Download CSV",menu:"Menu",selection:"Selection",selectionZoom:"Selection Zoom",zoomIn:"Zoom In",zoomOut:"Zoom Out",pan:"Panning",reset:"Reset Zoom"}}},$=function(){function v(){X(this,v),this.yAxis={show:!0,showAlways:!1,showForNullSeries:!0,seriesName:void 0,opposite:!1,reversed:!1,logarithmic:!1,tickAmount:void 0,forceNiceScale:!1,max:void 0,min:void 0,floating:!1,decimalsInFloat:void 0,labels:{show:!0,minWidth:0,maxWidth:160,offsetX:0,offsetY:0,align:void 0,rotate:0,padding:20,style:{colors:[],fontSize:"11px",fontWeight:400,fontFamily:void 0,cssClass:""},formatter:void 0},axisBorder:{show:!1,color:"#e0e0e0",width:1,offsetX:0,offsetY:0},axisTicks:{show:!1,color:"#e0e0e0",width:6,offsetX:0,offsetY:0},title:{text:void 0,rotate:-90,offsetY:0,offsetX:0,style:{color:void 0,fontSize:"11px",fontWeight:900,fontFamily:void 0,cssClass:""}},tooltip:{enabled:!1,offsetX:0},crosshairs:{show:!0,position:"front",stroke:{color:"#b6b6b6",width:1,dashArray:0}}},this.pointAnnotation={x:0,y:null,yAxisIndex:0,seriesIndex:0,marker:{size:4,fillColor:"#fff",strokeWidth:2,strokeColor:"#333",shape:"circle",offsetX:0,offsetY:0,radius:2,cssClass:""},label:{borderColor:"#c2c2c2",borderWidth:1,borderRadius:2,text:void 0,textAnchor:"middle",offsetX:0,offsetY:0,style:{background:"#fff",color:void 0,fontSize:"11px",fontFamily:void 0,fontWeight:400,cssClass:"",padding:{left:5,right:5,top:2,bottom:2}}},customSVG:{SVG:void 0,cssClass:void 0,offsetX:0,offsetY:0},image:{path:void 0,width:20,height:20,offsetX:0,offsetY:0}},this.yAxisAnnotation={y:0,y2:null,strokeDashArray:1,fillColor:"#c2c2c2",borderColor:"#c2c2c2",borderWidth:1,opacity:.3,offsetX:0,offsetY:0,width:"100%",yAxisIndex:0,label:{borderColor:"#c2c2c2",borderWidth:1,borderRadius:2,text:void 0,textAnchor:"end",position:"right",offsetX:0,offsetY:-3,style:{background:"#fff",color:void 0,fontSize:"11px",fontFamily:void 0,fontWeight:400,cssClass:"",padding:{left:5,right:5,top:2,bottom:2}}}},this.xAxisAnnotation={x:0,x2:null,strokeDashArray:1,fillColor:"#c2c2c2",borderColor:"#c2c2c2",borderWidth:1,opacity:.3,offsetX:0,offsetY:0,label:{borderColor:"#c2c2c2",borderWidth:1,borderRadius:2,text:void 0,textAnchor:"middle",orientation:"vertical",position:"top",offsetX:0,offsetY:0,style:{background:"#fff",color:void 0,fontSize:"11px",fontFamily:void 0,fontWeight:400,cssClass:"",padding:{left:5,right:5,top:2,bottom:2}}}},this.text={x:0,y:0,text:"",textAnchor:"start",foreColor:void 0,fontSize:"13px",fontFamily:void 0,fontWeight:400,appendTo:".apexcharts-annotations",backgroundColor:"transparent",borderColor:"#c2c2c2",borderRadius:0,borderWidth:0,paddingLeft:4,paddingRight:4,paddingTop:2,paddingBottom:2}}return E(v,[{key:"init",value:function(){return{annotations:{position:"front",yaxis:[this.yAxisAnnotation],xaxis:[this.xAxisAnnotation],points:[this.pointAnnotation],texts:[],images:[],shapes:[]},chart:{animations:{enabled:!0,easing:"easeinout",speed:800,animateGradually:{delay:150,enabled:!0},dynamicAnimation:{enabled:!0,speed:350}},background:"transparent",locales:[Xe],defaultLocale:"en",dropShadow:{enabled:!1,enabledOnSeries:void 0,top:2,left:2,blur:4,color:"#000",opacity:.35},events:{animationEnd:void 0,beforeMount:void 0,mounted:void 0,updated:void 0,click:void 0,mouseMove:void 0,mouseLeave:void 0,legendClick:void 0,markerClick:void 0,selection:void 0,dataPointSelection:void 0,dataPointMouseEnter:void 0,dataPointMouseLeave:void 0,beforeZoom:void 0,beforeResetZoom:void 0,zoomed:void 0,scrolled:void 0,brushScrolled:void 0},foreColor:"#373d3f",fontFamily:"Helvetica, Arial, sans-serif",height:"auto",parentHeightOffset:15,redrawOnParentResize:!0,redrawOnWindowResize:!0,id:void 0,group:void 0,offsetX:0,offsetY:0,selection:{enabled:!1,type:"x",fill:{color:"#24292e",opacity:.1},stroke:{width:1,color:"#24292e",opacity:.4,dashArray:3},xaxis:{min:void 0,max:void 0},yaxis:{min:void 0,max:void 0}},sparkline:{enabled:!1},brush:{enabled:!1,autoScaleYaxis:!0,target:void 0},stacked:!1,stackType:"normal",toolbar:{show:!0,offsetX:0,offsetY:0,tools:{download:!0,selection:!0,zoom:!0,zoomin:!0,zoomout:!0,pan:!0,reset:!0,customIcons:[]},export:{csv:{filename:void 0,columnDelimiter:",",headerCategory:"category",headerValue:"value",dateFormatter:function(e){return new Date(e).toDateString()}},png:{filename:void 0},svg:{filename:void 0}},autoSelected:"zoom"},type:"line",width:"100%",zoom:{enabled:!0,type:"x",autoScaleYaxis:!1,zoomedArea:{fill:{color:"#90CAF9",opacity:.4},stroke:{color:"#0D47A1",opacity:.4,width:1}}}},plotOptions:{area:{fillTo:"origin"},bar:{horizontal:!1,columnWidth:"70%",barHeight:"70%",distributed:!1,borderRadius:0,startingShape:"flat",endingShape:"flat",rangeBarOverlap:!0,rangeBarGroupRows:!1,colors:{ranges:[],backgroundBarColors:[],backgroundBarOpacity:1,backgroundBarRadius:0},dataLabels:{position:"top",maxItems:100,hideOverflowingLabels:!0,orientation:"horizontal"}},bubble:{minBubbleRadius:void 0,maxBubbleRadius:void 0},candlestick:{colors:{upward:"#00B746",downward:"#EF403C"},wick:{useFillColor:!0}},boxPlot:{colors:{upper:"#00E396",lower:"#008FFB"}},heatmap:{radius:2,enableShades:!0,shadeIntensity:.5,reverseNegativeShade:!1,distributed:!1,useFillColorAsStroke:!1,colorScale:{inverse:!1,ranges:[],min:void 0,max:void 0}},treemap:{enableShades:!0,shadeIntensity:.5,distributed:!1,reverseNegativeShade:!1,useFillColorAsStroke:!1,colorScale:{inverse:!1,ranges:[],min:void 0,max:void 0}},radialBar:{inverseOrder:!1,startAngle:0,endAngle:360,offsetX:0,offsetY:0,hollow:{margin:5,size:"50%",background:"transparent",image:void 0,imageWidth:150,imageHeight:150,imageOffsetX:0,imageOffsetY:0,imageClipped:!0,position:"front",dropShadow:{enabled:!1,top:0,left:0,blur:3,color:"#000",opacity:.5}},track:{show:!0,startAngle:void 0,endAngle:void 0,background:"#f2f2f2",strokeWidth:"97%",opacity:1,margin:5,dropShadow:{enabled:!1,top:0,left:0,blur:3,color:"#000",opacity:.5}},dataLabels:{show:!0,name:{show:!0,fontSize:"16px",fontFamily:void 0,fontWeight:600,color:void 0,offsetY:0,formatter:function(e){return e}},value:{show:!0,fontSize:"14px",fontFamily:void 0,fontWeight:400,color:void 0,offsetY:16,formatter:function(e){return e+"%"}},total:{show:!1,label:"Total",fontSize:"16px",fontWeight:600,fontFamily:void 0,color:void 0,formatter:function(e){return e.globals.seriesTotals.reduce(function(t,i){return t+i},0)/e.globals.series.length+"%"}}}},pie:{customScale:1,offsetX:0,offsetY:0,startAngle:0,endAngle:360,expandOnClick:!0,dataLabels:{offset:0,minAngleToShowLabel:10},donut:{size:"65%",background:"transparent",labels:{show:!1,name:{show:!0,fontSize:"16px",fontFamily:void 0,fontWeight:600,color:void 0,offsetY:-10,formatter:function(e){return e}},value:{show:!0,fontSize:"20px",fontFamily:void 0,fontWeight:400,color:void 0,offsetY:10,formatter:function(e){return e}},total:{show:!1,showAlways:!1,label:"Total",fontSize:"16px",fontWeight:400,fontFamily:void 0,color:void 0,formatter:function(e){return e.globals.seriesTotals.reduce(function(t,i){return t+i},0)}}}}},polarArea:{rings:{strokeWidth:1,strokeColor:"#e8e8e8"},spokes:{strokeWidth:1,connectorColors:"#e8e8e8"}},radar:{size:void 0,offsetX:0,offsetY:0,polygons:{strokeWidth:1,strokeColors:"#e8e8e8",connectorColors:"#e8e8e8",fill:{colors:void 0}}}},colors:void 0,dataLabels:{enabled:!0,enabledOnSeries:void 0,formatter:function(e){return e!==null?e:""},textAnchor:"middle",distributed:!1,offsetX:0,offsetY:0,style:{fontSize:"12px",fontFamily:void 0,fontWeight:600,colors:void 0},background:{enabled:!0,foreColor:"#fff",borderRadius:2,padding:4,opacity:.9,borderWidth:1,borderColor:"#fff",dropShadow:{enabled:!1,top:1,left:1,blur:1,color:"#000",opacity:.45}},dropShadow:{enabled:!1,top:1,left:1,blur:1,color:"#000",opacity:.45}},fill:{type:"solid",colors:void 0,opacity:.85,gradient:{shade:"dark",type:"horizontal",shadeIntensity:.5,gradientToColors:void 0,inverseColors:!0,opacityFrom:1,opacityTo:1,stops:[0,50,100],colorStops:[]},image:{src:[],width:void 0,height:void 0},pattern:{style:"squares",width:6,height:6,strokeWidth:2}},forecastDataPoints:{count:0,fillOpacity:.5,strokeWidth:void 0,dashArray:4},grid:{show:!0,borderColor:"#e0e0e0",strokeDashArray:0,position:"back",xaxis:{lines:{show:!1}},yaxis:{lines:{show:!0}},row:{colors:void 0,opacity:.5},column:{colors:void 0,opacity:.5},padding:{top:0,right:10,bottom:0,left:12}},labels:[],legend:{show:!0,showForSingleSeries:!1,showForNullSeries:!0,showForZeroSeries:!0,floating:!1,position:"bottom",horizontalAlign:"center",inverseOrder:!1,fontSize:"12px",fontFamily:void 0,fontWeight:400,width:void 0,height:void 0,formatter:void 0,tooltipHoverFormatter:void 0,offsetX:-20,offsetY:4,customLegendItems:[],labels:{colors:void 0,useSeriesColors:!1},markers:{width:12,height:12,strokeWidth:0,fillColors:void 0,strokeColor:"#fff",radius:12,customHTML:void 0,offsetX:0,offsetY:0,onClick:void 0},itemMargin:{horizontal:5,vertical:2},onItemClick:{toggleDataSeries:!0},onItemHover:{highlightDataSeries:!0}},markers:{discrete:[],size:0,colors:void 0,strokeColors:"#fff",strokeWidth:2,strokeOpacity:.9,strokeDashArray:0,fillOpacity:1,shape:"circle",width:8,height:8,radius:2,offsetX:0,offsetY:0,onClick:void 0,onDblClick:void 0,showNullDataPoints:!0,hover:{size:void 0,sizeOffset:3}},noData:{text:void 0,align:"center",verticalAlign:"middle",offsetX:0,offsetY:0,style:{color:void 0,fontSize:"14px",fontFamily:void 0}},responsive:[],series:void 0,states:{normal:{filter:{type:"none",value:0}},hover:{filter:{type:"lighten",value:.1}},active:{allowMultipleDataPointsSelection:!1,filter:{type:"darken",value:.5}}},title:{text:void 0,align:"left",margin:5,offsetX:0,offsetY:0,floating:!1,style:{fontSize:"14px",fontWeight:900,fontFamily:void 0,color:void 0}},subtitle:{text:void 0,align:"left",margin:5,offsetX:0,offsetY:30,floating:!1,style:{fontSize:"12px",fontWeight:400,fontFamily:void 0,color:void 0}},stroke:{show:!0,curve:"smooth",lineCap:"butt",width:2,colors:void 0,dashArray:0},tooltip:{enabled:!0,enabledOnSeries:void 0,shared:!0,followCursor:!1,intersect:!1,inverseOrder:!1,custom:void 0,fillSeriesColor:!1,theme:"light",style:{fontSize:"12px",fontFamily:void 0},onDatasetHover:{highlightDataSeries:!1},x:{show:!0,format:"dd MMM",formatter:void 0},y:{formatter:void 0,title:{formatter:function(e){return e?e+": ":""}}},z:{formatter:void 0,title:"Size: "},marker:{show:!0,fillColors:void 0},items:{display:"flex"},fixed:{enabled:!1,position:"topRight",offsetX:0,offsetY:0}},xaxis:{type:"category",categories:[],convertedCatToNumeric:!1,offsetX:0,offsetY:0,overwriteCategories:void 0,labels:{show:!0,rotate:-45,rotateAlways:!1,hideOverlappingLabels:!0,trim:!1,minHeight:void 0,maxHeight:120,showDuplicates:!0,style:{colors:[],fontSize:"12px",fontWeight:400,fontFamily:void 0,cssClass:""},offsetX:0,offsetY:0,format:void 0,formatter:void 0,datetimeUTC:!0,datetimeFormatter:{year:"yyyy",month:"MMM 'yy",day:"dd MMM",hour:"HH:mm",minute:"HH:mm:ss",second:"HH:mm:ss"}},axisBorder:{show:!0,color:"#e0e0e0",width:"100%",height:1,offsetX:0,offsetY:0},axisTicks:{show:!0,color:"#e0e0e0",height:6,offsetX:0,offsetY:0},tickAmount:void 0,tickPlacement:"on",min:void 0,max:void 0,range:void 0,floating:!1,decimalsInFloat:void 0,position:"bottom",title:{text:void 0,offsetX:0,offsetY:0,style:{color:void 0,fontSize:"12px",fontWeight:900,fontFamily:void 0,cssClass:""}},crosshairs:{show:!0,width:1,position:"back",opacity:.9,stroke:{color:"#b6b6b6",width:1,dashArray:3},fill:{type:"solid",color:"#B1B9C4",gradient:{colorFrom:"#D8E3F0",colorTo:"#BED1E6",stops:[0,100],opacityFrom:.4,opacityTo:.5}},dropShadow:{enabled:!1,left:0,top:0,blur:1,opacity:.4}},tooltip:{enabled:!0,offsetY:0,formatter:void 0,style:{fontSize:"12px",fontFamily:void 0}}},yaxis:this.yAxis,theme:{mode:"light",palette:"palette1",monochrome:{enabled:!1,color:"#008FFB",shadeTo:"light",shadeIntensity:.65}}}}}]),v}(),et=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.graphics=new I(this.ctx),this.w.globals.isBarHorizontal&&(this.invertAxis=!0),this.helpers=new $e(this),this.xAxisAnnotations=new Je(this),this.yAxisAnnotations=new Qe(this),this.pointsAnnotations=new Ke(this),this.w.globals.isBarHorizontal&&this.w.config.yaxis[0].reversed&&(this.inversedReversedAxis=!0),this.xDivision=this.w.globals.gridWidth/this.w.globals.dataPoints}return E(v,[{key:"drawAxesAnnotations",value:function(){var e=this.w;if(e.globals.axisCharts){for(var t=this.yAxisAnnotations.drawYAxisAnnotations(),i=this.xAxisAnnotations.drawXAxisAnnotations(),a=this.pointsAnnotations.drawPointAnnotations(),s=e.config.chart.animations.enabled,n=[t,i,a],r=[i.node,t.node,a.node],o=0;o<3;o++)e.globals.dom.elGraphical.add(n[o]),!s||e.globals.resized||e.globals.dataChanged||e.config.chart.type!=="scatter"&&e.config.chart.type!=="bubble"&&e.globals.dataPoints>1&&r[o].classList.add("apexcharts-element-hidden"),e.globals.delayedElements.push({el:r[o],index:0});this.helpers.annotationsBackground()}}},{key:"drawImageAnnos",value:function(){var e=this;this.w.config.annotations.images.map(function(t,i){e.addImage(t,i)})}},{key:"drawTextAnnos",value:function(){var e=this;this.w.config.annotations.texts.map(function(t,i){e.addText(t,i)})}},{key:"addXaxisAnnotation",value:function(e,t,i){this.xAxisAnnotations.addXaxisAnnotation(e,t,i)}},{key:"addYaxisAnnotation",value:function(e,t,i){this.yAxisAnnotations.addYaxisAnnotation(e,t,i)}},{key:"addPointAnnotation",value:function(e,t,i){this.pointsAnnotations.addPointAnnotation(e,t,i)}},{key:"addText",value:function(e,t){var i=e.x,a=e.y,s=e.text,n=e.textAnchor,r=e.foreColor,o=e.fontSize,h=e.fontFamily,c=e.fontWeight,d=e.cssClass,g=e.backgroundColor,f=e.borderWidth,p=e.strokeDashArray,x=e.borderRadius,b=e.borderColor,y=e.appendTo,k=y===void 0?".apexcharts-annotations":y,A=e.paddingLeft,l=A===void 0?4:A,u=e.paddingRight,m=u===void 0?4:u,w=e.paddingBottom,S=w===void 0?2:w,P=e.paddingTop,C=P===void 0?2:P,M=this.w,T=this.graphics.drawText({x:i,y:a,text:s,textAnchor:n||"start",fontSize:o||"12px",fontWeight:c||"regular",fontFamily:h||M.config.chart.fontFamily,foreColor:r||M.config.chart.foreColor,cssClass:d}),z=M.globals.dom.baseEl.querySelector(k);z&&z.appendChild(T.node);var Y=T.bbox();if(s){var H=this.graphics.drawRect(Y.x-l,Y.y-C,Y.width+l+m,Y.height+S+C,x,g||"transparent",1,f,b,p);z.insertBefore(H.node,T.node)}}},{key:"addImage",value:function(e,t){var i=this.w,a=e.path,s=e.x,n=s===void 0?0:s,r=e.y,o=r===void 0?0:r,h=e.width,c=h===void 0?20:h,d=e.height,g=d===void 0?20:d,f=e.appendTo,p=f===void 0?".apexcharts-annotations":f,x=i.globals.dom.Paper.image(a);x.size(c,g).move(n,o);var b=i.globals.dom.baseEl.querySelector(p);b&&b.appendChild(x.node)}},{key:"addXaxisAnnotationExternal",value:function(e,t,i){return this.addAnnotationExternal({params:e,pushToMemory:t,context:i,type:"xaxis",contextMethod:i.addXaxisAnnotation}),i}},{key:"addYaxisAnnotationExternal",value:function(e,t,i){return this.addAnnotationExternal({params:e,pushToMemory:t,context:i,type:"yaxis",contextMethod:i.addYaxisAnnotation}),i}},{key:"addPointAnnotationExternal",value:function(e,t,i){return this.invertAxis===void 0&&(this.invertAxis=i.w.globals.isBarHorizontal),this.addAnnotationExternal({params:e,pushToMemory:t,context:i,type:"point",contextMethod:i.addPointAnnotation}),i}},{key:"addAnnotationExternal",value:function(e){var t=e.params,i=e.pushToMemory,a=e.context,s=e.type,n=e.contextMethod,r=a,o=r.w,h=o.globals.dom.baseEl.querySelector(".apexcharts-".concat(s,"-annotations")),c=h.childNodes.length+1,d=new $,g=Object.assign({},s==="xaxis"?d.xAxisAnnotation:s==="yaxis"?d.yAxisAnnotation:d.pointAnnotation),f=L.extend(g,t);switch(s){case"xaxis":this.addXaxisAnnotation(f,h,c);break;case"yaxis":this.addYaxisAnnotation(f,h,c);break;case"point":this.addPointAnnotation(f,h,c)}var p=o.globals.dom.baseEl.querySelector(".apexcharts-".concat(s,"-annotations .apexcharts-").concat(s,"-annotation-label[rel='").concat(c,"']")),x=this.helpers.addBackgroundToAnno(p,f);return x&&h.insertBefore(x.node,p),i&&o.globals.memory.methodsToExec.push({context:r,id:f.id?f.id:L.randomId(),method:n,label:"addAnnotation",params:t}),a}},{key:"clearAnnotations",value:function(e){var t=e.w,i=t.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis-annotations, .apexcharts-xaxis-annotations, .apexcharts-point-annotations");t.globals.memory.methodsToExec.map(function(a,s){a.label!=="addText"&&a.label!=="addAnnotation"||t.globals.memory.methodsToExec.splice(s,1)}),i=L.listToArray(i),Array.prototype.forEach.call(i,function(a){for(;a.firstChild;)a.removeChild(a.firstChild)})}},{key:"removeAnnotation",value:function(e,t){var i=e.w,a=i.globals.dom.baseEl.querySelectorAll(".".concat(t));a&&(i.globals.memory.methodsToExec.map(function(s,n){s.id===t&&i.globals.memory.methodsToExec.splice(n,1)}),Array.prototype.forEach.call(a,function(s){s.parentElement.removeChild(s)}))}}]),v}(),q=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.opts=null,this.seriesIndex=0}return E(v,[{key:"clippedImgArea",value:function(e){var t=this.w,i=t.config,a=parseInt(t.globals.gridWidth,10),s=parseInt(t.globals.gridHeight,10),n=a>s?a:s,r=e.image,o=0,h=0;e.width===void 0&&e.height===void 0?i.fill.image.width!==void 0&&i.fill.image.height!==void 0?(o=i.fill.image.width+1,h=i.fill.image.height):(o=n+1,h=n):(o=e.width,h=e.height);var c=document.createElementNS(t.globals.SVGNS,"pattern");I.setAttrs(c,{id:e.patternID,patternUnits:e.patternUnits?e.patternUnits:"userSpaceOnUse",width:o+"px",height:h+"px"});var d=document.createElementNS(t.globals.SVGNS,"image");c.appendChild(d),d.setAttributeNS(window.SVG.xlink,"href",r),I.setAttrs(d,{x:0,y:0,preserveAspectRatio:"none",width:o+"px",height:h+"px"}),d.style.opacity=e.opacity,t.globals.dom.elDefs.node.appendChild(c)}},{key:"getSeriesIndex",value:function(e){var t=this.w;return(t.config.chart.type==="bar"||t.config.chart.type==="rangeBar")&&t.config.plotOptions.bar.distributed||t.config.chart.type==="heatmap"||t.config.chart.type==="treemap"?this.seriesIndex=e.seriesNumber:this.seriesIndex=e.seriesNumber%t.globals.series.length,this.seriesIndex}},{key:"fillPath",value:function(e){var t=this.w;this.opts=e;var i,a,s,n=this.w.config;this.seriesIndex=this.getSeriesIndex(e);var r=this.getFillColors()[this.seriesIndex];t.globals.seriesColors[this.seriesIndex]!==void 0&&(r=t.globals.seriesColors[this.seriesIndex]),typeof r=="function"&&(r=r({seriesIndex:this.seriesIndex,dataPointIndex:e.dataPointIndex,value:e.value,w:t}));var o=this.getFillType(this.seriesIndex),h=Array.isArray(n.fill.opacity)?n.fill.opacity[this.seriesIndex]:n.fill.opacity;e.color&&(r=e.color);var c=r;if(r.indexOf("rgb")===-1?r.length<9&&(c=L.hexToRgba(r,h)):r.indexOf("rgba")>-1&&(h=L.getOpacityFromRGBA(r)),e.opacity&&(h=e.opacity),o==="pattern"&&(a=this.handlePatternFill(a,r,h,c)),o==="gradient"&&(s=this.handleGradientFill(r,h,this.seriesIndex)),o==="image"){var d=n.fill.image.src,g=e.patternID?e.patternID:"";this.clippedImgArea({opacity:h,image:Array.isArray(d)?e.seriesNumber<d.length?d[e.seriesNumber]:d[0]:d,width:e.width?e.width:void 0,height:e.height?e.height:void 0,patternUnits:e.patternUnits,patternID:"pattern".concat(t.globals.cuid).concat(e.seriesNumber+1).concat(g)}),i="url(#pattern".concat(t.globals.cuid).concat(e.seriesNumber+1).concat(g,")")}else i=o==="gradient"?s:o==="pattern"?a:c;return e.solid&&(i=c),i}},{key:"getFillType",value:function(e){var t=this.w;return Array.isArray(t.config.fill.type)?t.config.fill.type[e]:t.config.fill.type}},{key:"getFillColors",value:function(){var e=this.w,t=e.config,i=this.opts,a=[];return e.globals.comboCharts?e.config.series[this.seriesIndex].type==="line"?Array.isArray(e.globals.stroke.colors)?a=e.globals.stroke.colors:a.push(e.globals.stroke.colors):Array.isArray(e.globals.fill.colors)?a=e.globals.fill.colors:a.push(e.globals.fill.colors):t.chart.type==="line"?Array.isArray(e.globals.stroke.colors)?a=e.globals.stroke.colors:a.push(e.globals.stroke.colors):Array.isArray(e.globals.fill.colors)?a=e.globals.fill.colors:a.push(e.globals.fill.colors),i.fillColors!==void 0&&(a=[],Array.isArray(i.fillColors)?a=i.fillColors.slice():a.push(i.fillColors)),a}},{key:"handlePatternFill",value:function(e,t,i,a){var s=this.w.config,n=this.opts,r=new I(this.ctx),o=s.fill.pattern.strokeWidth===void 0?Array.isArray(s.stroke.width)?s.stroke.width[this.seriesIndex]:s.stroke.width:Array.isArray(s.fill.pattern.strokeWidth)?s.fill.pattern.strokeWidth[this.seriesIndex]:s.fill.pattern.strokeWidth,h=t;return Array.isArray(s.fill.pattern.style)?e=s.fill.pattern.style[n.seriesNumber]!==void 0?r.drawPattern(s.fill.pattern.style[n.seriesNumber],s.fill.pattern.width,s.fill.pattern.height,h,o,i):a:e=r.drawPattern(s.fill.pattern.style,s.fill.pattern.width,s.fill.pattern.height,h,o,i),e}},{key:"handleGradientFill",value:function(e,t,i){var a,s=this.w.config,n=this.opts,r=new I(this.ctx),o=new L,h=s.fill.gradient.type,c=e,d=s.fill.gradient.opacityFrom===void 0?t:Array.isArray(s.fill.gradient.opacityFrom)?s.fill.gradient.opacityFrom[i]:s.fill.gradient.opacityFrom;c.indexOf("rgba")>-1&&(d=L.getOpacityFromRGBA(c));var g=s.fill.gradient.opacityTo===void 0?t:Array.isArray(s.fill.gradient.opacityTo)?s.fill.gradient.opacityTo[i]:s.fill.gradient.opacityTo;if(s.fill.gradient.gradientToColors===void 0||s.fill.gradient.gradientToColors.length===0)a=s.fill.gradient.shade==="dark"?o.shadeColor(-1*parseFloat(s.fill.gradient.shadeIntensity),e.indexOf("rgb")>-1?L.rgb2hex(e):e):o.shadeColor(parseFloat(s.fill.gradient.shadeIntensity),e.indexOf("rgb")>-1?L.rgb2hex(e):e);else if(s.fill.gradient.gradientToColors[n.seriesNumber]){var f=s.fill.gradient.gradientToColors[n.seriesNumber];a=f,f.indexOf("rgba")>-1&&(g=L.getOpacityFromRGBA(f))}else a=e;if(s.fill.gradient.inverseColors){var p=c;c=a,a=p}return c.indexOf("rgb")>-1&&(c=L.rgb2hex(c)),a.indexOf("rgb")>-1&&(a=L.rgb2hex(a)),r.drawGradient(h,c,a,d,g,n.size,s.fill.gradient.stops,s.fill.gradient.colorStops,i)}}]),v}(),ne=function(){function v(e,t){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"setGlobalMarkerSize",value:function(){var e=this.w;if(e.globals.markers.size=Array.isArray(e.config.markers.size)?e.config.markers.size:[e.config.markers.size],e.globals.markers.size.length>0){if(e.globals.markers.size.length<e.globals.series.length+1)for(var t=0;t<=e.globals.series.length;t++)e.globals.markers.size[t]===void 0&&e.globals.markers.size.push(e.globals.markers.size[0])}else e.globals.markers.size=e.config.series.map(function(i){return e.config.markers.size})}},{key:"plotChartMarkers",value:function(e,t,i,a){var s,n=arguments.length>4&&arguments[4]!==void 0&&arguments[4],r=this.w,o=t,h=e,c=null,d=new I(this.ctx);if((r.globals.markers.size[t]>0||n)&&(c=d.group({class:n?"":"apexcharts-series-markers"})).attr("clip-path","url(#gridRectMarkerMask".concat(r.globals.cuid,")")),Array.isArray(h.x))for(var g=0;g<h.x.length;g++){var f=i;i===1&&g===0&&(f=0),i===1&&g===1&&(f=1);var p="apexcharts-marker";r.config.chart.type!=="line"&&r.config.chart.type!=="area"||r.globals.comboCharts||r.config.tooltip.intersect||(p+=" no-pointer-events");var x=Array.isArray(r.config.markers.size)?r.globals.markers.size[t]>0:r.config.markers.size>0;if(x||n){L.isNumber(h.y[g])?p+=" w".concat(L.randomId()):p="apexcharts-nullpoint";var b=this.getMarkerConfig({cssClass:p,seriesIndex:t,dataPointIndex:f});r.config.series[o].data[f]&&(r.config.series[o].data[f].fillColor&&(b.pointFillColor=r.config.series[o].data[f].fillColor),r.config.series[o].data[f].strokeColor&&(b.pointStrokeColor=r.config.series[o].data[f].strokeColor)),a&&(b.pSize=a),(s=d.drawMarker(h.x[g],h.y[g],b)).attr("rel",f),s.attr("j",f),s.attr("index",t),s.node.setAttribute("default-marker-size",b.pSize);var y=new O(this.ctx);y.setSelectionFilter(s,t,f),this.addEvents(s),c&&c.add(s)}else r.globals.pointsArray[t]===void 0&&(r.globals.pointsArray[t]=[]),r.globals.pointsArray[t].push([h.x[g],h.y[g]])}return c}},{key:"getMarkerConfig",value:function(e){var t=e.cssClass,i=e.seriesIndex,a=e.dataPointIndex,s=a===void 0?null:a,n=e.finishRadius,r=n===void 0?null:n,o=this.w,h=this.getMarkerStyle(i),c=o.globals.markers.size[i],d=o.config.markers;return s!==null&&d.discrete.length&&d.discrete.map(function(g){g.seriesIndex===i&&g.dataPointIndex===s&&(h.pointStrokeColor=g.strokeColor,h.pointFillColor=g.fillColor,c=g.size,h.pointShape=g.shape)}),{pSize:r===null?c:r,pRadius:d.radius,width:Array.isArray(d.width)?d.width[i]:d.width,height:Array.isArray(d.height)?d.height[i]:d.height,pointStrokeWidth:Array.isArray(d.strokeWidth)?d.strokeWidth[i]:d.strokeWidth,pointStrokeColor:h.pointStrokeColor,pointFillColor:h.pointFillColor,shape:h.pointShape||(Array.isArray(d.shape)?d.shape[i]:d.shape),class:t,pointStrokeOpacity:Array.isArray(d.strokeOpacity)?d.strokeOpacity[i]:d.strokeOpacity,pointStrokeDashArray:Array.isArray(d.strokeDashArray)?d.strokeDashArray[i]:d.strokeDashArray,pointFillOpacity:Array.isArray(d.fillOpacity)?d.fillOpacity[i]:d.fillOpacity,seriesIndex:i}}},{key:"addEvents",value:function(e){var t=this.w,i=new I(this.ctx);e.node.addEventListener("mouseenter",i.pathMouseEnter.bind(this.ctx,e)),e.node.addEventListener("mouseleave",i.pathMouseLeave.bind(this.ctx,e)),e.node.addEventListener("mousedown",i.pathMouseDown.bind(this.ctx,e)),e.node.addEventListener("click",t.config.markers.onClick),e.node.addEventListener("dblclick",t.config.markers.onDblClick),e.node.addEventListener("touchstart",i.pathMouseDown.bind(this.ctx,e),{passive:!0})}},{key:"getMarkerStyle",value:function(e){var t=this.w,i=t.globals.markers.colors,a=t.config.markers.strokeColor||t.config.markers.strokeColors;return{pointStrokeColor:Array.isArray(a)?a[e]:a,pointFillColor:Array.isArray(i)?i[e]:i}}}]),v}(),Ee=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.initialAnim=this.w.config.chart.animations.enabled,this.dynamicAnim=this.initialAnim&&this.w.config.chart.animations.dynamicAnimation.enabled}return E(v,[{key:"draw",value:function(e,t,i){var a=this.w,s=new I(this.ctx),n=i.realIndex,r=i.pointsPos,o=i.zRatio,h=i.elParent,c=s.group({class:"apexcharts-series-markers apexcharts-series-".concat(a.config.chart.type)});if(c.attr("clip-path","url(#gridRectMarkerMask".concat(a.globals.cuid,")")),Array.isArray(r.x))for(var d=0;d<r.x.length;d++){var g=t+1,f=!0;t===0&&d===0&&(g=0),t===0&&d===1&&(g=1);var p=0,x=a.globals.markers.size[n];if(o!==1/0){x=a.globals.seriesZ[n][g]/o;var b=a.config.plotOptions.bubble;b.minBubbleRadius&&x<b.minBubbleRadius&&(x=b.minBubbleRadius),b.maxBubbleRadius&&x>b.maxBubbleRadius&&(x=b.maxBubbleRadius)}a.config.chart.animations.enabled||(p=x);var y=r.x[d],k=r.y[d];if(p=p||0,k!==null&&a.globals.series[n][g]!==void 0||(f=!1),f){var A=this.drawPoint(y,k,p,x,n,g,t);c.add(A)}h.add(c)}}},{key:"drawPoint",value:function(e,t,i,a,s,n,r){var o=this.w,h=s,c=new K(this.ctx),d=new O(this.ctx),g=new q(this.ctx),f=new ne(this.ctx),p=new I(this.ctx),x=f.getMarkerConfig({cssClass:"apexcharts-marker",seriesIndex:h,dataPointIndex:n,finishRadius:o.config.chart.type==="bubble"||o.globals.comboCharts&&o.config.series[s]&&o.config.series[s].type==="bubble"?a:null});a=x.pSize;var b,y=g.fillPath({seriesNumber:s,dataPointIndex:n,color:x.pointFillColor,patternUnits:"objectBoundingBox",value:o.globals.series[s][r]});if(x.shape==="circle"?b=p.drawCircle(i):x.shape!=="square"&&x.shape!=="rect"||(b=p.drawRect(0,0,x.width-x.pointStrokeWidth/2,x.height-x.pointStrokeWidth/2,x.pRadius)),o.config.series[h].data[n]&&o.config.series[h].data[n].fillColor&&(y=o.config.series[h].data[n].fillColor),b.attr({x:e-x.width/2-x.pointStrokeWidth/2,y:t-x.height/2-x.pointStrokeWidth/2,cx:e,cy:t,fill:y,"fill-opacity":x.pointFillOpacity,stroke:x.pointStrokeColor,r:a,"stroke-width":x.pointStrokeWidth,"stroke-dasharray":x.pointStrokeDashArray,"stroke-opacity":x.pointStrokeOpacity}),o.config.chart.dropShadow.enabled){var k=o.config.chart.dropShadow;d.dropShadow(b,k,s)}if(!this.initialAnim||o.globals.dataChanged||o.globals.resized)o.globals.animationEnded=!0;else{var A=o.config.chart.animations.speed;c.animateMarker(b,0,x.shape==="circle"?a:{width:x.width,height:x.height},A,o.globals.easing,function(){window.setTimeout(function(){c.animationCompleted(b)},100)})}if(o.globals.dataChanged&&x.shape==="circle")if(this.dynamicAnim){var l,u,m,w,S=o.config.chart.animations.dynamicAnimation.speed;(w=o.globals.previousPaths[s]&&o.globals.previousPaths[s][r])!=null&&(l=w.x,u=w.y,m=w.r!==void 0?w.r:a);for(var P=0;P<o.globals.collapsedSeries.length;P++)o.globals.collapsedSeries[P].index===s&&(S=1,a=0);e===0&&t===0&&(a=0),c.animateCircle(b,{cx:l,cy:u,r:m},{cx:e,cy:t,r:a},S,o.globals.easing)}else b.attr({r:a});return b.attr({rel:n,j:n,index:s,"default-marker-size":a}),d.setSelectionFilter(b,s,n),f.addEvents(b),b.node.classList.add("apexcharts-marker"),b}},{key:"centerTextInBubble",value:function(e){var t=this.w;return{y:e+=parseInt(t.config.dataLabels.style.fontSize,10)/4}}}]),v}(),ee=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"dataLabelsCorrection",value:function(e,t,i,a,s,n,r){var o=this.w,h=!1,c=new I(this.ctx).getTextRects(i,r),d=c.width,g=c.height;t<0&&(t=0),t>o.globals.gridHeight+g&&(t=o.globals.gridHeight+g/2),o.globals.dataLabelsRects[a]===void 0&&(o.globals.dataLabelsRects[a]=[]),o.globals.dataLabelsRects[a].push({x:e,y:t,width:d,height:g});var f=o.globals.dataLabelsRects[a].length-2,p=o.globals.lastDrawnDataLabelsIndexes[a]!==void 0?o.globals.lastDrawnDataLabelsIndexes[a][o.globals.lastDrawnDataLabelsIndexes[a].length-1]:0;if(o.globals.dataLabelsRects[a][f]!==void 0){var x=o.globals.dataLabelsRects[a][p];(e>x.x+x.width+2||t>x.y+x.height+2||e+d<x.x)&&(h=!0)}return(s===0||n)&&(h=!0),{x:e,y:t,textRects:c,drawnextLabel:h}}},{key:"drawDataLabel",value:function(e,t,i){var a=this,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:2,n=this.w,r=new I(this.ctx),o=n.config.dataLabels,h=0,c=0,d=i,g=null;if(!o.enabled||!Array.isArray(e.x))return g;g=r.group({class:"apexcharts-data-labels"});for(var f=0;f<e.x.length;f++)if(h=e.x[f]+o.offsetX,c=e.y[f]+o.offsetY+s,!isNaN(h)){i===1&&f===0&&(d=0),i===1&&f===1&&(d=1);var p=n.globals.series[t][d],x="",b=function(A){return n.config.dataLabels.formatter(A,{ctx:a.ctx,seriesIndex:t,dataPointIndex:d,w:n})};if(n.config.chart.type==="bubble"){x=b(p=n.globals.seriesZ[t][d]),c=e.y[f];var y=new Ee(this.ctx),k=y.centerTextInBubble(c,t,d);c=k.y}else p!==void 0&&(x=b(p));this.plotDataLabelsText({x:h,y:c,text:x,i:t,j:d,parent:g,offsetCorrection:!0,dataLabelsConfig:n.config.dataLabels})}return g}},{key:"plotDataLabelsText",value:function(e){var t=this.w,i=new I(this.ctx),a=e.x,s=e.y,n=e.i,r=e.j,o=e.text,h=e.textAnchor,c=e.fontSize,d=e.parent,g=e.dataLabelsConfig,f=e.color,p=e.alwaysDrawDataLabel,x=e.offsetCorrection;if(!(Array.isArray(t.config.dataLabels.enabledOnSeries)&&t.config.dataLabels.enabledOnSeries.indexOf(n)<0)){var b={x:a,y:s,drawnextLabel:!0};x&&(b=this.dataLabelsCorrection(a,s,o,n,r,p,parseInt(g.style.fontSize,10))),t.globals.zoomed||(a=b.x,s=b.y),b.textRects&&(a<-10-b.textRects.width||a>t.globals.gridWidth+b.textRects.width+10)&&(o="");var y=t.globals.dataLabels.style.colors[n];((t.config.chart.type==="bar"||t.config.chart.type==="rangeBar")&&t.config.plotOptions.bar.distributed||t.config.dataLabels.distributed)&&(y=t.globals.dataLabels.style.colors[r]),typeof y=="function"&&(y=y({series:t.globals.series,seriesIndex:n,dataPointIndex:r,w:t})),f&&(y=f);var k=g.offsetX,A=g.offsetY;if(t.config.chart.type!=="bar"&&t.config.chart.type!=="rangeBar"||(k=0,A=0),b.drawnextLabel){var l=i.drawText({width:100,height:parseInt(g.style.fontSize,10),x:a+k,y:s+A,foreColor:y,textAnchor:h||g.textAnchor,text:o,fontSize:c||g.style.fontSize,fontFamily:g.style.fontFamily,fontWeight:g.style.fontWeight||"normal"});if(l.attr({class:"apexcharts-datalabel",cx:a,cy:s}),g.dropShadow.enabled){var u=g.dropShadow;new O(this.ctx).dropShadow(l,u)}d.add(l),t.globals.lastDrawnDataLabelsIndexes[n]===void 0&&(t.globals.lastDrawnDataLabelsIndexes[n]=[]),t.globals.lastDrawnDataLabelsIndexes[n].push(r)}}}},{key:"addBackgroundToDataLabel",value:function(e,t){var i=this.w,a=i.config.dataLabels.background,s=a.padding,n=a.padding/2,r=t.width,o=t.height,h=new I(this.ctx).drawRect(t.x-s,t.y-n/2,r+2*s,o+n,a.borderRadius,i.config.chart.background==="transparent"?"#fff":i.config.chart.background,a.opacity,a.borderWidth,a.borderColor);return a.dropShadow.enabled&&new O(this.ctx).dropShadow(h,a.dropShadow),h}},{key:"dataLabelsBackground",value:function(){var e=this.w;if(e.config.chart.type!=="bubble")for(var t=e.globals.dom.baseEl.querySelectorAll(".apexcharts-datalabels text"),i=0;i<t.length;i++){var a=t[i],s=a.getBBox(),n=null;if(s.width&&s.height&&(n=this.addBackgroundToDataLabel(a,s)),n){a.parentNode.insertBefore(n.node,a);var r=a.getAttribute("fill");e.config.chart.animations.enabled&&!e.globals.resized&&!e.globals.dataChanged?n.animate().attr({fill:r}):n.attr({fill:r}),a.setAttribute("fill",e.config.dataLabels.background.foreColor)}}}},{key:"bringForward",value:function(){for(var e=this.w,t=e.globals.dom.baseEl.querySelectorAll(".apexcharts-datalabels"),i=e.globals.dom.baseEl.querySelector(".apexcharts-plot-series:last-child"),a=0;a<t.length;a++)i&&i.insertBefore(t[a],i.nextSibling)}}]),v}(),tt=function(){function v(e){X(this,v),this.w=e.w,this.barCtx=e}return E(v,[{key:"handleBarDataLabels",value:function(e){var t=e.x,i=e.y,a=e.y1,s=e.y2,n=e.i,r=e.j,o=e.realIndex,h=e.series,c=e.barHeight,d=e.barWidth,g=e.barYPosition,f=e.visibleSeries,p=e.renderedPath,x=this.w,b=new I(this.barCtx.ctx),y=Array.isArray(this.barCtx.strokeWidth)?this.barCtx.strokeWidth[o]:this.barCtx.strokeWidth,k=t+parseFloat(d*f),A=i+parseFloat(c*f);x.globals.isXNumeric&&!x.globals.isBarHorizontal&&(k=t+parseFloat(d*(f+1)),A=i+parseFloat(c*(f+1))-y);var l=t,u=i,m={},w=x.config.dataLabels,S=this.barCtx.barOptions.dataLabels;g!==void 0&&this.barCtx.isTimelineBar&&(A=g,u=g);var P=w.offsetX,C=w.offsetY,M={width:0,height:0};if(x.config.dataLabels.enabled){var T=this.barCtx.series[n][r];M=b.getTextRects(x.globals.yLabelFormatters[0](T),parseFloat(w.style.fontSize))}var z={x:t,y:i,i:n,j:r,renderedPath:p,bcx:k,bcy:A,barHeight:c,barWidth:d,textRects:M,strokeWidth:y,dataLabelsX:l,dataLabelsY:u,barDataLabelsConfig:S,offX:P,offY:C};return m=this.barCtx.isHorizontal?this.calculateBarsDataLabelsPosition(z):this.calculateColumnsDataLabelsPosition(z),p.attr({cy:m.bcy,cx:m.bcx,j:r,val:h[n][r],barHeight:c,barWidth:d}),this.drawCalculatedDataLabels({x:m.dataLabelsX,y:m.dataLabelsY,val:this.barCtx.isTimelineBar?[a,s]:h[n][r],i:o,j:r,barWidth:d,barHeight:c,textRects:M,dataLabelsConfig:w})}},{key:"calculateColumnsDataLabelsPosition",value:function(e){var t,i=this.w,a=e.i,s=e.j,n=e.y,r=e.bcx,o=e.barWidth,h=e.barHeight,c=e.textRects,d=e.dataLabelsY,g=e.barDataLabelsConfig,f=e.strokeWidth,p=e.offX,x=e.offY;h=Math.abs(h);var b=i.config.plotOptions.bar.dataLabels.orientation==="vertical";r-=f/2;var y=i.globals.gridWidth/i.globals.dataPoints;t=i.globals.isXNumeric?r-o/2+p:r-y+o/2+p,b&&(t=t+c.height/2-f/2-2);var k=this.barCtx.series[a][s]<0;i.config.plotOptions.bar.startingShape!=="rounded"&&i.config.plotOptions.bar.endingShape!=="rounded"||a===0||(n-=i.config.plotOptions.bar.borderRadius);var A=n;switch(this.barCtx.isReversed&&(A=n-h+(k?2*h:0),n-=h),g.position){case"center":d=b?k?A+h/2+x:A+h/2-x:k?A-h/2+c.height/2+x:A+h/2+c.height/3-x;break;case"bottom":d=b?k?A+h+x:A+h-x:k?A-h+c.height+f+x:A+h-c.height/2+f-x;break;case"top":d=b?k?A+x:A-x:k?A-c.height/2-x:A+c.height+x}return i.config.chart.stacked||(d<0?d=0+f:d+c.height/3>i.globals.gridHeight&&(d=i.globals.gridHeight-f)),{bcx:r,bcy:n,dataLabelsX:t,dataLabelsY:d}}},{key:"calculateBarsDataLabelsPosition",value:function(e){var t=this.w,i=e.x,a=e.i,s=e.j,n=e.bcy,r=e.barHeight,o=e.barWidth,h=e.textRects,c=e.dataLabelsX,d=e.strokeWidth,g=e.barDataLabelsConfig,f=e.offX,p=e.offY,x=t.globals.gridHeight/t.globals.dataPoints;o=Math.abs(o);var b=n-(this.barCtx.isTimelineBar?0:x)+r/2+h.height/2+p-3,y=this.barCtx.series[a][s]<0,k=i;switch(this.barCtx.isReversed&&(k=i+o-(y?2*o:0),i=t.globals.gridWidth-o),g.position){case"center":c=y?k+o/2-f:Math.max(h.width/2,k-o/2)+f;break;case"bottom":c=y?k+o-d-Math.round(h.width/2)-f:k-o+d+Math.round(h.width/2)+f;break;case"top":c=y?k-d+Math.round(h.width/2)-f:k-d-Math.round(h.width/2)+f}return t.config.chart.stacked||(c<0?c=c+h.width+d:c+h.width/2>t.globals.gridWidth&&(c=t.globals.gridWidth-h.width-d)),{bcx:i,bcy:n,dataLabelsX:c,dataLabelsY:b}}},{key:"drawCalculatedDataLabels",value:function(e){var t=e.x,i=e.y,a=e.val,s=e.i,n=e.j,r=e.textRects,o=e.barHeight,h=e.barWidth,c=e.dataLabelsConfig,d=this.w,g="rotate(0)";d.config.plotOptions.bar.dataLabels.orientation==="vertical"&&(g="rotate(-90, ".concat(t,", ").concat(i,")"));var f=new ee(this.barCtx.ctx),p=new I(this.barCtx.ctx),x=c.formatter,b=null,y=d.globals.collapsedSeriesIndices.indexOf(s)>-1;if(c.enabled&&!y){b=p.group({class:"apexcharts-data-labels",transform:g});var k="";a!==void 0&&(k=x(a,{seriesIndex:s,dataPointIndex:n,w:d}));var A=d.globals.series[s][n]<0,l=d.config.plotOptions.bar.dataLabels.position;d.config.plotOptions.bar.dataLabels.orientation==="vertical"&&(l==="top"&&(c.textAnchor=A?"end":"start"),l==="center"&&(c.textAnchor="middle"),l==="bottom"&&(c.textAnchor=A?"end":"start")),this.barCtx.isTimelineBar&&this.barCtx.barOptions.dataLabels.hideOverflowingLabels&&h<p.getTextRects(k,parseFloat(c.style.fontSize)).width&&(k=""),d.config.chart.stacked&&this.barCtx.barOptions.dataLabels.hideOverflowingLabels&&(this.barCtx.isHorizontal?r.width/1.6>Math.abs(h)&&(k=""):r.height/1.6>Math.abs(o)&&(k=""));var u=F({},c);this.barCtx.isHorizontal&&a<0&&(c.textAnchor==="start"?u.textAnchor="end":c.textAnchor==="end"&&(u.textAnchor="start")),f.plotDataLabelsText({x:t,y:i,text:k,i:s,j:n,parent:b,dataLabelsConfig:u,alwaysDrawDataLabel:!0,offsetCorrection:!0})}return b}}]),v}(),j=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.legendInactiveClass="legend-mouseover-inactive"}return E(v,[{key:"getAllSeriesEls",value:function(){return this.w.globals.dom.baseEl.getElementsByClassName("apexcharts-series")}},{key:"getSeriesByName",value:function(e){return this.w.globals.dom.baseEl.querySelector(".apexcharts-inner .apexcharts-series[seriesName='".concat(L.escapeString(e),"']"))}},{key:"isSeriesHidden",value:function(e){var t=this.getSeriesByName(e),i=parseInt(t.getAttribute("data:realIndex"),10);return{isHidden:t.classList.contains("apexcharts-series-collapsed"),realIndex:i}}},{key:"addCollapsedClassToSeries",value:function(e,t){var i=this.w;function a(s){for(var n=0;n<s.length;n++)s[n].index===t&&e.node.classList.add("apexcharts-series-collapsed")}a(i.globals.collapsedSeries),a(i.globals.ancillaryCollapsedSeries)}},{key:"toggleSeries",value:function(e){var t=this.isSeriesHidden(e);return this.ctx.legend.legendHelpers.toggleDataSeries(t.realIndex,t.isHidden),t.isHidden}},{key:"showSeries",value:function(e){var t=this.isSeriesHidden(e);t.isHidden&&this.ctx.legend.legendHelpers.toggleDataSeries(t.realIndex,!0)}},{key:"hideSeries",value:function(e){var t=this.isSeriesHidden(e);t.isHidden||this.ctx.legend.legendHelpers.toggleDataSeries(t.realIndex,!1)}},{key:"resetSeries",value:function(){var e=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],a=this.w,s=L.clone(a.globals.initialSeries);a.globals.previousPaths=[],i?(a.globals.collapsedSeries=[],a.globals.ancillaryCollapsedSeries=[],a.globals.collapsedSeriesIndices=[],a.globals.ancillaryCollapsedSeriesIndices=[]):s=this.emptyCollapsedSeries(s),a.config.series=s,e&&(t&&(a.globals.zoomed=!1,this.ctx.updateHelpers.revertDefaultAxisMinMax()),this.ctx.updateHelpers._updateSeries(s,a.config.chart.animations.dynamicAnimation.enabled))}},{key:"emptyCollapsedSeries",value:function(e){for(var t=this.w,i=0;i<e.length;i++)t.globals.collapsedSeriesIndices.indexOf(i)>-1&&(e[i].data=[]);return e}},{key:"toggleSeriesOnHover",value:function(e,t){var i=this.w,a=i.globals.dom.baseEl.querySelectorAll(".apexcharts-series, .apexcharts-datalabels");if(e.type==="mousemove"){var s=parseInt(t.getAttribute("rel"),10)-1,n=null,r=null;i.globals.axisCharts||i.config.chart.type==="radialBar"?i.globals.axisCharts?(n=i.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(s,"']")),r=i.globals.dom.baseEl.querySelector(".apexcharts-datalabels[data\\:realIndex='".concat(s,"']"))):n=i.globals.dom.baseEl.querySelector(".apexcharts-series[rel='".concat(s+1,"']")):n=i.globals.dom.baseEl.querySelector(".apexcharts-series[rel='".concat(s+1,"'] path"));for(var o=0;o<a.length;o++)a[o].classList.add(this.legendInactiveClass);n!==null&&(i.globals.axisCharts||n.parentNode.classList.remove(this.legendInactiveClass),n.classList.remove(this.legendInactiveClass),r!==null&&r.classList.remove(this.legendInactiveClass))}else if(e.type==="mouseout")for(var h=0;h<a.length;h++)a[h].classList.remove(this.legendInactiveClass)}},{key:"highlightRangeInSeries",value:function(e,t){var i=this,a=this.w,s=a.globals.dom.baseEl.getElementsByClassName("apexcharts-heatmap-rect"),n=function(o){for(var h=0;h<s.length;h++)s[h].classList[o](i.legendInactiveClass)};if(e.type==="mousemove"){var r=parseInt(t.getAttribute("rel"),10)-1;n("add"),function(o){for(var h=0;h<s.length;h++){var c=parseInt(s[h].getAttribute("val"),10);c>=o.from&&c<=o.to&&s[h].classList.remove(i.legendInactiveClass)}}(a.config.plotOptions.heatmap.colorScale.ranges[r])}else e.type==="mouseout"&&n("remove")}},{key:"getActiveConfigSeriesIndex",value:function(){var e=arguments.length>0&&arguments[0]!==void 0&&arguments[0],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"asc",i=this.w,a=0;if(i.config.series.length>1){for(var s=i.config.series.map(function(r,o){var h=!1;return e&&(h=i.config.series[o].type==="bar"||i.config.series[o].type==="column"),r.data&&r.data.length>0&&!h?o:-1}),n=t==="asc"?0:s.length-1;t==="asc"?n<s.length:n>=0;t==="asc"?n++:n--)if(s[n]!==-1){a=s[n];break}}return a}},{key:"getPreviousPaths",value:function(){var e=this.w;function t(n,r,o){for(var h=n[r].childNodes,c={type:o,paths:[],realIndex:n[r].getAttribute("data:realIndex")},d=0;d<h.length;d++)if(h[d].hasAttribute("pathTo")){var g=h[d].getAttribute("pathTo");c.paths.push({d:g})}e.globals.previousPaths.push(c)}e.globals.previousPaths=[],["line","area","bar","rangebar","candlestick","radar"].forEach(function(n){for(var r,o=(r=n,e.globals.dom.baseEl.querySelectorAll(".apexcharts-".concat(r,"-series .apexcharts-series"))),h=0;h<o.length;h++)t(o,h,n)}),this.handlePrevBubbleScatterPaths("bubble"),this.handlePrevBubbleScatterPaths("scatter");var i=e.globals.dom.baseEl.querySelectorAll(".apexcharts-".concat(e.config.chart.type," .apexcharts-series"));if(i.length>0)for(var a=function(n){for(var r=e.globals.dom.baseEl.querySelectorAll(".apexcharts-".concat(e.config.chart.type," .apexcharts-series[data\\:realIndex='").concat(n,"'] rect")),o=[],h=function(d){var g=function(p){return r[d].getAttribute(p)},f={x:parseFloat(g("x")),y:parseFloat(g("y")),width:parseFloat(g("width")),height:parseFloat(g("height"))};o.push({rect:f,color:r[d].getAttribute("color")})},c=0;c<r.length;c++)h(c);e.globals.previousPaths.push(o)},s=0;s<i.length;s++)a(s);e.globals.axisCharts||(e.globals.previousPaths=e.globals.series)}},{key:"handlePrevBubbleScatterPaths",value:function(e){var t=this.w,i=t.globals.dom.baseEl.querySelectorAll(".apexcharts-".concat(e,"-series .apexcharts-series"));if(i.length>0)for(var a=0;a<i.length;a++){for(var s=t.globals.dom.baseEl.querySelectorAll(".apexcharts-".concat(e,"-series .apexcharts-series[data\\:realIndex='").concat(a,"'] circle")),n=[],r=0;r<s.length;r++)n.push({x:s[r].getAttribute("cx"),y:s[r].getAttribute("cy"),r:s[r].getAttribute("r")});t.globals.previousPaths.push(n)}}},{key:"clearPreviousPaths",value:function(){var e=this.w;e.globals.previousPaths=[],e.globals.allSeriesCollapsed=!1}},{key:"handleNoData",value:function(){var e=this.w,t=e.config.noData,i=new I(this.ctx),a=e.globals.svgWidth/2,s=e.globals.svgHeight/2,n="middle";if(e.globals.noData=!0,e.globals.animationEnded=!0,t.align==="left"?(a=10,n="start"):t.align==="right"&&(a=e.globals.svgWidth-10,n="end"),t.verticalAlign==="top"?s=50:t.verticalAlign==="bottom"&&(s=e.globals.svgHeight-50),a+=t.offsetX,s=s+parseInt(t.style.fontSize,10)+2+t.offsetY,t.text!==void 0&&t.text!==""){var r=i.drawText({x:a,y:s,text:t.text,textAnchor:n,fontSize:t.style.fontSize,fontFamily:t.style.fontFamily,foreColor:t.style.color,opacity:1,class:"apexcharts-text-nodata"});e.globals.dom.Paper.add(r)}}},{key:"setNullSeriesToZeroValues",value:function(e){for(var t=this.w,i=0;i<e.length;i++)if(e[i].length===0)for(var a=0;a<e[t.globals.maxValsInArrayIndex].length;a++)e[i].push(0);return e}},{key:"hasAllSeriesEqualX",value:function(){for(var e=!0,t=this.w,i=this.filteredSeriesX(),a=0;a<i.length-1;a++)if(i[a][0]!==i[a+1][0]){e=!1;break}return t.globals.allSeriesHasEqualX=e,e}},{key:"filteredSeriesX",value:function(){var e=this.w.globals.seriesX.map(function(t){return t.length>0?t:[]});return e}}]),v}(),it=function(){function v(e){X(this,v),this.w=e.w,this.barCtx=e}return E(v,[{key:"initVariables",value:function(e){var t=this.w;this.barCtx.series=e,this.barCtx.totalItems=0,this.barCtx.seriesLen=0,this.barCtx.visibleI=-1,this.barCtx.visibleItems=1;for(var i=0;i<e.length;i++)if(e[i].length>0&&(this.barCtx.seriesLen=this.barCtx.seriesLen+1,this.barCtx.totalItems+=e[i].length),t.globals.isXNumeric)for(var a=0;a<e[i].length;a++)t.globals.seriesX[i][a]>t.globals.minX&&t.globals.seriesX[i][a]<t.globals.maxX&&this.barCtx.visibleItems++;else this.barCtx.visibleItems=t.globals.dataPoints;this.barCtx.seriesLen===0&&(this.barCtx.seriesLen=1),this.barCtx.zeroSerieses=[],this.barCtx.radiusOnSeriesNumber=e.length-1,t.globals.comboCharts||this.checkZeroSeries({series:e})}},{key:"initialPositions",value:function(){var e,t,i,a,s,n,r,o,h=this.w,c=h.globals.dataPoints;this.barCtx.isTimelineBar&&(c=h.globals.labels.length);var d=this.barCtx.seriesLen;if(h.config.plotOptions.bar.rangeBarGroupRows&&(d=1),this.barCtx.isHorizontal)s=(i=h.globals.gridHeight/c)/d,h.globals.isXNumeric&&(s=(i=h.globals.gridHeight/this.barCtx.totalItems)/this.barCtx.seriesLen),s=s*parseInt(this.barCtx.barOptions.barHeight,10)/100,o=this.barCtx.baseLineInvertedY+h.globals.padHorizontal+(this.barCtx.isReversed?h.globals.gridWidth:0)-(this.barCtx.isReversed?2*this.barCtx.baseLineInvertedY:0),t=(i-s*this.barCtx.seriesLen)/2;else{if(a=h.globals.gridWidth/this.barCtx.visibleItems,h.config.xaxis.convertedCatToNumeric&&(a=h.globals.gridWidth/h.globals.dataPoints),n=a/this.barCtx.seriesLen*parseInt(this.barCtx.barOptions.columnWidth,10)/100,h.globals.isXNumeric){var g=this.barCtx.xRatio;h.config.xaxis.convertedCatToNumeric&&(g=this.barCtx.initialXRatio),h.globals.minXDiff&&h.globals.minXDiff!==.5&&h.globals.minXDiff/g>0&&(a=h.globals.minXDiff/g),(n=a/this.barCtx.seriesLen*parseInt(this.barCtx.barOptions.columnWidth,10)/100)<1&&(n=1)}r=h.globals.gridHeight-this.barCtx.baseLineY[this.barCtx.yaxisIndex]-(this.barCtx.isReversed?h.globals.gridHeight:0)+(this.barCtx.isReversed?2*this.barCtx.baseLineY[this.barCtx.yaxisIndex]:0),e=h.globals.padHorizontal+(a-n*this.barCtx.seriesLen)/2}return{x:e,y:t,yDivision:i,xDivision:a,barHeight:s,barWidth:n,zeroH:r,zeroW:o}}},{key:"getPathFillColor",value:function(e,t,i,a){var s=this.w,n=new q(this.barCtx.ctx),r=null,o=this.barCtx.barOptions.distributed?i:t;return this.barCtx.barOptions.colors.ranges.length>0&&this.barCtx.barOptions.colors.ranges.map(function(h){e[t][i]>=h.from&&e[t][i]<=h.to&&(r=h.color)}),s.config.series[t].data[i]&&s.config.series[t].data[i].fillColor&&(r=s.config.series[t].data[i].fillColor),n.fillPath({seriesNumber:this.barCtx.barOptions.distributed?o:a,dataPointIndex:i,color:r,value:e[t][i]})}},{key:"getStrokeWidth",value:function(e,t,i){var a=0,s=this.w;return this.barCtx.series[e][t]===void 0||this.barCtx.series[e][t]===null?this.barCtx.isNullValue=!0:this.barCtx.isNullValue=!1,s.config.stroke.show&&(this.barCtx.isNullValue||(a=Array.isArray(this.barCtx.strokeWidth)?this.barCtx.strokeWidth[i]:this.barCtx.strokeWidth)),a}},{key:"barBackground",value:function(e){var t=e.j,i=e.i,a=e.x1,s=e.x2,n=e.y1,r=e.y2,o=e.elSeries,h=this.w,c=new I(this.barCtx.ctx),d=new j(this.barCtx.ctx).getActiveConfigSeriesIndex();if(this.barCtx.barOptions.colors.backgroundBarColors.length>0&&d===i){t>=this.barCtx.barOptions.colors.backgroundBarColors.length&&(t-=this.barCtx.barOptions.colors.backgroundBarColors.length);var g=this.barCtx.barOptions.colors.backgroundBarColors[t],f=c.drawRect(a!==void 0?a:0,n!==void 0?n:0,s!==void 0?s:h.globals.gridWidth,r!==void 0?r:h.globals.gridHeight,this.barCtx.barOptions.colors.backgroundBarRadius,g,this.barCtx.barOptions.colors.backgroundBarOpacity);o.add(f),f.node.classList.add("apexcharts-backgroundBar")}}},{key:"getColumnPaths",value:function(e){var t=e.barWidth,i=e.barXPosition,a=e.yRatio,s=e.y1,n=e.y2,r=e.strokeWidth,o=e.series,h=e.realIndex,c=e.i,d=e.j,g=e.w,f=new I(this.barCtx.ctx);(r=Array.isArray(r)?r[h]:r)&&o[c][d]!==0||(r=0),o[c][d]===0&&(t=0);var p={barWidth:t,strokeWidth:r,yRatio:a,barXPosition:i,y1:s,y2:n},x=this.getRoundedBars(g,p,o,c,d),b=i,y=i+t,k=f.move(b,x.y1),A=f.move(b,x.y1),l=f.line(y-r,x.y1);return g.globals.previousPaths.length>0&&(A=this.barCtx.getPreviousPath(h,d,!1)),k=k+f.line(b,x.y2)+x.startingShape+f.line(y-r,x.y2)+l+x.endingShape+"z",A=A+f.line(b,x.y1)+l+l+l+l+l+f.line(b,x.y1),g.config.chart.stacked&&(this.barCtx.yArrj.push(x.y2),this.barCtx.yArrjF.push(Math.abs(s-x.y2)),this.barCtx.yArrjVal.push(this.barCtx.series[c][d])),{pathTo:k,pathFrom:A}}},{key:"getBarpaths",value:function(e){var t=e.barYPosition,i=e.barHeight,a=e.x1,s=e.x2,n=e.strokeWidth,r=e.series,o=e.realIndex,h=e.i,c=e.j,d=e.w,g=new I(this.barCtx.ctx);(n=Array.isArray(n)?n[o]:n)&&!r[h][c]||(n=0),r[h][c]===0&&(i=0);var f={barHeight:i,strokeWidth:n,barYPosition:t,x2:s,x1:a},p=this.getRoundedBars(d,f,r,h,c),x=g.move(p.x1,t),b=g.move(p.x1,t);d.globals.previousPaths.length>0&&(b=this.barCtx.getPreviousPath(o,c,!1));var y=t,k=t+i,A=g.line(p.x1,k-n);return x=x+g.line(p.x2,y)+p.startingShape+g.line(p.x2,k-n)+A+A+p.endingShape+"z",b=b+g.line(p.x1,y)+A+A+A+A+A+g.line(p.x1,y),d.config.chart.stacked&&(this.barCtx.xArrj.push(p.x2),this.barCtx.xArrjF.push(Math.abs(p.x1-p.x2)),this.barCtx.xArrjVal.push(this.barCtx.series[h][c])),{pathTo:x,pathFrom:b}}},{key:"getRoundedBars",value:function(e,t,i,a,s){var n=new I(this.barCtx.ctx),r=0,o=e.config.plotOptions.bar.borderRadius;if(Array.isArray(o)?r=o[a>o.length-1?o.length-1:a]:r=o,this.barCtx.isHorizontal){var h="",c="",d=r,g=r,f=t.x1,p=t.x2;if(Math.abs(t.x1-t.x2)<r&&(r=Math.abs(t.x1-t.x2)),i[a][s]!==void 0||i[a][s]!==null){var x=this.barCtx.isReversed?i[a][s]>0:i[a][s]<0;x&&(r*=-1),d=g=r,this.barCtx.barOptions.startingShape==="flat"&&(d=0),this.barCtx.barOptions.endingShape==="flat"&&(g=0),this.barCtx.barOptions.startingShape==="flat"&&this.barCtx.barOptions.endingShape==="flat"&&(r=0),e.config.chart.stacked&&i.length>1&&a!==0?this.barCtx.barOptions.endingShape==="flat"||this.barCtx.barOptions.startingShape==="flat"?f+=r:f+=2*g:(f+=g,p-=d),h=n.quadraticCurve(p+d,t.barYPosition,p+d,t.barYPosition+(x?-1*d:d))+n.line(p+d,t.barYPosition+t.barHeight-t.strokeWidth-(x?-1*d:d))+n.quadraticCurve(p+d,t.barYPosition+t.barHeight-t.strokeWidth,p,t.barYPosition+t.barHeight-t.strokeWidth),c=n.quadraticCurve(f-g,t.barYPosition+t.barHeight-t.strokeWidth,f-g,t.barYPosition+t.barHeight-t.strokeWidth-(x?-1*g:g))+n.line(f-g,t.barYPosition+(x?-1*g:g))+n.quadraticCurve(f-g,t.barYPosition,f,t.barYPosition)}return{startingShape:h,endingShape:c,x1:f,x2:p}}var b="",y="",k=r,A=r,l=t.y2,u=t.y1;if(Math.abs(t.y1-t.y2)<r&&(r=Math.abs(t.y1-t.y2)),i[a][s]!==void 0||i[a][s]!==null){var m=i[a][s]<0;m&&(r*=-1),k=A=r,this.barCtx.barOptions.startingShape==="flat"&&(k=0),this.barCtx.barOptions.endingShape==="flat"&&(A=0),this.barCtx.barOptions.startingShape==="flat"&&this.barCtx.barOptions.endingShape==="flat"&&(r=0),e.config.chart.stacked&&i.length>1&&a!==0?this.barCtx.barOptions.endingShape==="flat"||this.barCtx.barOptions.startingShape==="flat"?u-=r:u-=2*A:(u-=A,l+=k),b=n.quadraticCurve(t.barXPosition,l-k,t.barXPosition+(m?-1*k:k),l-k)+n.line(t.barXPosition+t.barWidth-t.strokeWidth-(m?-1*k:k),l-k)+n.quadraticCurve(t.barXPosition+t.barWidth-t.strokeWidth,l-k,t.barXPosition+t.barWidth-t.strokeWidth,l),y=n.quadraticCurve(t.barXPosition+t.barWidth-t.strokeWidth,u+A,t.barXPosition+t.barWidth-t.strokeWidth-(m?-1*A:A),u+A)+n.line(t.barXPosition+(m?-1*A:A),u+A)+n.quadraticCurve(t.barXPosition,u+A,t.barXPosition,u)}return{startingShape:b,endingShape:y,y1:u,y2:l}}},{key:"checkZeroSeries",value:function(e){for(var t=e.series,i=this.w,a=0;a<t.length;a++){for(var s=0,n=0;n<t[i.globals.maxValsInArrayIndex].length;n++)s+=t[a][n];s===0&&this.barCtx.zeroSerieses.push(a)}for(var r=t.length-1;r>=0;r--)this.barCtx.zeroSerieses.indexOf(r)>-1&&r===this.radiusOnSeriesNumber&&(this.barCtx.radiusOnSeriesNumber-=1);for(var o=t.length-1;o>=0;o--)i.globals.collapsedSeriesIndices.indexOf(this.barCtx.radiusOnSeriesNumber)>-1&&(this.barCtx.radiusOnSeriesNumber-=1)}},{key:"getXForValue",value:function(e,t){var i=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],a=i?t:null;return e!=null&&(a=t+e/this.barCtx.invertedYRatio-2*(this.barCtx.isReversed?e/this.barCtx.invertedYRatio:0)),a}},{key:"getYForValue",value:function(e,t){var i=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],a=i?t:null;return e!=null&&(a=t-e/this.barCtx.yRatio[this.barCtx.yaxisIndex]+2*(this.barCtx.isReversed?e/this.barCtx.yRatio[this.barCtx.yaxisIndex]:0)),a}},{key:"getGoalValues",value:function(e,t,i,a,s){var n=this,r=this.w,o=[];return r.globals.seriesGoals[a]&&r.globals.seriesGoals[a][s]&&Array.isArray(r.globals.seriesGoals[a][s])&&r.globals.seriesGoals[a][s].forEach(function(h){var c;o.push((de(c={},e,e==="x"?n.getXForValue(h.value,t,!1):n.getYForValue(h.value,i,!1)),de(c,"attrs",h),c))}),o}},{key:"drawGoalLine",value:function(e){var t=e.barXPosition,i=e.barYPosition,a=e.goalX,s=e.goalY,n=e.barWidth,r=e.barHeight,o=new I(this.barCtx.ctx),h=o.group({className:"apexcharts-bar-goals-groups"}),c=null;return this.barCtx.isHorizontal?Array.isArray(a)&&a.forEach(function(d){var g=d.attrs.strokeHeight!==void 0?d.attrs.strokeHeight:r/2,f=i+g+r/2;c=o.drawLine(d.x,f-2*g,d.x,f,d.attrs.strokeColor?d.attrs.strokeColor:void 0,d.attrs.strokeDashArray,d.attrs.strokeWidth?d.attrs.strokeWidth:2,d.attrs.strokeLineCap),h.add(c)}):Array.isArray(s)&&s.forEach(function(d){var g=d.attrs.strokeWidth!==void 0?d.attrs.strokeWidth:n/2,f=t+g+n/2;c=o.drawLine(f-2*g,d.y,f,d.y,d.attrs.strokeColor?d.attrs.strokeColor:void 0,d.attrs.strokeDashArray,d.attrs.strokeHeight?d.attrs.strokeHeight:2,d.attrs.strokeLineCap),h.add(c)}),h}}]),v}(),te=function(){function v(e,t){X(this,v),this.ctx=e,this.w=e.w;var i=this.w;this.barOptions=i.config.plotOptions.bar,this.isHorizontal=this.barOptions.horizontal,this.strokeWidth=i.config.stroke.width,this.isNullValue=!1,this.isTimelineBar=i.config.xaxis.type==="datetime"&&i.globals.seriesRangeBarTimeline.length,this.xyRatios=t,this.xyRatios!==null&&(this.xRatio=t.xRatio,this.initialXRatio=t.initialXRatio,this.yRatio=t.yRatio,this.invertedXRatio=t.invertedXRatio,this.invertedYRatio=t.invertedYRatio,this.baseLineY=t.baseLineY,this.baseLineInvertedY=t.baseLineInvertedY),this.yaxisIndex=0,this.seriesLen=0,this.barHelpers=new it(this)}return E(v,[{key:"draw",value:function(e,t){var i=this.w,a=new I(this.ctx),s=new D(this.ctx,i);e=s.getLogSeries(e),this.series=e,this.yRatio=s.getLogYRatios(this.yRatio),this.barHelpers.initVariables(e);var n=a.group({class:"apexcharts-bar-series apexcharts-plot-series"});i.config.dataLabels.enabled&&this.totalItems>this.barOptions.dataLabels.maxItems&&console.warn("WARNING: DataLabels are enabled but there are too many to display. This may cause performance issue when rendering.");for(var r=0,o=0;r<e.length;r++,o++){var h,c,d,g,f=void 0,p=void 0,x=[],b=[],y=i.globals.comboCharts?t[r]:r,k=a.group({class:"apexcharts-series",rel:r+1,seriesName:L.escapeString(i.globals.seriesNames[y]),"data:realIndex":y});this.ctx.series.addCollapsedClassToSeries(k,y),e[r].length>0&&(this.visibleI=this.visibleI+1);var A=0,l=0;this.yRatio.length>1&&(this.yaxisIndex=y),this.isReversed=i.config.yaxis[this.yaxisIndex]&&i.config.yaxis[this.yaxisIndex].reversed;var u=this.barHelpers.initialPositions();p=u.y,A=u.barHeight,c=u.yDivision,g=u.zeroW,f=u.x,l=u.barWidth,h=u.xDivision,d=u.zeroH,this.horizontal||b.push(f+l/2);for(var m=a.group({class:"apexcharts-datalabels","data:realIndex":y}),w=a.group({class:"apexcharts-bar-goals-markers",style:"pointer-events: none"}),S=0;S<i.globals.dataPoints;S++){var P=this.barHelpers.getStrokeWidth(r,S,y),C=null,M={indexes:{i:r,j:S,realIndex:y,bc:o},x:f,y:p,strokeWidth:P,elSeries:k};this.isHorizontal?(C=this.drawBarPaths(F(F({},M),{},{barHeight:A,zeroW:g,yDivision:c})),l=this.series[r][S]/this.invertedYRatio):(C=this.drawColumnPaths(F(F({},M),{},{xDivision:h,barWidth:l,zeroH:d})),A=this.series[r][S]/this.yRatio[this.yaxisIndex]);var T=this.barHelpers.drawGoalLine({barXPosition:C.barXPosition,barYPosition:C.barYPosition,goalX:C.goalX,goalY:C.goalY,barHeight:A,barWidth:l});T&&w.add(T),p=C.y,f=C.x,S>0&&b.push(f+l/2),x.push(p);var z=this.barHelpers.getPathFillColor(e,r,S,y);this.renderSeries({realIndex:y,pathFill:z,j:S,i:r,pathFrom:C.pathFrom,pathTo:C.pathTo,strokeWidth:P,elSeries:k,x:f,y:p,series:e,barHeight:A,barWidth:l,elDataLabelsWrap:m,elGoalsMarkers:w,visibleSeries:this.visibleI,type:"bar"})}i.globals.seriesXvalues[y]=b,i.globals.seriesYvalues[y]=x,n.add(k)}return n}},{key:"renderSeries",value:function(e){var t=e.realIndex,i=e.pathFill,a=e.lineFill,s=e.j,n=e.i,r=e.pathFrom,o=e.pathTo,h=e.strokeWidth,c=e.elSeries,d=e.x,g=e.y,f=e.y1,p=e.y2,x=e.series,b=e.barHeight,y=e.barWidth,k=e.barYPosition,A=e.elDataLabelsWrap,l=e.elGoalsMarkers,u=e.visibleSeries,m=e.type,w=this.w,S=new I(this.ctx);a||(a=this.barOptions.distributed?w.globals.stroke.colors[s]:w.globals.stroke.colors[t]),w.config.series[n].data[s]&&w.config.series[n].data[s].strokeColor&&(a=w.config.series[n].data[s].strokeColor),this.isNullValue&&(i="none");var P=s/w.config.chart.animations.animateGradually.delay*(w.config.chart.animations.speed/w.globals.dataPoints)/2.4,C=S.renderPaths({i:n,j:s,realIndex:t,pathFrom:r,pathTo:o,stroke:a,strokeWidth:h,strokeLineCap:w.config.stroke.lineCap,fill:i,animationDelay:P,initialSpeed:w.config.chart.animations.speed,dataChangeSpeed:w.config.chart.animations.dynamicAnimation.speed,className:"apexcharts-".concat(m,"-area")});C.attr("clip-path","url(#gridRectMask".concat(w.globals.cuid,")"));var M=w.config.forecastDataPoints;M.count>0&&s>=w.globals.dataPoints-M.count&&(C.node.setAttribute("stroke-dasharray",M.dashArray),C.node.setAttribute("stroke-width",M.strokeWidth),C.node.setAttribute("fill-opacity",M.fillOpacity)),f!==void 0&&p!==void 0&&(C.attr("data-range-y1",f),C.attr("data-range-y2",p)),new O(this.ctx).setSelectionFilter(C,t,s),c.add(C);var T=new tt(this).handleBarDataLabels({x:d,y:g,y1:f,y2:p,i:n,j:s,series:x,realIndex:t,barHeight:b,barWidth:y,barYPosition:k,renderedPath:C,visibleSeries:u});return T!==null&&A.add(T),c.add(A),l&&c.add(l),c}},{key:"drawBarPaths",value:function(e){var t=e.indexes,i=e.barHeight,a=e.strokeWidth,s=e.zeroW,n=e.x,r=e.y,o=e.yDivision,h=e.elSeries,c=this.w,d=t.i,g=t.j;c.globals.isXNumeric&&(r=(c.globals.seriesX[d][g]-c.globals.minX)/this.invertedXRatio-i);var f=r+i*this.visibleI;n=this.barHelpers.getXForValue(this.series[d][g],s);var p=this.barHelpers.getBarpaths({barYPosition:f,barHeight:i,x1:s,x2:n,strokeWidth:a,series:this.series,realIndex:t.realIndex,i:d,j:g,w:c});return c.globals.isXNumeric||(r+=o),this.barHelpers.barBackground({j:g,i:d,y1:f-i*this.visibleI,y2:i*this.seriesLen,elSeries:h}),{pathTo:p.pathTo,pathFrom:p.pathFrom,x:n,y:r,goalX:this.barHelpers.getGoalValues("x",s,null,d,g),barYPosition:f}}},{key:"drawColumnPaths",value:function(e){var t=e.indexes,i=e.x,a=e.y,s=e.xDivision,n=e.barWidth,r=e.zeroH,o=e.strokeWidth,h=e.elSeries,c=this.w,d=t.realIndex,g=t.i,f=t.j,p=t.bc;if(c.globals.isXNumeric){var x=d;c.globals.seriesX[d].length||(x=c.globals.maxValsInArrayIndex),i=(c.globals.seriesX[x][f]-c.globals.minX)/this.xRatio-n*this.seriesLen/2}var b=i+n*this.visibleI;a=this.barHelpers.getYForValue(this.series[g][f],r);var y=this.barHelpers.getColumnPaths({barXPosition:b,barWidth:n,y1:r,y2:a,strokeWidth:o,series:this.series,realIndex:t.realIndex,i:g,j:f,w:c});return c.globals.isXNumeric||(i+=s),this.barHelpers.barBackground({bc:p,j:f,i:g,x1:b-o/2-n*this.visibleI,x2:n*this.seriesLen+o/2,elSeries:h}),{pathTo:y.pathTo,pathFrom:y.pathFrom,x:i,y:a,goalY:this.barHelpers.getGoalValues("y",null,r,g,f),barXPosition:b}}},{key:"getPreviousPath",value:function(e,t){for(var i,a=this.w,s=0;s<a.globals.previousPaths.length;s++){var n=a.globals.previousPaths[s];n.paths&&n.paths.length>0&&parseInt(n.realIndex,10)===parseInt(e,10)&&a.globals.previousPaths[s].paths[t]!==void 0&&(i=a.globals.previousPaths[s].paths[t].d)}return i}}]),v}(),W=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.months31=[1,3,5,7,8,10,12],this.months30=[2,4,6,9,11],this.daysCntOfYear=[0,31,59,90,120,151,181,212,243,273,304,334]}return E(v,[{key:"isValidDate",value:function(e){return!isNaN(this.parseDate(e))}},{key:"getTimeStamp",value:function(e){return Date.parse(e)?this.w.config.xaxis.labels.datetimeUTC?new Date(new Date(e).toISOString().substr(0,25)).getTime():new Date(e).getTime():e}},{key:"getDate",value:function(e){return this.w.config.xaxis.labels.datetimeUTC?new Date(new Date(e).toUTCString()):new Date(e)}},{key:"parseDate",value:function(e){var t=Date.parse(e);if(!isNaN(t))return this.getTimeStamp(e);var i=Date.parse(e.replace(/-/g,"/").replace(/[a-z]+/gi," "));return i=this.getTimeStamp(i)}},{key:"parseDateWithTimezone",value:function(e){return Date.parse(e.replace(/-/g,"/").replace(/[a-z]+/gi," "))}},{key:"formatDate",value:function(e,t){var i=this.w.globals.locale,a=this.w.config.xaxis.labels.datetimeUTC,s=["\0"].concat(U(i.months)),n=[""].concat(U(i.shortMonths)),r=[""].concat(U(i.days)),o=[""].concat(U(i.shortDays));function h(S,P){var C=S+"";for(P=P||2;C.length<P;)C="0"+C;return C}var c=a?e.getUTCFullYear():e.getFullYear();t=(t=(t=t.replace(/(^|[^\\])yyyy+/g,"$1"+c)).replace(/(^|[^\\])yy/g,"$1"+c.toString().substr(2,2))).replace(/(^|[^\\])y/g,"$1"+c);var d=(a?e.getUTCMonth():e.getMonth())+1;t=(t=(t=(t=t.replace(/(^|[^\\])MMMM+/g,"$1"+s[0])).replace(/(^|[^\\])MMM/g,"$1"+n[0])).replace(/(^|[^\\])MM/g,"$1"+h(d))).replace(/(^|[^\\])M/g,"$1"+d);var g=a?e.getUTCDate():e.getDate();t=(t=(t=(t=t.replace(/(^|[^\\])dddd+/g,"$1"+r[0])).replace(/(^|[^\\])ddd/g,"$1"+o[0])).replace(/(^|[^\\])dd/g,"$1"+h(g))).replace(/(^|[^\\])d/g,"$1"+g);var f=a?e.getUTCHours():e.getHours(),p=f>12?f-12:f===0?12:f;t=(t=(t=(t=t.replace(/(^|[^\\])HH+/g,"$1"+h(f))).replace(/(^|[^\\])H/g,"$1"+f)).replace(/(^|[^\\])hh+/g,"$1"+h(p))).replace(/(^|[^\\])h/g,"$1"+p);var x=a?e.getUTCMinutes():e.getMinutes();t=(t=t.replace(/(^|[^\\])mm+/g,"$1"+h(x))).replace(/(^|[^\\])m/g,"$1"+x);var b=a?e.getUTCSeconds():e.getSeconds();t=(t=t.replace(/(^|[^\\])ss+/g,"$1"+h(b))).replace(/(^|[^\\])s/g,"$1"+b);var y=a?e.getUTCMilliseconds():e.getMilliseconds();t=t.replace(/(^|[^\\])fff+/g,"$1"+h(y,3)),y=Math.round(y/10),t=t.replace(/(^|[^\\])ff/g,"$1"+h(y)),y=Math.round(y/10);var k=f<12?"AM":"PM";t=(t=(t=t.replace(/(^|[^\\])f/g,"$1"+y)).replace(/(^|[^\\])TT+/g,"$1"+k)).replace(/(^|[^\\])T/g,"$1"+k.charAt(0));var A=k.toLowerCase();t=(t=t.replace(/(^|[^\\])tt+/g,"$1"+A)).replace(/(^|[^\\])t/g,"$1"+A.charAt(0));var l=-e.getTimezoneOffset(),u=a||!l?"Z":l>0?"+":"-";if(!a){var m=(l=Math.abs(l))%60;u+=h(Math.floor(l/60))+":"+h(m)}t=t.replace(/(^|[^\\])K/g,"$1"+u);var w=(a?e.getUTCDay():e.getDay())+1;return t=(t=(t=(t=(t=t.replace(new RegExp(r[0],"g"),r[w])).replace(new RegExp(o[0],"g"),o[w])).replace(new RegExp(s[0],"g"),s[d])).replace(new RegExp(n[0],"g"),n[d])).replace(/\\(.)/g,"$1")}},{key:"getTimeUnitsfromTimestamp",value:function(e,t,i){var a=this.w;a.config.xaxis.min!==void 0&&(e=a.config.xaxis.min),a.config.xaxis.max!==void 0&&(t=a.config.xaxis.max);var s=this.getDate(e),n=this.getDate(t),r=this.formatDate(s,"yyyy MM dd HH mm ss fff").split(" "),o=this.formatDate(n,"yyyy MM dd HH mm ss fff").split(" ");return{minMillisecond:parseInt(r[6],10),maxMillisecond:parseInt(o[6],10),minSecond:parseInt(r[5],10),maxSecond:parseInt(o[5],10),minMinute:parseInt(r[4],10),maxMinute:parseInt(o[4],10),minHour:parseInt(r[3],10),maxHour:parseInt(o[3],10),minDate:parseInt(r[2],10),maxDate:parseInt(o[2],10),minMonth:parseInt(r[1],10)-1,maxMonth:parseInt(o[1],10)-1,minYear:parseInt(r[0],10),maxYear:parseInt(o[0],10)}}},{key:"isLeapYear",value:function(e){return e%4==0&&e%100!=0||e%400==0}},{key:"calculcateLastDaysOfMonth",value:function(e,t,i){return this.determineDaysOfMonths(e,t)-i}},{key:"determineDaysOfYear",value:function(e){var t=365;return this.isLeapYear(e)&&(t=366),t}},{key:"determineRemainingDaysOfYear",value:function(e,t,i){var a=this.daysCntOfYear[t]+i;return t>1&&this.isLeapYear()&&a++,a}},{key:"determineDaysOfMonths",value:function(e,t){var i=30;switch(e=L.monthMod(e),!0){case this.months30.indexOf(e)>-1:e===2&&(i=this.isLeapYear(t)?29:28);break;case this.months31.indexOf(e)>-1:default:i=31}return i}}]),v}(),me=function(v){se(t,te);var e=re(t);function t(){return X(this,t),e.apply(this,arguments)}return E(t,[{key:"draw",value:function(i,a){var s=this.w,n=new I(this.ctx);this.rangeBarOptions=this.w.config.plotOptions.rangeBar,this.series=i,this.seriesRangeStart=s.globals.seriesRangeStart,this.seriesRangeEnd=s.globals.seriesRangeEnd,this.barHelpers.initVariables(i);for(var r=n.group({class:"apexcharts-rangebar-series apexcharts-plot-series"}),o=0;o<i.length;o++){var h,c,d,g=void 0,f=void 0,p=void 0,x=s.globals.comboCharts?a[o]:o,b=n.group({class:"apexcharts-series",seriesName:L.escapeString(s.globals.seriesNames[x]),rel:o+1,"data:realIndex":x});i[o].length>0&&(this.visibleI=this.visibleI+1);var y=0,k=0;this.yRatio.length>1&&(this.yaxisIndex=x);var A=this.barHelpers.initialPositions();f=A.y,d=A.zeroW,g=A.x,k=A.barWidth,h=A.xDivision,c=A.zeroH;for(var l=n.group({class:"apexcharts-datalabels","data:realIndex":x}),u=n.group({class:"apexcharts-rangebar-goals-markers",style:"pointer-events: none"}),m=0;m<s.globals.dataPoints;m++){var w=this.barHelpers.getStrokeWidth(o,m,x),S=this.seriesRangeStart[o][m],P=this.seriesRangeEnd[o][m],C=null,M=null,T={x:g,y:f,strokeWidth:w,elSeries:b};if(p=A.yDivision,y=A.barHeight,this.isHorizontal){M=f+y*this.visibleI;var z=this.seriesLen;s.config.plotOptions.bar.rangeBarGroupRows&&(z=1);var Y=(p-y*z)/2;if(s.config.series[o].data[m]===void 0)break;if(this.isTimelineBar&&s.config.series[o].data[m].x){var H=this.detectOverlappingBars({i:o,j:m,barYPosition:M,srty:Y,barHeight:y,yDivision:p,initPositions:A});y=H.barHeight,M=H.barYPosition}k=(C=this.drawRangeBarPaths(F({indexes:{i:o,j:m,realIndex:x},barHeight:y,barYPosition:M,zeroW:d,yDivision:p,y1:S,y2:P},T))).barWidth}else y=(C=this.drawRangeColumnPaths(F({indexes:{i:o,j:m,realIndex:x},zeroH:c,barWidth:k,xDivision:h},T))).barHeight;var R=this.barHelpers.drawGoalLine({barXPosition:C.barXPosition,barYPosition:M,goalX:C.goalX,goalY:C.goalY,barHeight:y,barWidth:k});R&&u.add(R),f=C.y,g=C.x;var V=this.barHelpers.getPathFillColor(i,o,m,x),G=s.globals.stroke.colors[x];this.renderSeries({realIndex:x,pathFill:V,lineFill:G,j:m,i:o,x:g,y:f,y1:S,y2:P,pathFrom:C.pathFrom,pathTo:C.pathTo,strokeWidth:w,elSeries:b,series:i,barHeight:y,barYPosition:M,barWidth:k,elDataLabelsWrap:l,elGoalsMarkers:u,visibleSeries:this.visibleI,type:"rangebar"})}r.add(b)}return r}},{key:"detectOverlappingBars",value:function(i){var a=i.i,s=i.j,n=i.barYPosition,r=i.srty,o=i.barHeight,h=i.yDivision,c=i.initPositions,d=this.w,g=[],f=d.config.series[a].data[s].rangeName,p=d.config.series[a].data[s].x,x=d.globals.labels.indexOf(p),b=d.globals.seriesRangeBarTimeline[a].findIndex(function(y){return y.x===p&&y.overlaps.length>0});return n=d.config.plotOptions.bar.rangeBarGroupRows?r+h*x:r+o*this.visibleI+h*x,b>-1&&!d.config.plotOptions.bar.rangeBarOverlap&&(g=d.globals.seriesRangeBarTimeline[a][b].overlaps).indexOf(f)>-1&&(n=(o=c.barHeight/g.length)*this.visibleI+h*(100-parseInt(this.barOptions.barHeight,10))/100/2+o*(this.visibleI+g.indexOf(f))+h*x),{barYPosition:n,barHeight:o}}},{key:"drawRangeColumnPaths",value:function(i){var a=i.indexes,s=i.x;i.strokeWidth;var n=i.xDivision,r=i.barWidth,o=i.zeroH,h=this.w,c=a.i,d=a.j,g=this.yRatio[this.yaxisIndex],f=a.realIndex,p=this.getRangeValue(f,d),x=Math.min(p.start,p.end),b=Math.max(p.start,p.end);h.globals.isXNumeric&&(s=(h.globals.seriesX[c][d]-h.globals.minX)/this.xRatio-r/2);var y=s+r*this.visibleI;this.series[c][d]===void 0||this.series[c][d]===null?x=o:(x=o-x/g,b=o-b/g);var k=Math.abs(b-x),A=this.barHelpers.getColumnPaths({barXPosition:y,barWidth:r,y1:x,y2:b,strokeWidth:this.strokeWidth,series:this.seriesRangeEnd,realIndex:a.realIndex,i:f,j:d,w:h});return h.globals.isXNumeric||(s+=n),{pathTo:A.pathTo,pathFrom:A.pathFrom,barHeight:k,x:s,y:b,goalY:this.barHelpers.getGoalValues("y",null,o,c,d),barXPosition:y}}},{key:"drawRangeBarPaths",value:function(i){var a=i.indexes,s=i.y,n=i.y1,r=i.y2,o=i.yDivision,h=i.barHeight,c=i.barYPosition,d=i.zeroW,g=this.w,f=d+n/this.invertedYRatio,p=d+r/this.invertedYRatio,x=Math.abs(p-f),b=this.barHelpers.getBarpaths({barYPosition:c,barHeight:h,x1:f,x2:p,strokeWidth:this.strokeWidth,series:this.seriesRangeEnd,i:a.realIndex,realIndex:a.realIndex,j:a.j,w:g});return g.globals.isXNumeric||(s+=o),{pathTo:b.pathTo,pathFrom:b.pathFrom,barWidth:x,x:p,goalX:this.barHelpers.getGoalValues("x",d,null,a.realIndex,a.j),y:s}}},{key:"getRangeValue",value:function(i,a){var s=this.w;return{start:s.globals.seriesRangeStart[i][a],end:s.globals.seriesRangeEnd[i][a]}}},{key:"getTooltipValues",value:function(i){var a=i.ctx,s=i.seriesIndex,n=i.dataPointIndex,r=i.y1,o=i.y2,h=i.w,c=h.globals.seriesRangeStart[s][n],d=h.globals.seriesRangeEnd[s][n],g=h.globals.labels[n],f=h.config.series[s].name?h.config.series[s].name:"",p=h.config.tooltip.y.formatter,x=h.config.tooltip.y.title.formatter,b={w:h,seriesIndex:s,dataPointIndex:n,start:c,end:d};typeof x=="function"&&(f=x(f,b)),Number.isFinite(r)&&Number.isFinite(o)&&(c=r,d=o,h.config.series[s].data[n].x&&(g=h.config.series[s].data[n].x+":"),typeof p=="function"&&(g=p(g,b)));var y="",k="",A=h.globals.colors[s];if(h.config.tooltip.x.formatter===void 0)if(h.config.xaxis.type==="datetime"){var l=new W(a);y=l.formatDate(l.getDate(c),h.config.tooltip.x.format),k=l.formatDate(l.getDate(d),h.config.tooltip.x.format)}else y=c,k=d;else y=h.config.tooltip.x.formatter(c),k=h.config.tooltip.x.formatter(d);return{start:c,end:d,startVal:y,endVal:k,ylabel:g,color:A,seriesName:f}}},{key:"buildCustomTooltipHTML",value:function(i){var a=i.color,s=i.seriesName;return'<div class="apexcharts-tooltip-rangebar"><div> <span class="series-name" style="color: '+a+'">'+(s||"")+'</span></div><div> <span class="category">'+i.ylabel+' </span> <span class="value start-value">'+i.start+'</span> <span class="separator">-</span> <span class="value end-value">'+i.end+"</span></div></div>"}}]),t}(),oe=function(){function v(e){X(this,v),this.opts=e}return E(v,[{key:"line",value:function(){return{chart:{animations:{easing:"swing"}},dataLabels:{enabled:!1},stroke:{width:5,curve:"straight"},markers:{size:0,hover:{sizeOffset:6}},xaxis:{crosshairs:{width:1}}}}},{key:"sparkline",value:function(e){return this.opts.yaxis[0].show=!1,this.opts.yaxis[0].title.text="",this.opts.yaxis[0].axisBorder.show=!1,this.opts.yaxis[0].axisTicks.show=!1,this.opts.yaxis[0].floating=!0,L.extend(e,{grid:{show:!1,padding:{left:0,right:0,top:0,bottom:0}},legend:{show:!1},xaxis:{labels:{show:!1},tooltip:{enabled:!1},axisBorder:{show:!1},axisTicks:{show:!1}},chart:{toolbar:{show:!1},zoom:{enabled:!1}},dataLabels:{enabled:!1}})}},{key:"bar",value:function(){return{chart:{stacked:!1,animations:{easing:"swing"}},plotOptions:{bar:{dataLabels:{position:"center"}}},dataLabels:{style:{colors:["#fff"]},background:{enabled:!1}},stroke:{width:0,lineCap:"round"},fill:{opacity:.85},legend:{markers:{shape:"square",radius:2,size:8}},tooltip:{shared:!1,intersect:!0},xaxis:{tooltip:{enabled:!1},tickPlacement:"between",crosshairs:{width:"barWidth",position:"back",fill:{type:"gradient"},dropShadow:{enabled:!1},stroke:{width:0}}}}}},{key:"candlestick",value:function(){var e=this;return{stroke:{width:1,colors:["#333"]},fill:{opacity:1},dataLabels:{enabled:!1},tooltip:{shared:!0,custom:function(t){var i=t.seriesIndex,a=t.dataPointIndex,s=t.w;return e._getBoxTooltip(s,i,a,["Open","High","","Low","Close"],"candlestick")}},states:{active:{filter:{type:"none"}}},xaxis:{crosshairs:{width:1}}}}},{key:"boxPlot",value:function(){var e=this;return{chart:{animations:{dynamicAnimation:{enabled:!1}}},stroke:{width:1,colors:["#24292e"]},dataLabels:{enabled:!1},tooltip:{shared:!0,custom:function(t){var i=t.seriesIndex,a=t.dataPointIndex,s=t.w;return e._getBoxTooltip(s,i,a,["Minimum","Q1","Median","Q3","Maximum"],"boxPlot")}},markers:{size:5,strokeWidth:1,strokeColors:"#111"},xaxis:{crosshairs:{width:1}}}}},{key:"rangeBar",value:function(){return{stroke:{width:0,lineCap:"square"},plotOptions:{bar:{borderRadius:0,dataLabels:{position:"center"}}},dataLabels:{enabled:!1,formatter:function(e,t){t.ctx;var i=t.seriesIndex,a=t.dataPointIndex,s=t.w,n=s.globals.seriesRangeStart[i][a];return s.globals.seriesRangeEnd[i][a]-n},background:{enabled:!1},style:{colors:["#fff"]}},tooltip:{shared:!1,followCursor:!0,custom:function(e){return e.w.config.plotOptions&&e.w.config.plotOptions.bar&&e.w.config.plotOptions.bar.horizontal?function(t){var i=new me(t.ctx,null),a=i.getTooltipValues(t),s=a.color,n=a.seriesName,r=a.ylabel,o=a.startVal,h=a.endVal;return i.buildCustomTooltipHTML({color:s,seriesName:n,ylabel:r,start:o,end:h})}(e):function(t){var i=new me(t.ctx,null),a=i.getTooltipValues(t),s=a.color,n=a.seriesName,r=a.ylabel,o=a.start,h=a.end;return i.buildCustomTooltipHTML({color:s,seriesName:n,ylabel:r,start:o,end:h})}(e)}},xaxis:{tickPlacement:"between",tooltip:{enabled:!1},crosshairs:{stroke:{width:0}}}}}},{key:"area",value:function(){return{stroke:{width:4},fill:{type:"gradient",gradient:{inverseColors:!1,shade:"light",type:"vertical",opacityFrom:.65,opacityTo:.5,stops:[0,100,100]}},markers:{size:0,hover:{sizeOffset:6}},tooltip:{followCursor:!1}}}},{key:"brush",value:function(e){return L.extend(e,{chart:{toolbar:{autoSelected:"selection",show:!1},zoom:{enabled:!1}},dataLabels:{enabled:!1},stroke:{width:1},tooltip:{enabled:!1},xaxis:{tooltip:{enabled:!1}}})}},{key:"stacked100",value:function(e){e.dataLabels=e.dataLabels||{},e.dataLabels.formatter=e.dataLabels.formatter||void 0;var t=e.dataLabels.formatter;return e.yaxis.forEach(function(i,a){e.yaxis[a].min=0,e.yaxis[a].max=100}),e.chart.type==="bar"&&(e.dataLabels.formatter=t||function(i){return typeof i=="number"&&i?i.toFixed(0)+"%":i}),e}},{key:"convertCatToNumeric",value:function(e){return e.xaxis.convertedCatToNumeric=!0,e}},{key:"convertCatToNumericXaxis",value:function(e,t,i){e.xaxis.type="numeric",e.xaxis.labels=e.xaxis.labels||{},e.xaxis.labels.formatter=e.xaxis.labels.formatter||function(n){return L.isNumber(n)?Math.floor(n):n};var a=e.xaxis.labels.formatter,s=e.xaxis.categories&&e.xaxis.categories.length?e.xaxis.categories:e.labels;return i&&i.length&&(s=i.map(function(n){return Array.isArray(n)?n:String(n)})),s&&s.length&&(e.xaxis.labels.formatter=function(n){return L.isNumber(n)?a(s[Math.floor(n)-1]):a(n)}),e.xaxis.categories=[],e.labels=[],e.xaxis.tickAmount=e.xaxis.tickAmount||"dataPoints",e}},{key:"bubble",value:function(){return{dataLabels:{style:{colors:["#fff"]}},tooltip:{shared:!1,intersect:!0},xaxis:{crosshairs:{width:0}},fill:{type:"solid",gradient:{shade:"light",inverse:!0,shadeIntensity:.55,opacityFrom:.4,opacityTo:.8}}}}},{key:"scatter",value:function(){return{dataLabels:{enabled:!1},tooltip:{shared:!1,intersect:!0},markers:{size:6,strokeWidth:1,hover:{sizeOffset:2}}}}},{key:"heatmap",value:function(){return{chart:{stacked:!1},fill:{opacity:1},dataLabels:{style:{colors:["#fff"]}},stroke:{colors:["#fff"]},tooltip:{followCursor:!0,marker:{show:!1},x:{show:!1}},legend:{position:"top",markers:{shape:"square",size:10,offsetY:2}},grid:{padding:{right:20}}}}},{key:"treemap",value:function(){return{chart:{zoom:{enabled:!1}},dataLabels:{style:{fontSize:14,fontWeight:600,colors:["#fff"]}},stroke:{show:!0,width:2,colors:["#fff"]},legend:{show:!1},fill:{gradient:{stops:[0,100]}},tooltip:{followCursor:!0,x:{show:!1}},grid:{padding:{left:0,right:0}},xaxis:{crosshairs:{show:!1},tooltip:{enabled:!1}}}}},{key:"pie",value:function(){return{chart:{toolbar:{show:!1}},plotOptions:{pie:{donut:{labels:{show:!1}}}},dataLabels:{formatter:function(e){return e.toFixed(1)+"%"},style:{colors:["#fff"]},background:{enabled:!1},dropShadow:{enabled:!0}},stroke:{colors:["#fff"]},fill:{opacity:1,gradient:{shade:"light",stops:[0,100]}},tooltip:{theme:"dark",fillSeriesColor:!0},legend:{position:"right"}}}},{key:"donut",value:function(){return{chart:{toolbar:{show:!1}},dataLabels:{formatter:function(e){return e.toFixed(1)+"%"},style:{colors:["#fff"]},background:{enabled:!1},dropShadow:{enabled:!0}},stroke:{colors:["#fff"]},fill:{opacity:1,gradient:{shade:"light",shadeIntensity:.35,stops:[80,100],opacityFrom:1,opacityTo:1}},tooltip:{theme:"dark",fillSeriesColor:!0},legend:{position:"right"}}}},{key:"polarArea",value:function(){return this.opts.yaxis[0].tickAmount=this.opts.yaxis[0].tickAmount?this.opts.yaxis[0].tickAmount:6,{chart:{toolbar:{show:!1}},dataLabels:{formatter:function(e){return e.toFixed(1)+"%"},enabled:!1},stroke:{show:!0,width:2},fill:{opacity:.7},tooltip:{theme:"dark",fillSeriesColor:!0},legend:{position:"right"}}}},{key:"radar",value:function(){return this.opts.yaxis[0].labels.offsetY=this.opts.yaxis[0].labels.offsetY?this.opts.yaxis[0].labels.offsetY:6,{dataLabels:{enabled:!1,style:{fontSize:"11px"}},stroke:{width:2},markers:{size:3,strokeWidth:1,strokeOpacity:1},fill:{opacity:.2},tooltip:{shared:!1,intersect:!0,followCursor:!0},grid:{show:!1},xaxis:{labels:{formatter:function(e){return e},style:{colors:["#a8a8a8"],fontSize:"11px"}},tooltip:{enabled:!1},crosshairs:{show:!1}}}}},{key:"radialBar",value:function(){return{chart:{animations:{dynamicAnimation:{enabled:!0,speed:800}},toolbar:{show:!1}},fill:{gradient:{shade:"dark",shadeIntensity:.4,inverseColors:!1,type:"diagonal2",opacityFrom:1,opacityTo:1,stops:[70,98,100]}},legend:{show:!1,position:"right"},tooltip:{enabled:!1,fillSeriesColor:!0}}}},{key:"_getBoxTooltip",value:function(e,t,i,a,s){var n=e.globals.seriesCandleO[t][i],r=e.globals.seriesCandleH[t][i],o=e.globals.seriesCandleM[t][i],h=e.globals.seriesCandleL[t][i],c=e.globals.seriesCandleC[t][i];return e.config.series[t].type&&e.config.series[t].type!==s?`<div class="apexcharts-custom-tooltip">
          `.concat(e.config.series[t].name?e.config.series[t].name:"series-"+(t+1),": <strong>").concat(e.globals.series[t][i],`</strong>
        </div>`):'<div class="apexcharts-tooltip-box apexcharts-tooltip-'.concat(e.config.chart.type,'">')+"<div>".concat(a[0],': <span class="value">')+n+"</span></div>"+"<div>".concat(a[1],': <span class="value">')+r+"</span></div>"+(o?"<div>".concat(a[2],': <span class="value">')+o+"</span></div>":"")+"<div>".concat(a[3],': <span class="value">')+h+"</span></div>"+"<div>".concat(a[4],': <span class="value">')+c+"</span></div></div>"}}]),v}(),le=function(){function v(e){X(this,v),this.opts=e}return E(v,[{key:"init",value:function(e){var t=e.responsiveOverride,i=this.opts,a=new $,s=new oe(i);this.chartType=i.chart.type,this.chartType==="histogram"&&(i.chart.type="bar",i=L.extend({plotOptions:{bar:{columnWidth:"99.99%"}}},i)),i=this.extendYAxis(i),i=this.extendAnnotations(i);var n=a.init(),r={};if(i&&N(i)==="object"){var o={};o=["line","area","bar","candlestick","boxPlot","rangeBar","histogram","bubble","scatter","heatmap","treemap","pie","polarArea","donut","radar","radialBar"].indexOf(i.chart.type)!==-1?s[i.chart.type]():s.line(),i.chart.brush&&i.chart.brush.enabled&&(o=s.brush(o)),i.chart.stacked&&i.chart.stackType==="100%"&&(i=s.stacked100(i)),this.checkForDarkTheme(window.Apex),this.checkForDarkTheme(i),i.xaxis=i.xaxis||window.Apex.xaxis||{},t||(i.xaxis.convertedCatToNumeric=!1),((i=this.checkForCatToNumericXAxis(this.chartType,o,i)).chart.sparkline&&i.chart.sparkline.enabled||window.Apex.chart&&window.Apex.chart.sparkline&&window.Apex.chart.sparkline.enabled)&&(o=s.sparkline(o)),r=L.extend(n,o)}var h=L.extend(r,window.Apex);return n=L.extend(h,i),n=this.handleUserInputErrors(n)}},{key:"checkForCatToNumericXAxis",value:function(e,t,i){var a=new oe(i),s=e==="bar"&&i.plotOptions&&i.plotOptions.bar&&i.plotOptions.bar.horizontal,n=e==="pie"||e==="polarArea"||e==="donut"||e==="radar"||e==="radialBar"||e==="heatmap",r=i.xaxis.type!=="datetime"&&i.xaxis.type!=="numeric",o=i.xaxis.tickPlacement?i.xaxis.tickPlacement:t.xaxis&&t.xaxis.tickPlacement;return s||n||!r||o==="between"||(i=a.convertCatToNumeric(i)),i}},{key:"extendYAxis",value:function(e,t){var i=new $;(e.yaxis===void 0||!e.yaxis||Array.isArray(e.yaxis)&&e.yaxis.length===0)&&(e.yaxis={}),e.yaxis.constructor!==Array&&window.Apex.yaxis&&window.Apex.yaxis.constructor!==Array&&(e.yaxis=L.extend(e.yaxis,window.Apex.yaxis)),e.yaxis.constructor!==Array?e.yaxis=[L.extend(i.yAxis,e.yaxis)]:e.yaxis=L.extendArray(e.yaxis,i.yAxis);var a=!1;e.yaxis.forEach(function(n){n.logarithmic&&(a=!0)});var s=e.series;return t&&!s&&(s=t.config.series),a&&s.length!==e.yaxis.length&&s.length&&(e.yaxis=s.map(function(n,r){if(n.name||(s[r].name="series-".concat(r+1)),e.yaxis[r])return e.yaxis[r].seriesName=s[r].name,e.yaxis[r];var o=L.extend(i.yAxis,e.yaxis[0]);return o.show=!1,o})),a&&s.length>1&&s.length!==e.yaxis.length&&console.warn("A multi-series logarithmic chart should have equal number of series and y-axes. Please make sure to equalize both."),e}},{key:"extendAnnotations",value:function(e){return e.annotations===void 0&&(e.annotations={},e.annotations.yaxis=[],e.annotations.xaxis=[],e.annotations.points=[]),e=this.extendYAxisAnnotations(e),e=this.extendXAxisAnnotations(e),e=this.extendPointAnnotations(e)}},{key:"extendYAxisAnnotations",value:function(e){var t=new $;return e.annotations.yaxis=L.extendArray(e.annotations.yaxis!==void 0?e.annotations.yaxis:[],t.yAxisAnnotation),e}},{key:"extendXAxisAnnotations",value:function(e){var t=new $;return e.annotations.xaxis=L.extendArray(e.annotations.xaxis!==void 0?e.annotations.xaxis:[],t.xAxisAnnotation),e}},{key:"extendPointAnnotations",value:function(e){var t=new $;return e.annotations.points=L.extendArray(e.annotations.points!==void 0?e.annotations.points:[],t.pointAnnotation),e}},{key:"checkForDarkTheme",value:function(e){e.theme&&e.theme.mode==="dark"&&(e.tooltip||(e.tooltip={}),e.tooltip.theme!=="light"&&(e.tooltip.theme="dark"),e.chart.foreColor||(e.chart.foreColor="#f6f7f8"),e.chart.background||(e.chart.background="#424242"),e.theme.palette||(e.theme.palette="palette4"))}},{key:"handleUserInputErrors",value:function(e){var t=e;if(t.tooltip.shared&&t.tooltip.intersect)throw new Error("tooltip.shared cannot be enabled when tooltip.intersect is true. Turn off any other option by setting it to false.");if(t.chart.type==="bar"&&t.plotOptions.bar.horizontal){if(t.yaxis.length>1)throw new Error("Multiple Y Axis for bars are not supported. Switch to column chart by setting plotOptions.bar.horizontal=false");t.yaxis[0].reversed&&(t.yaxis[0].opposite=!0),t.xaxis.tooltip.enabled=!1,t.yaxis[0].tooltip.enabled=!1,t.chart.zoom.enabled=!1}return t.chart.type!=="bar"&&t.chart.type!=="rangeBar"||t.tooltip.shared&&t.xaxis.crosshairs.width==="barWidth"&&t.series.length>1&&(t.xaxis.crosshairs.width="tickWidth"),t.chart.type!=="candlestick"&&t.chart.type!=="boxPlot"||t.yaxis[0].reversed&&(console.warn("Reversed y-axis in ".concat(t.chart.type," chart is not supported.")),t.yaxis[0].reversed=!1),t.chart.group&&t.yaxis[0].labels.minWidth===0&&console.warn("It looks like you have multiple charts in synchronization. You must provide yaxis.labels.minWidth which must be EQUAL for all grouped charts to prevent incorrect behaviour."),Array.isArray(t.stroke.width)&&t.chart.type!=="line"&&t.chart.type!=="area"&&(console.warn("stroke.width option accepts array only for line and area charts. Reverted back to Number"),t.stroke.width=t.stroke.width[0]),t}}]),v}(),Ye=function(){function v(){X(this,v)}return E(v,[{key:"initGlobalVars",value:function(e){e.series=[],e.seriesCandleO=[],e.seriesCandleH=[],e.seriesCandleM=[],e.seriesCandleL=[],e.seriesCandleC=[],e.seriesRangeStart=[],e.seriesRangeEnd=[],e.seriesRangeBarTimeline=[],e.seriesPercent=[],e.seriesGoals=[],e.seriesX=[],e.seriesZ=[],e.seriesNames=[],e.seriesTotals=[],e.seriesLog=[],e.seriesColors=[],e.stackedSeriesTotals=[],e.seriesXvalues=[],e.seriesYvalues=[],e.labels=[],e.categoryLabels=[],e.timescaleLabels=[],e.noLabelsProvided=!1,e.resizeTimer=null,e.selectionResizeTimer=null,e.delayedElements=[],e.pointsArray=[],e.dataLabelsRects=[],e.isXNumeric=!1,e.xaxisLabelsCount=0,e.skipLastTimelinelabel=!1,e.skipFirstTimelinelabel=!1,e.isDataXYZ=!1,e.isMultiLineX=!1,e.isMultipleYAxis=!1,e.maxY=-Number.MAX_VALUE,e.minY=Number.MIN_VALUE,e.minYArr=[],e.maxYArr=[],e.maxX=-Number.MAX_VALUE,e.minX=Number.MAX_VALUE,e.initialMaxX=-Number.MAX_VALUE,e.initialMinX=Number.MAX_VALUE,e.maxDate=0,e.minDate=Number.MAX_VALUE,e.minZ=Number.MAX_VALUE,e.maxZ=-Number.MAX_VALUE,e.minXDiff=Number.MAX_VALUE,e.yAxisScale=[],e.xAxisScale=null,e.xAxisTicksPositions=[],e.yLabelsCoords=[],e.yTitleCoords=[],e.barPadForNumericAxis=0,e.padHorizontal=0,e.xRange=0,e.yRange=[],e.zRange=0,e.dataPoints=0,e.xTickAmount=0}},{key:"globalVars",value:function(e){return{chartID:null,cuid:null,events:{beforeMount:[],mounted:[],updated:[],clicked:[],selection:[],dataPointSelection:[],zoomed:[],scrolled:[]},colors:[],clientX:null,clientY:null,fill:{colors:[]},stroke:{colors:[]},dataLabels:{style:{colors:[]}},radarPolygons:{fill:{colors:[]}},markers:{colors:[],size:e.markers.size,largestSize:0},animationEnded:!1,isTouchDevice:"ontouchstart"in window||navigator.msMaxTouchPoints,isDirty:!1,isExecCalled:!1,initialConfig:null,initialSeries:[],lastXAxis:[],lastYAxis:[],columnSeries:null,labels:[],timescaleLabels:[],noLabelsProvided:!1,allSeriesCollapsed:!1,collapsedSeries:[],collapsedSeriesIndices:[],ancillaryCollapsedSeries:[],ancillaryCollapsedSeriesIndices:[],risingSeries:[],dataFormatXNumeric:!1,capturedSeriesIndex:-1,capturedDataPointIndex:-1,selectedDataPoints:[],goldenPadding:35,invalidLogScale:!1,ignoreYAxisIndexes:[],yAxisSameScaleIndices:[],maxValsInArrayIndex:0,radialSize:0,selection:void 0,zoomEnabled:e.chart.toolbar.autoSelected==="zoom"&&e.chart.toolbar.tools.zoom&&e.chart.zoom.enabled,panEnabled:e.chart.toolbar.autoSelected==="pan"&&e.chart.toolbar.tools.pan,selectionEnabled:e.chart.toolbar.autoSelected==="selection"&&e.chart.toolbar.tools.selection,yaxis:null,mousedown:!1,lastClientPosition:{},visibleXRange:void 0,yValueDecimal:0,total:0,SVGNS:"http://www.w3.org/2000/svg",svgWidth:0,svgHeight:0,noData:!1,locale:{},dom:{},memory:{methodsToExec:[]},shouldAnimate:!0,skipLastTimelinelabel:!1,skipFirstTimelinelabel:!1,delayedElements:[],axisCharts:!0,isDataXYZ:!1,resized:!1,resizeTimer:null,comboCharts:!1,dataChanged:!1,previousPaths:[],allSeriesHasEqualX:!0,pointsArray:[],dataLabelsRects:[],lastDrawnDataLabelsIndexes:[],hasNullValues:!1,easing:null,zoomed:!1,gridWidth:0,gridHeight:0,rotateXLabels:!1,defaultLabels:!1,xLabelFormatter:void 0,yLabelFormatters:[],xaxisTooltipFormatter:void 0,ttKeyFormatter:void 0,ttVal:void 0,ttZFormatter:void 0,LINE_HEIGHT_RATIO:1.618,xAxisLabelsHeight:0,xAxisLabelsWidth:0,yAxisLabelsWidth:0,scaleX:1,scaleY:1,translateX:0,translateY:0,translateYAxisX:[],yAxisWidths:[],translateXAxisY:0,translateXAxisX:0,tooltip:null}}},{key:"init",value:function(e){var t=this.globalVars(e);return this.initGlobalVars(t),t.initialConfig=L.extend({},e),t.initialSeries=L.clone(e.series),t.lastXAxis=L.clone(t.initialConfig.xaxis),t.lastYAxis=L.clone(t.initialConfig.yaxis),t}}]),v}(),at=function(){function v(e){X(this,v),this.opts=e}return E(v,[{key:"init",value:function(){var e=new le(this.opts).init({responsiveOverride:!1});return{config:e,globals:new Ye().init(e)}}}]),v}(),Fe=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.twoDSeries=[],this.threeDSeries=[],this.twoDSeriesX=[],this.seriesGoals=[],this.coreUtils=new D(this.ctx)}return E(v,[{key:"isMultiFormat",value:function(){return this.isFormatXY()||this.isFormat2DArray()}},{key:"isFormatXY",value:function(){var e=this.w.config.series.slice(),t=new j(this.ctx);if(this.activeSeriesIndex=t.getActiveConfigSeriesIndex(),e[this.activeSeriesIndex].data!==void 0&&e[this.activeSeriesIndex].data.length>0&&e[this.activeSeriesIndex].data[0]!==null&&e[this.activeSeriesIndex].data[0].x!==void 0&&e[this.activeSeriesIndex].data[0]!==null)return!0}},{key:"isFormat2DArray",value:function(){var e=this.w.config.series.slice(),t=new j(this.ctx);if(this.activeSeriesIndex=t.getActiveConfigSeriesIndex(),e[this.activeSeriesIndex].data!==void 0&&e[this.activeSeriesIndex].data.length>0&&e[this.activeSeriesIndex].data[0]!==void 0&&e[this.activeSeriesIndex].data[0]!==null&&e[this.activeSeriesIndex].data[0].constructor===Array)return!0}},{key:"handleFormat2DArray",value:function(e,t){for(var i=this.w.config,a=this.w.globals,s=i.chart.type==="boxPlot"||i.series[t].type==="boxPlot",n=0;n<e[t].data.length;n++)if(e[t].data[n][1]!==void 0&&(Array.isArray(e[t].data[n][1])&&e[t].data[n][1].length===4&&!s?this.twoDSeries.push(L.parseNumber(e[t].data[n][1][3])):e[t].data[n].length>=5?this.twoDSeries.push(L.parseNumber(e[t].data[n][4])):this.twoDSeries.push(L.parseNumber(e[t].data[n][1])),a.dataFormatXNumeric=!0),i.xaxis.type==="datetime"){var r=new Date(e[t].data[n][0]);r=new Date(r).getTime(),this.twoDSeriesX.push(r)}else this.twoDSeriesX.push(e[t].data[n][0]);for(var o=0;o<e[t].data.length;o++)e[t].data[o][2]!==void 0&&(this.threeDSeries.push(e[t].data[o][2]),a.isDataXYZ=!0)}},{key:"handleFormatXY",value:function(e,t){var i=this.w.config,a=this.w.globals,s=new W(this.ctx),n=t;a.collapsedSeriesIndices.indexOf(t)>-1&&(n=this.activeSeriesIndex);for(var r=0;r<e[t].data.length;r++)e[t].data[r].y!==void 0&&(Array.isArray(e[t].data[r].y)?this.twoDSeries.push(L.parseNumber(e[t].data[r].y[e[t].data[r].y.length-1])):this.twoDSeries.push(L.parseNumber(e[t].data[r].y))),e[t].data[r].goals!==void 0&&Array.isArray(e[t].data[r].goals)?(this.seriesGoals[t]===void 0&&(this.seriesGoals[t]=[]),this.seriesGoals[t].push(e[t].data[r].goals)):(this.seriesGoals[t]===void 0&&(this.seriesGoals[t]=[]),this.seriesGoals[t].push(null));for(var o=0;o<e[n].data.length;o++){var h=typeof e[n].data[o].x=="string",c=Array.isArray(e[n].data[o].x),d=!c&&!!s.isValidDate(e[n].data[o].x.toString());if(h||d)if(h||i.xaxis.convertedCatToNumeric){var g=a.isBarHorizontal&&a.isRangeData;i.xaxis.type!=="datetime"||g?(this.fallbackToCategory=!0,this.twoDSeriesX.push(e[n].data[o].x)):this.twoDSeriesX.push(s.parseDate(e[n].data[o].x))}else i.xaxis.type==="datetime"?this.twoDSeriesX.push(s.parseDate(e[n].data[o].x.toString())):(a.dataFormatXNumeric=!0,a.isXNumeric=!0,this.twoDSeriesX.push(parseFloat(e[n].data[o].x)));else c?(this.fallbackToCategory=!0,this.twoDSeriesX.push(e[n].data[o].x)):(a.isXNumeric=!0,a.dataFormatXNumeric=!0,this.twoDSeriesX.push(e[n].data[o].x))}if(e[t].data[0]&&e[t].data[0].z!==void 0){for(var f=0;f<e[t].data.length;f++)this.threeDSeries.push(e[t].data[f].z);a.isDataXYZ=!0}}},{key:"handleRangeData",value:function(e,t){var i=this.w.config,a=this.w.globals,s={};return this.isFormat2DArray()?s=this.handleRangeDataFormat("array",e,t):this.isFormatXY()&&(s=this.handleRangeDataFormat("xy",e,t)),a.seriesRangeStart.push(s.start),a.seriesRangeEnd.push(s.end),i.xaxis.type==="datetime"&&a.seriesRangeBarTimeline.push(s.rangeUniques),a.seriesRangeBarTimeline.forEach(function(n,r){n&&n.forEach(function(o,h){o.y.forEach(function(c,d){for(var g=0;g<o.y.length;g++)if(d!==g){var f=c.y1,p=c.y2,x=o.y[g].y1;f<=o.y[g].y2&&x<=p&&(o.overlaps.indexOf(c.rangeName)<0&&o.overlaps.push(c.rangeName),o.overlaps.indexOf(o.y[g].rangeName)<0&&o.overlaps.push(o.y[g].rangeName))}})})}),s}},{key:"handleCandleStickBoxData",value:function(e,t){var i=this.w.globals,a={};return this.isFormat2DArray()?a=this.handleCandleStickBoxDataFormat("array",e,t):this.isFormatXY()&&(a=this.handleCandleStickBoxDataFormat("xy",e,t)),i.seriesCandleO[t]=a.o,i.seriesCandleH[t]=a.h,i.seriesCandleM[t]=a.m,i.seriesCandleL[t]=a.l,i.seriesCandleC[t]=a.c,a}},{key:"handleRangeDataFormat",value:function(e,t,i){var a=[],s=[],n=t[i].data.filter(function(g,f,p){return f===p.findIndex(function(x){return x.x===g.x})}).map(function(g,f){return{x:g.x,overlaps:[],y:[]}}),r="Please provide [Start, End] values in valid format. Read more https://apexcharts.com/docs/series/#rangecharts",o=new j(this.ctx).getActiveConfigSeriesIndex();if(e==="array"){if(t[o].data[0][1].length!==2)throw new Error(r);for(var h=0;h<t[i].data.length;h++)a.push(t[i].data[h][1][0]),s.push(t[i].data[h][1][1])}else if(e==="xy"){if(t[o].data[0].y.length!==2)throw new Error(r);for(var c=function(g){var f=L.randomId(),p=t[i].data[g].x,x={y1:t[i].data[g].y[0],y2:t[i].data[g].y[1],rangeName:f};t[i].data[g].rangeName=f;var b=n.findIndex(function(y){return y.x===p});n[b].y.push(x),a.push(x.y1),s.push(x.y2)},d=0;d<t[i].data.length;d++)c(d)}return{start:a,end:s,rangeUniques:n}}},{key:"handleCandleStickBoxDataFormat",value:function(e,t,i){var a=this.w,s=a.config.chart.type==="boxPlot"||a.config.series[i].type==="boxPlot",n=[],r=[],o=[],h=[],c=[];if(e==="array")if(s&&t[i].data[0].length===6||!s&&t[i].data[0].length===5)for(var d=0;d<t[i].data.length;d++)n.push(t[i].data[d][1]),r.push(t[i].data[d][2]),s?(o.push(t[i].data[d][3]),h.push(t[i].data[d][4]),c.push(t[i].data[d][5])):(h.push(t[i].data[d][3]),c.push(t[i].data[d][4]));else for(var g=0;g<t[i].data.length;g++)Array.isArray(t[i].data[g][1])&&(n.push(t[i].data[g][1][0]),r.push(t[i].data[g][1][1]),s?(o.push(t[i].data[g][1][2]),h.push(t[i].data[g][1][3]),c.push(t[i].data[g][1][4])):(h.push(t[i].data[g][1][2]),c.push(t[i].data[g][1][3])));else if(e==="xy")for(var f=0;f<t[i].data.length;f++)Array.isArray(t[i].data[f].y)&&(n.push(t[i].data[f].y[0]),r.push(t[i].data[f].y[1]),s?(o.push(t[i].data[f].y[2]),h.push(t[i].data[f].y[3]),c.push(t[i].data[f].y[4])):(h.push(t[i].data[f].y[2]),c.push(t[i].data[f].y[3])));return{o:n,h:r,m:o,l:h,c}}},{key:"parseDataAxisCharts",value:function(e){var t=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.ctx,a=this.w.config,s=this.w.globals,n=new W(i),r=a.labels.length>0?a.labels.slice():a.xaxis.categories.slice();s.isTimelineBar=a.chart.type==="rangeBar"&&a.xaxis.type==="datetime";for(var o=function(){for(var d=0;d<r.length;d++)if(typeof r[d]=="string"){if(!n.isValidDate(r[d]))throw new Error("You have provided invalid Date format. Please provide a valid JavaScript Date");t.twoDSeriesX.push(n.parseDate(r[d]))}else t.twoDSeriesX.push(r[d])},h=0;h<e.length;h++){if(this.twoDSeries=[],this.twoDSeriesX=[],this.threeDSeries=[],e[h].data===void 0)return void console.error("It is a possibility that you may have not included 'data' property in series.");if(a.chart.type!=="rangeBar"&&a.chart.type!=="rangeArea"&&e[h].type!=="rangeBar"&&e[h].type!=="rangeArea"||(s.isRangeData=!0,this.handleRangeData(e,h)),this.isMultiFormat())this.isFormat2DArray()?this.handleFormat2DArray(e,h):this.isFormatXY()&&this.handleFormatXY(e,h),a.chart.type!=="candlestick"&&e[h].type!=="candlestick"&&a.chart.type!=="boxPlot"&&e[h].type!=="boxPlot"||this.handleCandleStickBoxData(e,h),s.series.push(this.twoDSeries),s.labels.push(this.twoDSeriesX),s.seriesX.push(this.twoDSeriesX),s.seriesGoals=this.seriesGoals,h!==this.activeSeriesIndex||this.fallbackToCategory||(s.isXNumeric=!0);else{a.xaxis.type==="datetime"?(s.isXNumeric=!0,o(),s.seriesX.push(this.twoDSeriesX)):a.xaxis.type==="numeric"&&(s.isXNumeric=!0,r.length>0&&(this.twoDSeriesX=r,s.seriesX.push(this.twoDSeriesX))),s.labels.push(this.twoDSeriesX);var c=e[h].data.map(function(d){return L.parseNumber(d)});s.series.push(c)}s.seriesZ.push(this.threeDSeries),e[h].name!==void 0?s.seriesNames.push(e[h].name):s.seriesNames.push("series-"+parseInt(h+1,10)),e[h].color!==void 0?s.seriesColors.push(e[h].color):s.seriesColors.push(void 0)}return this.w}},{key:"parseDataNonAxisCharts",value:function(e){var t=this.w.globals,i=this.w.config;t.series=e.slice(),t.seriesNames=i.labels.slice();for(var a=0;a<t.series.length;a++)t.seriesNames[a]===void 0&&t.seriesNames.push("series-"+(a+1));return this.w}},{key:"handleExternalLabelsData",value:function(e){var t=this.w.config,i=this.w.globals;t.xaxis.categories.length>0?i.labels=t.xaxis.categories:t.labels.length>0?i.labels=t.labels.slice():this.fallbackToCategory?(i.labels=i.labels[0],i.seriesRangeBarTimeline.length&&(i.seriesRangeBarTimeline.map(function(a){a.forEach(function(s){i.labels.indexOf(s.x)<0&&s.x&&i.labels.push(s.x)})}),i.labels=i.labels.filter(function(a,s,n){return n.indexOf(a)===s})),t.xaxis.convertedCatToNumeric&&(new oe(t).convertCatToNumericXaxis(t,this.ctx,i.seriesX[0]),this._generateExternalLabels(e))):this._generateExternalLabels(e)}},{key:"_generateExternalLabels",value:function(e){var t=this.w.globals,i=this.w.config,a=[];if(t.axisCharts){if(t.series.length>0)for(var s=0;s<t.series[t.maxValsInArrayIndex].length;s++)a.push(s+1);t.seriesX=[];for(var n=0;n<e.length;n++)t.seriesX.push(a);t.isXNumeric=!0}if(a.length===0){a=t.axisCharts?[]:t.series.map(function(o,h){return h+1});for(var r=0;r<e.length;r++)t.seriesX.push(a)}t.labels=a,i.xaxis.convertedCatToNumeric&&(t.categoryLabels=a.map(function(o){return i.xaxis.labels.formatter(o)})),t.noLabelsProvided=!0}},{key:"parseData",value:function(e){var t=this.w,i=t.config,a=t.globals;if(this.excludeCollapsedSeriesInYAxis(),this.fallbackToCategory=!1,this.ctx.core.resetGlobals(),this.ctx.core.isMultipleY(),a.axisCharts?this.parseDataAxisCharts(e):this.parseDataNonAxisCharts(e),this.coreUtils.getLargestSeries(),i.chart.type==="bar"&&i.chart.stacked){var s=new j(this.ctx);a.series=s.setNullSeriesToZeroValues(a.series)}this.coreUtils.getSeriesTotals(),a.axisCharts&&this.coreUtils.getStackedSeriesTotals(),this.coreUtils.getPercentSeries(),a.dataFormatXNumeric||a.isXNumeric&&(i.xaxis.type!=="numeric"||i.labels.length!==0||i.xaxis.categories.length!==0)||this.handleExternalLabelsData(e);for(var n=this.coreUtils.getCategoryLabels(a.labels),r=0;r<n.length;r++)if(Array.isArray(n[r])){a.isMultiLineX=!0;break}}},{key:"excludeCollapsedSeriesInYAxis",value:function(){var e=this,t=this.w;t.globals.ignoreYAxisIndexes=t.globals.collapsedSeries.map(function(i,a){if(e.w.globals.isMultipleYAxis&&!t.config.chart.stacked)return i.index})}}]),v}(),ge=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.tooltipKeyFormat="dd MMM"}return E(v,[{key:"xLabelFormat",value:function(e,t,i,a){var s=this.w;if(s.config.xaxis.type==="datetime"&&s.config.xaxis.labels.formatter===void 0&&s.config.tooltip.x.formatter===void 0){var n=new W(this.ctx);return n.formatDate(n.getDate(t),s.config.tooltip.x.format)}return e(t,i,a)}},{key:"defaultGeneralFormatter",value:function(e){return Array.isArray(e)?e.map(function(t){return t}):e}},{key:"defaultYFormatter",value:function(e,t,i){var a=this.w;return L.isNumber(e)&&(e=a.globals.yValueDecimal!==0?e.toFixed(t.decimalsInFloat!==void 0?t.decimalsInFloat:a.globals.yValueDecimal):a.globals.maxYArr[i]-a.globals.minYArr[i]<5?e.toFixed(1):e.toFixed(0)),e}},{key:"setLabelFormatters",value:function(){var e=this,t=this.w;return t.globals.xaxisTooltipFormatter=function(i){return e.defaultGeneralFormatter(i)},t.globals.ttKeyFormatter=function(i){return e.defaultGeneralFormatter(i)},t.globals.ttZFormatter=function(i){return i},t.globals.legendFormatter=function(i){return e.defaultGeneralFormatter(i)},t.config.xaxis.labels.formatter!==void 0?t.globals.xLabelFormatter=t.config.xaxis.labels.formatter:t.globals.xLabelFormatter=function(i){if(L.isNumber(i)){if(!t.config.xaxis.convertedCatToNumeric&&t.config.xaxis.type==="numeric"){if(L.isNumber(t.config.xaxis.decimalsInFloat))return i.toFixed(t.config.xaxis.decimalsInFloat);var a=t.globals.maxX-t.globals.minX;return a>0&&a<100?i.toFixed(1):i.toFixed(0)}return t.globals.isBarHorizontal&&t.globals.maxY-t.globals.minYArr<4?i.toFixed(1):i.toFixed(0)}return i},typeof t.config.tooltip.x.formatter=="function"?t.globals.ttKeyFormatter=t.config.tooltip.x.formatter:t.globals.ttKeyFormatter=t.globals.xLabelFormatter,typeof t.config.xaxis.tooltip.formatter=="function"&&(t.globals.xaxisTooltipFormatter=t.config.xaxis.tooltip.formatter),(Array.isArray(t.config.tooltip.y)||t.config.tooltip.y.formatter!==void 0)&&(t.globals.ttVal=t.config.tooltip.y),t.config.tooltip.z.formatter!==void 0&&(t.globals.ttZFormatter=t.config.tooltip.z.formatter),t.config.legend.formatter!==void 0&&(t.globals.legendFormatter=t.config.legend.formatter),t.config.yaxis.forEach(function(i,a){i.labels.formatter!==void 0?t.globals.yLabelFormatters[a]=i.labels.formatter:t.globals.yLabelFormatters[a]=function(s){return t.globals.xyCharts?Array.isArray(s)?s.map(function(n){return e.defaultYFormatter(n,i,a)}):e.defaultYFormatter(s,i,a):s}}),t.globals}},{key:"heatmapLabelFormatters",value:function(){var e=this.w;if(e.config.chart.type==="heatmap"){e.globals.yAxisScale[0].result=e.globals.seriesNames.slice();var t=e.globals.seriesNames.reduce(function(i,a){return i.length>a.length?i:a},0);e.globals.yAxisScale[0].niceMax=t,e.globals.yAxisScale[0].niceMin=t}}}]),v}(),J=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"getLabel",value:function(e,t,i,a){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:[],n=arguments.length>5&&arguments[5]!==void 0?arguments[5]:"12px",r=this.w,o=e[a]===void 0?"":e[a],h=o,c=r.globals.xLabelFormatter,d=r.config.xaxis.labels.formatter,g=!1,f=new ge(this.ctx),p=o;h=f.xLabelFormat(c,o,p,{i:a,dateFormatter:new W(this.ctx).formatDate,w:r}),d!==void 0&&(h=d(o,e[a],{i:a,dateFormatter:new W(this.ctx).formatDate,w:r}));var x=function(A){var l=null;return t.forEach(function(u){u.unit==="month"?l="year":u.unit==="day"?l="month":u.unit==="hour"?l="day":u.unit==="minute"&&(l="hour")}),l===A};t.length>0?(g=x(t[a].unit),i=t[a].position,h=t[a].value):r.config.xaxis.type==="datetime"&&d===void 0&&(h=""),h===void 0&&(h=""),h=Array.isArray(h)?h:h.toString();var b=new I(this.ctx),y={};y=r.globals.rotateXLabels?b.getTextRects(h,parseInt(n,10),null,"rotate(".concat(r.config.xaxis.labels.rotate," 0 0)"),!1):b.getTextRects(h,parseInt(n,10));var k=!r.config.xaxis.labels.showDuplicates&&this.ctx.timeScale;return!Array.isArray(h)&&(h.indexOf("NaN")===0||h.toLowerCase().indexOf("invalid")===0||h.toLowerCase().indexOf("infinity")>=0||s.indexOf(h)>=0&&k)&&(h=""),{x:i,text:h,textRect:y,isBold:g}}},{key:"checkLabelBasedOnTickamount",value:function(e,t,i){var a=this.w,s=a.config.xaxis.tickAmount;return s==="dataPoints"&&(s=Math.round(a.globals.gridWidth/120)),s>i||e%Math.round(i/(s+1))==0||(t.text=""),t}},{key:"checkForOverflowingLabels",value:function(e,t,i,a,s){var n=this.w;if(e===0&&n.globals.skipFirstTimelinelabel&&(t.text=""),e===i-1&&n.globals.skipLastTimelinelabel&&(t.text=""),n.config.xaxis.labels.hideOverlappingLabels&&a.length>0){var r=s[s.length-1];t.x<r.textRect.width/(n.globals.rotateXLabels?Math.abs(n.config.xaxis.labels.rotate)/12:1.01)+r.x&&(t.text="")}return t}},{key:"checkForReversedLabels",value:function(e,t){var i=this.w;return i.config.yaxis[e]&&i.config.yaxis[e].reversed&&t.reverse(),t}},{key:"isYAxisHidden",value:function(e){var t=this.w,i=new D(this.ctx);return!t.config.yaxis[e].show||!t.config.yaxis[e].showForNullSeries&&i.isSeriesNull(e)&&t.globals.collapsedSeriesIndices.indexOf(e)===-1}},{key:"getYAxisForeColor",value:function(e,t){var i=this.w;return Array.isArray(e)&&i.globals.yAxisScale[t]&&this.ctx.theme.pushExtraColors(e,i.globals.yAxisScale[t].result.length,!1),e}},{key:"drawYAxisTicks",value:function(e,t,i,a,s,n,r){var o=this.w,h=new I(this.ctx),c=o.globals.translateY;if(a.show&&t>0){o.config.yaxis[s].opposite===!0&&(e+=a.width);for(var d=t;d>=0;d--){var g=c+t/10+o.config.yaxis[s].labels.offsetY-1;o.globals.isBarHorizontal&&(g=n*d),o.config.chart.type==="heatmap"&&(g+=n/2);var f=h.drawLine(e+i.offsetX-a.width+a.offsetX,g+a.offsetY,e+i.offsetX+a.offsetX,g+a.offsetY,a.color);r.add(f),c+=n}}}}]),v}(),ve=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"scaleSvgNode",value:function(e,t){var i=parseFloat(e.getAttributeNS(null,"width")),a=parseFloat(e.getAttributeNS(null,"height"));e.setAttributeNS(null,"width",i*t),e.setAttributeNS(null,"height",a*t),e.setAttributeNS(null,"viewBox","0 0 "+i+" "+a)}},{key:"fixSvgStringForIe11",value:function(e){if(!L.isIE11())return e;var t=0,i=e.replace(/xmlns="http:\/\/www.w3.org\/2000\/svg"/g,function(a){return++t===2?'xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev"':a});return i=(i=i.replace(/xmlns:NS\d+=""/g,"")).replace(/NS\d+:(\w+:\w+=")/g,"$1")}},{key:"getSvgString",value:function(e){var t=this.w.globals.dom.Paper.svg();if(e!==1){var i=this.w.globals.dom.Paper.node.cloneNode(!0);this.scaleSvgNode(i,e),t=new XMLSerializer().serializeToString(i)}return this.fixSvgStringForIe11(t)}},{key:"cleanup",value:function(){var e=this.w,t=e.globals.dom.baseEl.getElementsByClassName("apexcharts-xcrosshairs"),i=e.globals.dom.baseEl.getElementsByClassName("apexcharts-ycrosshairs"),a=e.globals.dom.baseEl.querySelectorAll(".apexcharts-zoom-rect, .apexcharts-selection-rect");Array.prototype.forEach.call(a,function(s){s.setAttribute("width",0)}),t&&t[0]&&(t[0].setAttribute("x",-500),t[0].setAttribute("x1",-500),t[0].setAttribute("x2",-500)),i&&i[0]&&(i[0].setAttribute("y",-100),i[0].setAttribute("y1",-100),i[0].setAttribute("y2",-100))}},{key:"svgUrl",value:function(){this.cleanup();var e=this.getSvgString(),t=new Blob([e],{type:"image/svg+xml;charset=utf-8"});return URL.createObjectURL(t)}},{key:"dataURI",value:function(e){var t=this;return new Promise(function(i){var a=t.w,s=e?e.scale||e.width/a.globals.svgWidth:1;t.cleanup();var n=document.createElement("canvas");n.width=a.globals.svgWidth*s,n.height=parseInt(a.globals.dom.elWrap.style.height,10)*s;var r=a.config.chart.background==="transparent"?"#fff":a.config.chart.background,o=n.getContext("2d");o.fillStyle=r,o.fillRect(0,0,n.width*s,n.height*s);var h=t.getSvgString(s);if(window.canvg&&L.isIE11()){var c=window.canvg.Canvg.fromString(o,h,{ignoreClear:!0,ignoreDimensions:!0});c.start();var d=n.msToBlob();c.stop(),i({blob:d})}else{var g="data:image/svg+xml,"+encodeURIComponent(h),f=new Image;f.crossOrigin="anonymous",f.onload=function(){if(o.drawImage(f,0,0),n.msToBlob){var p=n.msToBlob();i({blob:p})}else{var x=n.toDataURL("image/png");i({imgURI:x})}},f.src=g}})}},{key:"exportToSVG",value:function(){this.triggerDownload(this.svgUrl(),this.w.config.chart.toolbar.export.svg.filename,".svg")}},{key:"exportToPng",value:function(){var e=this;this.dataURI().then(function(t){var i=t.imgURI,a=t.blob;a?navigator.msSaveOrOpenBlob(a,e.w.globals.chartID+".png"):e.triggerDownload(i,e.w.config.chart.toolbar.export.png.filename,".png")})}},{key:"exportToCSV",value:function(e){var t=this,i=e.series,a=e.columnDelimiter,s=e.lineDelimiter,n=s===void 0?`
`:s,r=this.w,o=[],h=[],c="data:text/csv;charset=utf-8,\uFEFF",d=new Fe(this.ctx),g=new J(this.ctx),f=function(p){var x="";if(r.globals.axisCharts){if(r.config.xaxis.type==="category"||r.config.xaxis.convertedCatToNumeric)if(r.globals.isBarHorizontal){var b=r.globals.yLabelFormatters[0],y=new j(t.ctx).getActiveConfigSeriesIndex();x=b(r.globals.labels[p],{seriesIndex:y,dataPointIndex:p,w:r})}else x=g.getLabel(r.globals.labels,r.globals.timescaleLabels,0,p).text;r.config.xaxis.type==="datetime"&&(r.config.xaxis.categories.length?x=r.config.xaxis.categories[p]:r.config.labels.length&&(x=r.config.labels[p]))}else x=r.config.labels[p];return Array.isArray(x)&&(x=x.join(" ")),L.isNumber(x)?x:x.split(a).join("")};o.push(r.config.chart.toolbar.export.csv.headerCategory),i.map(function(p,x){var b=p.name?p.name:"series-".concat(x);r.globals.axisCharts&&o.push(b.split(a).join("")?b.split(a).join(""):"series-".concat(x))}),r.globals.axisCharts||(o.push(r.config.chart.toolbar.export.csv.headerValue),h.push(o.join(a))),i.map(function(p,x){r.globals.axisCharts?function(b,y){if(o.length&&y===0&&h.push(o.join(a)),b.data&&b.data.length)for(var k=0;k<b.data.length;k++){o=[];var A=f(k);if(A||(d.isFormatXY()?A=i[y].data[k].x:d.isFormat2DArray()&&(A=i[y].data[k]?i[y].data[k][0]:"")),y===0){o.push((u=A,r.config.xaxis.type==="datetime"&&String(u).length>=10?r.config.chart.toolbar.export.csv.dateFormatter(A):L.isNumber(A)?A:A.split(a).join("")));for(var l=0;l<r.globals.series.length;l++)o.push(r.globals.series[l][k])}(r.config.chart.type==="candlestick"||b.type&&b.type==="candlestick")&&(o.pop(),o.push(r.globals.seriesCandleO[y][k]),o.push(r.globals.seriesCandleH[y][k]),o.push(r.globals.seriesCandleL[y][k]),o.push(r.globals.seriesCandleC[y][k])),(r.config.chart.type==="boxPlot"||b.type&&b.type==="boxPlot")&&(o.pop(),o.push(r.globals.seriesCandleO[y][k]),o.push(r.globals.seriesCandleH[y][k]),o.push(r.globals.seriesCandleM[y][k]),o.push(r.globals.seriesCandleL[y][k]),o.push(r.globals.seriesCandleC[y][k])),r.config.chart.type==="rangeBar"&&(o.pop(),o.push(r.globals.seriesRangeStart[y][k]),o.push(r.globals.seriesRangeEnd[y][k])),o.length&&h.push(o.join(a))}var u}(p,x):((o=[]).push(r.globals.labels[x].split(a).join("")),o.push(r.globals.series[x]),h.push(o.join(a)))}),c+=h.join(n),this.triggerDownload(encodeURI(c),r.config.chart.toolbar.export.csv.filename,".csv")}},{key:"triggerDownload",value:function(e,t,i){var a=document.createElement("a");a.href=e,a.download=(t||this.w.globals.chartID)+i,document.body.appendChild(a),a.click(),document.body.removeChild(a)}}]),v}(),he=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w;var t=this.w;this.axesUtils=new J(e),this.xaxisLabels=t.globals.labels.slice(),t.globals.timescaleLabels.length>0&&!t.globals.isBarHorizontal&&(this.xaxisLabels=t.globals.timescaleLabels.slice()),t.config.xaxis.overwriteCategories&&(this.xaxisLabels=t.config.xaxis.overwriteCategories),this.drawnLabels=[],this.drawnLabelsRects=[],t.config.xaxis.position==="top"?this.offY=0:this.offY=t.globals.gridHeight+1,this.offY=this.offY+t.config.xaxis.axisBorder.offsetY,this.isCategoryBarHorizontal=t.config.chart.type==="bar"&&t.config.plotOptions.bar.horizontal,this.xaxisFontSize=t.config.xaxis.labels.style.fontSize,this.xaxisFontFamily=t.config.xaxis.labels.style.fontFamily,this.xaxisForeColors=t.config.xaxis.labels.style.colors,this.xaxisBorderWidth=t.config.xaxis.axisBorder.width,this.isCategoryBarHorizontal&&(this.xaxisBorderWidth=t.config.yaxis[0].axisBorder.width.toString()),this.xaxisBorderWidth.indexOf("%")>-1?this.xaxisBorderWidth=t.globals.gridWidth*parseInt(this.xaxisBorderWidth,10)/100:this.xaxisBorderWidth=parseInt(this.xaxisBorderWidth,10),this.xaxisBorderHeight=t.config.xaxis.axisBorder.height,this.yaxis=t.config.yaxis[0]}return E(v,[{key:"drawXaxis",value:function(){var e,t=this,i=this.w,a=new I(this.ctx),s=a.group({class:"apexcharts-xaxis",transform:"translate(".concat(i.config.xaxis.offsetX,", ").concat(i.config.xaxis.offsetY,")")}),n=a.group({class:"apexcharts-xaxis-texts-g",transform:"translate(".concat(i.globals.translateXAxisX,", ").concat(i.globals.translateXAxisY,")")});s.add(n);for(var r=i.globals.padHorizontal,o=[],h=0;h<this.xaxisLabels.length;h++)o.push(this.xaxisLabels[h]);var c=o.length;if(i.globals.isXNumeric){var d=c>1?c-1:c;e=i.globals.gridWidth/d,r=r+e/2+i.config.xaxis.labels.offsetX}else e=i.globals.gridWidth/o.length,r=r+e+i.config.xaxis.labels.offsetX;for(var g=function(k){var A=r-e/2+i.config.xaxis.labels.offsetX;k===0&&c===1&&e/2===r&&i.globals.dataPoints===1&&(A=i.globals.gridWidth/2);var l=t.axesUtils.getLabel(o,i.globals.timescaleLabels,A,k,t.drawnLabels,t.xaxisFontSize),u=28;if(i.globals.rotateXLabels&&(u=22),(l=i.config.xaxis.tickAmount!==void 0&&i.config.xaxis.tickAmount!=="dataPoints"&&i.config.xaxis.type!=="datetime"?t.axesUtils.checkLabelBasedOnTickamount(k,l,c):t.axesUtils.checkForOverflowingLabels(k,l,c,t.drawnLabels,t.drawnLabelsRects)).text&&i.globals.xaxisLabelsCount++,i.config.xaxis.labels.show){var m=a.drawText({x:l.x,y:t.offY+i.config.xaxis.labels.offsetY+u-(i.config.xaxis.position==="top"?i.globals.xAxisHeight+i.config.xaxis.axisTicks.height-2:0),text:l.text,textAnchor:"middle",fontWeight:l.isBold?600:i.config.xaxis.labels.style.fontWeight,fontSize:t.xaxisFontSize,fontFamily:t.xaxisFontFamily,foreColor:Array.isArray(t.xaxisForeColors)?i.config.xaxis.convertedCatToNumeric?t.xaxisForeColors[i.globals.minX+k-1]:t.xaxisForeColors[k]:t.xaxisForeColors,isPlainText:!1,cssClass:"apexcharts-xaxis-label "+i.config.xaxis.labels.style.cssClass});n.add(m);var w=document.createElementNS(i.globals.SVGNS,"title");w.textContent=Array.isArray(l.text)?l.text.join(" "):l.text,m.node.appendChild(w),l.text!==""&&(t.drawnLabels.push(l.text),t.drawnLabelsRects.push(l))}r+=e},f=0;f<=c-1;f++)g(f);if(i.config.xaxis.title.text!==void 0){var p=a.group({class:"apexcharts-xaxis-title"}),x=a.drawText({x:i.globals.gridWidth/2+i.config.xaxis.title.offsetX,y:this.offY+parseFloat(this.xaxisFontSize)+i.globals.xAxisLabelsHeight+i.config.xaxis.title.offsetY,text:i.config.xaxis.title.text,textAnchor:"middle",fontSize:i.config.xaxis.title.style.fontSize,fontFamily:i.config.xaxis.title.style.fontFamily,fontWeight:i.config.xaxis.title.style.fontWeight,foreColor:i.config.xaxis.title.style.color,cssClass:"apexcharts-xaxis-title-text "+i.config.xaxis.title.style.cssClass});p.add(x),s.add(p)}if(i.config.xaxis.axisBorder.show){var b=i.globals.barPadForNumericAxis,y=a.drawLine(i.globals.padHorizontal+i.config.xaxis.axisBorder.offsetX-b,this.offY,this.xaxisBorderWidth+b,this.offY,i.config.xaxis.axisBorder.color,0,this.xaxisBorderHeight);s.add(y)}return s}},{key:"drawXaxisInversed",value:function(e){var t,i,a=this,s=this.w,n=new I(this.ctx),r=s.config.yaxis[0].opposite?s.globals.translateYAxisX[e]:0,o=n.group({class:"apexcharts-yaxis apexcharts-xaxis-inversed",rel:e}),h=n.group({class:"apexcharts-yaxis-texts-g apexcharts-xaxis-inversed-texts-g",transform:"translate("+r+", 0)"});o.add(h);var c=[];if(s.config.yaxis[e].show)for(var d=0;d<this.xaxisLabels.length;d++)c.push(this.xaxisLabels[d]);t=s.globals.gridHeight/c.length,i=-t/2.2;var g=s.globals.yLabelFormatters[0],f=s.config.yaxis[0].labels;if(f.show)for(var p=function(u){var m=c[u]===void 0?"":c[u];m=g(m,{seriesIndex:e,dataPointIndex:u,w:s});var w=a.axesUtils.getYAxisForeColor(f.style.colors,e),S=0;Array.isArray(m)&&(S=m.length/2*parseInt(f.style.fontSize,10));var P=n.drawText({x:f.offsetX-15,y:i+t+f.offsetY-S,text:m,textAnchor:a.yaxis.opposite?"start":"end",foreColor:Array.isArray(w)?w[u]:w,fontSize:f.style.fontSize,fontFamily:f.style.fontFamily,fontWeight:f.style.fontWeight,isPlainText:!1,cssClass:"apexcharts-yaxis-label "+f.style.cssClass});h.add(P);var C=document.createElementNS(s.globals.SVGNS,"title");if(C.textContent=Array.isArray(m)?m.join(" "):m,P.node.appendChild(C),s.config.yaxis[e].labels.rotate!==0){var M=n.rotateAroundCenter(P.node);P.node.setAttribute("transform","rotate(".concat(s.config.yaxis[e].labels.rotate," 0 ").concat(M.y,")"))}i+=t},x=0;x<=c.length-1;x++)p(x);if(s.config.yaxis[0].title.text!==void 0){var b=n.group({class:"apexcharts-yaxis-title apexcharts-xaxis-title-inversed",transform:"translate("+r+", 0)"}),y=n.drawText({x:0,y:s.globals.gridHeight/2,text:s.config.yaxis[0].title.text,textAnchor:"middle",foreColor:s.config.yaxis[0].title.style.color,fontSize:s.config.yaxis[0].title.style.fontSize,fontWeight:s.config.yaxis[0].title.style.fontWeight,fontFamily:s.config.yaxis[0].title.style.fontFamily,cssClass:"apexcharts-yaxis-title-text "+s.config.yaxis[0].title.style.cssClass});b.add(y),o.add(b)}var k=0;this.isCategoryBarHorizontal&&s.config.yaxis[0].opposite&&(k=s.globals.gridWidth);var A=s.config.xaxis.axisBorder;if(A.show){var l=n.drawLine(s.globals.padHorizontal+A.offsetX+k,1+A.offsetY,s.globals.padHorizontal+A.offsetX+k,s.globals.gridHeight+A.offsetY,A.color,0);o.add(l)}return s.config.yaxis[0].axisTicks.show&&this.axesUtils.drawYAxisTicks(k,c.length,s.config.yaxis[0].axisBorder,s.config.yaxis[0].axisTicks,0,t,o),o}},{key:"drawXaxisTicks",value:function(e,t){var i=this.w,a=e;if(!(e<0||e-2>i.globals.gridWidth)){var s=this.offY+i.config.xaxis.axisTicks.offsetY,n=s+i.config.xaxis.axisTicks.height;if(i.config.xaxis.position==="top"&&(n=s-i.config.xaxis.axisTicks.height),i.config.xaxis.axisTicks.show){var r=new I(this.ctx).drawLine(e+i.config.xaxis.axisTicks.offsetX,s+i.config.xaxis.offsetY,a+i.config.xaxis.axisTicks.offsetX,n+i.config.xaxis.offsetY,i.config.xaxis.axisTicks.color);t.add(r),r.node.classList.add("apexcharts-xaxis-tick")}}}},{key:"getXAxisTicksPositions",value:function(){var e=this.w,t=[],i=this.xaxisLabels.length,a=e.globals.padHorizontal;if(e.globals.timescaleLabels.length>0)for(var s=0;s<i;s++)a=this.xaxisLabels[s].position,t.push(a);else for(var n=i,r=0;r<n;r++){var o=n;e.globals.isXNumeric&&e.config.chart.type!=="bar"&&(o-=1),a+=e.globals.gridWidth/o,t.push(a)}return t}},{key:"xAxisLabelCorrections",value:function(){var e=this.w,t=new I(this.ctx),i=e.globals.dom.baseEl.querySelector(".apexcharts-xaxis-texts-g"),a=e.globals.dom.baseEl.querySelectorAll(".apexcharts-xaxis-texts-g text"),s=e.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis-inversed text"),n=e.globals.dom.baseEl.querySelectorAll(".apexcharts-xaxis-inversed-texts-g text tspan");if(e.globals.rotateXLabels||e.config.xaxis.labels.rotateAlways)for(var r=0;r<a.length;r++){var o=t.rotateAroundCenter(a[r]);o.y=o.y-1,o.x=o.x+1,a[r].setAttribute("transform","rotate(".concat(e.config.xaxis.labels.rotate," ").concat(o.x," ").concat(o.y,")")),a[r].setAttribute("text-anchor","end"),i.setAttribute("transform","translate(0, ".concat(-10,")"));var h=a[r].childNodes;e.config.xaxis.labels.trim&&Array.prototype.forEach.call(h,function(f){t.placeTextWithEllipsis(f,f.textContent,e.globals.xAxisLabelsHeight-(e.config.legend.position==="bottom"?20:10))})}else(function(){for(var f=e.globals.gridWidth/(e.globals.labels.length+1),p=0;p<a.length;p++){var x=a[p].childNodes;e.config.xaxis.labels.trim&&e.config.xaxis.type!=="datetime"&&Array.prototype.forEach.call(x,function(b){t.placeTextWithEllipsis(b,b.textContent,f)})}})();if(s.length>0){var c=s[s.length-1].getBBox(),d=s[0].getBBox();c.x<-20&&s[s.length-1].parentNode.removeChild(s[s.length-1]),d.x+d.width>e.globals.gridWidth&&!e.globals.isBarHorizontal&&s[0].parentNode.removeChild(s[0]);for(var g=0;g<n.length;g++)t.placeTextWithEllipsis(n[g],n[g].textContent,e.config.yaxis[0].labels.maxWidth-2*parseFloat(e.config.yaxis[0].title.style.fontSize)-20)}}}]),v}(),Re=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w;var t=this.w;this.xaxisLabels=t.globals.labels.slice(),this.axesUtils=new J(e),this.isTimelineBar=t.config.xaxis.type==="datetime"&&t.globals.seriesRangeBarTimeline.length,t.globals.timescaleLabels.length>0&&(this.xaxisLabels=t.globals.timescaleLabels.slice())}return E(v,[{key:"drawGridArea",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,t=this.w,i=new I(this.ctx);e===null&&(e=i.group({class:"apexcharts-grid"}));var a=i.drawLine(t.globals.padHorizontal,1,t.globals.padHorizontal,t.globals.gridHeight,"transparent"),s=i.drawLine(t.globals.padHorizontal,t.globals.gridHeight,t.globals.gridWidth,t.globals.gridHeight,"transparent");return e.add(s),e.add(a),e}},{key:"drawGrid",value:function(){var e=null;return this.w.globals.axisCharts&&(e=this.renderGrid(),this.drawGridArea(e.el)),e}},{key:"createGridMask",value:function(){var e=this.w,t=e.globals,i=new I(this.ctx),a=Array.isArray(e.config.stroke.width)?0:e.config.stroke.width;if(Array.isArray(e.config.stroke.width)){var s=0;e.config.stroke.width.forEach(function(d){s=Math.max(s,d)}),a=s}t.dom.elGridRectMask=document.createElementNS(t.SVGNS,"clipPath"),t.dom.elGridRectMask.setAttribute("id","gridRectMask".concat(t.cuid)),t.dom.elGridRectMarkerMask=document.createElementNS(t.SVGNS,"clipPath"),t.dom.elGridRectMarkerMask.setAttribute("id","gridRectMarkerMask".concat(t.cuid)),t.dom.elForecastMask=document.createElementNS(t.SVGNS,"clipPath"),t.dom.elForecastMask.setAttribute("id","forecastMask".concat(t.cuid)),t.dom.elNonForecastMask=document.createElementNS(t.SVGNS,"clipPath"),t.dom.elNonForecastMask.setAttribute("id","nonForecastMask".concat(t.cuid));var n=e.config.chart.type,r=0,o=0;(n==="bar"||n==="rangeBar"||n==="candlestick"||n==="boxPlot"||e.globals.comboBarCount>0)&&e.globals.isXNumeric&&!e.globals.isBarHorizontal&&(r=e.config.grid.padding.left,o=e.config.grid.padding.right,t.barPadForNumericAxis>r&&(r=t.barPadForNumericAxis,o=t.barPadForNumericAxis)),t.dom.elGridRect=i.drawRect(-a/2-r-2,-a/2,t.gridWidth+a+o+r+4,t.gridHeight+a,0,"#fff"),new D(this).getLargestMarkerSize();var h=e.globals.markers.largestSize+1;t.dom.elGridRectMarker=i.drawRect(2*-h,2*-h,t.gridWidth+4*h,t.gridHeight+4*h,0,"#fff"),t.dom.elGridRectMask.appendChild(t.dom.elGridRect.node),t.dom.elGridRectMarkerMask.appendChild(t.dom.elGridRectMarker.node);var c=t.dom.baseEl.querySelector("defs");c.appendChild(t.dom.elGridRectMask),c.appendChild(t.dom.elForecastMask),c.appendChild(t.dom.elNonForecastMask),c.appendChild(t.dom.elGridRectMarkerMask)}},{key:"_drawGridLines",value:function(e){var t=e.i,i=e.x1,a=e.y1,s=e.x2,n=e.y2,r=e.xCount,o=e.parent,h=this.w;t===0&&h.globals.skipFirstTimelinelabel||t===r-1&&h.globals.skipLastTimelinelabel&&!h.config.xaxis.labels.formatter||h.config.chart.type==="radar"||(h.config.grid.xaxis.lines.show&&this._drawGridLine({x1:i,y1:a,x2:s,y2:n,parent:o}),new he(this.ctx).drawXaxisTicks(i,this.elg))}},{key:"_drawGridLine",value:function(e){var t=e.x1,i=e.y1,a=e.x2,s=e.y2,n=e.parent,r=this.w,o=n.node.classList.contains("apexcharts-gridlines-horizontal"),h=r.config.grid.strokeDashArray,c=r.globals.barPadForNumericAxis,d=new I(this).drawLine(t-(o?c:0),i,a+(o?c:0),s,r.config.grid.borderColor,h);d.node.classList.add("apexcharts-gridline"),n.add(d)}},{key:"_drawGridBandRect",value:function(e){var t=e.c,i=e.x1,a=e.y1,s=e.x2,n=e.y2,r=e.type,o=this.w,h=new I(this.ctx),c=o.globals.barPadForNumericAxis;if(r!=="column"||o.config.xaxis.type!=="datetime"){var d=o.config.grid[r].colors[t],g=h.drawRect(i-(r==="row"?c:0),a,s+(r==="row"?2*c:0),n,0,d,o.config.grid[r].opacity);this.elg.add(g),g.attr("clip-path","url(#gridRectMask".concat(o.globals.cuid,")")),g.node.classList.add("apexcharts-grid-".concat(r))}}},{key:"_drawXYLines",value:function(e){var t=this,i=e.xCount,a=e.tickAmount,s=this.w;if(s.config.grid.xaxis.lines.show||s.config.xaxis.axisTicks.show){var n,r=s.globals.padHorizontal,o=s.globals.gridHeight;s.globals.timescaleLabels.length?function(p){for(var x=p.xC,b=p.x1,y=p.y1,k=p.x2,A=p.y2,l=0;l<x;l++)b=t.xaxisLabels[l].position,k=t.xaxisLabels[l].position,t._drawGridLines({i:l,x1:b,y1:y,x2:k,y2:A,xCount:i,parent:t.elgridLinesV})}({xC:i,x1:r,y1:0,x2:n,y2:o}):(s.globals.isXNumeric&&(i=s.globals.xAxisScale.result.length),s.config.xaxis.convertedCatToNumeric&&(i=s.globals.xaxisLabelsCount),function(p){var x=p.xC,b=p.x1,y=p.y1,k=p.x2,A=p.y2;if(s.config.xaxis.tickAmount!==void 0&&s.config.xaxis.tickAmount!=="dataPoints")s.globals.dom.baseEl.querySelectorAll(".apexcharts-text.apexcharts-xaxis-label tspan:not(:empty)").forEach(function(u,m){var w=u.getBBox();t._drawGridLines({i:m,x1:w.x+w.width/2,y1:y,x2:w.x+w.width/2,y2:A,xCount:i,parent:t.elgridLinesV})});else for(var l=0;l<x+(s.globals.isXNumeric?0:1);l++)l===0&&x===1&&s.globals.dataPoints===1&&(k=b=s.globals.gridWidth/2),t._drawGridLines({i:l,x1:b,y1:y,x2:k,y2:A,xCount:i,parent:t.elgridLinesV}),k=b+=s.globals.gridWidth/(s.globals.isXNumeric?x-1:x)}({xC:i,x1:r,y1:0,x2:n,y2:o}))}if(s.config.grid.yaxis.lines.show){var h=0,c=0,d=s.globals.gridWidth,g=a+1;this.isTimelineBar&&(g=s.globals.labels.length);for(var f=0;f<g+(this.isTimelineBar?1:0);f++)this._drawGridLine({x1:0,y1:h,x2:d,y2:c,parent:this.elgridLinesH}),c=h+=s.globals.gridHeight/(this.isTimelineBar?g:a)}}},{key:"_drawInvertedXYLines",value:function(e){var t=e.xCount,i=this.w;if(i.config.grid.xaxis.lines.show||i.config.xaxis.axisTicks.show)for(var a,s=i.globals.padHorizontal,n=i.globals.gridHeight,r=0;r<t+1;r++)i.config.grid.xaxis.lines.show&&this._drawGridLine({x1:s,y1:0,x2:a,y2:n,parent:this.elgridLinesV}),new he(this.ctx).drawXaxisTicks(s,this.elg),a=s=s+i.globals.gridWidth/t+.3;if(i.config.grid.yaxis.lines.show)for(var o=0,h=0,c=i.globals.gridWidth,d=0;d<i.globals.dataPoints+1;d++)this._drawGridLine({x1:0,y1:o,x2:c,y2:h,parent:this.elgridLinesH}),h=o+=i.globals.gridHeight/i.globals.dataPoints}},{key:"renderGrid",value:function(){var e=this.w,t=new I(this.ctx);this.elg=t.group({class:"apexcharts-grid"}),this.elgridLinesH=t.group({class:"apexcharts-gridlines-horizontal"}),this.elgridLinesV=t.group({class:"apexcharts-gridlines-vertical"}),this.elg.add(this.elgridLinesH),this.elg.add(this.elgridLinesV),e.config.grid.show||(this.elgridLinesV.hide(),this.elgridLinesH.hide());for(var i,a=e.globals.yAxisScale.length?e.globals.yAxisScale[0].result.length-1:5,s=0;s<e.globals.series.length&&(e.globals.yAxisScale[s]!==void 0&&(a=e.globals.yAxisScale[s].result.length-1),!(a>2));s++);return!e.globals.isBarHorizontal||this.isTimelineBar?(i=this.xaxisLabels.length,this.isTimelineBar&&(a=e.globals.labels.length,e.config.xaxis.tickAmount&&e.config.xaxis.labels.formatter&&(i=e.config.xaxis.tickAmount)),this._drawXYLines({xCount:i,tickAmount:a})):(i=a,a=e.globals.xTickAmount,this._drawInvertedXYLines({xCount:i,tickAmount:a})),this.drawGridBands(i,a),{el:this.elg,xAxisTickWidth:e.globals.gridWidth/i}}},{key:"drawGridBands",value:function(e,t){var i=this.w;if(i.config.grid.row.colors!==void 0&&i.config.grid.row.colors.length>0)for(var a=0,s=i.globals.gridHeight/t,n=i.globals.gridWidth,r=0,o=0;r<t;r++,o++)o>=i.config.grid.row.colors.length&&(o=0),this._drawGridBandRect({c:o,x1:0,y1:a,x2:n,y2:s,type:"row"}),a+=i.globals.gridHeight/t;if(i.config.grid.column.colors!==void 0&&i.config.grid.column.colors.length>0)for(var h=i.globals.isBarHorizontal||i.config.xaxis.type!=="category"&&!i.config.xaxis.convertedCatToNumeric?e:e-1,c=i.globals.padHorizontal,d=i.globals.padHorizontal+i.globals.gridWidth/h,g=i.globals.gridHeight,f=0,p=0;f<e;f++,p++)p>=i.config.grid.column.colors.length&&(p=0),this._drawGridBandRect({c:p,x1:c,y1:0,x2:d,y2:g,type:"column"}),c+=i.globals.gridWidth/h}}]),v}(),ie=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"niceScale",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:10,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,s=arguments.length>4?arguments[4]:void 0,n=this.w,r=Math.abs(t-e);if((i=this._adjustTicksForSmallRange(i,a,r))==="dataPoints"&&(i=n.globals.dataPoints-1),e===Number.MIN_VALUE&&t===0||!L.isNumber(e)&&!L.isNumber(t)||e===Number.MIN_VALUE&&t===-Number.MAX_VALUE){e=0,t=i;var o=this.linearScale(e,t,i);return o}e>t?(console.warn("axis.min cannot be greater than axis.max"),t=e+.1):e===t&&(e=e===0?0:e-.5,t=t===0?2:t+.5);var h=[];r<1&&s&&(n.config.chart.type==="candlestick"||n.config.series[a].type==="candlestick"||n.config.chart.type==="boxPlot"||n.config.series[a].type==="boxPlot"||n.globals.isRangeData)&&(t*=1.01);var c=i+1;c<2?c=2:c>2&&(c-=2);var d=r/c,g=Math.floor(L.log10(d)),f=Math.pow(10,g),p=Math.round(d/f);p<1&&(p=1);var x=p*f,b=x*Math.floor(e/x),y=x*Math.ceil(t/x),k=b;if(s&&r>2){for(;h.push(k),!((k+=x)>y););return{result:h,niceMin:h[0],niceMax:h[h.length-1]}}var A=e;(h=[]).push(A);for(var l=Math.abs(t-e)/i,u=0;u<=i;u++)A+=l,h.push(A);return h[h.length-2]>=t&&h.pop(),{result:h,niceMin:h[0],niceMax:h[h.length-1]}}},{key:"linearScale",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:10,a=arguments.length>3?arguments[3]:void 0,s=Math.abs(t-e);(i=this._adjustTicksForSmallRange(i,a,s))==="dataPoints"&&(i=this.w.globals.dataPoints-1);var n=s/i;i===Number.MAX_VALUE&&(i=10,n=1);for(var r=[],o=e;i>=0;)r.push(o),o+=n,i-=1;return{result:r,niceMin:r[0],niceMax:r[r.length-1]}}},{key:"logarithmicScale",value:function(e){for(var t=[],i=Math.ceil(Math.log10(e))+1,a=0;a<i;a++)t.push(Math.pow(10,a));return{result:t,niceMin:t[0],niceMax:t[t.length-1]}}},{key:"_adjustTicksForSmallRange",value:function(e,t,i){var a=e;if(t!==void 0&&this.w.config.yaxis[t].labels.formatter&&this.w.config.yaxis[t].tickAmount===void 0){var s=this.w.config.yaxis[t].labels.formatter(1);L.isNumber(Number(s))&&!L.isFloat(s)&&(a=Math.ceil(i))}return a<e?a:e}},{key:"setYScaleForIndex",value:function(e,t,i){var a=this.w.globals,s=this.w.config,n=a.isBarHorizontal?s.xaxis:s.yaxis[e];a.yAxisScale[e]===void 0&&(a.yAxisScale[e]=[]);var r=Math.abs(i-t);if(n.logarithmic&&r<=5&&(a.invalidLogScale=!0),n.logarithmic&&r>5)a.allSeriesCollapsed=!1,a.yAxisScale[e]=this.logarithmicScale(i);else if(i!==-Number.MAX_VALUE&&L.isNumber(i))if(a.allSeriesCollapsed=!1,n.min===void 0&&n.max===void 0||n.forceNiceScale){var o=s.yaxis[e].max===void 0&&s.yaxis[e].min===void 0||s.yaxis[e].forceNiceScale;a.yAxisScale[e]=this.niceScale(t,i,n.tickAmount?n.tickAmount:r<5&&r>1?r+1:5,e,o)}else a.yAxisScale[e]=this.linearScale(t,i,n.tickAmount,e);else a.yAxisScale[e]=this.linearScale(0,5,5)}},{key:"setXScale",value:function(e,t){var i=this.w,a=i.globals,s=i.config.xaxis,n=Math.abs(t-e);return t!==-Number.MAX_VALUE&&L.isNumber(t)?a.xAxisScale=this.linearScale(e,t,s.tickAmount?s.tickAmount:n<5&&n>1?n+1:5,0):a.xAxisScale=this.linearScale(0,5,5),a.xAxisScale}},{key:"setMultipleYScales",value:function(){var e=this,t=this.w.globals,i=this.w.config,a=t.minYArr.concat([]),s=t.maxYArr.concat([]),n=[];i.yaxis.forEach(function(r,o){var h=o;i.series.forEach(function(g,f){g.name===r.seriesName&&(h=f,o!==f?n.push({index:f,similarIndex:o,alreadyExists:!0}):n.push({index:f}))});var c=a[h],d=s[h];e.setYScaleForIndex(o,c,d)}),this.sameScaleInMultipleAxes(a,s,n)}},{key:"sameScaleInMultipleAxes",value:function(e,t,i){var a=this,s=this.w.config,n=this.w.globals,r=[];i.forEach(function(x){x.alreadyExists&&(r[x.index]===void 0&&(r[x.index]=[]),r[x.index].push(x.index),r[x.index].push(x.similarIndex))}),n.yAxisSameScaleIndices=r,r.forEach(function(x,b){r.forEach(function(y,k){var A,l;b!==k&&(A=x,l=y,A.filter(function(u){return l.indexOf(u)!==-1})).length>0&&(r[b]=r[b].concat(r[k]))})});var o=r.map(function(x){return x.filter(function(b,y){return x.indexOf(b)===y})}).map(function(x){return x.sort()});r=r.filter(function(x){return!!x});var h=o.slice(),c=h.map(function(x){return JSON.stringify(x)});h=h.filter(function(x,b){return c.indexOf(JSON.stringify(x))===b});var d=[],g=[];e.forEach(function(x,b){h.forEach(function(y,k){y.indexOf(b)>-1&&(d[k]===void 0&&(d[k]=[],g[k]=[]),d[k].push({key:b,value:x}),g[k].push({key:b,value:t[b]}))})});var f=Array.apply(null,Array(h.length)).map(Number.prototype.valueOf,Number.MIN_VALUE),p=Array.apply(null,Array(h.length)).map(Number.prototype.valueOf,-Number.MAX_VALUE);d.forEach(function(x,b){x.forEach(function(y,k){f[b]=Math.min(y.value,f[b])})}),g.forEach(function(x,b){x.forEach(function(y,k){p[b]=Math.max(y.value,p[b])})}),e.forEach(function(x,b){g.forEach(function(y,k){var A=f[k],l=p[k];s.chart.stacked&&(l=0,y.forEach(function(u,m){u.value!==-Number.MAX_VALUE&&(l+=u.value),A!==Number.MIN_VALUE&&(A+=d[k][m].value)})),y.forEach(function(u,m){y[m].key===b&&(s.yaxis[b].min!==void 0&&(A=typeof s.yaxis[b].min=="function"?s.yaxis[b].min(n.minY):s.yaxis[b].min),s.yaxis[b].max!==void 0&&(l=typeof s.yaxis[b].max=="function"?s.yaxis[b].max(n.maxY):s.yaxis[b].max),a.setYScaleForIndex(b,A,l))})})})}},{key:"autoScaleY",value:function(e,t,i){e||(e=this);var a=e.w;if(a.globals.isMultipleYAxis||a.globals.collapsedSeries.length)return console.warn("autoScaleYaxis is not supported in a multi-yaxis chart."),t;var s=a.globals.seriesX[0],n=a.config.chart.stacked;return t.forEach(function(r,o){for(var h=0,c=0;c<s.length;c++)if(s[c]>=i.xaxis.min){h=c;break}var d,g,f=a.globals.minYArr[o],p=a.globals.maxYArr[o],x=a.globals.stackedSeriesTotals;a.globals.series.forEach(function(b,y){var k=b[h];n?(k=x[h],d=g=k,x.forEach(function(A,l){s[l]<=i.xaxis.max&&s[l]>=i.xaxis.min&&(A>g&&A!==null&&(g=A),b[l]<d&&b[l]!==null&&(d=b[l]))})):(d=g=k,b.forEach(function(A,l){if(s[l]<=i.xaxis.max&&s[l]>=i.xaxis.min){var u=A,m=A;a.globals.series.forEach(function(w,S){A!==null&&(u=Math.min(w[l],u),m=Math.max(w[l],m))}),m>g&&m!==null&&(g=m),u<d&&u!==null&&(d=u)}})),d===void 0&&g===void 0&&(d=f,g=p),(g*=g<0?.9:1.1)<0&&g<p&&(g=p),(d*=d<0?1.1:.9)<0&&d>f&&(d=f),t.length>1?(t[y].min=r.min===void 0?d:r.min,t[y].max=r.max===void 0?g:r.max):(t[0].min=r.min===void 0?d:r.min,t[0].max=r.max===void 0?g:r.max)})}),t}}]),v}(),ye=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.scales=new ie(e)}return E(v,[{key:"init",value:function(){this.setYRange(),this.setXRange(),this.setZRange()}},{key:"getMinYMaxY",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:-Number.MAX_VALUE,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,s=this.w.config,n=this.w.globals,r=-Number.MAX_VALUE,o=Number.MIN_VALUE;a===null&&(a=e+1);var h=n.series,c=h,d=h;s.chart.type==="candlestick"?(c=n.seriesCandleL,d=n.seriesCandleH):s.chart.type==="boxPlot"?(c=n.seriesCandleO,d=n.seriesCandleC):n.isRangeData&&(c=n.seriesRangeStart,d=n.seriesRangeEnd);for(var g=e;g<a;g++){n.dataPoints=Math.max(n.dataPoints,h[g].length);for(var f=0;f<n.series[g].length;f++){var p=h[g][f];p!==null&&L.isNumber(p)?(d[g][f]!==void 0&&(r=Math.max(r,d[g][f])),c[g][f]!==void 0&&(t=Math.min(t,c[g][f]),i=Math.max(i,c[g][f])),this.w.config.chart.type!=="candlestick"&&this.w.config.chart.type!=="boxPlot"||(n.seriesCandleC[g][f]!==void 0&&(r=Math.max(r,n.seriesCandleO[g][f]),r=Math.max(r,n.seriesCandleH[g][f]),r=Math.max(r,n.seriesCandleL[g][f]),r=Math.max(r,n.seriesCandleC[g][f]),this.w.config.chart.type==="boxPlot"&&(r=Math.max(r,n.seriesCandleM[g][f]))),!s.series[g].type||s.series[g].type==="candlestick"&&s.series[g].type==="boxPlot"||(r=Math.max(r,n.series[g][f]),t=Math.min(t,n.series[g][f])),i=r),n.seriesGoals[g]&&n.seriesGoals[g][f]&&Array.isArray(n.seriesGoals[g][f])&&n.seriesGoals[g][f].forEach(function(x){o!==Number.MIN_VALUE&&(o=Math.min(o,x.value),t=o),r=Math.max(r,x.value),i=r}),L.isFloat(p)&&(p=L.noExponents(p),n.yValueDecimal=Math.max(n.yValueDecimal,p.toString().split(".")[1].length)),o>c[g][f]&&c[g][f]<0&&(o=c[g][f])):n.hasNullValues=!0}}return s.chart.type==="rangeBar"&&n.seriesRangeStart.length&&n.isBarHorizontal&&s.xaxis.type==="datetime"&&(o=t),s.chart.type==="bar"&&(o<0&&r<0&&(r=0),o===Number.MIN_VALUE&&(o=0)),{minY:o,maxY:r,lowestY:t,highestY:i}}},{key:"setYRange",value:function(){var e=this.w.globals,t=this.w.config;e.maxY=-Number.MAX_VALUE,e.minY=Number.MIN_VALUE;var i=Number.MAX_VALUE;if(e.isMultipleYAxis)for(var a=0;a<e.series.length;a++){var s=this.getMinYMaxY(a,i,null,a+1);e.minYArr.push(s.minY),e.maxYArr.push(s.maxY),i=s.lowestY}var n=this.getMinYMaxY(0,i,null,e.series.length);if(e.minY=n.minY,e.maxY=n.maxY,i=n.lowestY,t.chart.stacked&&this._setStackedMinMax(),(t.chart.type==="line"||t.chart.type==="area"||t.chart.type==="candlestick"||t.chart.type==="boxPlot"||t.chart.type==="rangeBar"&&!e.isBarHorizontal)&&e.minY===Number.MIN_VALUE&&i!==-Number.MAX_VALUE&&i!==e.maxY){var r=e.maxY-i;(i>=0&&i<=10||t.yaxis[0].min!==void 0||t.yaxis[0].max!==void 0)&&(r=0),e.minY=i-5*r/100,i>0&&e.minY<0&&(e.minY=0),e.maxY=e.maxY+5*r/100}return t.yaxis.forEach(function(o,h){o.max!==void 0&&(typeof o.max=="number"?e.maxYArr[h]=o.max:typeof o.max=="function"&&(e.maxYArr[h]=o.max(e.isMultipleYAxis?e.maxYArr[h]:e.maxY)),e.maxY=e.maxYArr[h]),o.min!==void 0&&(typeof o.min=="number"?e.minYArr[h]=o.min:typeof o.min=="function"&&(e.minYArr[h]=o.min(e.isMultipleYAxis?e.minYArr[h]===Number.MIN_VALUE?0:e.minYArr[h]:e.minY)),e.minY=e.minYArr[h])}),e.isBarHorizontal&&["min","max"].forEach(function(o){t.xaxis[o]!==void 0&&typeof t.xaxis[o]=="number"&&(o==="min"?e.minY=t.xaxis[o]:e.maxY=t.xaxis[o])}),e.isMultipleYAxis?(this.scales.setMultipleYScales(),e.minY=i,e.yAxisScale.forEach(function(o,h){e.minYArr[h]=o.niceMin,e.maxYArr[h]=o.niceMax})):(this.scales.setYScaleForIndex(0,e.minY,e.maxY),e.minY=e.yAxisScale[0].niceMin,e.maxY=e.yAxisScale[0].niceMax,e.minYArr[0]=e.yAxisScale[0].niceMin,e.maxYArr[0]=e.yAxisScale[0].niceMax),{minY:e.minY,maxY:e.maxY,minYArr:e.minYArr,maxYArr:e.maxYArr,yAxisScale:e.yAxisScale}}},{key:"setXRange",value:function(){var e=this.w.globals,t=this.w.config,i=t.xaxis.type==="numeric"||t.xaxis.type==="datetime"||t.xaxis.type==="category"&&!e.noLabelsProvided||e.noLabelsProvided||e.isXNumeric;if(e.isXNumeric&&function(){for(var r=0;r<e.series.length;r++)if(e.labels[r])for(var o=0;o<e.labels[r].length;o++)e.labels[r][o]!==null&&L.isNumber(e.labels[r][o])&&(e.maxX=Math.max(e.maxX,e.labels[r][o]),e.initialMaxX=Math.max(e.maxX,e.labels[r][o]),e.minX=Math.min(e.minX,e.labels[r][o]),e.initialMinX=Math.min(e.minX,e.labels[r][o]))}(),e.noLabelsProvided&&t.xaxis.categories.length===0&&(e.maxX=e.labels[e.labels.length-1],e.initialMaxX=e.labels[e.labels.length-1],e.minX=1,e.initialMinX=1),e.isXNumeric||e.noLabelsProvided||e.dataFormatXNumeric){var a;if(t.xaxis.tickAmount===void 0?(a=Math.round(e.svgWidth/150),t.xaxis.type==="numeric"&&e.dataPoints<30&&(a=e.dataPoints-1),a>e.dataPoints&&e.dataPoints!==0&&(a=e.dataPoints-1)):t.xaxis.tickAmount==="dataPoints"?(e.series.length>1&&(a=e.series[e.maxValsInArrayIndex].length-1),e.isXNumeric&&(a=e.maxX-e.minX-1)):a=t.xaxis.tickAmount,e.xTickAmount=a,t.xaxis.max!==void 0&&typeof t.xaxis.max=="number"&&(e.maxX=t.xaxis.max),t.xaxis.min!==void 0&&typeof t.xaxis.min=="number"&&(e.minX=t.xaxis.min),t.xaxis.range!==void 0&&(e.minX=e.maxX-t.xaxis.range),e.minX!==Number.MAX_VALUE&&e.maxX!==-Number.MAX_VALUE)if(t.xaxis.convertedCatToNumeric&&!e.dataFormatXNumeric){for(var s=[],n=e.minX-1;n<e.maxX;n++)s.push(n+1);e.xAxisScale={result:s,niceMin:s[0],niceMax:s[s.length-1]}}else e.xAxisScale=this.scales.setXScale(e.minX,e.maxX);else e.xAxisScale=this.scales.linearScale(1,a,a),e.noLabelsProvided&&e.labels.length>0&&(e.xAxisScale=this.scales.linearScale(1,e.labels.length,a-1),e.seriesX=e.labels.slice());i&&(e.labels=e.xAxisScale.result.slice())}return e.isBarHorizontal&&e.labels.length&&(e.xTickAmount=e.labels.length),this._handleSingleDataPoint(),this._getMinXDiff(),{minX:e.minX,maxX:e.maxX}}},{key:"setZRange",value:function(){var e=this.w.globals;if(e.isDataXYZ){for(var t=0;t<e.series.length;t++)if(e.seriesZ[t]!==void 0)for(var i=0;i<e.seriesZ[t].length;i++)e.seriesZ[t][i]!==null&&L.isNumber(e.seriesZ[t][i])&&(e.maxZ=Math.max(e.maxZ,e.seriesZ[t][i]),e.minZ=Math.min(e.minZ,e.seriesZ[t][i]))}}},{key:"_handleSingleDataPoint",value:function(){var e=this.w.globals,t=this.w.config;if(e.minX===e.maxX){var i=new W(this.ctx);if(t.xaxis.type==="datetime"){var a=i.getDate(e.minX);a.setUTCDate(a.getDate()-2),e.minX=new Date(a).getTime();var s=i.getDate(e.maxX);s.setUTCDate(s.getDate()+2),e.maxX=new Date(s).getTime()}else(t.xaxis.type==="numeric"||t.xaxis.type==="category"&&!e.noLabelsProvided)&&(e.minX=e.minX-2,e.initialMinX=e.minX,e.maxX=e.maxX+2,e.initialMaxX=e.maxX)}}},{key:"_getMinXDiff",value:function(){var e=this.w.globals;e.isXNumeric&&e.seriesX.forEach(function(t,i){t.length===1&&t.push(e.seriesX[e.maxValsInArrayIndex][e.seriesX[e.maxValsInArrayIndex].length-1]);var a=t.slice();a.sort(function(s,n){return s-n}),a.forEach(function(s,n){if(n>0){var r=s-a[n-1];r>0&&(e.minXDiff=Math.min(r,e.minXDiff))}}),e.dataPoints===1&&e.minXDiff===Number.MAX_VALUE&&(e.minXDiff=.5)})}},{key:"_setStackedMinMax",value:function(){var e=this.w.globals,t=[],i=[];if(e.series.length)for(var a=0;a<e.series[e.maxValsInArrayIndex].length;a++)for(var s=0,n=0,r=0;r<e.series.length;r++)e.series[r][a]!==null&&L.isNumber(e.series[r][a])&&(e.series[r][a]>0?s=s+parseFloat(e.series[r][a])+1e-4:n+=parseFloat(e.series[r][a])),r===e.series.length-1&&(t.push(s),i.push(n));for(var o=0;o<t.length;o++)e.maxY=Math.max(e.maxY,t[o]),e.minY=Math.min(e.minY,i[o])}}]),v}(),we=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w;var t=this.w;this.xaxisFontSize=t.config.xaxis.labels.style.fontSize,this.axisFontFamily=t.config.xaxis.labels.style.fontFamily,this.xaxisForeColors=t.config.xaxis.labels.style.colors,this.isCategoryBarHorizontal=t.config.chart.type==="bar"&&t.config.plotOptions.bar.horizontal,this.xAxisoffX=0,t.config.xaxis.position==="bottom"&&(this.xAxisoffX=t.globals.gridHeight),this.drawnLabels=[],this.axesUtils=new J(e)}return E(v,[{key:"drawYaxis",value:function(e){var t=this,i=this.w,a=new I(this.ctx),s=i.config.yaxis[e].labels.style,n=s.fontSize,r=s.fontFamily,o=s.fontWeight,h=a.group({class:"apexcharts-yaxis",rel:e,transform:"translate("+i.globals.translateYAxisX[e]+", 0)"});if(this.axesUtils.isYAxisHidden(e))return h;var c=a.group({class:"apexcharts-yaxis-texts-g"});h.add(c);var d=i.globals.yAxisScale[e].result.length-1,g=i.globals.gridHeight/d,f=i.globals.translateY,p=i.globals.yLabelFormatters[e],x=i.globals.yAxisScale[e].result.slice();x=this.axesUtils.checkForReversedLabels(e,x);var b="";if(i.config.yaxis[e].labels.show)for(var y=function(P){var C=x[P];C=p(C,P,i);var M=i.config.yaxis[e].labels.padding;i.config.yaxis[e].opposite&&i.config.yaxis.length!==0&&(M*=-1);var T=t.axesUtils.getYAxisForeColor(s.colors,e),z=a.drawText({x:M,y:f+d/10+i.config.yaxis[e].labels.offsetY+1,text:C,textAnchor:i.config.yaxis[e].opposite?"start":"end",fontSize:n,fontFamily:r,fontWeight:o,foreColor:Array.isArray(T)?T[P]:T,isPlainText:!1,cssClass:"apexcharts-yaxis-label "+s.cssClass});P===d&&(b=z),c.add(z);var Y=document.createElementNS(i.globals.SVGNS,"title");if(Y.textContent=Array.isArray(C)?C.join(" "):C,z.node.appendChild(Y),i.config.yaxis[e].labels.rotate!==0){var H=a.rotateAroundCenter(b.node),R=a.rotateAroundCenter(z.node);z.node.setAttribute("transform","rotate(".concat(i.config.yaxis[e].labels.rotate," ").concat(H.x," ").concat(R.y,")"))}f+=g},k=d;k>=0;k--)y(k);if(i.config.yaxis[e].title.text!==void 0){var A=a.group({class:"apexcharts-yaxis-title"}),l=0;i.config.yaxis[e].opposite&&(l=i.globals.translateYAxisX[e]);var u=a.drawText({x:l,y:i.globals.gridHeight/2+i.globals.translateY+i.config.yaxis[e].title.offsetY,text:i.config.yaxis[e].title.text,textAnchor:"end",foreColor:i.config.yaxis[e].title.style.color,fontSize:i.config.yaxis[e].title.style.fontSize,fontWeight:i.config.yaxis[e].title.style.fontWeight,fontFamily:i.config.yaxis[e].title.style.fontFamily,cssClass:"apexcharts-yaxis-title-text "+i.config.yaxis[e].title.style.cssClass});A.add(u),h.add(A)}var m=i.config.yaxis[e].axisBorder,w=31+m.offsetX;if(i.config.yaxis[e].opposite&&(w=-31-m.offsetX),m.show){var S=a.drawLine(w,i.globals.translateY+m.offsetY-2,w,i.globals.gridHeight+i.globals.translateY+m.offsetY+2,m.color,0,m.width);h.add(S)}return i.config.yaxis[e].axisTicks.show&&this.axesUtils.drawYAxisTicks(w,d,m,i.config.yaxis[e].axisTicks,e,g,h),h}},{key:"drawYaxisInversed",value:function(e){var t=this.w,i=new I(this.ctx),a=i.group({class:"apexcharts-xaxis apexcharts-yaxis-inversed"}),s=i.group({class:"apexcharts-xaxis-texts-g",transform:"translate(".concat(t.globals.translateXAxisX,", ").concat(t.globals.translateXAxisY,")")});a.add(s);var n=t.globals.yAxisScale[e].result.length-1,r=t.globals.gridWidth/n+.1,o=r+t.config.xaxis.labels.offsetX,h=t.globals.xLabelFormatter,c=t.globals.yAxisScale[e].result.slice(),d=t.globals.timescaleLabels;d.length>0&&(this.xaxisLabels=d.slice(),n=(c=d.slice()).length),c=this.axesUtils.checkForReversedLabels(e,c);var g=d.length;if(t.config.xaxis.labels.show)for(var f=g?0:n;g?f<g:f>=0;g?f++:f--){var p=c[f];p=h(p,f,t);var x=t.globals.gridWidth+t.globals.padHorizontal-(o-r+t.config.xaxis.labels.offsetX);if(d.length){var b=this.axesUtils.getLabel(c,d,x,f,this.drawnLabels,this.xaxisFontSize);x=b.x,p=b.text,this.drawnLabels.push(b.text),f===0&&t.globals.skipFirstTimelinelabel&&(p=""),f===c.length-1&&t.globals.skipLastTimelinelabel&&(p="")}var y=i.drawText({x,y:this.xAxisoffX+t.config.xaxis.labels.offsetY+30-(t.config.xaxis.position==="top"?t.globals.xAxisHeight+t.config.xaxis.axisTicks.height-2:0),text:p,textAnchor:"middle",foreColor:Array.isArray(this.xaxisForeColors)?this.xaxisForeColors[e]:this.xaxisForeColors,fontSize:this.xaxisFontSize,fontFamily:this.xaxisFontFamily,fontWeight:t.config.xaxis.labels.style.fontWeight,isPlainText:!1,cssClass:"apexcharts-xaxis-label "+t.config.xaxis.labels.style.cssClass});s.add(y),y.tspan(p);var k=document.createElementNS(t.globals.SVGNS,"title");k.textContent=p,y.node.appendChild(k),o+=r}return this.inversedYAxisTitleText(a),this.inversedYAxisBorder(a),a}},{key:"inversedYAxisBorder",value:function(e){var t=this.w,i=new I(this.ctx),a=t.config.xaxis.axisBorder;if(a.show){var s=0;t.config.chart.type==="bar"&&t.globals.isXNumeric&&(s-=15);var n=i.drawLine(t.globals.padHorizontal+s+a.offsetX,this.xAxisoffX,t.globals.gridWidth,this.xAxisoffX,a.color,0,a.height);e.add(n)}}},{key:"inversedYAxisTitleText",value:function(e){var t=this.w,i=new I(this.ctx);if(t.config.xaxis.title.text!==void 0){var a=i.group({class:"apexcharts-xaxis-title apexcharts-yaxis-title-inversed"}),s=i.drawText({x:t.globals.gridWidth/2+t.config.xaxis.title.offsetX,y:this.xAxisoffX+parseFloat(this.xaxisFontSize)+parseFloat(t.config.xaxis.title.style.fontSize)+t.config.xaxis.title.offsetY+20,text:t.config.xaxis.title.text,textAnchor:"middle",fontSize:t.config.xaxis.title.style.fontSize,fontFamily:t.config.xaxis.title.style.fontFamily,fontWeight:t.config.xaxis.title.style.fontWeight,foreColor:t.config.xaxis.title.style.color,cssClass:"apexcharts-xaxis-title-text "+t.config.xaxis.title.style.cssClass});a.add(s),e.add(a)}}},{key:"yAxisTitleRotate",value:function(e,t){var i=this.w,a=new I(this.ctx),s={width:0,height:0},n={width:0,height:0},r=i.globals.dom.baseEl.querySelector(" .apexcharts-yaxis[rel='".concat(e,"'] .apexcharts-yaxis-texts-g"));r!==null&&(s=r.getBoundingClientRect());var o=i.globals.dom.baseEl.querySelector(".apexcharts-yaxis[rel='".concat(e,"'] .apexcharts-yaxis-title text"));if(o!==null&&(n=o.getBoundingClientRect()),o!==null){var h=this.xPaddingForYAxisTitle(e,s,n,t);o.setAttribute("x",h.xPos-(t?10:0))}if(o!==null){var c=a.rotateAroundCenter(o);o.setAttribute("transform","rotate(".concat(t?-1*i.config.yaxis[e].title.rotate:i.config.yaxis[e].title.rotate," ").concat(c.x," ").concat(c.y,")"))}}},{key:"xPaddingForYAxisTitle",value:function(e,t,i,a){var s=this.w,n=0,r=0,o=10;return s.config.yaxis[e].title.text===void 0||e<0?{xPos:r,padd:0}:(a?(r=t.width+s.config.yaxis[e].title.offsetX+i.width/2+o/2,(n+=1)===0&&(r-=o/2)):(r=-1*t.width+s.config.yaxis[e].title.offsetX+o/2+i.width/2,s.globals.isBarHorizontal&&(o=25,r=-1*t.width-s.config.yaxis[e].title.offsetX-o)),{xPos:r,padd:o})}},{key:"setYAxisXPosition",value:function(e,t){var i=this.w,a=0,s=0,n=18,r=1;i.config.yaxis.length>1&&(this.multipleYs=!0),i.config.yaxis.map(function(o,h){var c=i.globals.ignoreYAxisIndexes.indexOf(h)>-1||!o.show||o.floating||e[h].width===0,d=e[h].width+t[h].width;o.opposite?i.globals.isBarHorizontal?(s=i.globals.gridWidth+i.globals.translateX-1,i.globals.translateYAxisX[h]=s-o.labels.offsetX):(s=i.globals.gridWidth+i.globals.translateX+r,c||(r=r+d+20),i.globals.translateYAxisX[h]=s-o.labels.offsetX+20):(a=i.globals.translateX-n,c||(n=n+d+20),i.globals.translateYAxisX[h]=a+o.labels.offsetX)})}},{key:"setYAxisTextAlignments",value:function(){var e=this.w,t=e.globals.dom.baseEl.getElementsByClassName("apexcharts-yaxis");(t=L.listToArray(t)).forEach(function(i,a){var s=e.config.yaxis[a];if(s&&s.labels.align!==void 0){var n=e.globals.dom.baseEl.querySelector(".apexcharts-yaxis[rel='".concat(a,"'] .apexcharts-yaxis-texts-g")),r=e.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis[rel='".concat(a,"'] .apexcharts-yaxis-label"));r=L.listToArray(r);var o=n.getBoundingClientRect();s.labels.align==="left"?(r.forEach(function(h,c){h.setAttribute("text-anchor","start")}),s.opposite||n.setAttribute("transform","translate(-".concat(o.width,", 0)"))):s.labels.align==="center"?(r.forEach(function(h,c){h.setAttribute("text-anchor","middle")}),n.setAttribute("transform","translate(".concat(o.width/2*(s.opposite?1:-1),", 0)"))):s.labels.align==="right"&&(r.forEach(function(h,c){h.setAttribute("text-anchor","end")}),s.opposite&&n.setAttribute("transform","translate(".concat(o.width,", 0)")))}})}}]),v}(),st=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.documentEvent=L.bind(this.documentEvent,this)}return E(v,[{key:"addEventListener",value:function(e,t){var i=this.w;i.globals.events.hasOwnProperty(e)?i.globals.events[e].push(t):i.globals.events[e]=[t]}},{key:"removeEventListener",value:function(e,t){var i=this.w;if(i.globals.events.hasOwnProperty(e)){var a=i.globals.events[e].indexOf(t);a!==-1&&i.globals.events[e].splice(a,1)}}},{key:"fireEvent",value:function(e,t){var i=this.w;if(i.globals.events.hasOwnProperty(e)){t&&t.length||(t=[]);for(var a=i.globals.events[e],s=a.length,n=0;n<s;n++)a[n].apply(null,t)}}},{key:"setupEventHandlers",value:function(){var e=this,t=this.w,i=this.ctx,a=t.globals.dom.baseEl.querySelector(t.globals.chartClass);this.ctx.eventList.forEach(function(s){a.addEventListener(s,function(n){var r=Object.assign({},t,{seriesIndex:t.globals.capturedSeriesIndex,dataPointIndex:t.globals.capturedDataPointIndex});n.type==="mousemove"||n.type==="touchmove"?typeof t.config.chart.events.mouseMove=="function"&&t.config.chart.events.mouseMove(n,i,r):n.type==="mouseleave"||n.type==="touchleave"?typeof t.config.chart.events.mouseLeave=="function"&&t.config.chart.events.mouseLeave(n,i,r):(n.type==="mouseup"&&n.which===1||n.type==="touchend")&&(typeof t.config.chart.events.click=="function"&&t.config.chart.events.click(n,i,r),i.ctx.events.fireEvent("click",[n,i,r]))},{capture:!1,passive:!0})}),this.ctx.eventList.forEach(function(s){t.globals.dom.baseEl.addEventListener(s,e.documentEvent,{passive:!0})}),this.ctx.core.setupBrushHandler()}},{key:"documentEvent",value:function(e){var t=this.w,i=e.target.className;if(e.type==="click"){var a=t.globals.dom.baseEl.querySelector(".apexcharts-menu");a&&a.classList.contains("apexcharts-menu-open")&&i!=="apexcharts-menu-icon"&&a.classList.remove("apexcharts-menu-open")}t.globals.clientX=e.type==="touchmove"?e.touches[0].clientX:e.clientX,t.globals.clientY=e.type==="touchmove"?e.touches[0].clientY:e.clientY}}]),v}(),rt=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"setCurrentLocaleValues",value:function(e){var t=this.w.config.chart.locales;window.Apex.chart&&window.Apex.chart.locales&&window.Apex.chart.locales.length>0&&(t=this.w.config.chart.locales.concat(window.Apex.chart.locales));var i=t.filter(function(s){return s.name===e})[0];if(!i)throw new Error("Wrong locale name provided. Please make sure you set the correct locale name in options");var a=L.extend(Xe,i);this.w.globals.locale=a.options}}]),v}(),nt=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"drawAxis",value:function(e,t){var i,a,s=this.w.globals,n=this.w.config,r=new he(this.ctx),o=new we(this.ctx);s.axisCharts&&e!=="radar"&&(s.isBarHorizontal?(a=o.drawYaxisInversed(0),i=r.drawXaxisInversed(0),s.dom.elGraphical.add(i),s.dom.elGraphical.add(a)):(i=r.drawXaxis(),s.dom.elGraphical.add(i),n.yaxis.map(function(h,c){s.ignoreYAxisIndexes.indexOf(c)===-1&&(a=o.drawYaxis(c),s.dom.Paper.add(a))})))}}]),v}(),ke=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"drawXCrosshairs",value:function(){var e=this.w,t=new I(this.ctx),i=new O(this.ctx),a=e.config.xaxis.crosshairs.fill.gradient,s=e.config.xaxis.crosshairs.dropShadow,n=e.config.xaxis.crosshairs.fill.type,r=a.colorFrom,o=a.colorTo,h=a.opacityFrom,c=a.opacityTo,d=a.stops,g=s.enabled,f=s.left,p=s.top,x=s.blur,b=s.color,y=s.opacity,k=e.config.xaxis.crosshairs.fill.color;if(e.config.xaxis.crosshairs.show){n==="gradient"&&(k=t.drawGradient("vertical",r,o,h,c,null,d,null));var A=t.drawRect();e.config.xaxis.crosshairs.width===1&&(A=t.drawLine());var l=e.globals.gridHeight;(!L.isNumber(l)||l<0)&&(l=0);var u=e.config.xaxis.crosshairs.width;(!L.isNumber(u)||u<0)&&(u=0),A.attr({class:"apexcharts-xcrosshairs",x:0,y:0,y2:l,width:u,height:l,fill:k,filter:"none","fill-opacity":e.config.xaxis.crosshairs.opacity,stroke:e.config.xaxis.crosshairs.stroke.color,"stroke-width":e.config.xaxis.crosshairs.stroke.width,"stroke-dasharray":e.config.xaxis.crosshairs.stroke.dashArray}),g&&(A=i.dropShadow(A,{left:f,top:p,blur:x,color:b,opacity:y})),e.globals.dom.elGraphical.add(A)}}},{key:"drawYCrosshairs",value:function(){var e=this.w,t=new I(this.ctx),i=e.config.yaxis[0].crosshairs,a=e.globals.barPadForNumericAxis;if(e.config.yaxis[0].crosshairs.show){var s=t.drawLine(-a,0,e.globals.gridWidth+a,0,i.stroke.color,i.stroke.dashArray,i.stroke.width);s.attr({class:"apexcharts-ycrosshairs"}),e.globals.dom.elGraphical.add(s)}var n=t.drawLine(-a,0,e.globals.gridWidth+a,0,i.stroke.color,0,0);n.attr({class:"apexcharts-ycrosshairs-hidden"}),e.globals.dom.elGraphical.add(n)}}]),v}(),ot=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"checkResponsiveConfig",value:function(e){var t=this,i=this.w,a=i.config;if(a.responsive.length!==0){var s=a.responsive.slice();s.sort(function(h,c){return h.breakpoint>c.breakpoint?1:c.breakpoint>h.breakpoint?-1:0}).reverse();var n=new le({}),r=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},c=s[0].breakpoint,d=window.innerWidth>0?window.innerWidth:screen.width;if(d>c){var g=D.extendArrayProps(n,i.globals.initialConfig,i);h=L.extend(g,h),h=L.extend(i.config,h),t.overrideResponsiveOptions(h)}else for(var f=0;f<s.length;f++)d<s[f].breakpoint&&(h=D.extendArrayProps(n,s[f].options,i),h=L.extend(i.config,h),t.overrideResponsiveOptions(h))};if(e){var o=D.extendArrayProps(n,e,i);o=L.extend(i.config,o),r(o=L.extend(o,e))}else r({})}}},{key:"overrideResponsiveOptions",value:function(e){var t=new le(e).init({responsiveOverride:!0});this.w.config=t}}]),v}(),lt=function(){function v(e){X(this,v),this.ctx=e,this.colors=[],this.w=e.w;var t=this.w;this.isColorFn=!1,this.isHeatmapDistributed=t.config.chart.type==="treemap"&&t.config.plotOptions.treemap.distributed||t.config.chart.type==="heatmap"&&t.config.plotOptions.heatmap.distributed,this.isBarDistributed=t.config.plotOptions.bar.distributed&&(t.config.chart.type==="bar"||t.config.chart.type==="rangeBar")}return E(v,[{key:"init",value:function(){this.setDefaultColors()}},{key:"setDefaultColors",value:function(){var e=this,t=this.w,i=new L;if(t.globals.dom.elWrap.classList.add("apexcharts-theme-".concat(t.config.theme.mode)),t.config.colors===void 0?t.globals.colors=this.predefined():(t.globals.colors=t.config.colors,Array.isArray(t.config.colors)&&t.config.colors.length>0&&typeof t.config.colors[0]=="function"&&(t.globals.colors=t.config.series.map(function(f,p){var x=t.config.colors[p];return x||(x=t.config.colors[0]),typeof x=="function"?(e.isColorFn=!0,x({value:t.globals.axisCharts?t.globals.series[p][0]?t.globals.series[p][0]:0:t.globals.series[p],seriesIndex:p,dataPointIndex:p,w:t})):x}))),t.globals.seriesColors.map(function(f,p){f&&(t.globals.colors[p]=f)}),t.config.theme.monochrome.enabled){var a=[],s=t.globals.series.length;(this.isBarDistributed||this.isHeatmapDistributed)&&(s=t.globals.series[0].length*t.globals.series.length);for(var n=t.config.theme.monochrome.color,r=1/(s/t.config.theme.monochrome.shadeIntensity),o=t.config.theme.monochrome.shadeTo,h=0,c=0;c<s;c++){var d=void 0;o==="dark"?(d=i.shadeColor(-1*h,n),h+=r):(d=i.shadeColor(h,n),h+=r),a.push(d)}t.globals.colors=a.slice()}var g=t.globals.colors.slice();this.pushExtraColors(t.globals.colors),["fill","stroke"].forEach(function(f){t.config[f].colors===void 0?t.globals[f].colors=e.isColorFn?t.config.colors:g:t.globals[f].colors=t.config[f].colors.slice(),e.pushExtraColors(t.globals[f].colors)}),t.config.dataLabels.style.colors===void 0?t.globals.dataLabels.style.colors=g:t.globals.dataLabels.style.colors=t.config.dataLabels.style.colors.slice(),this.pushExtraColors(t.globals.dataLabels.style.colors,50),t.config.plotOptions.radar.polygons.fill.colors===void 0?t.globals.radarPolygons.fill.colors=[t.config.theme.mode==="dark"?"#424242":"none"]:t.globals.radarPolygons.fill.colors=t.config.plotOptions.radar.polygons.fill.colors.slice(),this.pushExtraColors(t.globals.radarPolygons.fill.colors,20),t.config.markers.colors===void 0?t.globals.markers.colors=g:t.globals.markers.colors=t.config.markers.colors.slice(),this.pushExtraColors(t.globals.markers.colors)}},{key:"pushExtraColors",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=this.w,s=t||a.globals.series.length;if(i===null&&(i=this.isBarDistributed||this.isHeatmapDistributed||a.config.chart.type==="heatmap"&&a.config.plotOptions.heatmap.colorScale.inverse),i&&a.globals.series.length&&(s=a.globals.series[a.globals.maxValsInArrayIndex].length*a.globals.series.length),e.length<s)for(var n=s-e.length,r=0;r<n;r++)e.push(e[r])}},{key:"updateThemeOptions",value:function(e){e.chart=e.chart||{},e.tooltip=e.tooltip||{};var t=e.theme.mode||"light",i=e.theme.palette?e.theme.palette:t==="dark"?"palette4":"palette1",a=e.chart.foreColor?e.chart.foreColor:t==="dark"?"#f6f7f8":"#373d3f";return e.tooltip.theme=t,e.chart.foreColor=a,e.theme.palette=i,e}},{key:"predefined",value:function(){switch(this.w.config.theme.palette){case"palette1":this.colors=["#008FFB","#00E396","#FEB019","#FF4560","#775DD0"];break;case"palette2":this.colors=["#3f51b5","#03a9f4","#4caf50","#f9ce1d","#FF9800"];break;case"palette3":this.colors=["#33b2df","#546E7A","#d4526e","#13d8aa","#A5978B"];break;case"palette4":this.colors=["#4ecdc4","#c7f464","#81D4FA","#fd6a6a","#546E7A"];break;case"palette5":this.colors=["#2b908f","#f9a3a4","#90ee7e","#fa4443","#69d2e7"];break;case"palette6":this.colors=["#449DD1","#F86624","#EA3546","#662E9B","#C5D86D"];break;case"palette7":this.colors=["#D7263D","#1B998B","#2E294E","#F46036","#E2C044"];break;case"palette8":this.colors=["#662E9B","#F86624","#F9C80E","#EA3546","#43BCCD"];break;case"palette9":this.colors=["#5C4742","#A5978B","#8D5B4C","#5A2A27","#C4BBAF"];break;case"palette10":this.colors=["#A300D6","#7D02EB","#5653FE","#2983FF","#00B1F2"];break;default:this.colors=["#008FFB","#00E396","#FEB019","#FF4560","#775DD0"]}return this.colors}}]),v}(),ht=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"draw",value:function(){this.drawTitleSubtitle("title"),this.drawTitleSubtitle("subtitle")}},{key:"drawTitleSubtitle",value:function(e){var t=this.w,i=e==="title"?t.config.title:t.config.subtitle,a=t.globals.svgWidth/2,s=i.offsetY,n="middle";if(i.align==="left"?(a=10,n="start"):i.align==="right"&&(a=t.globals.svgWidth-10,n="end"),a+=i.offsetX,s=s+parseInt(i.style.fontSize,10)+i.margin/2,i.text!==void 0){var r=new I(this.ctx).drawText({x:a,y:s,text:i.text,textAnchor:n,fontSize:i.style.fontSize,fontFamily:i.style.fontFamily,fontWeight:i.style.fontWeight,foreColor:i.style.color,opacity:1});r.node.setAttribute("class","apexcharts-".concat(e,"-text")),t.globals.dom.Paper.add(r)}}}]),v}(),ct=function(){function v(e){X(this,v),this.w=e.w,this.dCtx=e}return E(v,[{key:"getTitleSubtitleCoords",value:function(e){var t=this.w,i=0,a=0,s=e==="title"?t.config.title.floating:t.config.subtitle.floating,n=t.globals.dom.baseEl.querySelector(".apexcharts-".concat(e,"-text"));if(n!==null&&!s){var r=n.getBoundingClientRect();i=r.width,a=t.globals.axisCharts?r.height+5:r.height}return{width:i,height:a}}},{key:"getLegendsRect",value:function(){var e=this.w,t=e.globals.dom.baseEl.querySelector(".apexcharts-legend");e.config.legend.height||e.config.legend.position!=="top"&&e.config.legend.position!=="bottom"||(t.style.maxHeight=e.globals.svgHeight/2+"px");var i=Object.assign({},L.getBoundingClientRect(t));return t!==null&&!e.config.legend.floating&&e.config.legend.show?this.dCtx.lgRect={x:i.x,y:i.y,height:i.height,width:i.height===0?0:i.width}:this.dCtx.lgRect={x:0,y:0,height:0,width:0},e.config.legend.position!=="left"&&e.config.legend.position!=="right"||1.5*this.dCtx.lgRect.width>e.globals.svgWidth&&(this.dCtx.lgRect.width=e.globals.svgWidth/1.5),this.dCtx.lgRect}},{key:"getLargestStringFromMultiArr",value:function(e,t){var i=e;if(this.w.globals.isMultiLineX){var a=t.map(function(n,r){return Array.isArray(n)?n.length:1}),s=Math.max.apply(Math,U(a));i=t[a.indexOf(s)]}return i}}]),v}(),dt=function(){function v(e){X(this,v),this.w=e.w,this.dCtx=e}return E(v,[{key:"getxAxisLabelsCoords",value:function(){var e,t=this.w,i=t.globals.labels.slice();if(t.config.xaxis.convertedCatToNumeric&&i.length===0&&(i=t.globals.categoryLabels),t.globals.timescaleLabels.length>0){var a=this.getxAxisTimeScaleLabelsCoords();e={width:a.width,height:a.height},t.globals.rotateXLabels=!1}else{this.dCtx.lgWidthForSideLegends=t.config.legend.position!=="left"&&t.config.legend.position!=="right"||t.config.legend.floating?0:this.dCtx.lgRect.width;var s=t.globals.xLabelFormatter,n=L.getLargestStringFromArr(i),r=this.dCtx.dimHelpers.getLargestStringFromMultiArr(n,i);t.globals.isBarHorizontal&&(r=n=t.globals.yAxisScale[0].result.reduce(function(p,x){return p.length>x.length?p:x},0));var o=new ge(this.dCtx.ctx),h=n;n=o.xLabelFormat(s,n,h,{i:void 0,dateFormatter:new W(this.dCtx.ctx).formatDate,w:t}),r=o.xLabelFormat(s,r,h,{i:void 0,dateFormatter:new W(this.dCtx.ctx).formatDate,w:t}),(t.config.xaxis.convertedCatToNumeric&&n===void 0||String(n).trim()==="")&&(r=n="1");var c=new I(this.dCtx.ctx),d=c.getTextRects(n,t.config.xaxis.labels.style.fontSize),g=d;if(n!==r&&(g=c.getTextRects(r,t.config.xaxis.labels.style.fontSize)),(e={width:d.width>=g.width?d.width:g.width,height:d.height>=g.height?d.height:g.height}).width*i.length>t.globals.svgWidth-this.dCtx.lgWidthForSideLegends-this.dCtx.yAxisWidth-this.dCtx.gridPad.left-this.dCtx.gridPad.right&&t.config.xaxis.labels.rotate!==0||t.config.xaxis.labels.rotateAlways){if(!t.globals.isBarHorizontal){t.globals.rotateXLabels=!0;var f=function(p){return c.getTextRects(p,t.config.xaxis.labels.style.fontSize,t.config.xaxis.labels.style.fontFamily,"rotate(".concat(t.config.xaxis.labels.rotate," 0 0)"),!1)};d=f(n),n!==r&&(g=f(r)),e.height=(d.height>g.height?d.height:g.height)/1.5,e.width=d.width>g.width?d.width:g.width}}else t.globals.rotateXLabels=!1}return t.config.xaxis.labels.show||(e={width:0,height:0}),{width:e.width,height:e.height}}},{key:"getxAxisTitleCoords",value:function(){var e=this.w,t=0,i=0;if(e.config.xaxis.title.text!==void 0){var a=new I(this.dCtx.ctx).getTextRects(e.config.xaxis.title.text,e.config.xaxis.title.style.fontSize);t=a.width,i=a.height}return{width:t,height:i}}},{key:"getxAxisTimeScaleLabelsCoords",value:function(){var e,t=this.w;this.dCtx.timescaleLabels=t.globals.timescaleLabels.slice();var i=this.dCtx.timescaleLabels.map(function(s){return s.value}),a=i.reduce(function(s,n){return s===void 0?(console.error("You have possibly supplied invalid Date format. Please supply a valid JavaScript Date"),0):s.length>n.length?s:n},0);return 1.05*(e=new I(this.dCtx.ctx).getTextRects(a,t.config.xaxis.labels.style.fontSize)).width*i.length>t.globals.gridWidth&&t.config.xaxis.labels.rotate!==0&&(t.globals.overlappingXLabels=!0),e}},{key:"additionalPaddingXLabels",value:function(e){var t=this,i=this.w,a=i.globals,s=i.config,n=s.xaxis.type,r=e.width;a.skipLastTimelinelabel=!1,a.skipFirstTimelinelabel=!1;var o=i.config.yaxis[0].opposite&&i.globals.isBarHorizontal,h=function(c,d){(function(g){return a.collapsedSeriesIndices.indexOf(g)!==-1})(d)||function(g){if(t.dCtx.timescaleLabels&&t.dCtx.timescaleLabels.length){var f=t.dCtx.timescaleLabels[0],p=t.dCtx.timescaleLabels[t.dCtx.timescaleLabels.length-1].position+r/1.75-t.dCtx.yAxisWidthRight,x=f.position-r/1.75+t.dCtx.yAxisWidthLeft,b=i.config.legend.position==="right"&&t.dCtx.lgRect.width>0?t.dCtx.lgRect.width:0;p>a.svgWidth-a.translateX-b&&(a.skipLastTimelinelabel=!0),x<-(g.show&&!g.floating||s.chart.type!=="bar"&&s.chart.type!=="candlestick"&&s.chart.type!=="rangeBar"&&s.chart.type!=="boxPlot"?10:r/1.75)&&(a.skipFirstTimelinelabel=!0)}else n==="datetime"?t.dCtx.gridPad.right<r&&!a.rotateXLabels&&(a.skipLastTimelinelabel=!0):n!=="datetime"&&t.dCtx.gridPad.right<r/2-t.dCtx.yAxisWidthRight&&!a.rotateXLabels&&!i.config.xaxis.labels.trim&&(i.config.xaxis.tickPlacement!=="between"||i.globals.isBarHorizontal)&&(t.dCtx.xPadRight=r/2+1)}(c)};s.yaxis.forEach(function(c,d){o?(t.dCtx.gridPad.left<r&&(t.dCtx.xPadLeft=r/2+1),t.dCtx.xPadRight=r/2+1):h(c,d)})}}]),v}(),gt=function(){function v(e){X(this,v),this.w=e.w,this.dCtx=e}return E(v,[{key:"getyAxisLabelsCoords",value:function(){var e=this,t=this.w,i=[],a=10,s=new J(this.dCtx.ctx);return t.config.yaxis.map(function(n,r){var o=t.globals.yAxisScale[r],h=0;if(!s.isYAxisHidden(r)&&n.labels.show&&n.labels.minWidth!==void 0&&(h=n.labels.minWidth),!s.isYAxisHidden(r)&&n.labels.show&&o.result.length){var c=t.globals.yLabelFormatters[r],d=o.niceMin===Number.MIN_VALUE?0:o.niceMin,g=String(d).length>String(o.niceMax).length?d:o.niceMax,f=c(g,{seriesIndex:r,dataPointIndex:-1,w:t}),p=f;if(f!==void 0&&f.length!==0||(f=g),t.globals.isBarHorizontal){a=0;var x=t.globals.labels.slice();f=c(f=L.getLargestStringFromArr(x),{seriesIndex:r,dataPointIndex:-1,w:t}),p=e.dCtx.dimHelpers.getLargestStringFromMultiArr(f,x)}var b=new I(e.dCtx.ctx),y="rotate(".concat(n.labels.rotate," 0 0)"),k=b.getTextRects(f,n.labels.style.fontSize,n.labels.style.fontFamily,y,!1),A=k;f!==p&&(A=b.getTextRects(p,n.labels.style.fontSize,n.labels.style.fontFamily,y,!1)),i.push({width:(h>A.width||h>k.width?h:A.width>k.width?A.width:k.width)+a,height:A.height>k.height?A.height:k.height})}else i.push({width:0,height:0})}),i}},{key:"getyAxisTitleCoords",value:function(){var e=this,t=this.w,i=[];return t.config.yaxis.map(function(a,s){if(a.show&&a.title.text!==void 0){var n=new I(e.dCtx.ctx),r="rotate(".concat(a.title.rotate," 0 0)"),o=n.getTextRects(a.title.text,a.title.style.fontSize,a.title.style.fontFamily,r,!1);i.push({width:o.width,height:o.height})}else i.push({width:0,height:0})}),i}},{key:"getTotalYAxisWidth",value:function(){var e=this.w,t=0,i=0,a=0,s=e.globals.yAxisScale.length>1?10:0,n=new J(this.dCtx.ctx),r=function(o,h){var c=e.config.yaxis[h].floating,d=0;o.width>0&&!c?(d=o.width+s,function(g){return e.globals.ignoreYAxisIndexes.indexOf(g)>-1}(h)&&(d=d-o.width-s)):d=c||n.isYAxisHidden(h)?0:5,e.config.yaxis[h].opposite?a+=d:i+=d,t+=d};return e.globals.yLabelsCoords.map(function(o,h){r(o,h)}),e.globals.yTitleCoords.map(function(o,h){r(o,h)}),e.globals.isBarHorizontal&&!e.config.yaxis[0].floating&&(t=e.globals.yLabelsCoords[0].width+e.globals.yTitleCoords[0].width+15),this.dCtx.yAxisWidthLeft=i,this.dCtx.yAxisWidthRight=a,t}}]),v}(),ut=function(){function v(e){X(this,v),this.w=e.w,this.dCtx=e}return E(v,[{key:"gridPadForColumnsInNumericAxis",value:function(e){var t=this.w;if(t.globals.noData||t.globals.allSeriesCollapsed)return 0;var i=function(c){return c==="bar"||c==="rangeBar"||c==="candlestick"||c==="boxPlot"},a=t.config.chart.type,s=0,n=i(a)?t.config.series.length:1;if(t.globals.comboBarCount>0&&(n=t.globals.comboBarCount),t.globals.collapsedSeries.forEach(function(c){i(c.type)&&(n-=1)}),t.config.chart.stacked&&(n=1),(i(a)||t.globals.comboBarCount>0)&&t.globals.isXNumeric&&!t.globals.isBarHorizontal&&n>0){var r,o,h=Math.abs(t.globals.initialMaxX-t.globals.initialMinX);h<=3&&(h=t.globals.dataPoints),r=h/e,t.globals.minXDiff&&t.globals.minXDiff/r>0&&(o=t.globals.minXDiff/r),o>e/2&&(o/=2),(s=o/n*parseInt(t.config.plotOptions.bar.columnWidth,10)/100)<1&&(s=1),s=s/(n>1?1:1.5)+5,t.globals.barPadForNumericAxis=s}return s}},{key:"gridPadFortitleSubtitle",value:function(){var e=this,t=this.w,i=t.globals,a=this.dCtx.isSparkline||!t.globals.axisCharts?0:10;["title","subtitle"].forEach(function(r){t.config[r].text!==void 0?a+=t.config[r].margin:a+=e.dCtx.isSparkline||!t.globals.axisCharts?0:5}),!t.config.legend.show||t.config.legend.position!=="bottom"||t.config.legend.floating||t.globals.axisCharts||(a+=10);var s=this.dCtx.dimHelpers.getTitleSubtitleCoords("title"),n=this.dCtx.dimHelpers.getTitleSubtitleCoords("subtitle");i.gridHeight=i.gridHeight-s.height-n.height-a,i.translateY=i.translateY+s.height+n.height+a}},{key:"setGridXPosForDualYAxis",value:function(e,t){var i=this.w,a=new J(this.dCtx.ctx);i.config.yaxis.map(function(s,n){i.globals.ignoreYAxisIndexes.indexOf(n)!==-1||s.floating||a.isYAxisHidden(n)||(s.opposite&&(i.globals.translateX=i.globals.translateX-(t[n].width+e[n].width)-parseInt(i.config.yaxis[n].labels.style.fontSize,10)/1.2-12),i.globals.translateX<2&&(i.globals.translateX=2))})}}]),v}(),ue=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.lgRect={},this.yAxisWidth=0,this.yAxisWidthLeft=0,this.yAxisWidthRight=0,this.xAxisHeight=0,this.isSparkline=this.w.config.chart.sparkline.enabled,this.dimHelpers=new ct(this),this.dimYAxis=new gt(this),this.dimXAxis=new dt(this),this.dimGrid=new ut(this),this.lgWidthForSideLegends=0,this.gridPad=this.w.config.grid.padding,this.xPadRight=0,this.xPadLeft=0}return E(v,[{key:"plotCoords",value:function(){var e=this.w.globals;this.lgRect=this.dimHelpers.getLegendsRect(),e.axisCharts?this.setDimensionsForAxisCharts():this.setDimensionsForNonAxisCharts(),this.dimGrid.gridPadFortitleSubtitle(),e.gridHeight=e.gridHeight-this.gridPad.top-this.gridPad.bottom,e.gridWidth=e.gridWidth-this.gridPad.left-this.gridPad.right-this.xPadRight-this.xPadLeft;var t=this.dimGrid.gridPadForColumnsInNumericAxis(e.gridWidth);e.gridWidth=e.gridWidth-2*t,e.translateX=e.translateX+this.gridPad.left+this.xPadLeft+(t>0?t+4:0),e.translateY=e.translateY+this.gridPad.top}},{key:"setDimensionsForAxisCharts",value:function(){var e=this,t=this.w,i=t.globals,a=this.dimYAxis.getyAxisLabelsCoords(),s=this.dimYAxis.getyAxisTitleCoords();t.globals.yLabelsCoords=[],t.globals.yTitleCoords=[],t.config.yaxis.map(function(g,f){t.globals.yLabelsCoords.push({width:a[f].width,index:f}),t.globals.yTitleCoords.push({width:s[f].width,index:f})}),this.yAxisWidth=this.dimYAxis.getTotalYAxisWidth();var n=this.dimXAxis.getxAxisLabelsCoords(),r=this.dimXAxis.getxAxisTitleCoords();this.conditionalChecksForAxisCoords(n,r),i.translateXAxisY=t.globals.rotateXLabels?this.xAxisHeight/8:-4,i.translateXAxisX=t.globals.rotateXLabels&&t.globals.isXNumeric&&t.config.xaxis.labels.rotate<=-45?-this.xAxisWidth/4:0,t.globals.isBarHorizontal&&(i.rotateXLabels=!1,i.translateXAxisY=parseInt(t.config.xaxis.labels.style.fontSize,10)/1.5*-1),i.translateXAxisY=i.translateXAxisY+t.config.xaxis.labels.offsetY,i.translateXAxisX=i.translateXAxisX+t.config.xaxis.labels.offsetX;var o=this.yAxisWidth,h=this.xAxisHeight;i.xAxisLabelsHeight=this.xAxisHeight-r.height,i.xAxisLabelsWidth=this.xAxisWidth,i.xAxisHeight=this.xAxisHeight;var c=10;(t.config.chart.type==="radar"||this.isSparkline)&&(o=0,h=i.goldenPadding),this.isSparkline&&(this.lgRect={height:0,width:0}),(this.isSparkline||t.config.chart.type==="treemap")&&(o=0,h=0,c=0),this.isSparkline||this.dimXAxis.additionalPaddingXLabels(n);var d=function(){i.translateX=o,i.gridHeight=i.svgHeight-e.lgRect.height-h-(e.isSparkline||t.config.chart.type==="treemap"?0:t.globals.rotateXLabels?10:15),i.gridWidth=i.svgWidth-o};switch(t.config.xaxis.position==="top"&&(c=i.xAxisHeight-t.config.xaxis.axisTicks.height-5),t.config.legend.position){case"bottom":i.translateY=c,d();break;case"top":i.translateY=this.lgRect.height+c,d();break;case"left":i.translateY=c,i.translateX=this.lgRect.width+o,i.gridHeight=i.svgHeight-h-12,i.gridWidth=i.svgWidth-this.lgRect.width-o;break;case"right":i.translateY=c,i.translateX=o,i.gridHeight=i.svgHeight-h-12,i.gridWidth=i.svgWidth-this.lgRect.width-o-5;break;default:throw new Error("Legend position not supported")}this.dimGrid.setGridXPosForDualYAxis(s,a),new we(this.ctx).setYAxisXPosition(a,s)}},{key:"setDimensionsForNonAxisCharts",value:function(){var e=this.w,t=e.globals,i=e.config,a=0;e.config.legend.show&&!e.config.legend.floating&&(a=20);var s=i.chart.type==="pie"||i.chart.type==="polarArea"||i.chart.type==="donut"?"pie":"radialBar",n=i.plotOptions[s].offsetY,r=i.plotOptions[s].offsetX;if(!i.legend.show||i.legend.floating)return t.gridHeight=t.svgHeight-i.grid.padding.left+i.grid.padding.right,t.gridWidth=t.gridHeight,t.translateY=n,void(t.translateX=r+(t.svgWidth-t.gridWidth)/2);switch(i.legend.position){case"bottom":t.gridHeight=t.svgHeight-this.lgRect.height-t.goldenPadding,t.gridWidth=t.svgWidth,t.translateY=n-10,t.translateX=r+(t.svgWidth-t.gridWidth)/2;break;case"top":t.gridHeight=t.svgHeight-this.lgRect.height-t.goldenPadding,t.gridWidth=t.svgWidth,t.translateY=this.lgRect.height+n+10,t.translateX=r+(t.svgWidth-t.gridWidth)/2;break;case"left":t.gridWidth=t.svgWidth-this.lgRect.width-a,t.gridHeight=i.chart.height!=="auto"?t.svgHeight:t.gridWidth,t.translateY=n,t.translateX=r+this.lgRect.width+a;break;case"right":t.gridWidth=t.svgWidth-this.lgRect.width-a-5,t.gridHeight=i.chart.height!=="auto"?t.svgHeight:t.gridWidth,t.translateY=n,t.translateX=r+10;break;default:throw new Error("Legend position not supported")}}},{key:"conditionalChecksForAxisCoords",value:function(e,t){var i=this.w;this.xAxisHeight=(e.height+t.height)*(i.globals.isMultiLineX?1.2:i.globals.LINE_HEIGHT_RATIO)+(i.globals.rotateXLabels?22:10),this.xAxisWidth=e.width,this.xAxisHeight-t.height>i.config.xaxis.labels.maxHeight&&(this.xAxisHeight=i.config.xaxis.labels.maxHeight),i.config.xaxis.labels.minHeight&&this.xAxisHeight<i.config.xaxis.labels.minHeight&&(this.xAxisHeight=i.config.xaxis.labels.minHeight),i.config.xaxis.floating&&(this.xAxisHeight=0);var a=0,s=0;i.config.yaxis.forEach(function(n){a+=n.labels.minWidth,s+=n.labels.maxWidth}),this.yAxisWidth<a&&(this.yAxisWidth=a),this.yAxisWidth>s&&(this.yAxisWidth=s)}}]),v}(),ft=function(){function v(e){X(this,v),this.w=e.w,this.lgCtx=e}return E(v,[{key:"getLegendStyles",value:function(){var e=document.createElement("style");e.setAttribute("type","text/css");var t=document.createTextNode(`	
    	
      .apexcharts-legend {	
        display: flex;	
        overflow: auto;	
        padding: 0 10px;	
      }	
      .apexcharts-legend.apx-legend-position-bottom, .apexcharts-legend.apx-legend-position-top {	
        flex-wrap: wrap	
      }	
      .apexcharts-legend.apx-legend-position-right, .apexcharts-legend.apx-legend-position-left {	
        flex-direction: column;	
        bottom: 0;	
      }	
      .apexcharts-legend.apx-legend-position-bottom.apexcharts-align-left, .apexcharts-legend.apx-legend-position-top.apexcharts-align-left, .apexcharts-legend.apx-legend-position-right, .apexcharts-legend.apx-legend-position-left {	
        justify-content: flex-start;	
      }	
      .apexcharts-legend.apx-legend-position-bottom.apexcharts-align-center, .apexcharts-legend.apx-legend-position-top.apexcharts-align-center {	
        justify-content: center;  	
      }	
      .apexcharts-legend.apx-legend-position-bottom.apexcharts-align-right, .apexcharts-legend.apx-legend-position-top.apexcharts-align-right {	
        justify-content: flex-end;	
      }	
      .apexcharts-legend-series {	
        cursor: pointer;	
        line-height: normal;	
      }	
      .apexcharts-legend.apx-legend-position-bottom .apexcharts-legend-series, .apexcharts-legend.apx-legend-position-top .apexcharts-legend-series{	
        display: flex;	
        align-items: center;	
      }	
      .apexcharts-legend-text {	
        position: relative;	
        font-size: 14px;	
      }	
      .apexcharts-legend-text *, .apexcharts-legend-marker * {	
        pointer-events: none;	
      }	
      .apexcharts-legend-marker {	
        position: relative;	
        display: inline-block;	
        cursor: pointer;	
        margin-right: 3px;	
        border-style: solid;
      }	
      	
      .apexcharts-legend.apexcharts-align-right .apexcharts-legend-series, .apexcharts-legend.apexcharts-align-left .apexcharts-legend-series{	
        display: inline-block;	
      }	
      .apexcharts-legend-series.apexcharts-no-click {	
        cursor: auto;	
      }	
      .apexcharts-legend .apexcharts-hidden-zero-series, .apexcharts-legend .apexcharts-hidden-null-series {	
        display: none !important;	
      }	
      .apexcharts-inactive-legend {	
        opacity: 0.45;	
      }`);return e.appendChild(t),e}},{key:"getLegendBBox",value:function(){var e=this.w.globals.dom.baseEl.querySelector(".apexcharts-legend").getBoundingClientRect(),t=e.width;return{clwh:e.height,clww:t}}},{key:"appendToForeignObject",value:function(){var e=this.w.globals;e.dom.elLegendForeign=document.createElementNS(e.SVGNS,"foreignObject");var t=e.dom.elLegendForeign;t.setAttribute("x",0),t.setAttribute("y",0),t.setAttribute("width",e.svgWidth),t.setAttribute("height",e.svgHeight),e.dom.elLegendWrap.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),t.appendChild(e.dom.elLegendWrap),t.appendChild(this.getLegendStyles()),e.dom.Paper.node.insertBefore(t,e.dom.elGraphical.node)}},{key:"toggleDataSeries",value:function(e,t){var i=this,a=this.w;if(a.globals.axisCharts||a.config.chart.type==="radialBar"){a.globals.resized=!0;var s=null,n=null;a.globals.risingSeries=[],a.globals.axisCharts?(s=a.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(e,"']")),n=parseInt(s.getAttribute("data:realIndex"),10)):(s=a.globals.dom.baseEl.querySelector(".apexcharts-series[rel='".concat(e+1,"']")),n=parseInt(s.getAttribute("rel"),10)-1),t?[{cs:a.globals.collapsedSeries,csi:a.globals.collapsedSeriesIndices},{cs:a.globals.ancillaryCollapsedSeries,csi:a.globals.ancillaryCollapsedSeriesIndices}].forEach(function(c){i.riseCollapsedSeries(c.cs,c.csi,n)}):this.hideSeries({seriesEl:s,realIndex:n})}else{var r=a.globals.dom.Paper.select(" .apexcharts-series[rel='".concat(e+1,"'] path")),o=a.config.chart.type;if(o==="pie"||o==="polarArea"||o==="donut"){var h=a.config.plotOptions.pie.donut.labels;new I(this.lgCtx.ctx).pathMouseDown(r.members[0],null),this.lgCtx.ctx.pie.printDataLabelsInner(r.members[0].node,h)}r.fire("click")}}},{key:"hideSeries",value:function(e){var t=e.seriesEl,i=e.realIndex,a=this.w,s=L.clone(a.config.series);if(a.globals.axisCharts){var n=!1;if(a.config.yaxis[i]&&a.config.yaxis[i].show&&a.config.yaxis[i].showAlways&&(n=!0,a.globals.ancillaryCollapsedSeriesIndices.indexOf(i)<0&&(a.globals.ancillaryCollapsedSeries.push({index:i,data:s[i].data.slice(),type:t.parentNode.className.baseVal.split("-")[1]}),a.globals.ancillaryCollapsedSeriesIndices.push(i))),!n){a.globals.collapsedSeries.push({index:i,data:s[i].data.slice(),type:t.parentNode.className.baseVal.split("-")[1]}),a.globals.collapsedSeriesIndices.push(i);var r=a.globals.risingSeries.indexOf(i);a.globals.risingSeries.splice(r,1)}}else a.globals.collapsedSeries.push({index:i,data:s[i]}),a.globals.collapsedSeriesIndices.push(i);for(var o=t.childNodes,h=0;h<o.length;h++)o[h].classList.contains("apexcharts-series-markers-wrap")&&(o[h].classList.contains("apexcharts-hide")?o[h].classList.remove("apexcharts-hide"):o[h].classList.add("apexcharts-hide"));a.globals.allSeriesCollapsed=a.globals.collapsedSeries.length===a.config.series.length,s=this._getSeriesBasedOnCollapsedState(s),this.lgCtx.ctx.updateHelpers._updateSeries(s,a.config.chart.animations.dynamicAnimation.enabled)}},{key:"riseCollapsedSeries",value:function(e,t,i){var a=this.w,s=L.clone(a.config.series);if(e.length>0){for(var n=0;n<e.length;n++)e[n].index===i&&(a.globals.axisCharts?(s[i].data=e[n].data.slice(),e.splice(n,1),t.splice(n,1),a.globals.risingSeries.push(i)):(s[i]=e[n].data,e.splice(n,1),t.splice(n,1),a.globals.risingSeries.push(i)));s=this._getSeriesBasedOnCollapsedState(s),this.lgCtx.ctx.updateHelpers._updateSeries(s,a.config.chart.animations.dynamicAnimation.enabled)}}},{key:"_getSeriesBasedOnCollapsedState",value:function(e){var t=this.w;return t.globals.axisCharts?e.forEach(function(i,a){t.globals.collapsedSeriesIndices.indexOf(a)>-1&&(e[a].data=[])}):e.forEach(function(i,a){t.globals.collapsedSeriesIndices.indexOf(a)>-1&&(e[a]=0)}),e}}]),v}(),He=function(){function v(e,t){X(this,v),this.ctx=e,this.w=e.w,this.onLegendClick=this.onLegendClick.bind(this),this.onLegendHovered=this.onLegendHovered.bind(this),this.isBarsDistributed=this.w.config.chart.type==="bar"&&this.w.config.plotOptions.bar.distributed&&this.w.config.series.length===1,this.legendHelpers=new ft(this)}return E(v,[{key:"init",value:function(){var e=this.w,t=e.globals,i=e.config;if((i.legend.showForSingleSeries&&t.series.length===1||this.isBarsDistributed||t.series.length>1||!t.axisCharts)&&i.legend.show){for(;t.dom.elLegendWrap.firstChild;)t.dom.elLegendWrap.removeChild(t.dom.elLegendWrap.firstChild);this.drawLegends(),L.isIE11()?document.getElementsByTagName("head")[0].appendChild(this.legendHelpers.getLegendStyles()):this.legendHelpers.appendToForeignObject(),i.legend.position==="bottom"||i.legend.position==="top"?this.legendAlignHorizontal():i.legend.position!=="right"&&i.legend.position!=="left"||this.legendAlignVertical()}}},{key:"drawLegends",value:function(){var e=this,t=this.w,i=t.config.legend.fontFamily,a=t.globals.seriesNames,s=t.globals.colors.slice();if(t.config.chart.type==="heatmap"){var n=t.config.plotOptions.heatmap.colorScale.ranges;a=n.map(function(T){return T.name?T.name:T.from+" - "+T.to}),s=n.map(function(T){return T.color})}else this.isBarsDistributed&&(a=t.globals.labels.slice());t.config.legend.customLegendItems.length&&(a=t.config.legend.customLegendItems);for(var r=t.globals.legendFormatter,o=t.config.legend.inverseOrder,h=o?a.length-1:0;o?h>=0:h<=a.length-1;o?h--:h++){var c=r(a[h],{seriesIndex:h,w:t}),d=!1,g=!1;if(t.globals.collapsedSeries.length>0)for(var f=0;f<t.globals.collapsedSeries.length;f++)t.globals.collapsedSeries[f].index===h&&(d=!0);if(t.globals.ancillaryCollapsedSeriesIndices.length>0)for(var p=0;p<t.globals.ancillaryCollapsedSeriesIndices.length;p++)t.globals.ancillaryCollapsedSeriesIndices[p]===h&&(g=!0);var x=document.createElement("span");x.classList.add("apexcharts-legend-marker");var b=t.config.legend.markers.offsetX,y=t.config.legend.markers.offsetY,k=t.config.legend.markers.height,A=t.config.legend.markers.width,l=t.config.legend.markers.strokeWidth,u=t.config.legend.markers.strokeColor,m=t.config.legend.markers.radius,w=x.style;w.background=s[h],w.color=s[h],w.setProperty("background",s[h],"important"),t.config.legend.markers.fillColors&&t.config.legend.markers.fillColors[h]&&(w.background=t.config.legend.markers.fillColors[h]),t.globals.seriesColors[h]!==void 0&&(w.background=t.globals.seriesColors[h],w.color=t.globals.seriesColors[h]),w.height=Array.isArray(k)?parseFloat(k[h])+"px":parseFloat(k)+"px",w.width=Array.isArray(A)?parseFloat(A[h])+"px":parseFloat(A)+"px",w.left=(Array.isArray(b)?parseFloat(b[h]):parseFloat(b))+"px",w.top=(Array.isArray(y)?parseFloat(y[h]):parseFloat(y))+"px",w.borderWidth=Array.isArray(l)?l[h]:l,w.borderColor=Array.isArray(u)?u[h]:u,w.borderRadius=Array.isArray(m)?parseFloat(m[h])+"px":parseFloat(m)+"px",t.config.legend.markers.customHTML&&(Array.isArray(t.config.legend.markers.customHTML)?t.config.legend.markers.customHTML[h]&&(x.innerHTML=t.config.legend.markers.customHTML[h]()):x.innerHTML=t.config.legend.markers.customHTML()),I.setAttrs(x,{rel:h+1,"data:collapsed":d||g}),(d||g)&&x.classList.add("apexcharts-inactive-legend");var S=document.createElement("div"),P=document.createElement("span");P.classList.add("apexcharts-legend-text"),P.innerHTML=Array.isArray(c)?c.join(" "):c;var C=t.config.legend.labels.useSeriesColors?t.globals.colors[h]:t.config.legend.labels.colors;C||(C=t.config.chart.foreColor),P.style.color=C,P.style.fontSize=parseFloat(t.config.legend.fontSize)+"px",P.style.fontWeight=t.config.legend.fontWeight,P.style.fontFamily=i||t.config.chart.fontFamily,I.setAttrs(P,{rel:h+1,i:h,"data:default-text":encodeURIComponent(c),"data:collapsed":d||g}),S.appendChild(x),S.appendChild(P);var M=new D(this.ctx);t.config.legend.showForZeroSeries||M.getSeriesTotalByIndex(h)===0&&M.seriesHaveSameValues(h)&&!M.isSeriesNull(h)&&t.globals.collapsedSeriesIndices.indexOf(h)===-1&&t.globals.ancillaryCollapsedSeriesIndices.indexOf(h)===-1&&S.classList.add("apexcharts-hidden-zero-series"),t.config.legend.showForNullSeries||M.isSeriesNull(h)&&t.globals.collapsedSeriesIndices.indexOf(h)===-1&&t.globals.ancillaryCollapsedSeriesIndices.indexOf(h)===-1&&S.classList.add("apexcharts-hidden-null-series"),t.globals.dom.elLegendWrap.appendChild(S),t.globals.dom.elLegendWrap.classList.add("apexcharts-align-".concat(t.config.legend.horizontalAlign)),t.globals.dom.elLegendWrap.classList.add("apx-legend-position-"+t.config.legend.position),S.classList.add("apexcharts-legend-series"),S.style.margin="".concat(t.config.legend.itemMargin.vertical,"px ").concat(t.config.legend.itemMargin.horizontal,"px"),t.globals.dom.elLegendWrap.style.width=t.config.legend.width?t.config.legend.width+"px":"",t.globals.dom.elLegendWrap.style.height=t.config.legend.height?t.config.legend.height+"px":"",I.setAttrs(S,{rel:h+1,seriesName:L.escapeString(a[h]),"data:collapsed":d||g}),(d||g)&&S.classList.add("apexcharts-inactive-legend"),t.config.legend.onItemClick.toggleDataSeries||S.classList.add("apexcharts-no-click")}t.globals.dom.elWrap.addEventListener("click",e.onLegendClick,!0),t.config.legend.onItemHover.highlightDataSeries&&t.config.legend.customLegendItems.length===0&&(t.globals.dom.elWrap.addEventListener("mousemove",e.onLegendHovered,!0),t.globals.dom.elWrap.addEventListener("mouseout",e.onLegendHovered,!0))}},{key:"setLegendWrapXY",value:function(e,t){var i=this.w,a=i.globals.dom.baseEl.querySelector(".apexcharts-legend"),s=a.getBoundingClientRect(),n=0,r=0;if(i.config.legend.position==="bottom")r+=i.globals.svgHeight-s.height/2;else if(i.config.legend.position==="top"){var o=new ue(this.ctx),h=o.dimHelpers.getTitleSubtitleCoords("title").height,c=o.dimHelpers.getTitleSubtitleCoords("subtitle").height;r=r+(h>0?h-10:0)+(c>0?c-10:0)}a.style.position="absolute",n=n+e+i.config.legend.offsetX,r=r+t+i.config.legend.offsetY,a.style.left=n+"px",a.style.top=r+"px",i.config.legend.position==="bottom"?(a.style.top="auto",a.style.bottom=5-i.config.legend.offsetY+"px"):i.config.legend.position==="right"&&(a.style.left="auto",a.style.right=25+i.config.legend.offsetX+"px"),["width","height"].forEach(function(d){a.style[d]&&(a.style[d]=parseInt(i.config.legend[d],10)+"px")})}},{key:"legendAlignHorizontal",value:function(){var e=this.w;e.globals.dom.baseEl.querySelector(".apexcharts-legend").style.right=0;var t=this.legendHelpers.getLegendBBox(),i=new ue(this.ctx),a=i.dimHelpers.getTitleSubtitleCoords("title"),s=i.dimHelpers.getTitleSubtitleCoords("subtitle"),n=0;e.config.legend.position==="bottom"?n=-t.clwh/1.8:e.config.legend.position==="top"&&(n=a.height+s.height+e.config.title.margin+e.config.subtitle.margin-10),this.setLegendWrapXY(20,n)}},{key:"legendAlignVertical",value:function(){var e=this.w,t=this.legendHelpers.getLegendBBox(),i=0;e.config.legend.position==="left"&&(i=20),e.config.legend.position==="right"&&(i=e.globals.svgWidth-t.clww-10),this.setLegendWrapXY(i,20)}},{key:"onLegendHovered",value:function(e){var t=this.w,i=e.target.classList.contains("apexcharts-legend-text")||e.target.classList.contains("apexcharts-legend-marker");if(t.config.chart.type==="heatmap"||this.isBarsDistributed){if(i){var a=parseInt(e.target.getAttribute("rel"),10)-1;this.ctx.events.fireEvent("legendHover",[this.ctx,a,this.w]),new j(this.ctx).highlightRangeInSeries(e,e.target)}}else!e.target.classList.contains("apexcharts-inactive-legend")&&i&&new j(this.ctx).toggleSeriesOnHover(e,e.target)}},{key:"onLegendClick",value:function(e){var t=this.w;if(!t.config.legend.customLegendItems.length&&(e.target.classList.contains("apexcharts-legend-text")||e.target.classList.contains("apexcharts-legend-marker"))){var i=parseInt(e.target.getAttribute("rel"),10)-1,a=e.target.getAttribute("data:collapsed")==="true",s=this.w.config.chart.events.legendClick;typeof s=="function"&&s(this.ctx,i,this.w),this.ctx.events.fireEvent("legendClick",[this.ctx,i,this.w]);var n=this.w.config.legend.markers.onClick;typeof n=="function"&&e.target.classList.contains("apexcharts-legend-marker")&&(n(this.ctx,i,this.w),this.ctx.events.fireEvent("legendMarkerClick",[this.ctx,i,this.w])),t.config.chart.type!=="treemap"&&t.config.chart.type!=="heatmap"&&!this.isBarsDistributed&&t.config.legend.onItemClick.toggleDataSeries&&this.legendHelpers.toggleDataSeries(i,a)}}}]),v}(),De=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w;var t=this.w;this.ev=this.w.config.chart.events,this.selectedClass="apexcharts-selected",this.localeValues=this.w.globals.locale.toolbar,this.minX=t.globals.minX,this.maxX=t.globals.maxX}return E(v,[{key:"createToolbar",value:function(){var e=this,t=this.w,i=function(){return document.createElement("div")},a=i();if(a.setAttribute("class","apexcharts-toolbar"),a.style.top=t.config.chart.toolbar.offsetY+"px",a.style.right=3-t.config.chart.toolbar.offsetX+"px",t.globals.dom.elWrap.appendChild(a),this.elZoom=i(),this.elZoomIn=i(),this.elZoomOut=i(),this.elPan=i(),this.elSelection=i(),this.elZoomReset=i(),this.elMenuIcon=i(),this.elMenu=i(),this.elCustomIcons=[],this.t=t.config.chart.toolbar.tools,Array.isArray(this.t.customIcons))for(var s=0;s<this.t.customIcons.length;s++)this.elCustomIcons.push(i());var n=[],r=function(d,g,f){var p=d.toLowerCase();e.t[p]&&t.config.chart.zoom.enabled&&n.push({el:g,icon:typeof e.t[p]=="string"?e.t[p]:f,title:e.localeValues[d],class:"apexcharts-".concat(p,"-icon")})};r("zoomIn",this.elZoomIn,`<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
    <path d="M0 0h24v24H0z" fill="none"/>
    <path d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
</svg>
`),r("zoomOut",this.elZoomOut,`<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
    <path d="M0 0h24v24H0z" fill="none"/>
    <path d="M7 11v2h10v-2H7zm5-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
</svg>
`);var o=function(d){e.t[d]&&t.config.chart[d].enabled&&n.push({el:d==="zoom"?e.elZoom:e.elSelection,icon:typeof e.t[d]=="string"?e.t[d]:d==="zoom"?`<svg xmlns="http://www.w3.org/2000/svg" fill="#000000" height="24" viewBox="0 0 24 24" width="24">
    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
    <path d="M0 0h24v24H0V0z" fill="none"/>
    <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
</svg>`:`<svg fill="#6E8192" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
    <path d="M0 0h24v24H0z" fill="none"/>
    <path d="M3 5h2V3c-1.1 0-2 .9-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2c0-1.1-.9-2-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2z"/>
</svg>`,title:e.localeValues[d==="zoom"?"selectionZoom":"selection"],class:t.globals.isTouchDevice?"apexcharts-element-hidden":"apexcharts-".concat(d,"-icon")})};o("zoom"),o("selection"),this.t.pan&&t.config.chart.zoom.enabled&&n.push({el:this.elPan,icon:typeof this.t.pan=="string"?this.t.pan:`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" height="24" viewBox="0 0 24 24" width="24">
    <defs>
        <path d="M0 0h24v24H0z" id="a"/>
    </defs>
    <clipPath id="b">
        <use overflow="visible" xlink:href="#a"/>
    </clipPath>
    <path clip-path="url(#b)" d="M23 5.5V20c0 2.2-1.8 4-4 4h-7.3c-1.08 0-2.1-.43-2.85-1.19L1 14.83s1.26-1.23 1.3-1.25c.22-.19.49-.29.79-.29.22 0 .**********.04.01 4.31 2.46 4.31 2.46V4c0-.83.67-1.5 1.5-1.5S11 3.17 11 4v7h1V1.5c0-.83.67-1.5 1.5-1.5S15 .67 15 1.5V11h1V2.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V11h1V5.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5z"/>
</svg>`,title:this.localeValues.pan,class:t.globals.isTouchDevice?"apexcharts-element-hidden":"apexcharts-pan-icon"}),r("reset",this.elZoomReset,`<svg fill="#000000" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
    <path d="M0 0h24v24H0z" fill="none"/>
</svg>`),this.t.download&&n.push({el:this.elMenuIcon,icon:typeof this.t.download=="string"?this.t.download:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0V0z"/><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>',title:this.localeValues.menu,class:"apexcharts-menu-icon"});for(var h=0;h<this.elCustomIcons.length;h++)n.push({el:this.elCustomIcons[h],icon:this.t.customIcons[h].icon,title:this.t.customIcons[h].title,index:this.t.customIcons[h].index,class:"apexcharts-toolbar-custom-icon "+this.t.customIcons[h].class});n.forEach(function(d,g){d.index&&L.moveIndexInArray(n,g,d.index)});for(var c=0;c<n.length;c++)I.setAttrs(n[c].el,{class:n[c].class,title:n[c].title}),n[c].el.innerHTML=n[c].icon,a.appendChild(n[c].el);this._createHamburgerMenu(a),t.globals.zoomEnabled?this.elZoom.classList.add(this.selectedClass):t.globals.panEnabled?this.elPan.classList.add(this.selectedClass):t.globals.selectionEnabled&&this.elSelection.classList.add(this.selectedClass),this.addToolbarEventListeners()}},{key:"_createHamburgerMenu",value:function(e){this.elMenuItems=[],e.appendChild(this.elMenu),I.setAttrs(this.elMenu,{class:"apexcharts-menu"});var t=[{name:"exportSVG",title:this.localeValues.exportToSVG},{name:"exportPNG",title:this.localeValues.exportToPNG},{name:"exportCSV",title:this.localeValues.exportToCSV}];this.w.globals.allSeriesHasEqualX||t.splice(2,1);for(var i=0;i<t.length;i++)this.elMenuItems.push(document.createElement("div")),this.elMenuItems[i].innerHTML=t[i].title,I.setAttrs(this.elMenuItems[i],{class:"apexcharts-menu-item ".concat(t[i].name),title:t[i].title}),this.elMenu.appendChild(this.elMenuItems[i])}},{key:"addToolbarEventListeners",value:function(){var e=this;this.elZoomReset.addEventListener("click",this.handleZoomReset.bind(this)),this.elSelection.addEventListener("click",this.toggleZoomSelection.bind(this,"selection")),this.elZoom.addEventListener("click",this.toggleZoomSelection.bind(this,"zoom")),this.elZoomIn.addEventListener("click",this.handleZoomIn.bind(this)),this.elZoomOut.addEventListener("click",this.handleZoomOut.bind(this)),this.elPan.addEventListener("click",this.togglePanning.bind(this)),this.elMenuIcon.addEventListener("click",this.toggleMenu.bind(this)),this.elMenuItems.forEach(function(i){i.classList.contains("exportSVG")?i.addEventListener("click",e.handleDownload.bind(e,"svg")):i.classList.contains("exportPNG")?i.addEventListener("click",e.handleDownload.bind(e,"png")):i.classList.contains("exportCSV")&&i.addEventListener("click",e.handleDownload.bind(e,"csv"))});for(var t=0;t<this.t.customIcons.length;t++)this.elCustomIcons[t].addEventListener("click",this.t.customIcons[t].click.bind(this,this.ctx,this.ctx.w))}},{key:"toggleZoomSelection",value:function(e){this.ctx.getSyncedCharts().forEach(function(t){t.ctx.toolbar.toggleOtherControls();var i=e==="selection"?t.ctx.toolbar.elSelection:t.ctx.toolbar.elZoom,a=e==="selection"?"selectionEnabled":"zoomEnabled";t.w.globals[a]=!t.w.globals[a],i.classList.contains(t.ctx.toolbar.selectedClass)?i.classList.remove(t.ctx.toolbar.selectedClass):i.classList.add(t.ctx.toolbar.selectedClass)})}},{key:"getToolbarIconsReference",value:function(){var e=this.w;this.elZoom||(this.elZoom=e.globals.dom.baseEl.querySelector(".apexcharts-zoom-icon")),this.elPan||(this.elPan=e.globals.dom.baseEl.querySelector(".apexcharts-pan-icon")),this.elSelection||(this.elSelection=e.globals.dom.baseEl.querySelector(".apexcharts-selection-icon"))}},{key:"enableZoomPanFromToolbar",value:function(e){this.toggleOtherControls(),e==="pan"?this.w.globals.panEnabled=!0:this.w.globals.zoomEnabled=!0;var t=e==="pan"?this.elPan:this.elZoom,i=e==="pan"?this.elZoom:this.elPan;t&&t.classList.add(this.selectedClass),i&&i.classList.remove(this.selectedClass)}},{key:"togglePanning",value:function(){this.ctx.getSyncedCharts().forEach(function(e){e.ctx.toolbar.toggleOtherControls(),e.w.globals.panEnabled=!e.w.globals.panEnabled,e.ctx.toolbar.elPan.classList.contains(e.ctx.toolbar.selectedClass)?e.ctx.toolbar.elPan.classList.remove(e.ctx.toolbar.selectedClass):e.ctx.toolbar.elPan.classList.add(e.ctx.toolbar.selectedClass)})}},{key:"toggleOtherControls",value:function(){var e=this,t=this.w;t.globals.panEnabled=!1,t.globals.zoomEnabled=!1,t.globals.selectionEnabled=!1,this.getToolbarIconsReference(),[this.elPan,this.elSelection,this.elZoom].forEach(function(i){i&&i.classList.remove(e.selectedClass)})}},{key:"handleZoomIn",value:function(){var e=this.w;e.globals.isTimelineBar&&(this.minX=e.globals.minY,this.maxX=e.globals.maxY);var t=(this.minX+this.maxX)/2,i=(this.minX+t)/2,a=(this.maxX+t)/2,s=this._getNewMinXMaxX(i,a);e.globals.disableZoomIn||this.zoomUpdateOptions(s.minX,s.maxX)}},{key:"handleZoomOut",value:function(){var e=this.w;if(e.globals.isTimelineBar&&(this.minX=e.globals.minY,this.maxX=e.globals.maxY),!(e.config.xaxis.type==="datetime"&&new Date(this.minX).getUTCFullYear()<1e3)){var t=(this.minX+this.maxX)/2,i=this.minX-(t-this.minX),a=this.maxX-(t-this.maxX),s=this._getNewMinXMaxX(i,a);e.globals.disableZoomOut||this.zoomUpdateOptions(s.minX,s.maxX)}}},{key:"_getNewMinXMaxX",value:function(e,t){var i=this.w.config.xaxis.convertedCatToNumeric;return{minX:i?Math.floor(e):e,maxX:i?Math.floor(t):t}}},{key:"zoomUpdateOptions",value:function(e,t){var i=this.w;if(e!==void 0||t!==void 0){if(!(i.config.xaxis.convertedCatToNumeric&&(e<1&&(e=1,t=i.globals.dataPoints),t-e<2))){var a={min:e,max:t},s=this.getBeforeZoomRange(a);s&&(a=s.xaxis);var n={xaxis:a},r=L.clone(i.globals.initialConfig.yaxis);i.config.chart.zoom.autoScaleYaxis&&(r=new ie(this.ctx).autoScaleY(this.ctx,r,{xaxis:a})),i.config.chart.group||(n.yaxis=r),this.w.globals.zoomed=!0,this.ctx.updateHelpers._updateOptions(n,!1,this.w.config.chart.animations.dynamicAnimation.enabled),this.zoomCallback(a,r)}}else this.handleZoomReset()}},{key:"zoomCallback",value:function(e,t){typeof this.ev.zoomed=="function"&&this.ev.zoomed(this.ctx,{xaxis:e,yaxis:t})}},{key:"getBeforeZoomRange",value:function(e,t){var i=null;return typeof this.ev.beforeZoom=="function"&&(i=this.ev.beforeZoom(this,{xaxis:e,yaxis:t})),i}},{key:"toggleMenu",value:function(){var e=this;window.setTimeout(function(){e.elMenu.classList.contains("apexcharts-menu-open")?e.elMenu.classList.remove("apexcharts-menu-open"):e.elMenu.classList.add("apexcharts-menu-open")},0)}},{key:"handleDownload",value:function(e){var t=this.w,i=new ve(this.ctx);switch(e){case"svg":i.exportToSVG(this.ctx);break;case"png":i.exportToPng(this.ctx);break;case"csv":i.exportToCSV({series:t.config.series,columnDelimiter:t.config.chart.toolbar.export.csv.columnDelimiter})}}},{key:"handleZoomReset",value:function(e){this.ctx.getSyncedCharts().forEach(function(t){var i=t.w;if(i.globals.lastXAxis.min=void 0,i.globals.lastXAxis.max=void 0,t.updateHelpers.revertDefaultAxisMinMax(),typeof i.config.chart.events.beforeResetZoom=="function"){var a=i.config.chart.events.beforeResetZoom(t,i);a&&t.updateHelpers.revertDefaultAxisMinMax(a)}typeof i.config.chart.events.zoomed=="function"&&t.ctx.toolbar.zoomCallback({min:i.config.xaxis.min,max:i.config.xaxis.max}),i.globals.zoomed=!1;var s=t.ctx.series.emptyCollapsedSeries(L.clone(i.globals.initialSeries));t.updateHelpers._updateSeries(s,i.config.chart.animations.dynamicAnimation.enabled)})}},{key:"destroy",value:function(){this.elZoom=null,this.elZoomIn=null,this.elZoomOut=null,this.elPan=null,this.elSelection=null,this.elZoomReset=null,this.elMenuIcon=null}}]),v}(),pt=function(v){se(t,De);var e=re(t);function t(i){var a;return X(this,t),(a=e.call(this,i)).ctx=i,a.w=i.w,a.dragged=!1,a.graphics=new I(a.ctx),a.eventList=["mousedown","mouseleave","mousemove","touchstart","touchmove","mouseup","touchend"],a.clientX=0,a.clientY=0,a.startX=0,a.endX=0,a.dragX=0,a.startY=0,a.endY=0,a.dragY=0,a.moveDirection="none",a}return E(t,[{key:"init",value:function(i){var a=this,s=i.xyRatios,n=this.w,r=this;this.xyRatios=s,this.zoomRect=this.graphics.drawRect(0,0,0,0),this.selectionRect=this.graphics.drawRect(0,0,0,0),this.gridRect=n.globals.dom.baseEl.querySelector(".apexcharts-grid"),this.zoomRect.node.classList.add("apexcharts-zoom-rect"),this.selectionRect.node.classList.add("apexcharts-selection-rect"),n.globals.dom.elGraphical.add(this.zoomRect),n.globals.dom.elGraphical.add(this.selectionRect),n.config.chart.selection.type==="x"?this.slDraggableRect=this.selectionRect.draggable({minX:0,minY:0,maxX:n.globals.gridWidth,maxY:n.globals.gridHeight}).on("dragmove",this.selectionDragging.bind(this,"dragging")):n.config.chart.selection.type==="y"?this.slDraggableRect=this.selectionRect.draggable({minX:0,maxX:n.globals.gridWidth}).on("dragmove",this.selectionDragging.bind(this,"dragging")):this.slDraggableRect=this.selectionRect.draggable().on("dragmove",this.selectionDragging.bind(this,"dragging")),this.preselectedSelection(),this.hoverArea=n.globals.dom.baseEl.querySelector("".concat(n.globals.chartClass," .apexcharts-svg")),this.hoverArea.classList.add("apexcharts-zoomable"),this.eventList.forEach(function(o){a.hoverArea.addEventListener(o,r.svgMouseEvents.bind(r,s),{capture:!1,passive:!0})})}},{key:"destroy",value:function(){this.slDraggableRect&&(this.slDraggableRect.draggable(!1),this.slDraggableRect.off(),this.selectionRect.off()),this.selectionRect=null,this.zoomRect=null,this.gridRect=null}},{key:"svgMouseEvents",value:function(i,a){var s=this.w,n=this,r=this.ctx.toolbar,o=s.globals.zoomEnabled?s.config.chart.zoom.type:s.config.chart.selection.type,h=s.config.chart.toolbar.autoSelected;a.shiftKey?(this.shiftWasPressed=!0,r.enableZoomPanFromToolbar(h==="pan"?"zoom":"pan")):this.shiftWasPressed&&(r.enableZoomPanFromToolbar(h),this.shiftWasPressed=!1);var c=a.target.classList;if(!(c.contains("apexcharts-selection-rect")||c.contains("apexcharts-legend-marker")||c.contains("apexcharts-legend-text")||a.target.parentNode.classList.contains("apexcharts-toolbar"))){if(n.clientX=a.type==="touchmove"||a.type==="touchstart"?a.touches[0].clientX:a.type==="touchend"?a.changedTouches[0].clientX:a.clientX,n.clientY=a.type==="touchmove"||a.type==="touchstart"?a.touches[0].clientY:a.type==="touchend"?a.changedTouches[0].clientY:a.clientY,a.type==="mousedown"&&a.which===1){var d=n.gridRect.getBoundingClientRect();n.startX=n.clientX-d.left,n.startY=n.clientY-d.top,n.dragged=!1,n.w.globals.mousedown=!0}if((a.type==="mousemove"&&a.which===1||a.type==="touchmove")&&(n.dragged=!0,s.globals.panEnabled?(s.globals.selection=null,n.w.globals.mousedown&&n.panDragging({context:n,zoomtype:o,xyRatios:i})):(n.w.globals.mousedown&&s.globals.zoomEnabled||n.w.globals.mousedown&&s.globals.selectionEnabled)&&(n.selection=n.selectionDrawing({context:n,zoomtype:o}))),a.type==="mouseup"||a.type==="touchend"||a.type==="mouseleave"){var g=n.gridRect.getBoundingClientRect();n.w.globals.mousedown&&(n.endX=n.clientX-g.left,n.endY=n.clientY-g.top,n.dragX=Math.abs(n.endX-n.startX),n.dragY=Math.abs(n.endY-n.startY),(s.globals.zoomEnabled||s.globals.selectionEnabled)&&n.selectionDrawn({context:n,zoomtype:o}),s.globals.panEnabled&&s.config.xaxis.convertedCatToNumeric&&n.delayedPanScrolled()),s.globals.zoomEnabled&&n.hideSelectionRect(this.selectionRect),n.dragged=!1,n.w.globals.mousedown=!1}this.makeSelectionRectDraggable()}}},{key:"makeSelectionRectDraggable",value:function(){var i=this.w;if(this.selectionRect){var a=this.selectionRect.node.getBoundingClientRect();a.width>0&&a.height>0&&this.slDraggableRect.selectize({points:"l, r",pointSize:8,pointType:"rect"}).resize({constraint:{minX:0,minY:0,maxX:i.globals.gridWidth,maxY:i.globals.gridHeight}}).on("resizing",this.selectionDragging.bind(this,"resizing"))}}},{key:"preselectedSelection",value:function(){var i=this.w,a=this.xyRatios;if(!i.globals.zoomEnabled){if(i.globals.selection!==void 0&&i.globals.selection!==null)this.drawSelectionRect(i.globals.selection);else if(i.config.chart.selection.xaxis.min!==void 0&&i.config.chart.selection.xaxis.max!==void 0){var s=(i.config.chart.selection.xaxis.min-i.globals.minX)/a.xRatio,n={x:s,y:0,width:i.globals.gridWidth-(i.globals.maxX-i.config.chart.selection.xaxis.max)/a.xRatio-s,height:i.globals.gridHeight,translateX:0,translateY:0,selectionEnabled:!0};this.drawSelectionRect(n),this.makeSelectionRectDraggable(),typeof i.config.chart.events.selection=="function"&&i.config.chart.events.selection(this.ctx,{xaxis:{min:i.config.chart.selection.xaxis.min,max:i.config.chart.selection.xaxis.max},yaxis:{}})}}}},{key:"drawSelectionRect",value:function(i){var a=i.x,s=i.y,n=i.width,r=i.height,o=i.translateX,h=o===void 0?0:o,c=i.translateY,d=c===void 0?0:c,g=this.w,f=this.zoomRect,p=this.selectionRect;if(this.dragged||g.globals.selection!==null){var x={transform:"translate("+h+", "+d+")"};g.globals.zoomEnabled&&this.dragged&&(n<0&&(n=1),f.attr({x:a,y:s,width:n,height:r,fill:g.config.chart.zoom.zoomedArea.fill.color,"fill-opacity":g.config.chart.zoom.zoomedArea.fill.opacity,stroke:g.config.chart.zoom.zoomedArea.stroke.color,"stroke-width":g.config.chart.zoom.zoomedArea.stroke.width,"stroke-opacity":g.config.chart.zoom.zoomedArea.stroke.opacity}),I.setAttrs(f.node,x)),g.globals.selectionEnabled&&(p.attr({x:a,y:s,width:n>0?n:0,height:r>0?r:0,fill:g.config.chart.selection.fill.color,"fill-opacity":g.config.chart.selection.fill.opacity,stroke:g.config.chart.selection.stroke.color,"stroke-width":g.config.chart.selection.stroke.width,"stroke-dasharray":g.config.chart.selection.stroke.dashArray,"stroke-opacity":g.config.chart.selection.stroke.opacity}),I.setAttrs(p.node,x))}}},{key:"hideSelectionRect",value:function(i){i&&i.attr({x:0,y:0,width:0,height:0})}},{key:"selectionDrawing",value:function(i){var a=i.context,s=i.zoomtype,n=this.w,r=a,o=this.gridRect.getBoundingClientRect(),h=r.startX-1,c=r.startY,d=!1,g=!1,f=r.clientX-o.left-h,p=r.clientY-o.top-c,x={};return Math.abs(f+h)>n.globals.gridWidth?f=n.globals.gridWidth-h:r.clientX-o.left<0&&(f=h),h>r.clientX-o.left&&(d=!0,f=Math.abs(f)),c>r.clientY-o.top&&(g=!0,p=Math.abs(p)),x=s==="x"?{x:d?h-f:h,y:0,width:f,height:n.globals.gridHeight}:s==="y"?{x:0,y:g?c-p:c,width:n.globals.gridWidth,height:p}:{x:d?h-f:h,y:g?c-p:c,width:f,height:p},r.drawSelectionRect(x),r.selectionDragging("resizing"),x}},{key:"selectionDragging",value:function(i,a){var s=this,n=this.w,r=this.xyRatios,o=this.selectionRect,h=0;i==="resizing"&&(h=30);var c=function(g){return parseFloat(o.node.getAttribute(g))},d={x:c("x"),y:c("y"),width:c("width"),height:c("height")};n.globals.selection=d,typeof n.config.chart.events.selection=="function"&&n.globals.selectionEnabled&&(clearTimeout(this.w.globals.selectionResizeTimer),this.w.globals.selectionResizeTimer=window.setTimeout(function(){var g=s.gridRect.getBoundingClientRect(),f=o.node.getBoundingClientRect(),p={xaxis:{min:n.globals.xAxisScale.niceMin+(f.left-g.left)*r.xRatio,max:n.globals.xAxisScale.niceMin+(f.right-g.left)*r.xRatio},yaxis:{min:n.globals.yAxisScale[0].niceMin+(g.bottom-f.bottom)*r.yRatio[0],max:n.globals.yAxisScale[0].niceMax-(f.top-g.top)*r.yRatio[0]}};n.config.chart.events.selection(s.ctx,p),n.config.chart.brush.enabled&&n.config.chart.events.brushScrolled!==void 0&&n.config.chart.events.brushScrolled(s.ctx,p)},h))}},{key:"selectionDrawn",value:function(i){var a=i.context,s=i.zoomtype,n=this.w,r=a,o=this.xyRatios,h=this.ctx.toolbar;if(r.startX>r.endX){var c=r.startX;r.startX=r.endX,r.endX=c}if(r.startY>r.endY){var d=r.startY;r.startY=r.endY,r.endY=d}var g=void 0,f=void 0;n.globals.isTimelineBar?(g=n.globals.yAxisScale[0].niceMin+r.startX*o.invertedYRatio,f=n.globals.yAxisScale[0].niceMin+r.endX*o.invertedYRatio):(g=n.globals.xAxisScale.niceMin+r.startX*o.xRatio,f=n.globals.xAxisScale.niceMin+r.endX*o.xRatio);var p=[],x=[];if(n.config.yaxis.forEach(function(w,S){p.push(n.globals.yAxisScale[S].niceMax-o.yRatio[S]*r.startY),x.push(n.globals.yAxisScale[S].niceMax-o.yRatio[S]*r.endY)}),r.dragged&&(r.dragX>10||r.dragY>10)&&g!==f){if(n.globals.zoomEnabled){var b=L.clone(n.globals.initialConfig.yaxis),y=L.clone(n.globals.initialConfig.xaxis);if(n.globals.zoomed=!0,n.config.xaxis.convertedCatToNumeric&&(g=Math.floor(g),f=Math.floor(f),g<1&&(g=1,f=n.globals.dataPoints),f-g<2&&(f=g+1)),s!=="xy"&&s!=="x"||(y={min:g,max:f}),s!=="xy"&&s!=="y"||b.forEach(function(w,S){b[S].min=x[S],b[S].max=p[S]}),n.config.chart.zoom.autoScaleYaxis){var k=new ie(r.ctx);b=k.autoScaleY(r.ctx,b,{xaxis:y})}if(h){var A=h.getBeforeZoomRange(y,b);A&&(y=A.xaxis?A.xaxis:y,b=A.yaxis?A.yaxis:b)}var l={xaxis:y};n.config.chart.group||(l.yaxis=b),r.ctx.updateHelpers._updateOptions(l,!1,r.w.config.chart.animations.dynamicAnimation.enabled),typeof n.config.chart.events.zoomed=="function"&&h.zoomCallback(y,b)}else if(n.globals.selectionEnabled){var u,m=null;u={min:g,max:f},s!=="xy"&&s!=="y"||(m=L.clone(n.config.yaxis)).forEach(function(w,S){m[S].min=x[S],m[S].max=p[S]}),n.globals.selection=r.selection,typeof n.config.chart.events.selection=="function"&&n.config.chart.events.selection(r.ctx,{xaxis:u,yaxis:m})}}}},{key:"panDragging",value:function(i){var a=i.context,s=this.w,n=a;if(s.globals.lastClientPosition.x!==void 0){var r=s.globals.lastClientPosition.x-n.clientX,o=s.globals.lastClientPosition.y-n.clientY;Math.abs(r)>Math.abs(o)&&r>0?this.moveDirection="left":Math.abs(r)>Math.abs(o)&&r<0?this.moveDirection="right":Math.abs(o)>Math.abs(r)&&o>0?this.moveDirection="up":Math.abs(o)>Math.abs(r)&&o<0&&(this.moveDirection="down")}s.globals.lastClientPosition={x:n.clientX,y:n.clientY};var h=s.globals.isTimelineBar?s.globals.minY:s.globals.minX,c=s.globals.isTimelineBar?s.globals.maxY:s.globals.maxX;s.config.xaxis.convertedCatToNumeric||n.panScrolled(h,c)}},{key:"delayedPanScrolled",value:function(){var i=this.w,a=i.globals.minX,s=i.globals.maxX,n=(i.globals.maxX-i.globals.minX)/2;this.moveDirection==="left"?(a=i.globals.minX+n,s=i.globals.maxX+n):this.moveDirection==="right"&&(a=i.globals.minX-n,s=i.globals.maxX-n),a=Math.floor(a),s=Math.floor(s),this.updateScrolledChart({xaxis:{min:a,max:s}},a,s)}},{key:"panScrolled",value:function(i,a){var s=this.w,n=this.xyRatios,r=L.clone(s.globals.initialConfig.yaxis),o=n.xRatio,h=s.globals.minX,c=s.globals.maxX;s.globals.isTimelineBar&&(o=n.invertedYRatio,h=s.globals.minY,c=s.globals.maxY),this.moveDirection==="left"?(i=h+s.globals.gridWidth/15*o,a=c+s.globals.gridWidth/15*o):this.moveDirection==="right"&&(i=h-s.globals.gridWidth/15*o,a=c-s.globals.gridWidth/15*o),s.globals.isTimelineBar||(i<s.globals.initialMinX||a>s.globals.initialMaxX)&&(i=h,a=c);var d={min:i,max:a};s.config.chart.zoom.autoScaleYaxis&&(r=new ie(this.ctx).autoScaleY(this.ctx,r,{xaxis:d}));var g={xaxis:{min:i,max:a}};s.config.chart.group||(g.yaxis=r),this.updateScrolledChart(g,i,a)}},{key:"updateScrolledChart",value:function(i,a,s){var n=this.w;this.ctx.updateHelpers._updateOptions(i,!1,!1),typeof n.config.chart.events.scrolled=="function"&&n.config.chart.events.scrolled(this.ctx,{xaxis:{min:a,max:s}})}}]),t}(),Ne=function(){function v(e){X(this,v),this.w=e.w,this.ttCtx=e,this.ctx=e.ctx}return E(v,[{key:"getNearestValues",value:function(e){var t=e.hoverArea,i=e.elGrid,a=e.clientX,s=e.clientY,n=this.w,r=n.globals.gridWidth,o=n.globals.gridHeight,h=r/(n.globals.dataPoints-1),c=o/n.globals.dataPoints,d=i.getBoundingClientRect(),g=this.hasBars();!n.globals.comboCharts&&!g||n.config.xaxis.convertedCatToNumeric||(h=r/n.globals.dataPoints);var f=a-d.left-n.globals.barPadForNumericAxis,p=s-d.top;f<0||p<0||f>n.globals.gridWidth||p>n.globals.gridHeight?(t.classList.remove("hovering-zoom"),t.classList.remove("hovering-pan")):n.globals.zoomEnabled?(t.classList.remove("hovering-pan"),t.classList.add("hovering-zoom")):n.globals.panEnabled&&(t.classList.remove("hovering-zoom"),t.classList.add("hovering-pan"));var x=Math.round(f/h),b=Math.floor(p/c);g&&!n.config.xaxis.convertedCatToNumeric&&(x=Math.ceil(f/h),x-=1);for(var y,k=null,A=null,l=[],u=0;u<n.globals.seriesXvalues.length;u++)l.push([n.globals.seriesXvalues[u][0]-1e-6].concat(n.globals.seriesXvalues[u]));return l=l.map(function(m){return m.filter(function(w){return w})}),y=n.globals.seriesYvalues.map(function(m){return m.filter(function(w){return L.isNumber(w)})}),n.globals.isXNumeric&&(k=(A=this.closestInMultiArray(f,p,l,y)).index,x=A.j,k!==null&&(l=n.globals.seriesXvalues[k],x=(A=this.closestInArray(f,l)).index)),n.globals.capturedSeriesIndex=k===null?-1:k,(!x||x<1)&&(x=0),n.globals.isBarHorizontal?n.globals.capturedDataPointIndex=b:n.globals.capturedDataPointIndex=x,{capturedSeries:k,j:n.globals.isBarHorizontal?b:x,hoverX:f,hoverY:p}}},{key:"closestInMultiArray",value:function(e,t,i,a){var s=this.w,n=0,r=null,o=-1;s.globals.series.length>1?n=this.getFirstActiveXArray(i):r=0;var h=a[n][0],c=i[n][0],d=Math.abs(e-c),g=Math.abs(t-h),f=g+d;return a.map(function(p,x){p.map(function(b,y){var k=Math.abs(t-a[x][y]),A=Math.abs(e-i[x][y]),l=A+k;l<f&&(f=l,d=A,g=k,r=x,o=y)})}),{index:r,j:o}}},{key:"getFirstActiveXArray",value:function(e){for(var t=0,i=e.map(function(s,n){return s.length>0?n:-1}),a=0;a<i.length;a++)if(i[a]!==-1){t=i[a];break}return t}},{key:"closestInArray",value:function(e,t){for(var i=t[0],a=null,s=Math.abs(e-i),n=0;n<t.length;n++){var r=Math.abs(e-t[n]);r<s&&(s=r,a=n)}return{index:a}}},{key:"isXoverlap",value:function(e){var t=[],i=this.w.globals.seriesX.filter(function(s){return s[0]!==void 0});if(i.length>0)for(var a=0;a<i.length-1;a++)i[a][e]!==void 0&&i[a+1][e]!==void 0&&i[a][e]!==i[a+1][e]&&t.push("unEqual");return t.length===0}},{key:"isInitialSeriesSameLen",value:function(){for(var e=!0,t=this.w.globals.initialSeries,i=0;i<t.length-1;i++)if(t[i].data.length!==t[i+1].data.length){e=!1;break}return e}},{key:"getBarsHeight",value:function(e){return U(e).reduce(function(t,i){return t+i.getBBox().height},0)}},{key:"getElMarkers",value:function(){return this.w.globals.dom.baseEl.querySelectorAll(" .apexcharts-series-markers")}},{key:"getAllMarkers",value:function(){var e=this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series-markers-wrap");(e=U(e)).sort(function(i,a){return Number(a.getAttribute("data:realIndex"))<Number(i.getAttribute("data:realIndex"))?0:-1});var t=[];return e.forEach(function(i){t.push(i.querySelector(".apexcharts-marker"))}),t}},{key:"hasMarkers",value:function(){return this.getElMarkers().length>0}},{key:"getElBars",value:function(){return this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-bar-series,  .apexcharts-candlestick-series, .apexcharts-boxPlot-series, .apexcharts-rangebar-series")}},{key:"hasBars",value:function(){return this.getElBars().length>0}},{key:"getHoverMarkerSize",value:function(e){var t=this.w,i=t.config.markers.hover.size;return i===void 0&&(i=t.globals.markers.size[e]+t.config.markers.hover.sizeOffset),i}},{key:"toggleAllTooltipSeriesGroups",value:function(e){var t=this.w,i=this.ttCtx;i.allTooltipSeriesGroups.length===0&&(i.allTooltipSeriesGroups=t.globals.dom.baseEl.querySelectorAll(".apexcharts-tooltip-series-group"));for(var a=i.allTooltipSeriesGroups,s=0;s<a.length;s++)e==="enable"?(a[s].classList.add("apexcharts-active"),a[s].style.display=t.config.tooltip.items.display):(a[s].classList.remove("apexcharts-active"),a[s].style.display="none")}}]),v}(),xt=function(){function v(e){X(this,v),this.w=e.w,this.ctx=e.ctx,this.ttCtx=e,this.tooltipUtil=new Ne(e)}return E(v,[{key:"drawSeriesTexts",value:function(e){var t=e.shared,i=t===void 0||t,a=e.ttItems,s=e.i,n=s===void 0?0:s,r=e.j,o=r===void 0?null:r,h=e.y1,c=e.y2,d=e.e,g=this.w;g.config.tooltip.custom!==void 0?this.handleCustomTooltip({i:n,j:o,y1:h,y2:c,w:g}):this.toggleActiveInactiveSeries(i);var f=this.getValuesToPrint({i:n,j:o});this.printLabels({i:n,j:o,values:f,ttItems:a,shared:i,e:d});var p=this.ttCtx.getElTooltip();this.ttCtx.tooltipRect.ttWidth=p.getBoundingClientRect().width,this.ttCtx.tooltipRect.ttHeight=p.getBoundingClientRect().height}},{key:"printLabels",value:function(e){var t,i=this,a=e.i,s=e.j,n=e.values,r=e.ttItems,o=e.shared,h=e.e,c=this.w,d=[],g=function(u){return c.globals.seriesGoals[u]&&c.globals.seriesGoals[u][s]&&Array.isArray(c.globals.seriesGoals[u][s])},f=n.xVal,p=n.zVal,x=n.xAxisTTVal,b="",y=c.globals.colors[a];s!==null&&c.config.plotOptions.bar.distributed&&(y=c.globals.colors[s]);for(var k=function(u,m){var w=i.getFormatters(a);b=i.getSeriesName({fn:w.yLbTitleFormatter,index:a,seriesIndex:a,j:s}),c.config.chart.type==="treemap"&&(b=w.yLbTitleFormatter(String(c.config.series[a].data[s].x),{series:c.globals.series,seriesIndex:a,dataPointIndex:s,w:c}));var S=c.config.tooltip.inverseOrder?m:u;if(c.globals.axisCharts){var P=function(C){return w.yLbFormatter(c.globals.series[C][s],{series:c.globals.series,seriesIndex:C,dataPointIndex:s,w:c})};o?(w=i.getFormatters(S),b=i.getSeriesName({fn:w.yLbTitleFormatter,index:S,seriesIndex:a,j:s}),y=c.globals.colors[S],t=P(S),g(S)&&(d=c.globals.seriesGoals[S][s].map(function(C){return{attrs:C,val:w.yLbFormatter(C.value,{seriesIndex:S,dataPointIndex:s,w:c})}}))):(h&&h.target&&h.target.getAttribute("fill")&&(y=h.target.getAttribute("fill")),t=P(a),g(a)&&Array.isArray(c.globals.seriesGoals[a][s])&&(d=c.globals.seriesGoals[a][s].map(function(C){return{attrs:C,val:w.yLbFormatter(C.value,{seriesIndex:a,dataPointIndex:s,w:c})}})))}s===null&&(t=w.yLbFormatter(c.globals.series[a],F(F({},c),{},{seriesIndex:a,dataPointIndex:a}))),i.DOMHandling({i:a,t:S,j:s,ttItems:r,values:{val:t,goalVals:d,xVal:f,xAxisTTVal:x,zVal:p},seriesName:b,shared:o,pColor:y})},A=0,l=c.globals.series.length-1;A<c.globals.series.length;A++,l--)k(A,l)}},{key:"getFormatters",value:function(e){var t,i=this.w,a=i.globals.yLabelFormatters[e];return i.globals.ttVal!==void 0?Array.isArray(i.globals.ttVal)?(a=i.globals.ttVal[e]&&i.globals.ttVal[e].formatter,t=i.globals.ttVal[e]&&i.globals.ttVal[e].title&&i.globals.ttVal[e].title.formatter):(a=i.globals.ttVal.formatter,typeof i.globals.ttVal.title.formatter=="function"&&(t=i.globals.ttVal.title.formatter)):t=i.config.tooltip.y.title.formatter,typeof a!="function"&&(a=i.globals.yLabelFormatters[0]?i.globals.yLabelFormatters[0]:function(s){return s}),typeof t!="function"&&(t=function(s){return s}),{yLbFormatter:a,yLbTitleFormatter:t}}},{key:"getSeriesName",value:function(e){var t=e.fn,i=e.index,a=e.seriesIndex,s=e.j,n=this.w;return t(String(n.globals.seriesNames[i]),{series:n.globals.series,seriesIndex:a,dataPointIndex:s,w:n})}},{key:"DOMHandling",value:function(e){e.i;var t=e.t,i=e.j,a=e.ttItems,s=e.values,n=e.seriesName,r=e.shared,o=e.pColor,h=this.w,c=this.ttCtx,d=s.val,g=s.goalVals,f=s.xVal,p=s.xAxisTTVal,x=s.zVal,b=null;b=a[t].children,h.config.tooltip.fillSeriesColor&&(a[t].style.backgroundColor=o,b[0].style.display="none"),c.showTooltipTitle&&(c.tooltipTitle===null&&(c.tooltipTitle=h.globals.dom.baseEl.querySelector(".apexcharts-tooltip-title")),c.tooltipTitle.innerHTML=f),c.blxaxisTooltip&&(c.xaxisTooltipText.innerHTML=p!==""?p:f);var y=a[t].querySelector(".apexcharts-tooltip-text-y-label");y&&(y.innerHTML=n||"");var k=a[t].querySelector(".apexcharts-tooltip-text-y-value");k&&(k.innerHTML=d!==void 0?d:""),b[0]&&b[0].classList.contains("apexcharts-tooltip-marker")&&(h.config.tooltip.marker.fillColors&&Array.isArray(h.config.tooltip.marker.fillColors)&&(o=h.config.tooltip.marker.fillColors[t]),b[0].style.backgroundColor=o),h.config.tooltip.marker.show||(b[0].style.display="none");var A=a[t].querySelector(".apexcharts-tooltip-text-goals-label"),l=a[t].querySelector(".apexcharts-tooltip-text-goals-value");if(g.length&&h.globals.seriesGoals[t]){var u=function(){var m="<div >",w="<div>";g.forEach(function(S,P){m+=' <div style="display: flex"><span class="apexcharts-tooltip-marker" style="background-color: '.concat(S.attrs.strokeColor,'; height: 3px; border-radius: 0; top: 5px;"></span> ').concat(S.attrs.name,"</div>"),w+="<div>".concat(S.val,"</div>")}),A.innerHTML=m+"</div>",l.innerHTML=w+"</div>"};r?h.globals.seriesGoals[t][i]&&Array.isArray(h.globals.seriesGoals[t][i])?u():(A.innerHTML="",l.innerHTML=""):u()}else A.innerHTML="",l.innerHTML="";x!==null&&(a[t].querySelector(".apexcharts-tooltip-text-z-label").innerHTML=h.config.tooltip.z.title,a[t].querySelector(".apexcharts-tooltip-text-z-value").innerHTML=x!==void 0?x:""),r&&b[0]&&(d==null||h.globals.collapsedSeriesIndices.indexOf(t)>-1?b[0].parentNode.style.display="none":b[0].parentNode.style.display=h.config.tooltip.items.display)}},{key:"toggleActiveInactiveSeries",value:function(e){var t=this.w;if(e)this.tooltipUtil.toggleAllTooltipSeriesGroups("enable");else{this.tooltipUtil.toggleAllTooltipSeriesGroups("disable");var i=t.globals.dom.baseEl.querySelector(".apexcharts-tooltip-series-group");i&&(i.classList.add("apexcharts-active"),i.style.display=t.config.tooltip.items.display)}}},{key:"getValuesToPrint",value:function(e){var t=e.i,i=e.j,a=this.w,s=this.ctx.series.filteredSeriesX(),n="",r="",o=null,h=null,c={series:a.globals.series,seriesIndex:t,dataPointIndex:i,w:a},d=a.globals.ttZFormatter;i===null?h=a.globals.series[t]:a.globals.isXNumeric&&a.config.chart.type!=="treemap"?(n=s[t][i],s[t].length===0&&(n=s[this.tooltipUtil.getFirstActiveXArray(s)][i])):n=a.globals.labels[i]!==void 0?a.globals.labels[i]:"";var g=n;return a.globals.isXNumeric&&a.config.xaxis.type==="datetime"?n=new ge(this.ctx).xLabelFormat(a.globals.ttKeyFormatter,g,g,{i:void 0,dateFormatter:new W(this.ctx).formatDate,w:this.w}):n=a.globals.isBarHorizontal?a.globals.yLabelFormatters[0](g,c):a.globals.xLabelFormatter(g,c),a.config.tooltip.x.formatter!==void 0&&(n=a.globals.ttKeyFormatter(g,c)),a.globals.seriesZ.length>0&&a.globals.seriesZ[t].length>0&&(o=d(a.globals.seriesZ[t][i],a)),r=typeof a.config.xaxis.tooltip.formatter=="function"?a.globals.xaxisTooltipFormatter(g,c):n,{val:Array.isArray(h)?h.join(" "):h,xVal:Array.isArray(n)?n.join(" "):n,xAxisTTVal:Array.isArray(r)?r.join(" "):r,zVal:o}}},{key:"handleCustomTooltip",value:function(e){var t=e.i,i=e.j,a=e.y1,s=e.y2,n=e.w,r=this.ttCtx.getElTooltip(),o=n.config.tooltip.custom;Array.isArray(o)&&o[t]&&(o=o[t]),r.innerHTML=o({ctx:this.ctx,series:n.globals.series,seriesIndex:t,dataPointIndex:i,y1:a,y2:s,w:n})}}]),v}(),Oe=function(){function v(e){X(this,v),this.ttCtx=e,this.ctx=e.ctx,this.w=e.w}return E(v,[{key:"moveXCrosshairs",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,i=this.ttCtx,a=this.w,s=i.getElXCrosshairs(),n=e-i.xcrosshairsWidth/2,r=a.globals.labels.slice().length;if(t!==null&&(n=a.globals.gridWidth/r*t),s!==null&&(s.setAttribute("x",n),s.setAttribute("x1",n),s.setAttribute("x2",n),s.setAttribute("y2",a.globals.gridHeight),s.classList.add("apexcharts-active")),n<0&&(n=0),n>a.globals.gridWidth&&(n=a.globals.gridWidth),i.blxaxisTooltip){var o=n;a.config.xaxis.crosshairs.width!=="tickWidth"&&a.config.xaxis.crosshairs.width!=="barWidth"||(o=n+i.xcrosshairsWidth/2),this.moveXAxisTooltip(o)}}},{key:"moveYCrosshairs",value:function(e){var t=this.ttCtx;t.ycrosshairs!==null&&I.setAttrs(t.ycrosshairs,{y1:e,y2:e}),t.ycrosshairsHidden!==null&&I.setAttrs(t.ycrosshairsHidden,{y1:e,y2:e})}},{key:"moveXAxisTooltip",value:function(e){var t=this.w,i=this.ttCtx;if(i.xaxisTooltip!==null){i.xaxisTooltip.classList.add("apexcharts-active");var a=i.xaxisOffY+t.config.xaxis.tooltip.offsetY+t.globals.translateY+1+t.config.xaxis.offsetY;if(e-=i.xaxisTooltip.getBoundingClientRect().width/2,!isNaN(e)){e+=t.globals.translateX;var s;s=new I(this.ctx).getTextRects(i.xaxisTooltipText.innerHTML),i.xaxisTooltipText.style.minWidth=s.width+"px",i.xaxisTooltip.style.left=e+"px",i.xaxisTooltip.style.top=a+"px"}}}},{key:"moveYAxisTooltip",value:function(e){var t=this.w,i=this.ttCtx;i.yaxisTTEls===null&&(i.yaxisTTEls=t.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxistooltip"));var a=parseInt(i.ycrosshairsHidden.getAttribute("y1"),10),s=t.globals.translateY+a,n=i.yaxisTTEls[e].getBoundingClientRect().height,r=t.globals.translateYAxisX[e]-2;t.config.yaxis[e].opposite&&(r-=26),s-=n/2,t.globals.ignoreYAxisIndexes.indexOf(e)===-1?(i.yaxisTTEls[e].classList.add("apexcharts-active"),i.yaxisTTEls[e].style.top=s+"px",i.yaxisTTEls[e].style.left=r+t.config.yaxis[e].tooltip.offsetX+"px"):i.yaxisTTEls[e].classList.remove("apexcharts-active")}},{key:"moveTooltip",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=this.w,s=this.ttCtx,n=s.getElTooltip(),r=s.tooltipRect,o=i!==null?parseFloat(i):1,h=parseFloat(e)+o+5,c=parseFloat(t)+o/2;if(h>a.globals.gridWidth/2&&(h=h-r.ttWidth-o-15),h>a.globals.gridWidth-r.ttWidth-10&&(h=a.globals.gridWidth-r.ttWidth),h<-20&&(h=-20),a.config.tooltip.followCursor){var d=s.getElGrid(),g=d.getBoundingClientRect();c=s.e.clientY+a.globals.translateY-g.top-r.ttHeight/2}else a.globals.isBarHorizontal?c-=r.ttHeight:(r.ttHeight/2+c>a.globals.gridHeight&&(c=a.globals.gridHeight-r.ttHeight+a.globals.translateY),c<0&&(c=0));isNaN(h)||(h+=a.globals.translateX,n.style.left=h+"px",n.style.top=c+"px")}},{key:"moveMarkers",value:function(e,t){var i=this.w,a=this.ttCtx;if(i.globals.markers.size[e]>0)for(var s=i.globals.dom.baseEl.querySelectorAll(" .apexcharts-series[data\\:realIndex='".concat(e,"'] .apexcharts-marker")),n=0;n<s.length;n++)parseInt(s[n].getAttribute("rel"),10)===t&&(a.marker.resetPointsSize(),a.marker.enlargeCurrentPoint(t,s[n]));else a.marker.resetPointsSize(),this.moveDynamicPointOnHover(t,e)}},{key:"moveDynamicPointOnHover",value:function(e,t){var i,a,s=this.w,n=this.ttCtx,r=s.globals.pointsArray,o=n.tooltipUtil.getHoverMarkerSize(t),h=s.config.series[t].type;if(!h||h!=="column"&&h!=="candlestick"&&h!=="boxPlot"){i=r[t][e][0],a=r[t][e][1]?r[t][e][1]:0;var c=s.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(t,"'] .apexcharts-series-markers circle"));c&&a<s.globals.gridHeight&&a>0&&(c.setAttribute("r",o),c.setAttribute("cx",i),c.setAttribute("cy",a)),this.moveXCrosshairs(i),n.fixedTooltip||this.moveTooltip(i,a,o)}}},{key:"moveDynamicPointsOnHover",value:function(e){var t,i=this.ttCtx,a=i.w,s=0,n=0,r=a.globals.pointsArray;t=new j(this.ctx).getActiveConfigSeriesIndex(!0);var o=i.tooltipUtil.getHoverMarkerSize(t);r[t]&&(s=r[t][e][0],n=r[t][e][1]);var h=i.tooltipUtil.getAllMarkers();if(h!==null)for(var c=0;c<a.globals.series.length;c++){var d=r[c];if(a.globals.comboCharts&&d===void 0&&h.splice(c,0,null),d&&d.length){var g=r[c][e][1];h[c].setAttribute("cx",s),g!==null&&!isNaN(g)&&g<a.globals.gridHeight&&g>0?(h[c]&&h[c].setAttribute("r",o),h[c]&&h[c].setAttribute("cy",g)):h[c]&&h[c].setAttribute("r",0)}}if(this.moveXCrosshairs(s),!i.fixedTooltip){var f=n||a.globals.gridHeight;this.moveTooltip(s,f,o)}}},{key:"moveStickyTooltipOverBars",value:function(e){var t=this.w,i=this.ttCtx,a=t.globals.columnSeries?t.globals.columnSeries.length:t.globals.series.length,s=a>=2&&a%2==0?Math.floor(a/2):Math.floor(a/2)+1;t.globals.isBarHorizontal&&(s=new j(this.ctx).getActiveConfigSeriesIndex(!1,"desc")+1);var n=t.globals.dom.baseEl.querySelector(".apexcharts-bar-series .apexcharts-series[rel='".concat(s,"'] path[j='").concat(e,"'], .apexcharts-candlestick-series .apexcharts-series[rel='").concat(s,"'] path[j='").concat(e,"'], .apexcharts-boxPlot-series .apexcharts-series[rel='").concat(s,"'] path[j='").concat(e,"'], .apexcharts-rangebar-series .apexcharts-series[rel='").concat(s,"'] path[j='").concat(e,"']")),r=n?parseFloat(n.getAttribute("cx")):0,o=n?parseFloat(n.getAttribute("cy")):0,h=n?parseFloat(n.getAttribute("barWidth")):0,c=n?parseFloat(n.getAttribute("barHeight")):0,d=i.getElGrid().getBoundingClientRect();if(t.globals.isXNumeric?(r-=a%2!=0?h/2:0,n&&(n.classList.contains("apexcharts-candlestick-area")||n.classList.contains("apexcharts-boxPlot-area"))&&t.globals.comboCharts&&(r-=h/2)):t.globals.isBarHorizontal||(r=i.xAxisTicksPositions[e-1]+i.dataPointsDividedWidth/2,isNaN(r)&&(r=i.xAxisTicksPositions[e]-i.dataPointsDividedWidth/2)),t.globals.isBarHorizontal?o+=c/3:o=i.e.clientY-d.top-i.tooltipRect.ttHeight/2,t.globals.isBarHorizontal||this.moveXCrosshairs(r),!i.fixedTooltip){var g=o||t.globals.gridHeight;this.moveTooltip(r,g)}}}]),v}(),bt=function(){function v(e){X(this,v),this.w=e.w,this.ttCtx=e,this.ctx=e.ctx,this.tooltipPosition=new Oe(e)}return E(v,[{key:"drawDynamicPoints",value:function(){var e=this.w,t=new I(this.ctx),i=new ne(this.ctx),a=e.globals.dom.baseEl.querySelectorAll(".apexcharts-series");a=U(a),e.config.chart.stacked&&a.sort(function(d,g){return parseFloat(d.getAttribute("data:realIndex"))-parseFloat(g.getAttribute("data:realIndex"))});for(var s=0;s<a.length;s++){var n=a[s].querySelector(".apexcharts-series-markers-wrap");if(n!==null){var r=void 0,o="apexcharts-marker w".concat((Math.random()+1).toString(36).substring(4));e.config.chart.type!=="line"&&e.config.chart.type!=="area"||e.globals.comboCharts||e.config.tooltip.intersect||(o+=" no-pointer-events");var h=i.getMarkerConfig({cssClass:o,seriesIndex:s});(r=t.drawMarker(0,0,h)).node.setAttribute("default-marker-size",0);var c=document.createElementNS(e.globals.SVGNS,"g");c.classList.add("apexcharts-series-markers"),c.appendChild(r.node),n.appendChild(c)}}}},{key:"enlargeCurrentPoint",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,s=this.w;s.config.chart.type!=="bubble"&&this.newPointSize(e,t);var n=t.getAttribute("cx"),r=t.getAttribute("cy");if(i!==null&&a!==null&&(n=i,r=a),this.tooltipPosition.moveXCrosshairs(n),!this.fixedTooltip){if(s.config.chart.type==="radar"){var o=this.ttCtx.getElGrid(),h=o.getBoundingClientRect();n=this.ttCtx.e.clientX-h.left}this.tooltipPosition.moveTooltip(n,r,s.config.markers.hover.size)}}},{key:"enlargePoints",value:function(e){for(var t=this.w,i=this,a=this.ttCtx,s=e,n=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series:not(.apexcharts-series-collapsed) .apexcharts-marker"),r=t.config.markers.hover.size,o=0;o<n.length;o++){var h=n[o].getAttribute("rel"),c=n[o].getAttribute("index");if(r===void 0&&(r=t.globals.markers.size[c]+t.config.markers.hover.sizeOffset),s===parseInt(h,10)){i.newPointSize(s,n[o]);var d=n[o].getAttribute("cx"),g=n[o].getAttribute("cy");i.tooltipPosition.moveXCrosshairs(d),a.fixedTooltip||i.tooltipPosition.moveTooltip(d,g,r)}else i.oldPointSize(n[o])}}},{key:"newPointSize",value:function(e,t){var i=this.w,a=i.config.markers.hover.size,s=e===0?t.parentNode.firstChild:t.parentNode.lastChild;if(s.getAttribute("default-marker-size")!=="0"){var n=parseInt(s.getAttribute("index"),10);a===void 0&&(a=i.globals.markers.size[n]+i.config.markers.hover.sizeOffset),a<0&&(a=0),s.setAttribute("r",a)}}},{key:"oldPointSize",value:function(e){var t=parseFloat(e.getAttribute("default-marker-size"));e.setAttribute("r",t)}},{key:"resetPointsSize",value:function(){for(var e=this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series:not(.apexcharts-series-collapsed) .apexcharts-marker"),t=0;t<e.length;t++){var i=parseFloat(e[t].getAttribute("default-marker-size"));L.isNumber(i)&&i>=0?e[t].setAttribute("r",i):e[t].setAttribute("r",0)}}}]),v}(),mt=function(){function v(e){X(this,v),this.w=e.w,this.ttCtx=e}return E(v,[{key:"getAttr",value:function(e,t){return parseFloat(e.target.getAttribute(t))}},{key:"handleHeatTreeTooltip",value:function(e){var t=e.e,i=e.opt,a=e.x,s=e.y,n=e.type,r=this.ttCtx,o=this.w;if(t.target.classList.contains("apexcharts-".concat(n,"-rect"))){var h=this.getAttr(t,"i"),c=this.getAttr(t,"j"),d=this.getAttr(t,"cx"),g=this.getAttr(t,"cy"),f=this.getAttr(t,"width"),p=this.getAttr(t,"height");if(r.tooltipLabels.drawSeriesTexts({ttItems:i.ttItems,i:h,j:c,shared:!1,e:t}),o.globals.capturedSeriesIndex=h,o.globals.capturedDataPointIndex=c,a=d+r.tooltipRect.ttWidth/2+f,s=g+r.tooltipRect.ttHeight/2-p/2,r.tooltipPosition.moveXCrosshairs(d+f/2),a>o.globals.gridWidth/2&&(a=d-r.tooltipRect.ttWidth/2+f),r.w.config.tooltip.followCursor){var x=o.globals.dom.elWrap.getBoundingClientRect();a=o.globals.clientX-x.left-r.tooltipRect.ttWidth/2,s=o.globals.clientY-x.top-r.tooltipRect.ttHeight-5}}return{x:a,y:s}}},{key:"handleMarkerTooltip",value:function(e){var t,i,a=e.e,s=e.opt,n=e.x,r=e.y,o=this.w,h=this.ttCtx;if(a.target.classList.contains("apexcharts-marker")){var c=parseInt(s.paths.getAttribute("cx"),10),d=parseInt(s.paths.getAttribute("cy"),10),g=parseFloat(s.paths.getAttribute("val"));if(i=parseInt(s.paths.getAttribute("rel"),10),t=parseInt(s.paths.parentNode.parentNode.parentNode.getAttribute("rel"),10)-1,h.intersect){var f=L.findAncestor(s.paths,"apexcharts-series");f&&(t=parseInt(f.getAttribute("data:realIndex"),10))}if(h.tooltipLabels.drawSeriesTexts({ttItems:s.ttItems,i:t,j:i,shared:!h.showOnIntersect&&o.config.tooltip.shared,e:a}),a.type==="mouseup"&&h.markerClick(a,t,i),o.globals.capturedSeriesIndex=t,o.globals.capturedDataPointIndex=i,n=c,r=d+o.globals.translateY-1.4*h.tooltipRect.ttHeight,h.w.config.tooltip.followCursor){var p=h.getElGrid().getBoundingClientRect();r=h.e.clientY+o.globals.translateY-p.top}g<0&&(r=d),h.marker.enlargeCurrentPoint(i,s.paths,n,r)}return{x:n,y:r}}},{key:"handleBarTooltip",value:function(e){var t,i,a=e.e,s=e.opt,n=this.w,r=this.ttCtx,o=r.getElTooltip(),h=0,c=0,d=0,g=this.getBarTooltipXY({e:a,opt:s});t=g.i;var f=g.barHeight,p=g.j;n.globals.capturedSeriesIndex=t,n.globals.capturedDataPointIndex=p,n.globals.isBarHorizontal&&r.tooltipUtil.hasBars()||!n.config.tooltip.shared?(c=g.x,d=g.y,i=Array.isArray(n.config.stroke.width)?n.config.stroke.width[t]:n.config.stroke.width,h=c):n.globals.comboCharts||n.config.tooltip.shared||(h/=2),isNaN(d)?d=n.globals.svgHeight-r.tooltipRect.ttHeight:d<0&&(d=0);var x=parseInt(s.paths.parentNode.getAttribute("data:realIndex"),10),b=n.globals.isMultipleYAxis?n.config.yaxis[x]&&n.config.yaxis[x].reversed:n.config.yaxis[0].reversed;if(c+r.tooltipRect.ttWidth>n.globals.gridWidth&&!b?c-=r.tooltipRect.ttWidth:c<0&&(c=0),r.w.config.tooltip.followCursor){var y=r.getElGrid().getBoundingClientRect();d=r.e.clientY-y.top}r.tooltip===null&&(r.tooltip=n.globals.dom.baseEl.querySelector(".apexcharts-tooltip")),n.config.tooltip.shared||(n.globals.comboBarCount>0?r.tooltipPosition.moveXCrosshairs(h+i/2):r.tooltipPosition.moveXCrosshairs(h)),!r.fixedTooltip&&(!n.config.tooltip.shared||n.globals.isBarHorizontal&&r.tooltipUtil.hasBars())&&(b&&(c-=r.tooltipRect.ttWidth)<0&&(c=0),o.style.left=c+n.globals.translateX+"px",!b||n.globals.isBarHorizontal&&r.tooltipUtil.hasBars()||(d=d+f-2*(n.globals.series[t][p]<0?f:0)),r.tooltipRect.ttHeight+d>n.globals.gridHeight?(d=n.globals.gridHeight-r.tooltipRect.ttHeight+n.globals.translateY,o.style.top=d+"px"):o.style.top=d+n.globals.translateY-r.tooltipRect.ttHeight/2+"px")}},{key:"getBarTooltipXY",value:function(e){var t=e.e,i=e.opt,a=this.w,s=null,n=this.ttCtx,r=0,o=0,h=0,c=0,d=0,g=t.target.classList;if(g.contains("apexcharts-bar-area")||g.contains("apexcharts-candlestick-area")||g.contains("apexcharts-boxPlot-area")||g.contains("apexcharts-rangebar-area")){var f=t.target,p=f.getBoundingClientRect(),x=i.elGrid.getBoundingClientRect(),b=p.height;d=p.height;var y=p.width,k=parseInt(f.getAttribute("cx"),10),A=parseInt(f.getAttribute("cy"),10);c=parseFloat(f.getAttribute("barWidth"));var l=t.type==="touchmove"?t.touches[0].clientX:t.clientX;s=parseInt(f.getAttribute("j"),10),r=parseInt(f.parentNode.getAttribute("rel"),10)-1;var u=f.getAttribute("data-range-y1"),m=f.getAttribute("data-range-y2");a.globals.comboCharts&&(r=parseInt(f.parentNode.getAttribute("data:realIndex"),10)),n.tooltipLabels.drawSeriesTexts({ttItems:i.ttItems,i:r,j:s,y1:u?parseInt(u,10):null,y2:m?parseInt(m,10):null,shared:!n.showOnIntersect&&a.config.tooltip.shared,e:t}),a.config.tooltip.followCursor?a.globals.isBarHorizontal?(o=l-x.left+15,h=A-n.dataPointsDividedHeight+b/2-n.tooltipRect.ttHeight/2):(o=a.globals.isXNumeric?k-y/2:k-n.dataPointsDividedWidth+y/2,h=t.clientY-x.top-n.tooltipRect.ttHeight/2-15):a.globals.isBarHorizontal?((o=k)<n.xyRatios.baseLineInvertedY&&(o=k-n.tooltipRect.ttWidth),h=A-n.dataPointsDividedHeight+b/2-n.tooltipRect.ttHeight/2):(o=a.globals.isXNumeric?k-y/2:k-n.dataPointsDividedWidth+y/2,h=A)}return{x:o,y:h,barHeight:d,barWidth:c,i:r,j:s}}}]),v}(),vt=function(){function v(e){X(this,v),this.w=e.w,this.ttCtx=e}return E(v,[{key:"drawXaxisTooltip",value:function(){var e=this.w,t=this.ttCtx,i=e.config.xaxis.position==="bottom";t.xaxisOffY=i?e.globals.gridHeight+1:-e.globals.xAxisHeight-e.config.xaxis.axisTicks.height+3;var a=i?"apexcharts-xaxistooltip apexcharts-xaxistooltip-bottom":"apexcharts-xaxistooltip apexcharts-xaxistooltip-top",s=e.globals.dom.elWrap;t.blxaxisTooltip&&e.globals.dom.baseEl.querySelector(".apexcharts-xaxistooltip")===null&&(t.xaxisTooltip=document.createElement("div"),t.xaxisTooltip.setAttribute("class",a+" apexcharts-theme-"+e.config.tooltip.theme),s.appendChild(t.xaxisTooltip),t.xaxisTooltipText=document.createElement("div"),t.xaxisTooltipText.classList.add("apexcharts-xaxistooltip-text"),t.xaxisTooltipText.style.fontFamily=e.config.xaxis.tooltip.style.fontFamily||e.config.chart.fontFamily,t.xaxisTooltipText.style.fontSize=e.config.xaxis.tooltip.style.fontSize,t.xaxisTooltip.appendChild(t.xaxisTooltipText))}},{key:"drawYaxisTooltip",value:function(){for(var e=this.w,t=this.ttCtx,i=function(s){var n=e.config.yaxis[s].opposite||e.config.yaxis[s].crosshairs.opposite;t.yaxisOffX=n?e.globals.gridWidth+1:1;var r="apexcharts-yaxistooltip apexcharts-yaxistooltip-".concat(s,n?" apexcharts-yaxistooltip-right":" apexcharts-yaxistooltip-left");e.globals.yAxisSameScaleIndices.map(function(h,c){h.map(function(d,g){g===s&&(r+=e.config.yaxis[g].show?" ":" apexcharts-yaxistooltip-hidden")})});var o=e.globals.dom.elWrap;e.globals.dom.baseEl.querySelector(".apexcharts-yaxistooltip apexcharts-yaxistooltip-".concat(s))===null&&(t.yaxisTooltip=document.createElement("div"),t.yaxisTooltip.setAttribute("class",r+" apexcharts-theme-"+e.config.tooltip.theme),o.appendChild(t.yaxisTooltip),s===0&&(t.yaxisTooltipText=[]),t.yaxisTooltipText[s]=document.createElement("div"),t.yaxisTooltipText[s].classList.add("apexcharts-yaxistooltip-text"),t.yaxisTooltip.appendChild(t.yaxisTooltipText[s]))},a=0;a<e.config.yaxis.length;a++)i(a)}},{key:"setXCrosshairWidth",value:function(){var e=this.w,t=this.ttCtx,i=t.getElXCrosshairs();if(t.xcrosshairsWidth=parseInt(e.config.xaxis.crosshairs.width,10),e.globals.comboCharts){var a=e.globals.dom.baseEl.querySelector(".apexcharts-bar-area");if(a!==null&&e.config.xaxis.crosshairs.width==="barWidth"){var s=parseFloat(a.getAttribute("barWidth"));t.xcrosshairsWidth=s}else if(e.config.xaxis.crosshairs.width==="tickWidth"){var n=e.globals.labels.length;t.xcrosshairsWidth=e.globals.gridWidth/n}}else if(e.config.xaxis.crosshairs.width==="tickWidth"){var r=e.globals.labels.length;t.xcrosshairsWidth=e.globals.gridWidth/r}else if(e.config.xaxis.crosshairs.width==="barWidth"){var o=e.globals.dom.baseEl.querySelector(".apexcharts-bar-area");if(o!==null){var h=parseFloat(o.getAttribute("barWidth"));t.xcrosshairsWidth=h}else t.xcrosshairsWidth=1}e.globals.isBarHorizontal&&(t.xcrosshairsWidth=0),i!==null&&t.xcrosshairsWidth>0&&i.setAttribute("width",t.xcrosshairsWidth)}},{key:"handleYCrosshair",value:function(){var e=this.w,t=this.ttCtx;t.ycrosshairs=e.globals.dom.baseEl.querySelector(".apexcharts-ycrosshairs"),t.ycrosshairsHidden=e.globals.dom.baseEl.querySelector(".apexcharts-ycrosshairs-hidden")}},{key:"drawYaxisTooltipText",value:function(e,t,i){var a=this.ttCtx,s=this.w,n=s.globals.yLabelFormatters[e];if(a.yaxisTooltips[e]){var r=a.getElGrid().getBoundingClientRect(),o=(t-r.top)*i.yRatio[e],h=s.globals.maxYArr[e]-s.globals.minYArr[e],c=s.globals.minYArr[e]+(h-o);a.tooltipPosition.moveYCrosshairs(t-r.top),a.yaxisTooltipText[e].innerHTML=n(c),a.tooltipPosition.moveYAxisTooltip(e)}}}]),v}(),yt=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w;var t=this.w;this.tConfig=t.config.tooltip,this.tooltipUtil=new Ne(this),this.tooltipLabels=new xt(this),this.tooltipPosition=new Oe(this),this.marker=new bt(this),this.intersect=new mt(this),this.axesTooltip=new vt(this),this.showOnIntersect=this.tConfig.intersect,this.showTooltipTitle=this.tConfig.x.show,this.fixedTooltip=this.tConfig.fixed.enabled,this.xaxisTooltip=null,this.yaxisTTEls=null,this.isBarShared=!t.globals.isBarHorizontal&&this.tConfig.shared}return E(v,[{key:"getElTooltip",value:function(e){return e||(e=this),e.w.globals.dom.baseEl.querySelector(".apexcharts-tooltip")}},{key:"getElXCrosshairs",value:function(){return this.w.globals.dom.baseEl.querySelector(".apexcharts-xcrosshairs")}},{key:"getElGrid",value:function(){return this.w.globals.dom.baseEl.querySelector(".apexcharts-grid")}},{key:"drawTooltip",value:function(e){var t=this.w;this.xyRatios=e,this.blxaxisTooltip=t.config.xaxis.tooltip.enabled&&t.globals.axisCharts,this.yaxisTooltips=t.config.yaxis.map(function(n,r){return!!(n.show&&n.tooltip.enabled&&t.globals.axisCharts)}),this.allTooltipSeriesGroups=[],t.globals.axisCharts||(this.showTooltipTitle=!1);var i=document.createElement("div");if(i.classList.add("apexcharts-tooltip"),i.classList.add("apexcharts-theme-".concat(this.tConfig.theme)),t.globals.dom.elWrap.appendChild(i),t.globals.axisCharts){this.axesTooltip.drawXaxisTooltip(),this.axesTooltip.drawYaxisTooltip(),this.axesTooltip.setXCrosshairWidth(),this.axesTooltip.handleYCrosshair();var a=new he(this.ctx);this.xAxisTicksPositions=a.getXAxisTicksPositions()}if(!t.globals.comboCharts&&!this.tConfig.intersect&&t.config.chart.type!=="rangeBar"||this.tConfig.shared||(this.showOnIntersect=!0),t.config.markers.size!==0&&t.globals.markers.largestSize!==0||this.marker.drawDynamicPoints(this),t.globals.collapsedSeries.length!==t.globals.series.length){this.dataPointsDividedHeight=t.globals.gridHeight/t.globals.dataPoints,this.dataPointsDividedWidth=t.globals.gridWidth/t.globals.dataPoints,this.showTooltipTitle&&(this.tooltipTitle=document.createElement("div"),this.tooltipTitle.classList.add("apexcharts-tooltip-title"),this.tooltipTitle.style.fontFamily=this.tConfig.style.fontFamily||t.config.chart.fontFamily,this.tooltipTitle.style.fontSize=this.tConfig.style.fontSize,i.appendChild(this.tooltipTitle));var s=t.globals.series.length;(t.globals.xyCharts||t.globals.comboCharts)&&this.tConfig.shared&&(s=this.showOnIntersect?1:t.globals.series.length),this.legendLabels=t.globals.dom.baseEl.querySelectorAll(".apexcharts-legend-text"),this.ttItems=this.createTTElements(s),this.addSVGEvents()}}},{key:"createTTElements",value:function(e){for(var t=this,i=this.w,a=[],s=this.getElTooltip(),n=function(o){var h=document.createElement("div");h.classList.add("apexcharts-tooltip-series-group"),h.style.order=i.config.tooltip.inverseOrder?e-o:o+1,t.tConfig.shared&&t.tConfig.enabledOnSeries&&Array.isArray(t.tConfig.enabledOnSeries)&&t.tConfig.enabledOnSeries.indexOf(o)<0&&h.classList.add("apexcharts-tooltip-series-group-hidden");var c=document.createElement("span");c.classList.add("apexcharts-tooltip-marker"),c.style.backgroundColor=i.globals.colors[o],h.appendChild(c);var d=document.createElement("div");d.classList.add("apexcharts-tooltip-text"),d.style.fontFamily=t.tConfig.style.fontFamily||i.config.chart.fontFamily,d.style.fontSize=t.tConfig.style.fontSize,["y","goals","z"].forEach(function(g){var f=document.createElement("div");f.classList.add("apexcharts-tooltip-".concat(g,"-group"));var p=document.createElement("span");p.classList.add("apexcharts-tooltip-text-".concat(g,"-label")),f.appendChild(p);var x=document.createElement("span");x.classList.add("apexcharts-tooltip-text-".concat(g,"-value")),f.appendChild(x),d.appendChild(f)}),h.appendChild(d),s.appendChild(h),a.push(h)},r=0;r<e;r++)n(r);return a}},{key:"addSVGEvents",value:function(){var e=this.w,t=e.config.chart.type,i=this.getElTooltip(),a=!(t!=="bar"&&t!=="candlestick"&&t!=="boxPlot"&&t!=="rangeBar"),s=t==="area"||t==="line"||t==="scatter"||t==="bubble"||t==="radar",n=e.globals.dom.Paper.node,r=this.getElGrid();r&&(this.seriesBound=r.getBoundingClientRect());var o,h=[],c=[],d={hoverArea:n,elGrid:r,tooltipEl:i,tooltipY:h,tooltipX:c,ttItems:this.ttItems};if(e.globals.axisCharts&&(s?o=e.globals.dom.baseEl.querySelectorAll(".apexcharts-series[data\\:longestSeries='true'] .apexcharts-marker"):a?o=e.globals.dom.baseEl.querySelectorAll(".apexcharts-series .apexcharts-bar-area, .apexcharts-series .apexcharts-candlestick-area, .apexcharts-series .apexcharts-boxPlot-area, .apexcharts-series .apexcharts-rangebar-area"):t!=="heatmap"&&t!=="treemap"||(o=e.globals.dom.baseEl.querySelectorAll(".apexcharts-series .apexcharts-heatmap, .apexcharts-series .apexcharts-treemap")),o&&o.length))for(var g=0;g<o.length;g++)h.push(o[g].getAttribute("cy")),c.push(o[g].getAttribute("cx"));if(e.globals.xyCharts&&!this.showOnIntersect||e.globals.comboCharts&&!this.showOnIntersect||a&&this.tooltipUtil.hasBars()&&this.tConfig.shared)this.addPathsEventListeners([n],d);else if(a&&!e.globals.comboCharts||s&&this.showOnIntersect)this.addDatapointEventsListeners(d);else if(!e.globals.axisCharts||t==="heatmap"||t==="treemap"){var f=e.globals.dom.baseEl.querySelectorAll(".apexcharts-series");this.addPathsEventListeners(f,d)}if(this.showOnIntersect){var p=e.globals.dom.baseEl.querySelectorAll(".apexcharts-line-series .apexcharts-marker, .apexcharts-area-series .apexcharts-marker");p.length>0&&this.addPathsEventListeners(p,d),this.tooltipUtil.hasBars()&&!this.tConfig.shared&&this.addDatapointEventsListeners(d)}}},{key:"drawFixedTooltipRect",value:function(){var e=this.w,t=this.getElTooltip(),i=t.getBoundingClientRect(),a=i.width+10,s=i.height+10,n=this.tConfig.fixed.offsetX,r=this.tConfig.fixed.offsetY,o=this.tConfig.fixed.position.toLowerCase();return o.indexOf("right")>-1&&(n=n+e.globals.svgWidth-a+10),o.indexOf("bottom")>-1&&(r=r+e.globals.svgHeight-s-10),t.style.left=n+"px",t.style.top=r+"px",{x:n,y:r,ttWidth:a,ttHeight:s}}},{key:"addDatapointEventsListeners",value:function(e){var t=this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series-markers .apexcharts-marker, .apexcharts-bar-area, .apexcharts-candlestick-area, .apexcharts-boxPlot-area, .apexcharts-rangebar-area");this.addPathsEventListeners(t,e)}},{key:"addPathsEventListeners",value:function(e,t){for(var i=this,a=function(n){var r={paths:e[n],tooltipEl:t.tooltipEl,tooltipY:t.tooltipY,tooltipX:t.tooltipX,elGrid:t.elGrid,hoverArea:t.hoverArea,ttItems:t.ttItems};["mousemove","mouseup","touchmove","mouseout","touchend"].map(function(o){return e[n].addEventListener(o,i.seriesHover.bind(i,r),{capture:!1,passive:!0})})},s=0;s<e.length;s++)a(s)}},{key:"seriesHover",value:function(e,t){var i=this,a=[],s=this.w;s.config.chart.group&&(a=this.ctx.getGroupedCharts()),s.globals.axisCharts&&(s.globals.minX===-1/0&&s.globals.maxX===1/0||s.globals.dataPoints===0)||(a.length?a.forEach(function(n){var r=i.getElTooltip(n),o={paths:e.paths,tooltipEl:r,tooltipY:e.tooltipY,tooltipX:e.tooltipX,elGrid:e.elGrid,hoverArea:e.hoverArea,ttItems:n.w.globals.tooltip.ttItems};n.w.globals.minX===i.w.globals.minX&&n.w.globals.maxX===i.w.globals.maxX&&n.w.globals.tooltip.seriesHoverByContext({chartCtx:n,ttCtx:n.w.globals.tooltip,opt:o,e:t})}):this.seriesHoverByContext({chartCtx:this.ctx,ttCtx:this.w.globals.tooltip,opt:e,e:t}))}},{key:"seriesHoverByContext",value:function(e){var t=e.chartCtx,i=e.ttCtx,a=e.opt,s=e.e,n=t.w,r=this.getElTooltip();i.tooltipRect={x:0,y:0,ttWidth:r.getBoundingClientRect().width,ttHeight:r.getBoundingClientRect().height},i.e=s,!i.tooltipUtil.hasBars()||n.globals.comboCharts||i.isBarShared||this.tConfig.onDatasetHover.highlightDataSeries&&new j(t).toggleSeriesOnHover(s,s.target.parentNode),i.fixedTooltip&&i.drawFixedTooltipRect(),n.globals.axisCharts?i.axisChartsTooltips({e:s,opt:a,tooltipRect:i.tooltipRect}):i.nonAxisChartsTooltips({e:s,opt:a,tooltipRect:i.tooltipRect})}},{key:"axisChartsTooltips",value:function(e){var t,i,a=e.e,s=e.opt,n=this.w,r=s.elGrid.getBoundingClientRect(),o=a.type==="touchmove"?a.touches[0].clientX:a.clientX,h=a.type==="touchmove"?a.touches[0].clientY:a.clientY;if(this.clientY=h,this.clientX=o,n.globals.capturedSeriesIndex=-1,n.globals.capturedDataPointIndex=-1,h<r.top||h>r.top+r.height)this.handleMouseOut(s);else{if(Array.isArray(this.tConfig.enabledOnSeries)&&!n.config.tooltip.shared){var c=parseInt(s.paths.getAttribute("index"),10);if(this.tConfig.enabledOnSeries.indexOf(c)<0)return void this.handleMouseOut(s)}var d=this.getElTooltip(),g=this.getElXCrosshairs(),f=n.globals.xyCharts||n.config.chart.type==="bar"&&!n.globals.isBarHorizontal&&this.tooltipUtil.hasBars()&&this.tConfig.shared||n.globals.comboCharts&&this.tooltipUtil.hasBars();if(a.type==="mousemove"||a.type==="touchmove"||a.type==="mouseup"){g!==null&&g.classList.add("apexcharts-active");var p=this.yaxisTooltips.filter(function(y){return y===!0});if(this.ycrosshairs!==null&&p.length&&this.ycrosshairs.classList.add("apexcharts-active"),f&&!this.showOnIntersect)this.handleStickyTooltip(a,o,h,s);else if(n.config.chart.type==="heatmap"||n.config.chart.type==="treemap"){var x=this.intersect.handleHeatTreeTooltip({e:a,opt:s,x:t,y:i,type:n.config.chart.type});t=x.x,i=x.y,d.style.left=t+"px",d.style.top=i+"px"}else this.tooltipUtil.hasBars()&&this.intersect.handleBarTooltip({e:a,opt:s}),this.tooltipUtil.hasMarkers()&&this.intersect.handleMarkerTooltip({e:a,opt:s,x:t,y:i});if(this.yaxisTooltips.length)for(var b=0;b<n.config.yaxis.length;b++)this.axesTooltip.drawYaxisTooltipText(b,h,this.xyRatios);s.tooltipEl.classList.add("apexcharts-active")}else a.type!=="mouseout"&&a.type!=="touchend"||this.handleMouseOut(s)}}},{key:"nonAxisChartsTooltips",value:function(e){var t=e.e,i=e.opt,a=e.tooltipRect,s=this.w,n=i.paths.getAttribute("rel"),r=this.getElTooltip(),o=s.globals.dom.elWrap.getBoundingClientRect();if(t.type==="mousemove"||t.type==="touchmove"){r.classList.add("apexcharts-active"),this.tooltipLabels.drawSeriesTexts({ttItems:i.ttItems,i:parseInt(n,10)-1,shared:!1});var h=s.globals.clientX-o.left-a.ttWidth/2,c=s.globals.clientY-o.top-a.ttHeight-10;if(r.style.left=h+"px",r.style.top=c+"px",s.config.legend.tooltipHoverFormatter){var d=n-1,g=(0,s.config.legend.tooltipHoverFormatter)(this.legendLabels[d].getAttribute("data:default-text"),{seriesIndex:d,dataPointIndex:d,w:s});this.legendLabels[d].innerHTML=g}}else t.type!=="mouseout"&&t.type!=="touchend"||(r.classList.remove("apexcharts-active"),s.config.legend.tooltipHoverFormatter&&this.legendLabels.forEach(function(f){var p=f.getAttribute("data:default-text");f.innerHTML=decodeURIComponent(p)}))}},{key:"handleStickyTooltip",value:function(e,t,i,a){var s=this.w,n=this.tooltipUtil.getNearestValues({context:this,hoverArea:a.hoverArea,elGrid:a.elGrid,clientX:t,clientY:i}),r=n.j,o=n.capturedSeries;n.hoverX<0||n.hoverX>s.globals.gridWidth?this.handleMouseOut(a):o!==null?this.handleStickyCapturedSeries(e,o,a,r):(this.tooltipUtil.isXoverlap(r)||s.globals.isBarHorizontal)&&this.create(e,this,0,r,a.ttItems)}},{key:"handleStickyCapturedSeries",value:function(e,t,i,a){var s=this.w;s.globals.series[t][a]===null?this.handleMouseOut(i):s.globals.series[t][a]!==void 0?this.tConfig.shared&&this.tooltipUtil.isXoverlap(a)&&this.tooltipUtil.isInitialSeriesSameLen()?this.create(e,this,t,a,i.ttItems):this.create(e,this,t,a,i.ttItems,!1):this.tooltipUtil.isXoverlap(a)&&this.create(e,this,0,a,i.ttItems)}},{key:"deactivateHoverFilter",value:function(){for(var e=this.w,t=new I(this.ctx),i=e.globals.dom.Paper.select(".apexcharts-bar-area"),a=0;a<i.length;a++)t.pathMouseLeave(i[a])}},{key:"handleMouseOut",value:function(e){var t=this.w,i=this.getElXCrosshairs();if(e.tooltipEl.classList.remove("apexcharts-active"),this.deactivateHoverFilter(),t.config.chart.type!=="bubble"&&this.marker.resetPointsSize(),i!==null&&i.classList.remove("apexcharts-active"),this.ycrosshairs!==null&&this.ycrosshairs.classList.remove("apexcharts-active"),this.blxaxisTooltip&&this.xaxisTooltip.classList.remove("apexcharts-active"),this.yaxisTooltips.length){this.yaxisTTEls===null&&(this.yaxisTTEls=t.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxistooltip"));for(var a=0;a<this.yaxisTTEls.length;a++)this.yaxisTTEls[a].classList.remove("apexcharts-active")}t.config.legend.tooltipHoverFormatter&&this.legendLabels.forEach(function(s){var n=s.getAttribute("data:default-text");s.innerHTML=decodeURIComponent(n)})}},{key:"markerClick",value:function(e,t,i){var a=this.w;typeof a.config.chart.events.markerClick=="function"&&a.config.chart.events.markerClick(e,this.ctx,{seriesIndex:t,dataPointIndex:i,w:a}),this.ctx.events.fireEvent("markerClick",[e,this.ctx,{seriesIndex:t,dataPointIndex:i,w:a}])}},{key:"create",value:function(e,t,i,a,s){var n=arguments.length>5&&arguments[5]!==void 0?arguments[5]:null,r=this.w,o=t;e.type==="mouseup"&&this.markerClick(e,i,a),n===null&&(n=this.tConfig.shared);var h=this.tooltipUtil.hasMarkers(),c=this.tooltipUtil.getElBars();if(r.config.legend.tooltipHoverFormatter){var d=r.config.legend.tooltipHoverFormatter,g=Array.from(this.legendLabels);g.forEach(function(u){var m=u.getAttribute("data:default-text");u.innerHTML=decodeURIComponent(m)});for(var f=0;f<g.length;f++){var p=g[f],x=parseInt(p.getAttribute("i"),10),b=decodeURIComponent(p.getAttribute("data:default-text")),y=d(b,{seriesIndex:n?x:i,dataPointIndex:a,w:r});if(n)p.innerHTML=r.globals.collapsedSeriesIndices.indexOf(x)<0?y:b;else if(p.innerHTML=x===i?y:b,i===x)break}}if(n){if(o.tooltipLabels.drawSeriesTexts({ttItems:s,i,j:a,shared:!this.showOnIntersect&&this.tConfig.shared}),h&&(r.globals.markers.largestSize>0?o.marker.enlargePoints(a):o.tooltipPosition.moveDynamicPointsOnHover(a)),this.tooltipUtil.hasBars()&&(this.barSeriesHeight=this.tooltipUtil.getBarsHeight(c),this.barSeriesHeight>0)){var k=new I(this.ctx),A=r.globals.dom.Paper.select(".apexcharts-bar-area[j='".concat(a,"']"));this.deactivateHoverFilter(),this.tooltipPosition.moveStickyTooltipOverBars(a);for(var l=0;l<A.length;l++)k.pathMouseEnter(A[l])}}else o.tooltipLabels.drawSeriesTexts({shared:!1,ttItems:s,i,j:a}),this.tooltipUtil.hasBars()&&o.tooltipPosition.moveStickyTooltipOverBars(a),h&&o.tooltipPosition.moveMarkers(i,a)}}]),v}(),We=function(v){se(t,te);var e=re(t);function t(){return X(this,t),e.apply(this,arguments)}return E(t,[{key:"draw",value:function(i,a){var s=this,n=this.w;this.graphics=new I(this.ctx),this.bar=new te(this.ctx,this.xyRatios);var r=new D(this.ctx,n);i=r.getLogSeries(i),this.yRatio=r.getLogYRatios(this.yRatio),this.barHelpers.initVariables(i),n.config.chart.stackType==="100%"&&(i=n.globals.seriesPercent.slice()),this.series=i,this.totalItems=0,this.prevY=[],this.prevX=[],this.prevYF=[],this.prevXF=[],this.prevYVal=[],this.prevXVal=[],this.xArrj=[],this.xArrjF=[],this.xArrjVal=[],this.yArrj=[],this.yArrjF=[],this.yArrjVal=[];for(var o=0;o<i.length;o++)i[o].length>0&&(this.totalItems+=i[o].length);for(var h=this.graphics.group({class:"apexcharts-bar-series apexcharts-plot-series"}),c=0,d=0,g=function(x,b){var y=void 0,k=void 0,A=void 0,l=void 0,u=[],m=[],w=n.globals.comboCharts?a[x]:x;s.yRatio.length>1&&(s.yaxisIndex=w),s.isReversed=n.config.yaxis[s.yaxisIndex]&&n.config.yaxis[s.yaxisIndex].reversed;var S=s.graphics.group({class:"apexcharts-series",seriesName:L.escapeString(n.globals.seriesNames[w]),rel:x+1,"data:realIndex":w});s.ctx.series.addCollapsedClassToSeries(S,w);var P=s.graphics.group({class:"apexcharts-datalabels","data:realIndex":w}),C=0,M=0,T=s.initialPositions(c,d,y,k,A,l);d=T.y,C=T.barHeight,k=T.yDivision,l=T.zeroW,c=T.x,M=T.barWidth,y=T.xDivision,A=T.zeroH,s.yArrj=[],s.yArrjF=[],s.yArrjVal=[],s.xArrj=[],s.xArrjF=[],s.xArrjVal=[],s.prevY.length===1&&s.prevY[0].every(function(G){return isNaN(G)})&&(s.prevY[0]=s.prevY[0].map(function(G){return A}),s.prevYF[0]=s.prevYF[0].map(function(G){return 0}));for(var z=0;z<n.globals.dataPoints;z++){var Y=s.barHelpers.getStrokeWidth(x,z,w),H={indexes:{i:x,j:z,realIndex:w,bc:b},strokeWidth:Y,x:c,y:d,elSeries:S},R=null;s.isHorizontal?(R=s.drawStackedBarPaths(F(F({},H),{},{zeroW:l,barHeight:C,yDivision:k})),M=s.series[x][z]/s.invertedYRatio):(R=s.drawStackedColumnPaths(F(F({},H),{},{xDivision:y,barWidth:M,zeroH:A})),C=s.series[x][z]/s.yRatio[s.yaxisIndex]),d=R.y,c=R.x,u.push(c),m.push(d);var V=s.barHelpers.getPathFillColor(i,x,z,w);S=s.renderSeries({realIndex:w,pathFill:V,j:z,i:x,pathFrom:R.pathFrom,pathTo:R.pathTo,strokeWidth:Y,elSeries:S,x:c,y:d,series:i,barHeight:C,barWidth:M,elDataLabelsWrap:P,type:"bar",visibleSeries:0})}n.globals.seriesXvalues[w]=u,n.globals.seriesYvalues[w]=m,s.prevY.push(s.yArrj),s.prevYF.push(s.yArrjF),s.prevYVal.push(s.yArrjVal),s.prevX.push(s.xArrj),s.prevXF.push(s.xArrjF),s.prevXVal.push(s.xArrjVal),h.add(S)},f=0,p=0;f<i.length;f++,p++)g(f,p);return h}},{key:"initialPositions",value:function(i,a,s,n,r,o){var h,c,d=this.w;return this.isHorizontal?(h=(h=n=d.globals.gridHeight/d.globals.dataPoints)*parseInt(d.config.plotOptions.bar.barHeight,10)/100,o=this.baseLineInvertedY+d.globals.padHorizontal+(this.isReversed?d.globals.gridWidth:0)-(this.isReversed?2*this.baseLineInvertedY:0),a=(n-h)/2):(c=s=d.globals.gridWidth/d.globals.dataPoints,c=d.globals.isXNumeric&&d.globals.dataPoints>1?(s=d.globals.minXDiff/this.xRatio)*parseInt(this.barOptions.columnWidth,10)/100:c*parseInt(d.config.plotOptions.bar.columnWidth,10)/100,r=this.baseLineY[this.yaxisIndex]+(this.isReversed?d.globals.gridHeight:0)-(this.isReversed?2*this.baseLineY[this.yaxisIndex]:0),i=d.globals.padHorizontal+(s-c)/2),{x:i,y:a,yDivision:n,xDivision:s,barHeight:h,barWidth:c,zeroH:r,zeroW:o}}},{key:"drawStackedBarPaths",value:function(i){for(var a,s=i.indexes,n=i.barHeight,r=i.strokeWidth,o=i.zeroW,h=i.x,c=i.y,d=i.yDivision,g=i.elSeries,f=this.w,p=c,x=s.i,b=s.j,y=0,k=0;k<this.prevXF.length;k++)y+=this.prevXF[k][b];if(x>0){var A=o;this.prevXVal[x-1][b]<0?A=this.series[x][b]>=0?this.prevX[x-1][b]+y-2*(this.isReversed?y:0):this.prevX[x-1][b]:this.prevXVal[x-1][b]>=0&&(A=this.series[x][b]>=0?this.prevX[x-1][b]:this.prevX[x-1][b]-y+2*(this.isReversed?y:0)),a=A}else a=o;h=this.series[x][b]===null?a:a+this.series[x][b]/this.invertedYRatio-2*(this.isReversed?this.series[x][b]/this.invertedYRatio:0);var l=this.barHelpers.getBarpaths({barYPosition:p,barHeight:n,x1:a,x2:h,strokeWidth:r,series:this.series,realIndex:s.realIndex,i:x,j:b,w:f});return this.barHelpers.barBackground({j:b,i:x,y1:p,y2:n,elSeries:g}),c+=d,{pathTo:l.pathTo,pathFrom:l.pathFrom,x:h,y:c}}},{key:"drawStackedColumnPaths",value:function(i){var a=i.indexes,s=i.x,n=i.y,r=i.xDivision,o=i.barWidth,h=i.zeroH;i.strokeWidth;var c=i.elSeries,d=this.w,g=a.i,f=a.j,p=a.bc;if(d.globals.isXNumeric){var x=d.globals.seriesX[g][f];x||(x=0),s=(x-d.globals.minX)/this.xRatio-o/2}for(var b,y=s,k=0,A=0;A<this.prevYF.length;A++)k+=isNaN(this.prevYF[A][f])?0:this.prevYF[A][f];if(g>0&&!d.globals.isXNumeric||g>0&&d.globals.isXNumeric&&d.globals.seriesX[g-1][f]===d.globals.seriesX[g][f]){var l,u,m=Math.min(this.yRatio.length+1,g+1);if(this.prevY[g-1]!==void 0){for(var w=1;w<m;w++)if(!isNaN(this.prevY[g-w][f])){u=this.prevY[g-w][f];break}}for(var S=1;S<m;S++){if(this.prevYVal[g-S][f]<0){l=this.series[g][f]>=0?u-k+2*(this.isReversed?k:0):u;break}if(this.prevYVal[g-S][f]>=0){l=this.series[g][f]>=0?u:u+k-2*(this.isReversed?k:0);break}}l===void 0&&(l=d.globals.gridHeight),b=this.prevYF[0].every(function(C){return C===0})&&this.prevYF.slice(1,g).every(function(C){return C.every(function(M){return isNaN(M)})})?d.globals.gridHeight-h:l}else b=d.globals.gridHeight-h;n=b-this.series[g][f]/this.yRatio[this.yaxisIndex]+2*(this.isReversed?this.series[g][f]/this.yRatio[this.yaxisIndex]:0);var P=this.barHelpers.getColumnPaths({barXPosition:y,barWidth:o,y1:b,y2:n,yRatio:this.yRatio[this.yaxisIndex],strokeWidth:this.strokeWidth,series:this.series,realIndex:a.realIndex,i:g,j:f,w:d});return this.barHelpers.barBackground({bc:p,j:f,i:g,x1:y,x2:o,elSeries:c}),s+=r,{pathTo:P.pathTo,pathFrom:P.pathFrom,x:d.globals.isXNumeric?s-r:s,y:n}}}]),t}(),Ae=function(v){se(t,te);var e=re(t);function t(){return X(this,t),e.apply(this,arguments)}return E(t,[{key:"draw",value:function(i,a){var s=this,n=this.w,r=new I(this.ctx),o=new q(this.ctx);this.candlestickOptions=this.w.config.plotOptions.candlestick,this.boxOptions=this.w.config.plotOptions.boxPlot;var h=new D(this.ctx,n);i=h.getLogSeries(i),this.series=i,this.yRatio=h.getLogYRatios(this.yRatio),this.barHelpers.initVariables(i);for(var c=r.group({class:"apexcharts-".concat(n.config.chart.type,"-series apexcharts-plot-series")}),d=function(f){s.isBoxPlot=n.config.chart.type==="boxPlot"||n.config.series[f].type==="boxPlot";var p,x,b=void 0,y=void 0,k=[],A=[],l=n.globals.comboCharts?a[f]:f,u=r.group({class:"apexcharts-series",seriesName:L.escapeString(n.globals.seriesNames[l]),rel:f+1,"data:realIndex":l});i[f].length>0&&(s.visibleI=s.visibleI+1);var m,w;s.yRatio.length>1&&(s.yaxisIndex=l);var S=s.barHelpers.initialPositions();y=S.y,m=S.barHeight,b=S.x,w=S.barWidth,p=S.xDivision,x=S.zeroH,A.push(b+w/2);for(var P=r.group({class:"apexcharts-datalabels","data:realIndex":l}),C=function(T){var z=s.barHelpers.getStrokeWidth(f,T,l),Y=s.drawBoxPaths({indexes:{i:f,j:T,realIndex:l},x:b,y,xDivision:p,barWidth:w,zeroH:x,strokeWidth:z,elSeries:u});y=Y.y,b=Y.x,T>0&&A.push(b+w/2),k.push(y),Y.pathTo.forEach(function(H,R){var V=!s.isBoxPlot&&s.candlestickOptions.wick.useFillColor?Y.color[R]:n.globals.stroke.colors[f],G=o.fillPath({seriesNumber:l,dataPointIndex:T,color:Y.color[R],value:i[f][T]});s.renderSeries({realIndex:l,pathFill:G,lineFill:V,j:T,i:f,pathFrom:Y.pathFrom,pathTo:H,strokeWidth:z,elSeries:u,x:b,y,series:i,barHeight:m,barWidth:w,elDataLabelsWrap:P,visibleSeries:s.visibleI,type:n.config.chart.type})})},M=0;M<n.globals.dataPoints;M++)C(M);n.globals.seriesXvalues[l]=A,n.globals.seriesYvalues[l]=k,c.add(u)},g=0;g<i.length;g++)d(g);return c}},{key:"drawBoxPaths",value:function(i){var a=i.indexes,s=i.x;i.y;var n=i.xDivision,r=i.barWidth,o=i.zeroH,h=i.strokeWidth,c=this.w,d=new I(this.ctx),g=a.i,f=a.j,p=!0,x=c.config.plotOptions.candlestick.colors.upward,b=c.config.plotOptions.candlestick.colors.downward,y="";this.isBoxPlot&&(y=[this.boxOptions.colors.lower,this.boxOptions.colors.upper]);var k=this.yRatio[this.yaxisIndex],A=a.realIndex,l=this.getOHLCValue(A,f),u=o,m=o;l.o>l.c&&(p=!1);var w=Math.min(l.o,l.c),S=Math.max(l.o,l.c),P=l.m;c.globals.isXNumeric&&(s=(c.globals.seriesX[A][f]-c.globals.minX)/this.xRatio-r/2);var C=s+r*this.visibleI;this.series[g][f]===void 0||this.series[g][f]===null?(w=o,S=o):(w=o-w/k,S=o-S/k,u=o-l.h/k,m=o-l.l/k,P=o-l.m/k);var M=d.move(C,o),T=d.move(C+r/2,w);return c.globals.previousPaths.length>0&&(T=this.getPreviousPath(A,f,!0)),M=this.isBoxPlot?[d.move(C,w)+d.line(C+r/2,w)+d.line(C+r/2,u)+d.line(C+r/4,u)+d.line(C+r-r/4,u)+d.line(C+r/2,u)+d.line(C+r/2,w)+d.line(C+r,w)+d.line(C+r,P)+d.line(C,P)+d.line(C,w+h/2),d.move(C,P)+d.line(C+r,P)+d.line(C+r,S)+d.line(C+r/2,S)+d.line(C+r/2,m)+d.line(C+r-r/4,m)+d.line(C+r/4,m)+d.line(C+r/2,m)+d.line(C+r/2,S)+d.line(C,S)+d.line(C,P)+"z"]:[d.move(C,S)+d.line(C+r/2,S)+d.line(C+r/2,u)+d.line(C+r/2,S)+d.line(C+r,S)+d.line(C+r,w)+d.line(C+r/2,w)+d.line(C+r/2,m)+d.line(C+r/2,w)+d.line(C,w)+d.line(C,S-h/2)],T+=d.move(C,w),c.globals.isXNumeric||(s+=n),{pathTo:M,pathFrom:T,x:s,y:S,barXPosition:C,color:this.isBoxPlot?y:p?[x]:[b]}}},{key:"getOHLCValue",value:function(i,a){var s=this.w;return{o:this.isBoxPlot?s.globals.seriesCandleH[i][a]:s.globals.seriesCandleO[i][a],h:this.isBoxPlot?s.globals.seriesCandleO[i][a]:s.globals.seriesCandleH[i][a],m:s.globals.seriesCandleM[i][a],l:this.isBoxPlot?s.globals.seriesCandleC[i][a]:s.globals.seriesCandleL[i][a],c:this.isBoxPlot?s.globals.seriesCandleL[i][a]:s.globals.seriesCandleC[i][a]}}}]),t}(),Be=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"checkColorRange",value:function(){var e=this.w,t=!1,i=e.config.plotOptions[e.config.chart.type];return i.colorScale.ranges.length>0&&i.colorScale.ranges.map(function(a,s){a.from<=0&&(t=!0)}),t}},{key:"getShadeColor",value:function(e,t,i,a){var s=this.w,n=1,r=s.config.plotOptions[e].shadeIntensity,o=this.determineColor(e,t,i);s.globals.hasNegs||a?n=s.config.plotOptions[e].reverseNegativeShade?o.percent<0?o.percent/100*(1.25*r):(1-o.percent/100)*(1.25*r):o.percent<=0?1-(1+o.percent/100)*r:(1-o.percent/100)*r:(n=1-o.percent/100,e==="treemap"&&(n=(1-o.percent/100)*(1.25*r)));var h=o.color,c=new L;return s.config.plotOptions[e].enableShades&&(h=this.w.config.theme.mode==="dark"?L.hexToRgba(c.shadeColor(-1*n,o.color),s.config.fill.opacity):L.hexToRgba(c.shadeColor(n,o.color),s.config.fill.opacity)),{color:h,colorProps:o}}},{key:"determineColor",value:function(e,t,i){var a=this.w,s=a.globals.series[t][i],n=a.config.plotOptions[e],r=n.colorScale.inverse?i:t;a.config.plotOptions[e].distributed&&(r=i);var o=a.globals.colors[r],h=null,c=Math.min.apply(Math,U(a.globals.series[t])),d=Math.max.apply(Math,U(a.globals.series[t]));n.distributed||e!=="heatmap"||(c=a.globals.minY,d=a.globals.maxY),n.colorScale.min!==void 0&&(c=n.colorScale.min<a.globals.minY?n.colorScale.min:a.globals.minY,d=n.colorScale.max>a.globals.maxY?n.colorScale.max:a.globals.maxY);var g=Math.abs(d)+Math.abs(c),f=100*s/(g===0?g-1e-6:g);return n.colorScale.ranges.length>0&&n.colorScale.ranges.map(function(p,x){if(s>=p.from&&s<=p.to){o=p.color,h=p.foreColor?p.foreColor:null,c=p.from,d=p.to;var b=Math.abs(d)+Math.abs(c);f=100*s/(b===0?b-1e-6:b)}}),{color:o,foreColor:h,percent:f}}},{key:"calculateDataLabels",value:function(e){var t=e.text,i=e.x,a=e.y,s=e.i,n=e.j,r=e.colorProps,o=e.fontSize,h=this.w.config.dataLabels,c=new I(this.ctx),d=new ee(this.ctx),g=null;if(h.enabled){g=c.group({class:"apexcharts-data-labels"});var f=h.offsetX,p=h.offsetY,x=i+f,b=a+parseFloat(h.style.fontSize)/3+p;d.plotDataLabelsText({x,y:b,text:t,i:s,j:n,color:r.foreColor,parent:g,fontSize:o,dataLabelsConfig:h})}return g}},{key:"addListeners",value:function(e){var t=new I(this.ctx);e.node.addEventListener("mouseenter",t.pathMouseEnter.bind(this,e)),e.node.addEventListener("mouseleave",t.pathMouseLeave.bind(this,e)),e.node.addEventListener("mousedown",t.pathMouseDown.bind(this,e))}}]),v}(),wt=function(){function v(e,t){X(this,v),this.ctx=e,this.w=e.w,this.xRatio=t.xRatio,this.yRatio=t.yRatio,this.dynamicAnim=this.w.config.chart.animations.dynamicAnimation,this.helpers=new Be(e),this.rectRadius=this.w.config.plotOptions.heatmap.radius,this.strokeWidth=this.w.config.stroke.show?this.w.config.stroke.width:0}return E(v,[{key:"draw",value:function(e){var t=this.w,i=new I(this.ctx),a=i.group({class:"apexcharts-heatmap"});a.attr("clip-path","url(#gridRectMask".concat(t.globals.cuid,")"));var s=t.globals.gridWidth/t.globals.dataPoints,n=t.globals.gridHeight/t.globals.series.length,r=0,o=!1;this.negRange=this.helpers.checkColorRange();var h=e.slice();t.config.yaxis[0].reversed&&(o=!0,h.reverse());for(var c=o?0:h.length-1;o?c<h.length:c>=0;o?c++:c--){var d=i.group({class:"apexcharts-series apexcharts-heatmap-series",seriesName:L.escapeString(t.globals.seriesNames[c]),rel:c+1,"data:realIndex":c});if(this.ctx.series.addCollapsedClassToSeries(d,c),t.config.chart.dropShadow.enabled){var g=t.config.chart.dropShadow;new O(this.ctx).dropShadow(d,g,c)}for(var f=0,p=t.config.plotOptions.heatmap.shadeIntensity,x=0;x<h[c].length;x++){var b=this.helpers.getShadeColor(t.config.chart.type,c,x,this.negRange),y=b.color,k=b.colorProps;t.config.fill.type==="image"&&(y=new q(this.ctx).fillPath({seriesNumber:c,dataPointIndex:x,opacity:t.globals.hasNegs?k.percent<0?1-(1+k.percent/100):p+k.percent/100:k.percent/100,patternID:L.randomId(),width:t.config.fill.image.width?t.config.fill.image.width:s,height:t.config.fill.image.height?t.config.fill.image.height:n}));var A=this.rectRadius,l=i.drawRect(f,r,s,n,A);if(l.attr({cx:f,cy:r}),l.node.classList.add("apexcharts-heatmap-rect"),d.add(l),l.attr({fill:y,i:c,index:c,j:x,val:h[c][x],"stroke-width":this.strokeWidth,stroke:t.config.plotOptions.heatmap.useFillColorAsStroke?y:t.globals.stroke.colors[0],color:y}),this.helpers.addListeners(l),t.config.chart.animations.enabled&&!t.globals.dataChanged){var u=1;t.globals.resized||(u=t.config.chart.animations.speed),this.animateHeatMap(l,f,r,s,n,u)}if(t.globals.dataChanged){var m=1;if(this.dynamicAnim.enabled&&t.globals.shouldAnimate){m=this.dynamicAnim.speed;var w=t.globals.previousPaths[c]&&t.globals.previousPaths[c][x]&&t.globals.previousPaths[c][x].color;w||(w="rgba(255, 255, 255, 0)"),this.animateHeatColor(l,L.isColorHex(w)?w:L.rgb2hex(w),L.isColorHex(y)?y:L.rgb2hex(y),m)}}var S=(0,t.config.dataLabels.formatter)(t.globals.series[c][x],{value:t.globals.series[c][x],seriesIndex:c,dataPointIndex:x,w:t}),P=this.helpers.calculateDataLabels({text:S,x:f+s/2,y:r+n/2,i:c,j:x,colorProps:k,series:h});P!==null&&d.add(P),f+=s}r+=n,a.add(d)}var C=t.globals.yAxisScale[0].result.slice();t.config.yaxis[0].reversed?C.unshift(""):C.push(""),t.globals.yAxisScale[0].result=C;var M=t.globals.gridHeight/t.globals.series.length;return t.config.yaxis[0].labels.offsetY=-M/2,a}},{key:"animateHeatMap",value:function(e,t,i,a,s,n){var r=new K(this.ctx);r.animateRect(e,{x:t+a/2,y:i+s/2,width:0,height:0},{x:t,y:i,width:a,height:s},n,function(){r.animationCompleted(e)})}},{key:"animateHeatColor",value:function(e,t,i,a){e.attr({fill:t}).animate(a).attr({fill:i})}}]),v}(),Ve=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"drawYAxisTexts",value:function(e,t,i,a){var s=this.w,n=s.config.yaxis[0],r=s.globals.yLabelFormatters[0];return new I(this.ctx).drawText({x:e+n.labels.offsetX,y:t+n.labels.offsetY,text:r(a,i),textAnchor:"middle",fontSize:n.labels.style.fontSize,fontFamily:n.labels.style.fontFamily,foreColor:Array.isArray(n.labels.style.colors)?n.labels.style.colors[i]:n.labels.style.colors})}}]),v}(),Ge=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w;var t=this.w;this.chartType=this.w.config.chart.type,this.initialAnim=this.w.config.chart.animations.enabled,this.dynamicAnim=this.initialAnim&&this.w.config.chart.animations.dynamicAnimation.enabled,this.animBeginArr=[0],this.animDur=0,this.donutDataLabels=this.w.config.plotOptions.pie.donut.labels,this.lineColorArr=t.globals.stroke.colors!==void 0?t.globals.stroke.colors:t.globals.colors,this.defaultSize=Math.min(t.globals.gridWidth,t.globals.gridHeight),this.centerY=this.defaultSize/2,this.centerX=t.globals.gridWidth/2,t.config.chart.type==="radialBar"?this.fullAngle=360:this.fullAngle=Math.abs(t.config.plotOptions.pie.endAngle-t.config.plotOptions.pie.startAngle),this.initialAngle=t.config.plotOptions.pie.startAngle%this.fullAngle,t.globals.radialSize=this.defaultSize/2.05-t.config.stroke.width-(t.config.chart.sparkline.enabled?0:t.config.chart.dropShadow.blur),this.donutSize=t.globals.radialSize*parseInt(t.config.plotOptions.pie.donut.size,10)/100,this.maxY=0,this.sliceLabels=[],this.sliceSizes=[],this.prevSectorAngleArr=[]}return E(v,[{key:"draw",value:function(e){var t=this,i=this.w,a=new I(this.ctx);if(this.ret=a.group({class:"apexcharts-pie"}),i.globals.noData)return this.ret;for(var s=0,n=0;n<e.length;n++)s+=L.negToZero(e[n]);var r=[],o=a.group();s===0&&(s=1e-5),e.forEach(function(w){t.maxY=Math.max(t.maxY,w)}),i.config.yaxis[0].max&&(this.maxY=i.config.yaxis[0].max),i.config.grid.position==="back"&&this.chartType==="polarArea"&&this.drawPolarElements(this.ret);for(var h=0;h<e.length;h++){var c=this.fullAngle*L.negToZero(e[h])/s;r.push(c),this.chartType==="polarArea"?(r[h]=this.fullAngle/e.length,this.sliceSizes.push(i.globals.radialSize*e[h]/this.maxY)):this.sliceSizes.push(i.globals.radialSize)}if(i.globals.dataChanged){for(var d,g=0,f=0;f<i.globals.previousPaths.length;f++)g+=L.negToZero(i.globals.previousPaths[f]);for(var p=0;p<i.globals.previousPaths.length;p++)d=this.fullAngle*L.negToZero(i.globals.previousPaths[p])/g,this.prevSectorAngleArr.push(d)}this.donutSize<0&&(this.donutSize=0);var x=i.config.plotOptions.pie.customScale,b=i.globals.gridWidth/2,y=i.globals.gridHeight/2,k=b-i.globals.gridWidth/2*x,A=y-i.globals.gridHeight/2*x;if(this.chartType==="donut"){var l=a.drawCircle(this.donutSize);l.attr({cx:this.centerX,cy:this.centerY,fill:i.config.plotOptions.pie.donut.background?i.config.plotOptions.pie.donut.background:"transparent"}),o.add(l)}var u=this.drawArcs(r,e);if(this.sliceLabels.forEach(function(w){u.add(w)}),o.attr({transform:"translate(".concat(k,", ").concat(A,") scale(").concat(x,")")}),o.add(u),this.ret.add(o),this.donutDataLabels.show){var m=this.renderInnerDataLabels(this.donutDataLabels,{hollowSize:this.donutSize,centerX:this.centerX,centerY:this.centerY,opacity:this.donutDataLabels.show,translateX:k,translateY:A});this.ret.add(m)}return i.config.grid.position==="front"&&this.chartType==="polarArea"&&this.drawPolarElements(this.ret),this.ret}},{key:"drawArcs",value:function(e,t){var i=this.w,a=new O(this.ctx),s=new I(this.ctx),n=new q(this.ctx),r=s.group({class:"apexcharts-slices"}),o=this.initialAngle,h=this.initialAngle,c=this.initialAngle,d=this.initialAngle;this.strokeWidth=i.config.stroke.show?i.config.stroke.width:0;for(var g=0;g<e.length;g++){var f=s.group({class:"apexcharts-series apexcharts-pie-series",seriesName:L.escapeString(i.globals.seriesNames[g]),rel:g+1,"data:realIndex":g});r.add(f),h=d,c=(o=c)+e[g],d=h+this.prevSectorAngleArr[g];var p=c<o?this.fullAngle+c-o:c-o,x=n.fillPath({seriesNumber:g,size:this.sliceSizes[g],value:t[g]}),b=this.getChangedPath(h,d),y=s.drawPath({d:b,stroke:Array.isArray(this.lineColorArr)?this.lineColorArr[g]:this.lineColorArr,strokeWidth:0,fill:x,fillOpacity:i.config.fill.opacity,classes:"apexcharts-pie-area apexcharts-".concat(this.chartType.toLowerCase(),"-slice-").concat(g)});if(y.attr({index:0,j:g}),a.setSelectionFilter(y,0,g),i.config.chart.dropShadow.enabled){var k=i.config.chart.dropShadow;a.dropShadow(y,k,g)}this.addListeners(y,this.donutDataLabels),I.setAttrs(y.node,{"data:angle":p,"data:startAngle":o,"data:strokeWidth":this.strokeWidth,"data:value":t[g]});var A={x:0,y:0};this.chartType==="pie"||this.chartType==="polarArea"?A=L.polarToCartesian(this.centerX,this.centerY,i.globals.radialSize/1.25+i.config.plotOptions.pie.dataLabels.offset,(o+p/2)%this.fullAngle):this.chartType==="donut"&&(A=L.polarToCartesian(this.centerX,this.centerY,(i.globals.radialSize+this.donutSize)/2+i.config.plotOptions.pie.dataLabels.offset,(o+p/2)%this.fullAngle)),f.add(y);var l=0;if(!this.initialAnim||i.globals.resized||i.globals.dataChanged?this.animBeginArr.push(0):((l=p/this.fullAngle*i.config.chart.animations.speed)===0&&(l=1),this.animDur=l+this.animDur,this.animBeginArr.push(this.animDur)),this.dynamicAnim&&i.globals.dataChanged?this.animatePaths(y,{size:this.sliceSizes[g],endAngle:c,startAngle:o,prevStartAngle:h,prevEndAngle:d,animateStartingPos:!0,i:g,animBeginArr:this.animBeginArr,shouldSetPrevPaths:!0,dur:i.config.chart.animations.dynamicAnimation.speed}):this.animatePaths(y,{size:this.sliceSizes[g],endAngle:c,startAngle:o,i:g,totalItems:e.length-1,animBeginArr:this.animBeginArr,dur:l}),i.config.plotOptions.pie.expandOnClick&&this.chartType!=="polarArea"&&y.click(this.pieClicked.bind(this,g)),i.globals.selectedDataPoints[0]!==void 0&&i.globals.selectedDataPoints[0].indexOf(g)>-1&&this.pieClicked(g),i.config.dataLabels.enabled){var u=A.x,m=A.y,w=100*p/this.fullAngle+"%";if(p!==0&&i.config.plotOptions.pie.dataLabels.minAngleToShowLabel<e[g]){var S=i.config.dataLabels.formatter;S!==void 0&&(w=S(i.globals.seriesPercent[g][0],{seriesIndex:g,w:i}));var P=i.globals.dataLabels.style.colors[g],C=s.group({class:"apexcharts-datalabels"}),M=s.drawText({x:u,y:m,text:w,textAnchor:"middle",fontSize:i.config.dataLabels.style.fontSize,fontFamily:i.config.dataLabels.style.fontFamily,fontWeight:i.config.dataLabels.style.fontWeight,foreColor:P});if(C.add(M),i.config.dataLabels.dropShadow.enabled){var T=i.config.dataLabels.dropShadow;a.dropShadow(M,T)}M.node.classList.add("apexcharts-pie-label"),i.config.chart.animations.animate&&i.globals.resized===!1&&(M.node.classList.add("apexcharts-pie-label-delay"),M.node.style.animationDelay=i.config.chart.animations.speed/940+"s"),this.sliceLabels.push(C)}}}return r}},{key:"addListeners",value:function(e,t){var i=new I(this.ctx);e.node.addEventListener("mouseenter",i.pathMouseEnter.bind(this,e)),e.node.addEventListener("mouseleave",i.pathMouseLeave.bind(this,e)),e.node.addEventListener("mouseleave",this.revertDataLabelsInner.bind(this,e.node,t)),e.node.addEventListener("mousedown",i.pathMouseDown.bind(this,e)),this.donutDataLabels.total.showAlways||(e.node.addEventListener("mouseenter",this.printDataLabelsInner.bind(this,e.node,t)),e.node.addEventListener("mousedown",this.printDataLabelsInner.bind(this,e.node,t)))}},{key:"animatePaths",value:function(e,t){var i=this.w,a=t.endAngle<t.startAngle?this.fullAngle+t.endAngle-t.startAngle:t.endAngle-t.startAngle,s=a,n=t.startAngle,r=t.startAngle;t.prevStartAngle!==void 0&&t.prevEndAngle!==void 0&&(n=t.prevEndAngle,s=t.prevEndAngle<t.prevStartAngle?this.fullAngle+t.prevEndAngle-t.prevStartAngle:t.prevEndAngle-t.prevStartAngle),t.i===i.config.series.length-1&&(a+r>this.fullAngle?t.endAngle=t.endAngle-(a+r):a+r<this.fullAngle&&(t.endAngle=t.endAngle+(this.fullAngle-(a+r)))),a===this.fullAngle&&(a=this.fullAngle-.01),this.animateArc(e,n,r,a,s,t)}},{key:"animateArc",value:function(e,t,i,a,s,n){var r,o=this,h=this.w,c=new K(this.ctx),d=n.size;(isNaN(t)||isNaN(s))&&(t=i,s=a,n.dur=0);var g=a,f=i,p=t<i?this.fullAngle+t-i:t-i;h.globals.dataChanged&&n.shouldSetPrevPaths&&n.prevEndAngle&&(r=o.getPiePath({me:o,startAngle:n.prevStartAngle,angle:n.prevEndAngle<n.prevStartAngle?this.fullAngle+n.prevEndAngle-n.prevStartAngle:n.prevEndAngle-n.prevStartAngle,size:d}),e.attr({d:r})),n.dur!==0?e.animate(n.dur,h.globals.easing,n.animBeginArr[n.i]).afterAll(function(){o.chartType!=="pie"&&o.chartType!=="donut"&&o.chartType!=="polarArea"||this.animate(h.config.chart.animations.dynamicAnimation.speed).attr({"stroke-width":o.strokeWidth}),n.i===h.config.series.length-1&&c.animationCompleted(e)}).during(function(x){g=p+(a-p)*x,n.animateStartingPos&&(g=s+(a-s)*x,f=t-s+(i-(t-s))*x),r=o.getPiePath({me:o,startAngle:f,angle:g,size:d}),e.node.setAttribute("data:pathOrig",r),e.attr({d:r})}):(r=o.getPiePath({me:o,startAngle:f,angle:a,size:d}),n.isTrack||(h.globals.animationEnded=!0),e.node.setAttribute("data:pathOrig",r),e.attr({d:r,"stroke-width":o.strokeWidth}))}},{key:"pieClicked",value:function(e){var t,i=this.w,a=this,s=a.sliceSizes[e]+(i.config.plotOptions.pie.expandOnClick?4:0),n=i.globals.dom.Paper.select(".apexcharts-".concat(a.chartType.toLowerCase(),"-slice-").concat(e)).members[0];if(n.attr("data:pieClicked")!=="true"){var r=i.globals.dom.baseEl.getElementsByClassName("apexcharts-pie-area");Array.prototype.forEach.call(r,function(d){d.setAttribute("data:pieClicked","false");var g=d.getAttribute("data:pathOrig");d.setAttribute("d",g)}),n.attr("data:pieClicked","true");var o=parseInt(n.attr("data:startAngle"),10),h=parseInt(n.attr("data:angle"),10);t=a.getPiePath({me:a,startAngle:o,angle:h,size:s}),h!==360&&n.plot(t)}else{n.attr({"data:pieClicked":"false"}),this.revertDataLabelsInner(n.node,this.donutDataLabels);var c=n.attr("data:pathOrig");n.attr({d:c})}}},{key:"getChangedPath",value:function(e,t){var i="";return this.dynamicAnim&&this.w.globals.dataChanged&&(i=this.getPiePath({me:this,startAngle:e,angle:t-e,size:this.size})),i}},{key:"getPiePath",value:function(e){var t=e.me,i=e.startAngle,a=e.angle,s=e.size,n=i,r=Math.PI*(n-90)/180,o=a+i;Math.ceil(o)>=this.fullAngle+this.w.config.plotOptions.pie.startAngle%this.fullAngle&&(o=this.fullAngle+this.w.config.plotOptions.pie.startAngle%this.fullAngle-.01),Math.ceil(o)>this.fullAngle&&(o-=this.fullAngle);var h=Math.PI*(o-90)/180,c=t.centerX+s*Math.cos(r),d=t.centerY+s*Math.sin(r),g=t.centerX+s*Math.cos(h),f=t.centerY+s*Math.sin(h),p=L.polarToCartesian(t.centerX,t.centerY,t.donutSize,o),x=L.polarToCartesian(t.centerX,t.centerY,t.donutSize,n),b=a>180?1:0,y=["M",c,d,"A",s,s,0,b,1,g,f];return t.chartType==="donut"?[].concat(y,["L",p.x,p.y,"A",t.donutSize,t.donutSize,0,b,0,x.x,x.y,"L",c,d,"z"]).join(" "):t.chartType==="pie"||t.chartType==="polarArea"?[].concat(y,["L",t.centerX,t.centerY,"L",c,d]).join(" "):[].concat(y).join(" ")}},{key:"drawPolarElements",value:function(e){var t=this.w,i=new ie(this.ctx),a=new I(this.ctx),s=new Ve(this.ctx),n=a.group(),r=a.group(),o=i.niceScale(0,Math.ceil(this.maxY),t.config.yaxis[0].tickAmount,0,!0),h=o.result.reverse(),c=o.result.length;this.maxY=o.niceMax;for(var d=t.globals.radialSize,g=d/(c-1),f=0;f<c-1;f++){var p=a.drawCircle(d);if(p.attr({cx:this.centerX,cy:this.centerY,fill:"none","stroke-width":t.config.plotOptions.polarArea.rings.strokeWidth,stroke:t.config.plotOptions.polarArea.rings.strokeColor}),t.config.yaxis[0].show){var x=s.drawYAxisTexts(this.centerX,this.centerY-d+parseInt(t.config.yaxis[0].labels.style.fontSize,10)/2,f,h[f]);r.add(x)}n.add(p),d-=g}this.drawSpokes(e),e.add(n),e.add(r)}},{key:"renderInnerDataLabels",value:function(e,t){var i=this.w,a=new I(this.ctx),s=a.group({class:"apexcharts-datalabels-group",transform:"translate(".concat(t.translateX?t.translateX:0,", ").concat(t.translateY?t.translateY:0,") scale(").concat(i.config.plotOptions.pie.customScale,")")}),n=e.total.show;s.node.style.opacity=t.opacity;var r,o,h=t.centerX,c=t.centerY;r=e.name.color===void 0?i.globals.colors[0]:e.name.color;var d=e.name.fontSize,g=e.name.fontFamily,f=e.value.fontWeight;o=e.value.color===void 0?i.config.chart.foreColor:e.value.color;var p=e.value.formatter,x="",b="";if(n?(r=e.total.color,d=e.total.fontSize,g=e.total.fontFamily,f=e.total.fontWeight,b=e.total.label,x=e.total.formatter(i)):i.globals.series.length===1&&(x=p(i.globals.series[0],i),b=i.globals.seriesNames[0]),b&&(b=e.name.formatter(b,e.total.show,i)),e.name.show){var y=a.drawText({x:h,y:c+parseFloat(e.name.offsetY),text:b,textAnchor:"middle",foreColor:r,fontSize:d,fontWeight:f,fontFamily:g});y.node.classList.add("apexcharts-datalabel-label"),s.add(y)}if(e.value.show){var k=e.name.show?parseFloat(e.value.offsetY)+16:e.value.offsetY,A=a.drawText({x:h,y:c+k,text:x,textAnchor:"middle",foreColor:o,fontWeight:e.value.fontWeight,fontSize:e.value.fontSize,fontFamily:e.value.fontFamily});A.node.classList.add("apexcharts-datalabel-value"),s.add(A)}return s}},{key:"printInnerLabels",value:function(e,t,i,a){var s,n=this.w;a?s=e.name.color===void 0?n.globals.colors[parseInt(a.parentNode.getAttribute("rel"),10)-1]:e.name.color:n.globals.series.length>1&&e.total.show&&(s=e.total.color);var r=n.globals.dom.baseEl.querySelector(".apexcharts-datalabel-label"),o=n.globals.dom.baseEl.querySelector(".apexcharts-datalabel-value");i=(0,e.value.formatter)(i,n),a||typeof e.total.formatter!="function"||(i=e.total.formatter(n));var h=t===e.total.label;t=e.name.formatter(t,h,n),r!==null&&(r.textContent=t),o!==null&&(o.textContent=i),r!==null&&(r.style.fill=s)}},{key:"printDataLabelsInner",value:function(e,t){var i=this.w,a=e.getAttribute("data:value"),s=i.globals.seriesNames[parseInt(e.parentNode.getAttribute("rel"),10)-1];i.globals.series.length>1&&this.printInnerLabels(t,s,a,e);var n=i.globals.dom.baseEl.querySelector(".apexcharts-datalabels-group");n!==null&&(n.style.opacity=1)}},{key:"drawSpokes",value:function(e){var t=this,i=this.w,a=new I(this.ctx),s=i.config.plotOptions.polarArea.spokes;if(s.strokeWidth!==0){for(var n=[],r=360/i.globals.series.length,o=0;o<i.globals.series.length;o++)n.push(L.polarToCartesian(this.centerX,this.centerY,i.globals.radialSize,i.config.plotOptions.pie.startAngle+r*o));n.forEach(function(h,c){var d=a.drawLine(h.x,h.y,t.centerX,t.centerY,Array.isArray(s.connectorColors)?s.connectorColors[c]:s.connectorColors);e.add(d)})}}},{key:"revertDataLabelsInner",value:function(e,t,i){var a=this,s=this.w,n=s.globals.dom.baseEl.querySelector(".apexcharts-datalabels-group"),r=!1,o=s.globals.dom.baseEl.getElementsByClassName("apexcharts-pie-area"),h=function(g){var f=g.makeSliceOut,p=g.printLabel;Array.prototype.forEach.call(o,function(x){x.getAttribute("data:pieClicked")==="true"&&(f&&(r=!0),p&&a.printDataLabelsInner(x,t))})};if(h({makeSliceOut:!0,printLabel:!1}),t.total.show&&s.globals.series.length>1)r&&!t.total.showAlways?h({makeSliceOut:!1,printLabel:!0}):this.printInnerLabels(t,t.total.label,t.total.formatter(s));else if(h({makeSliceOut:!1,printLabel:!0}),!r)if(s.globals.selectedDataPoints.length&&s.globals.series.length>1)if(s.globals.selectedDataPoints[0].length>0){var c=s.globals.selectedDataPoints[0],d=s.globals.dom.baseEl.querySelector(".apexcharts-".concat(this.chartType.toLowerCase(),"-slice-").concat(c));this.printDataLabelsInner(d,t)}else n&&s.globals.selectedDataPoints.length&&s.globals.selectedDataPoints[0].length===0&&(n.style.opacity=0);else n&&s.globals.series.length>1&&(n.style.opacity=0)}}]),v}(),kt=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.chartType=this.w.config.chart.type,this.initialAnim=this.w.config.chart.animations.enabled,this.dynamicAnim=this.initialAnim&&this.w.config.chart.animations.dynamicAnimation.enabled,this.animDur=0;var t=this.w;this.graphics=new I(this.ctx),this.lineColorArr=t.globals.stroke.colors!==void 0?t.globals.stroke.colors:t.globals.colors,this.defaultSize=t.globals.svgHeight<t.globals.svgWidth?t.globals.gridHeight+1.5*t.globals.goldenPadding:t.globals.gridWidth,this.isLog=t.config.yaxis[0].logarithmic,this.coreUtils=new D(this.ctx),this.maxValue=this.isLog?this.coreUtils.getLogVal(t.globals.maxY,0):t.globals.maxY,this.minValue=this.isLog?this.coreUtils.getLogVal(this.w.globals.minY,0):t.globals.minY,this.polygons=t.config.plotOptions.radar.polygons,this.strokeWidth=t.config.stroke.show?t.config.stroke.width:0,this.size=this.defaultSize/2.1-this.strokeWidth-t.config.chart.dropShadow.blur,t.config.xaxis.labels.show&&(this.size=this.size-t.globals.xAxisLabelsWidth/1.75),t.config.plotOptions.radar.size!==void 0&&(this.size=t.config.plotOptions.radar.size),this.dataRadiusOfPercent=[],this.dataRadius=[],this.angleArr=[],this.yaxisLabelsTextsPos=[]}return E(v,[{key:"draw",value:function(e){var t=this,i=this.w,a=new q(this.ctx),s=[],n=new ee(this.ctx);e.length&&(this.dataPointsLen=e[i.globals.maxValsInArrayIndex].length),this.disAngle=2*Math.PI/this.dataPointsLen;var r=i.globals.gridWidth/2,o=i.globals.gridHeight/2,h=r+i.config.plotOptions.radar.offsetX,c=o+i.config.plotOptions.radar.offsetY,d=this.graphics.group({class:"apexcharts-radar-series apexcharts-plot-series",transform:"translate(".concat(h||0,", ").concat(c||0,")")}),g=[],f=null,p=null;if(this.yaxisLabels=this.graphics.group({class:"apexcharts-yaxis"}),e.forEach(function(b,y){var k=b.length===i.globals.dataPoints,A=t.graphics.group().attr({class:"apexcharts-series","data:longestSeries":k,seriesName:L.escapeString(i.globals.seriesNames[y]),rel:y+1,"data:realIndex":y});t.dataRadiusOfPercent[y]=[],t.dataRadius[y]=[],t.angleArr[y]=[],b.forEach(function(z,Y){var H=Math.abs(t.maxValue-t.minValue);z+=Math.abs(t.minValue),t.isLog&&(z=t.coreUtils.getLogVal(z,0)),t.dataRadiusOfPercent[y][Y]=z/H,t.dataRadius[y][Y]=t.dataRadiusOfPercent[y][Y]*t.size,t.angleArr[y][Y]=Y*t.disAngle}),g=t.getDataPointsPos(t.dataRadius[y],t.angleArr[y]);var l=t.createPaths(g,{x:0,y:0});f=t.graphics.group({class:"apexcharts-series-markers-wrap apexcharts-element-hidden"}),p=t.graphics.group({class:"apexcharts-datalabels","data:realIndex":y}),i.globals.delayedElements.push({el:f.node,index:y});var u={i:y,realIndex:y,animationDelay:y,initialSpeed:i.config.chart.animations.speed,dataChangeSpeed:i.config.chart.animations.dynamicAnimation.speed,className:"apexcharts-radar",shouldClipToGrid:!1,bindEventsOnPaths:!1,stroke:i.globals.stroke.colors[y],strokeLineCap:i.config.stroke.lineCap},m=null;i.globals.previousPaths.length>0&&(m=t.getPreviousPath(y));for(var w=0;w<l.linePathsTo.length;w++){var S=t.graphics.renderPaths(F(F({},u),{},{pathFrom:m===null?l.linePathsFrom[w]:m,pathTo:l.linePathsTo[w],strokeWidth:Array.isArray(t.strokeWidth)?t.strokeWidth[y]:t.strokeWidth,fill:"none",drawShadow:!1}));A.add(S);var P=a.fillPath({seriesNumber:y}),C=t.graphics.renderPaths(F(F({},u),{},{pathFrom:m===null?l.areaPathsFrom[w]:m,pathTo:l.areaPathsTo[w],strokeWidth:0,fill:P,drawShadow:!1}));if(i.config.chart.dropShadow.enabled){var M=new O(t.ctx),T=i.config.chart.dropShadow;M.dropShadow(C,Object.assign({},T,{noUserSpaceOnUse:!0}),y)}A.add(C)}b.forEach(function(z,Y){var H=new ne(t.ctx).getMarkerConfig({cssClass:"apexcharts-marker",seriesIndex:y,dataPointIndex:Y}),R=t.graphics.drawMarker(g[Y].x,g[Y].y,H);R.attr("rel",Y),R.attr("j",Y),R.attr("index",y),R.node.setAttribute("default-marker-size",H.pSize);var V=t.graphics.group({class:"apexcharts-series-markers"});V&&V.add(R),f.add(V),A.add(f);var G=i.config.dataLabels;if(G.enabled){var Z=G.formatter(i.globals.series[y][Y],{seriesIndex:y,dataPointIndex:Y,w:i});n.plotDataLabelsText({x:g[Y].x,y:g[Y].y,text:Z,textAnchor:"middle",i:y,j:y,parent:p,offsetCorrection:!1,dataLabelsConfig:F({},G)})}A.add(p)}),s.push(A)}),this.drawPolygons({parent:d}),i.config.xaxis.labels.show){var x=this.drawXAxisTexts();d.add(x)}return s.forEach(function(b){d.add(b)}),d.add(this.yaxisLabels),d}},{key:"drawPolygons",value:function(e){for(var t=this,i=this.w,a=e.parent,s=new Ve(this.ctx),n=i.globals.yAxisScale[0].result.reverse(),r=n.length,o=[],h=this.size/(r-1),c=0;c<r;c++)o[c]=h*c;o.reverse();var d=[],g=[];o.forEach(function(f,p){var x=L.getPolygonPos(f,t.dataPointsLen),b="";x.forEach(function(y,k){if(p===0){var A=t.graphics.drawLine(y.x,y.y,0,0,Array.isArray(t.polygons.connectorColors)?t.polygons.connectorColors[k]:t.polygons.connectorColors);g.push(A)}k===0&&t.yaxisLabelsTextsPos.push({x:y.x,y:y.y}),b+=y.x+","+y.y+" "}),d.push(b)}),d.forEach(function(f,p){var x=t.polygons.strokeColors,b=t.polygons.strokeWidth,y=t.graphics.drawPolygon(f,Array.isArray(x)?x[p]:x,Array.isArray(b)?b[p]:b,i.globals.radarPolygons.fill.colors[p]);a.add(y)}),g.forEach(function(f){a.add(f)}),i.config.yaxis[0].show&&this.yaxisLabelsTextsPos.forEach(function(f,p){var x=s.drawYAxisTexts(f.x,f.y,p,n[p]);t.yaxisLabels.add(x)})}},{key:"drawXAxisTexts",value:function(){var e=this,t=this.w,i=t.config.xaxis.labels,a=this.graphics.group({class:"apexcharts-xaxis"}),s=L.getPolygonPos(this.size,this.dataPointsLen);return t.globals.labels.forEach(function(n,r){var o=t.config.xaxis.labels.formatter,h=new ee(e.ctx);if(s[r]){var c=e.getTextPos(s[r],e.size),d=o(n,{seriesIndex:-1,dataPointIndex:r,w:t});h.plotDataLabelsText({x:c.newX,y:c.newY,text:d,textAnchor:c.textAnchor,i:r,j:r,parent:a,color:Array.isArray(i.style.colors)&&i.style.colors[r]?i.style.colors[r]:"#a8a8a8",dataLabelsConfig:F({textAnchor:c.textAnchor,dropShadow:{enabled:!1}},i),offsetCorrection:!1})}}),a}},{key:"createPaths",value:function(e,t){var i=this,a=[],s=[],n=[],r=[];if(e.length){s=[this.graphics.move(t.x,t.y)],r=[this.graphics.move(t.x,t.y)];var o=this.graphics.move(e[0].x,e[0].y),h=this.graphics.move(e[0].x,e[0].y);e.forEach(function(c,d){o+=i.graphics.line(c.x,c.y),h+=i.graphics.line(c.x,c.y),d===e.length-1&&(o+="Z",h+="Z")}),a.push(o),n.push(h)}return{linePathsFrom:s,linePathsTo:a,areaPathsFrom:r,areaPathsTo:n}}},{key:"getTextPos",value:function(e,t){var i="middle",a=e.x,s=e.y;return Math.abs(e.x)>=10?e.x>0?(i="start",a+=10):e.x<0&&(i="end",a-=10):i="middle",Math.abs(e.y)>=t-10&&(e.y<0?s-=10:e.y>0&&(s+=10)),{textAnchor:i,newX:a,newY:s}}},{key:"getPreviousPath",value:function(e){for(var t=this.w,i=null,a=0;a<t.globals.previousPaths.length;a++){var s=t.globals.previousPaths[a];s.paths.length>0&&parseInt(s.realIndex,10)===parseInt(e,10)&&t.globals.previousPaths[a].paths[0]!==void 0&&(i=t.globals.previousPaths[a].paths[0].d)}return i}},{key:"getDataPointsPos",value:function(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.dataPointsLen;e=e||[],t=t||[];for(var a=[],s=0;s<i;s++){var n={};n.x=e[s]*Math.sin(t[s]),n.y=-e[s]*Math.cos(t[s]),a.push(n)}return a}}]),v}(),At=function(v){se(t,Ge);var e=re(t);function t(i){var a;X(this,t),(a=e.call(this,i)).ctx=i,a.w=i.w,a.animBeginArr=[0],a.animDur=0;var s=a.w;return a.startAngle=s.config.plotOptions.radialBar.startAngle,a.endAngle=s.config.plotOptions.radialBar.endAngle,a.totalAngle=Math.abs(s.config.plotOptions.radialBar.endAngle-s.config.plotOptions.radialBar.startAngle),a.trackStartAngle=s.config.plotOptions.radialBar.track.startAngle,a.trackEndAngle=s.config.plotOptions.radialBar.track.endAngle,a.radialDataLabels=s.config.plotOptions.radialBar.dataLabels,a.trackStartAngle||(a.trackStartAngle=a.startAngle),a.trackEndAngle||(a.trackEndAngle=a.endAngle),a.endAngle===360&&(a.endAngle=359.99),a.margin=parseInt(s.config.plotOptions.radialBar.track.margin,10),a}return E(t,[{key:"draw",value:function(i){var a=this.w,s=new I(this.ctx),n=s.group({class:"apexcharts-radialbar"});if(a.globals.noData)return n;var r=s.group(),o=this.defaultSize/2,h=a.globals.gridWidth/2,c=this.defaultSize/2.05;a.config.chart.sparkline.enabled||(c=c-a.config.stroke.width-a.config.chart.dropShadow.blur);var d=a.globals.fill.colors;if(a.config.plotOptions.radialBar.track.show){var g=this.drawTracks({size:c,centerX:h,centerY:o,colorArr:d,series:i});r.add(g)}var f=this.drawArcs({size:c,centerX:h,centerY:o,colorArr:d,series:i}),p=360;a.config.plotOptions.radialBar.startAngle<0&&(p=this.totalAngle);var x=(360-p)/360;if(a.globals.radialSize=c-c*x,this.radialDataLabels.value.show){var b=Math.max(this.radialDataLabels.value.offsetY,this.radialDataLabels.name.offsetY);a.globals.radialSize+=b*x}return r.add(f.g),a.config.plotOptions.radialBar.hollow.position==="front"&&(f.g.add(f.elHollow),f.dataLabels&&f.g.add(f.dataLabels)),n.add(r),n}},{key:"drawTracks",value:function(i){var a=this.w,s=new I(this.ctx),n=s.group({class:"apexcharts-tracks"}),r=new O(this.ctx),o=new q(this.ctx),h=this.getStrokeWidth(i);i.size=i.size-h/2;for(var c=0;c<i.series.length;c++){var d=s.group({class:"apexcharts-radialbar-track apexcharts-track"});n.add(d),d.attr({rel:c+1}),i.size=i.size-h-this.margin;var g=a.config.plotOptions.radialBar.track,f=o.fillPath({seriesNumber:0,size:i.size,fillColors:Array.isArray(g.background)?g.background[c]:g.background,solid:!0}),p=this.trackStartAngle,x=this.trackEndAngle;Math.abs(x)+Math.abs(p)>=360&&(x=360-Math.abs(this.startAngle)-.1);var b=s.drawPath({d:"",stroke:f,strokeWidth:h*parseInt(g.strokeWidth,10)/100,fill:"none",strokeOpacity:g.opacity,classes:"apexcharts-radialbar-area"});if(g.dropShadow.enabled){var y=g.dropShadow;r.dropShadow(b,y)}d.add(b),b.attr("id","apexcharts-radialbarTrack-"+c),this.animatePaths(b,{centerX:i.centerX,centerY:i.centerY,endAngle:x,startAngle:p,size:i.size,i:c,totalItems:2,animBeginArr:0,dur:0,isTrack:!0,easing:a.globals.easing})}return n}},{key:"drawArcs",value:function(i){var a=this.w,s=new I(this.ctx),n=new q(this.ctx),r=new O(this.ctx),o=s.group(),h=this.getStrokeWidth(i);i.size=i.size-h/2;var c=a.config.plotOptions.radialBar.hollow.background,d=i.size-h*i.series.length-this.margin*i.series.length-h*parseInt(a.config.plotOptions.radialBar.track.strokeWidth,10)/100/2,g=d-a.config.plotOptions.radialBar.hollow.margin;a.config.plotOptions.radialBar.hollow.image!==void 0&&(c=this.drawHollowImage(i,o,d,c));var f=this.drawHollow({size:g,centerX:i.centerX,centerY:i.centerY,fill:c||"transparent"});if(a.config.plotOptions.radialBar.hollow.dropShadow.enabled){var p=a.config.plotOptions.radialBar.hollow.dropShadow;r.dropShadow(f,p)}var x=1;!this.radialDataLabels.total.show&&a.globals.series.length>1&&(x=0);var b=null;this.radialDataLabels.show&&(b=this.renderInnerDataLabels(this.radialDataLabels,{hollowSize:d,centerX:i.centerX,centerY:i.centerY,opacity:x})),a.config.plotOptions.radialBar.hollow.position==="back"&&(o.add(f),b&&o.add(b));var y=!1;a.config.plotOptions.radialBar.inverseOrder&&(y=!0);for(var k=y?i.series.length-1:0;y?k>=0:k<i.series.length;y?k--:k++){var A=s.group({class:"apexcharts-series apexcharts-radial-series",seriesName:L.escapeString(a.globals.seriesNames[k])});o.add(A),A.attr({rel:k+1,"data:realIndex":k}),this.ctx.series.addCollapsedClassToSeries(A,k),i.size=i.size-h-this.margin;var l=n.fillPath({seriesNumber:k,size:i.size,value:i.series[k]}),u=this.startAngle,m=void 0,w=L.negToZero(i.series[k]>100?100:i.series[k])/100,S=Math.round(this.totalAngle*w)+this.startAngle,P=void 0;a.globals.dataChanged&&(m=this.startAngle,P=Math.round(this.totalAngle*L.negToZero(a.globals.previousPaths[k])/100)+m),Math.abs(S)+Math.abs(u)>=360&&(S-=.01),Math.abs(P)+Math.abs(m)>=360&&(P-=.01);var C=S-u,M=Array.isArray(a.config.stroke.dashArray)?a.config.stroke.dashArray[k]:a.config.stroke.dashArray,T=s.drawPath({d:"",stroke:l,strokeWidth:h,fill:"none",fillOpacity:a.config.fill.opacity,classes:"apexcharts-radialbar-area apexcharts-radialbar-slice-"+k,strokeDashArray:M});if(I.setAttrs(T.node,{"data:angle":C,"data:value":i.series[k]}),a.config.chart.dropShadow.enabled){var z=a.config.chart.dropShadow;r.dropShadow(T,z,k)}r.setSelectionFilter(T,0,k),this.addListeners(T,this.radialDataLabels),A.add(T),T.attr({index:0,j:k});var Y=0;!this.initialAnim||a.globals.resized||a.globals.dataChanged||(Y=(S-u)/360*a.config.chart.animations.speed,this.animDur=Y/(1.2*i.series.length)+this.animDur,this.animBeginArr.push(this.animDur)),a.globals.dataChanged&&(Y=(S-u)/360*a.config.chart.animations.dynamicAnimation.speed,this.animDur=Y/(1.2*i.series.length)+this.animDur,this.animBeginArr.push(this.animDur)),this.animatePaths(T,{centerX:i.centerX,centerY:i.centerY,endAngle:S,startAngle:u,prevEndAngle:P,prevStartAngle:m,size:i.size,i:k,totalItems:2,animBeginArr:this.animBeginArr,dur:Y,shouldSetPrevPaths:!0,easing:a.globals.easing})}return{g:o,elHollow:f,dataLabels:b}}},{key:"drawHollow",value:function(i){var a=new I(this.ctx).drawCircle(2*i.size);return a.attr({class:"apexcharts-radialbar-hollow",cx:i.centerX,cy:i.centerY,r:i.size,fill:i.fill}),a}},{key:"drawHollowImage",value:function(i,a,s,n){var r=this.w,o=new q(this.ctx),h=L.randomId(),c=r.config.plotOptions.radialBar.hollow.image;if(r.config.plotOptions.radialBar.hollow.imageClipped)o.clippedImgArea({width:s,height:s,image:c,patternID:"pattern".concat(r.globals.cuid).concat(h)}),n="url(#pattern".concat(r.globals.cuid).concat(h,")");else{var d=r.config.plotOptions.radialBar.hollow.imageWidth,g=r.config.plotOptions.radialBar.hollow.imageHeight;if(d===void 0&&g===void 0){var f=r.globals.dom.Paper.image(c).loaded(function(x){this.move(i.centerX-x.width/2+r.config.plotOptions.radialBar.hollow.imageOffsetX,i.centerY-x.height/2+r.config.plotOptions.radialBar.hollow.imageOffsetY)});a.add(f)}else{var p=r.globals.dom.Paper.image(c).loaded(function(x){this.move(i.centerX-d/2+r.config.plotOptions.radialBar.hollow.imageOffsetX,i.centerY-g/2+r.config.plotOptions.radialBar.hollow.imageOffsetY),this.size(d,g)});a.add(p)}}return n}},{key:"getStrokeWidth",value:function(i){var a=this.w;return i.size*(100-parseInt(a.config.plotOptions.radialBar.hollow.size,10))/100/(i.series.length+1)-this.margin}}]),t}(),St=function(){function v(e){X(this,v),this.w=e.w,this.lineCtx=e}return E(v,[{key:"sameValueSeriesFix",value:function(e,t){var i=this.w;if(i.config.chart.type==="line"&&(i.config.fill.type==="gradient"||i.config.fill.type[e]==="gradient")&&new D(this.lineCtx.ctx,i).seriesHaveSameValues(e)){var a=t[e].slice();a[a.length-1]=a[a.length-1]+1e-6,t[e]=a}return t}},{key:"calculatePoints",value:function(e){var t=e.series,i=e.realIndex,a=e.x,s=e.y,n=e.i,r=e.j,o=e.prevY,h=this.w,c=[],d=[];if(r===0){var g=this.lineCtx.categoryAxisCorrection+h.config.markers.offsetX;h.globals.isXNumeric&&(g=(h.globals.seriesX[i][0]-h.globals.minX)/this.lineCtx.xRatio+h.config.markers.offsetX),c.push(g),d.push(L.isNumber(t[n][0])?o+h.config.markers.offsetY:null),c.push(a+h.config.markers.offsetX),d.push(L.isNumber(t[n][r+1])?s+h.config.markers.offsetY:null)}else c.push(a+h.config.markers.offsetX),d.push(L.isNumber(t[n][r+1])?s+h.config.markers.offsetY:null);return{x:c,y:d}}},{key:"checkPreviousPaths",value:function(e){for(var t=e.pathFromLine,i=e.pathFromArea,a=e.realIndex,s=this.w,n=0;n<s.globals.previousPaths.length;n++){var r=s.globals.previousPaths[n];(r.type==="line"||r.type==="area")&&r.paths.length>0&&parseInt(r.realIndex,10)===parseInt(a,10)&&(r.type==="line"?(this.lineCtx.appendPathFrom=!1,t=s.globals.previousPaths[n].paths[0].d):r.type==="area"&&(this.lineCtx.appendPathFrom=!1,i=s.globals.previousPaths[n].paths[0].d,s.config.stroke.show&&s.globals.previousPaths[n].paths[1]&&(t=s.globals.previousPaths[n].paths[1].d)))}return{pathFromLine:t,pathFromArea:i}}},{key:"determineFirstPrevY",value:function(e){var t=e.i,i=e.series,a=e.prevY,s=e.lineYPosition,n=this.w;if(i[t][0]!==void 0)a=(s=n.config.chart.stacked&&t>0?this.lineCtx.prevSeriesY[t-1][0]:this.lineCtx.zeroY)-i[t][0]/this.lineCtx.yRatio[this.lineCtx.yaxisIndex]+2*(this.lineCtx.isReversed?i[t][0]/this.lineCtx.yRatio[this.lineCtx.yaxisIndex]:0);else if(n.config.chart.stacked&&t>0&&i[t][0]===void 0){for(var r=t-1;r>=0;r--)if(i[r][0]!==null&&i[r][0]!==void 0){a=s=this.lineCtx.prevSeriesY[r][0];break}}return{prevY:a,lineYPosition:s}}}]),v}(),Se=function(){function v(e,t,i){X(this,v),this.ctx=e,this.w=e.w,this.xyRatios=t,this.pointsChart=!(this.w.config.chart.type!=="bubble"&&this.w.config.chart.type!=="scatter")||i,this.scatter=new Ee(this.ctx),this.noNegatives=this.w.globals.minX===Number.MAX_VALUE,this.lineHelpers=new St(this),this.markers=new ne(this.ctx),this.prevSeriesY=[],this.categoryAxisCorrection=0,this.yaxisIndex=0}return E(v,[{key:"draw",value:function(e,t,i){var a=this.w,s=new I(this.ctx),n=a.globals.comboCharts?t:a.config.chart.type,r=s.group({class:"apexcharts-".concat(n,"-series apexcharts-plot-series")}),o=new D(this.ctx,a);this.yRatio=this.xyRatios.yRatio,this.zRatio=this.xyRatios.zRatio,this.xRatio=this.xyRatios.xRatio,this.baseLineY=this.xyRatios.baseLineY,e=o.getLogSeries(e),this.yRatio=o.getLogYRatios(this.yRatio);for(var h=[],c=0;c<e.length;c++){e=this.lineHelpers.sameValueSeriesFix(c,e);var d=a.globals.comboCharts?i[c]:c;this._initSerieVariables(e,c,d);var g=[],f=[],p=a.globals.padHorizontal+this.categoryAxisCorrection;this.ctx.series.addCollapsedClassToSeries(this.elSeries,d),a.globals.isXNumeric&&a.globals.seriesX.length>0&&(p=(a.globals.seriesX[d][0]-a.globals.minX)/this.xRatio),f.push(p);var x,b=p,y=b,k=this.zeroY;k=this.lineHelpers.determineFirstPrevY({i:c,series:e,prevY:k,lineYPosition:0}).prevY,g.push(k),x=k;var A=this._calculatePathsFrom({series:e,i:c,realIndex:d,prevX:y,prevY:k}),l=this._iterateOverDataPoints({series:e,realIndex:d,i:c,x:p,y:1,pX:b,pY:x,pathsFrom:A,linePaths:[],areaPaths:[],seriesIndex:i,lineYPosition:0,xArrj:f,yArrj:g});this._handlePaths({type:n,realIndex:d,i:c,paths:l}),this.elSeries.add(this.elPointsMain),this.elSeries.add(this.elDataLabelsWrap),h.push(this.elSeries)}if(a.config.chart.stacked)for(var u=h.length;u>0;u--)r.add(h[u-1]);else for(var m=0;m<h.length;m++)r.add(h[m]);return r}},{key:"_initSerieVariables",value:function(e,t,i){var a=this.w,s=new I(this.ctx);this.xDivision=a.globals.gridWidth/(a.globals.dataPoints-(a.config.xaxis.tickPlacement==="on"?1:0)),this.strokeWidth=Array.isArray(a.config.stroke.width)?a.config.stroke.width[i]:a.config.stroke.width,this.yRatio.length>1&&(this.yaxisIndex=i),this.isReversed=a.config.yaxis[this.yaxisIndex]&&a.config.yaxis[this.yaxisIndex].reversed,this.zeroY=a.globals.gridHeight-this.baseLineY[this.yaxisIndex]-(this.isReversed?a.globals.gridHeight:0)+(this.isReversed?2*this.baseLineY[this.yaxisIndex]:0),this.areaBottomY=this.zeroY,(this.zeroY>a.globals.gridHeight||a.config.plotOptions.area.fillTo==="end")&&(this.areaBottomY=a.globals.gridHeight),this.categoryAxisCorrection=this.xDivision/2,this.elSeries=s.group({class:"apexcharts-series",seriesName:L.escapeString(a.globals.seriesNames[i])}),this.elPointsMain=s.group({class:"apexcharts-series-markers-wrap","data:realIndex":i}),this.elDataLabelsWrap=s.group({class:"apexcharts-datalabels","data:realIndex":i});var n=e[t].length===a.globals.dataPoints;this.elSeries.attr({"data:longestSeries":n,rel:t+1,"data:realIndex":i}),this.appendPathFrom=!0}},{key:"_calculatePathsFrom",value:function(e){var t,i,a,s,n=e.series,r=e.i,o=e.realIndex,h=e.prevX,c=e.prevY,d=this.w,g=new I(this.ctx);if(n[r][0]===null){for(var f=0;f<n[r].length;f++)if(n[r][f]!==null){h=this.xDivision*f,c=this.zeroY-n[r][f]/this.yRatio[this.yaxisIndex],t=g.move(h,c),i=g.move(h,this.areaBottomY);break}}else t=g.move(h,c),i=g.move(h,this.areaBottomY)+g.line(h,c);if(a=g.move(-1,this.zeroY)+g.line(-1,this.zeroY),s=g.move(-1,this.zeroY)+g.line(-1,this.zeroY),d.globals.previousPaths.length>0){var p=this.lineHelpers.checkPreviousPaths({pathFromLine:a,pathFromArea:s,realIndex:o});a=p.pathFromLine,s=p.pathFromArea}return{prevX:h,prevY:c,linePath:t,areaPath:i,pathFromLine:a,pathFromArea:s}}},{key:"_handlePaths",value:function(e){var t=e.type,i=e.realIndex,a=e.i,s=e.paths,n=this.w,r=new I(this.ctx),o=new q(this.ctx);this.prevSeriesY.push(s.yArrj),n.globals.seriesXvalues[i]=s.xArrj,n.globals.seriesYvalues[i]=s.yArrj;var h=n.config.forecastDataPoints;if(h.count>0){var c=n.globals.seriesXvalues[i][n.globals.seriesXvalues[i].length-h.count-1],d=r.drawRect(c,0,n.globals.gridWidth,n.globals.gridHeight,0);n.globals.dom.elForecastMask.appendChild(d.node);var g=r.drawRect(0,0,c,n.globals.gridHeight,0);n.globals.dom.elNonForecastMask.appendChild(g.node)}this.pointsChart||n.globals.delayedElements.push({el:this.elPointsMain.node,index:i});var f={i:a,realIndex:i,animationDelay:a,initialSpeed:n.config.chart.animations.speed,dataChangeSpeed:n.config.chart.animations.dynamicAnimation.speed,className:"apexcharts-".concat(t)};if(t==="area")for(var p=o.fillPath({seriesNumber:i}),x=0;x<s.areaPaths.length;x++){var b=r.renderPaths(F(F({},f),{},{pathFrom:s.pathFromArea,pathTo:s.areaPaths[x],stroke:"none",strokeWidth:0,strokeLineCap:null,fill:p}));this.elSeries.add(b)}if(n.config.stroke.show&&!this.pointsChart){var y=null;y=t==="line"?o.fillPath({seriesNumber:i,i:a}):n.globals.stroke.colors[i];for(var k=0;k<s.linePaths.length;k++){var A=F(F({},f),{},{pathFrom:s.pathFromLine,pathTo:s.linePaths[k],stroke:y,strokeWidth:this.strokeWidth,strokeLineCap:n.config.stroke.lineCap,fill:"none"}),l=r.renderPaths(A);if(this.elSeries.add(l),h.count>0){var u=r.renderPaths(A);u.node.setAttribute("stroke-dasharray",h.dashArray),h.strokeWidth&&u.node.setAttribute("stroke-width",h.strokeWidth),this.elSeries.add(u),u.attr("clip-path","url(#forecastMask".concat(n.globals.cuid,")")),l.attr("clip-path","url(#nonForecastMask".concat(n.globals.cuid,")"))}}}}},{key:"_iterateOverDataPoints",value:function(e){for(var t=e.series,i=e.realIndex,a=e.i,s=e.x,n=e.y,r=e.pX,o=e.pY,h=e.pathsFrom,c=e.linePaths,d=e.areaPaths,g=e.seriesIndex,f=e.lineYPosition,p=e.xArrj,x=e.yArrj,b=this.w,y=new I(this.ctx),k=this.yRatio,A=h.prevY,l=h.linePath,u=h.areaPath,m=h.pathFromLine,w=h.pathFromArea,S=L.isNumber(b.globals.minYArr[i])?b.globals.minYArr[i]:b.globals.minY,P=b.globals.dataPoints>1?b.globals.dataPoints-1:b.globals.dataPoints,C=0;C<P;C++){var M=t[a][C+1]===void 0||t[a][C+1]===null;if(b.globals.isXNumeric){var T=b.globals.seriesX[i][C+1];b.globals.seriesX[i][C+1]===void 0&&(T=b.globals.seriesX[i][P-1]),s=(T-b.globals.minX)/this.xRatio}else s+=this.xDivision;b.config.chart.stacked?a>0&&b.globals.collapsedSeries.length<b.config.series.length-1?f=this.prevSeriesY[function(H){for(var R=H,V=0;V<b.globals.series.length;V++)if(b.globals.collapsedSeriesIndices.indexOf(H)>-1){R--;break}return R>=0?R:0}(a-1)][C+1]:f=this.zeroY:f=this.zeroY,n=M?f-S/k[this.yaxisIndex]+2*(this.isReversed?S/k[this.yaxisIndex]:0):f-t[a][C+1]/k[this.yaxisIndex]+2*(this.isReversed?t[a][C+1]/k[this.yaxisIndex]:0),p.push(s),x.push(n);var z=this.lineHelpers.calculatePoints({series:t,x:s,y:n,realIndex:i,i:a,j:C,prevY:A}),Y=this._createPaths({series:t,i:a,realIndex:i,j:C,x:s,y:n,pX:r,pY:o,linePath:l,areaPath:u,linePaths:c,areaPaths:d,seriesIndex:g});d=Y.areaPaths,c=Y.linePaths,r=Y.pX,o=Y.pY,u=Y.areaPath,l=Y.linePath,this.appendPathFrom&&(m+=y.line(s,this.zeroY),w+=y.line(s,this.zeroY)),this.handleNullDataPoints(t,z,a,C,i),this._handleMarkersAndLabels({pointsPos:z,series:t,x:s,y:n,prevY:A,i:a,j:C,realIndex:i})}return{yArrj:x,xArrj:p,pathFromArea:w,areaPaths:d,pathFromLine:m,linePaths:c}}},{key:"_handleMarkersAndLabels",value:function(e){var t=e.pointsPos;e.series,e.x,e.y,e.prevY;var i=e.i,a=e.j,s=e.realIndex,n=this.w,r=new ee(this.ctx);if(this.pointsChart)this.scatter.draw(this.elSeries,a,{realIndex:s,pointsPos:t,zRatio:this.zRatio,elParent:this.elPointsMain});else{n.globals.series[i].length>1&&this.elPointsMain.node.classList.add("apexcharts-element-hidden");var o=this.markers.plotChartMarkers(t,s,a+1);o!==null&&this.elPointsMain.add(o)}var h=r.drawDataLabel(t,s,a+1,null);h!==null&&this.elDataLabelsWrap.add(h)}},{key:"_createPaths",value:function(e){var t=e.series,i=e.i,a=e.realIndex,s=e.j,n=e.x,r=e.y,o=e.pX,h=e.pY,c=e.linePath,d=e.areaPath,g=e.linePaths,f=e.areaPaths,p=e.seriesIndex,x=this.w,b=new I(this.ctx),y=x.config.stroke.curve,k=this.areaBottomY;if(Array.isArray(x.config.stroke.curve)&&(y=Array.isArray(p)?x.config.stroke.curve[p[i]]:x.config.stroke.curve[i]),y==="smooth"){var A=.35*(n-o);x.globals.hasNullValues?(t[i][s]!==null&&(t[i][s+1]!==null?(c=b.move(o,h)+b.curve(o+A,h,n-A,r,n+1,r),d=b.move(o+1,h)+b.curve(o+A,h,n-A,r,n+1,r)+b.line(n,k)+b.line(o,k)+"z"):(c=b.move(o,h),d=b.move(o,h)+"z")),g.push(c),f.push(d)):(c+=b.curve(o+A,h,n-A,r,n,r),d+=b.curve(o+A,h,n-A,r,n,r)),o=n,h=r,s===t[i].length-2&&(d=d+b.curve(o,h,n,r,n,k)+b.move(n,r)+"z",x.globals.hasNullValues||(g.push(c),f.push(d)))}else{if(t[i][s+1]===null){c+=b.move(n,r);var l=x.globals.isXNumeric?(x.globals.seriesX[a][s]-x.globals.minX)/this.xRatio:n-this.xDivision;d=d+b.line(l,k)+b.move(n,r)+"z"}t[i][s]===null&&(c+=b.move(n,r),d+=b.move(n,k)),y==="stepline"?(c=c+b.line(n,null,"H")+b.line(null,r,"V"),d=d+b.line(n,null,"H")+b.line(null,r,"V")):y==="straight"&&(c+=b.line(n,r),d+=b.line(n,r)),s===t[i].length-2&&(d=d+b.line(n,k)+b.move(n,r)+"z",g.push(c),f.push(d))}return{linePaths:g,areaPaths:f,pX:o,pY:h,linePath:c,areaPath:d}}},{key:"handleNullDataPoints",value:function(e,t,i,a,s){var n=this.w;if(e[i][a]===null&&n.config.markers.showNullDataPoints||e[i].length===1){var r=this.markers.plotChartMarkers(t,s,a+1,this.strokeWidth-n.config.markers.strokeWidth/2,!0);r!==null&&this.elPointsMain.add(r)}}}]),v}();window.TreemapSquared={},window.TreemapSquared.generate=function(){function v(r,o,h,c){this.xoffset=r,this.yoffset=o,this.height=c,this.width=h,this.shortestEdge=function(){return Math.min(this.height,this.width)},this.getCoordinates=function(d){var g,f=[],p=this.xoffset,x=this.yoffset,b=s(d)/this.height,y=s(d)/this.width;if(this.width>=this.height)for(g=0;g<d.length;g++)f.push([p,x,p+b,x+d[g]/b]),x+=d[g]/b;else for(g=0;g<d.length;g++)f.push([p,x,p+d[g]/y,x+y]),p+=d[g]/y;return f},this.cutArea=function(d){var g;if(this.width>=this.height){var f=d/this.height,p=this.width-f;g=new v(this.xoffset+f,this.yoffset,p,this.height)}else{var x=d/this.width,b=this.height-x;g=new v(this.xoffset,this.yoffset+x,this.width,b)}return g}}function e(r,o,h,c,d){return c=c===void 0?0:c,d=d===void 0?0:d,function(g){var f,p,x=[];for(f=0;f<g.length;f++)for(p=0;p<g[f].length;p++)x.push(g[f][p]);return x}(t(function(g,f){var p,x=[],b=f/s(g);for(p=0;p<g.length;p++)x[p]=g[p]*b;return x}(r,o*h),[],new v(c,d,o,h),[]))}function t(r,o,h,c){var d,g,f;if(r.length!==0)return d=h.shortestEdge(),function(p,x,b){var y;if(p.length===0)return!0;(y=p.slice()).push(x);var k=i(p,b),A=i(y,b);return k>=A}(o,g=r[0],d)?(o.push(g),t(r.slice(1),o,h,c)):(f=h.cutArea(s(o),c),c.push(h.getCoordinates(o)),t(r,[],f,c)),c;c.push(h.getCoordinates(o))}function i(r,o){var h=Math.min.apply(Math,r),c=Math.max.apply(Math,r),d=s(r);return Math.max(Math.pow(o,2)*c/Math.pow(d,2),Math.pow(d,2)/(Math.pow(o,2)*h))}function a(r){return r&&r.constructor===Array}function s(r){var o,h=0;for(o=0;o<r.length;o++)h+=r[o];return h}function n(r){var o,h=0;if(a(r[0]))for(o=0;o<r.length;o++)h+=n(r[o]);else h=s(r);return h}return function r(o,h,c,d,g){d=d===void 0?0:d,g=g===void 0?0:g;var f,p,x=[],b=[];if(a(o[0])){for(p=0;p<o.length;p++)x[p]=n(o[p]);for(f=e(x,h,c,d,g),p=0;p<o.length;p++)b.push(r(o[p],f[p][2]-f[p][0],f[p][3]-f[p][1],f[p][0],f[p][1]))}else b=e(o,h,c,d,g);return b}}();var Q,fe,Ct=function(){function v(e,t){X(this,v),this.ctx=e,this.w=e.w,this.strokeWidth=this.w.config.stroke.width,this.helpers=new Be(e),this.dynamicAnim=this.w.config.chart.animations.dynamicAnimation,this.labels=[]}return E(v,[{key:"draw",value:function(e){var t=this,i=this.w,a=new I(this.ctx),s=new q(this.ctx),n=a.group({class:"apexcharts-treemap"});if(i.globals.noData)return n;var r=[];return e.forEach(function(o){var h=o.map(function(c){return Math.abs(c)});r.push(h)}),this.negRange=this.helpers.checkColorRange(),i.config.series.forEach(function(o,h){o.data.forEach(function(c){Array.isArray(t.labels[h])||(t.labels[h]=[]),t.labels[h].push(c.x)})}),window.TreemapSquared.generate(r,i.globals.gridWidth,i.globals.gridHeight).forEach(function(o,h){var c=a.group({class:"apexcharts-series apexcharts-treemap-series",seriesName:L.escapeString(i.globals.seriesNames[h]),rel:h+1,"data:realIndex":h});if(i.config.chart.dropShadow.enabled){var d=i.config.chart.dropShadow;new O(t.ctx).dropShadow(n,d,h)}var g=a.group({class:"apexcharts-data-labels"});o.forEach(function(f,p){var x=f[0],b=f[1],y=f[2],k=f[3],A=a.drawRect(x,b,y-x,k-b,0,"#fff",1,t.strokeWidth,i.config.plotOptions.treemap.useFillColorAsStroke?u:i.globals.stroke.colors[h]);A.attr({cx:x,cy:b,index:h,i:h,j:p,width:y-x,height:k-b});var l=t.helpers.getShadeColor(i.config.chart.type,h,p,t.negRange),u=l.color;i.config.series[h].data[p]!==void 0&&i.config.series[h].data[p].fillColor&&(u=i.config.series[h].data[p].fillColor);var m=s.fillPath({color:u,seriesNumber:h,dataPointIndex:p});A.node.classList.add("apexcharts-treemap-rect"),A.attr({fill:m}),t.helpers.addListeners(A);var w={x:x+(y-x)/2,y:b+(k-b)/2,width:0,height:0},S={x,y:b,width:y-x,height:k-b};if(i.config.chart.animations.enabled&&!i.globals.dataChanged){var P=1;i.globals.resized||(P=i.config.chart.animations.speed),t.animateTreemap(A,w,S,P)}if(i.globals.dataChanged){var C=1;t.dynamicAnim.enabled&&i.globals.shouldAnimate&&(C=t.dynamicAnim.speed,i.globals.previousPaths[h]&&i.globals.previousPaths[h][p]&&i.globals.previousPaths[h][p].rect&&(w=i.globals.previousPaths[h][p].rect),t.animateTreemap(A,w,S,C))}var M=t.getFontSize(f),T=i.config.dataLabels.formatter(t.labels[h][p],{value:i.globals.series[h][p],seriesIndex:h,dataPointIndex:p,w:i}),z=t.helpers.calculateDataLabels({text:T,x:(x+y)/2,y:(b+k)/2+t.strokeWidth/2+M/3,i:h,j:p,colorProps:l,fontSize:M,series:e});i.config.dataLabels.enabled&&z&&t.rotateToFitLabel(z,T,x,b,y,k),c.add(A),z!==null&&c.add(z)}),c.add(g),n.add(c)}),n}},{key:"getFontSize",value:function(e){var t=this.w,i,a,s,n,r=function o(h){var c,d=0;if(Array.isArray(h[0]))for(c=0;c<h.length;c++)d+=o(h[c]);else for(c=0;c<h.length;c++)d+=h[c].length;return d}(this.labels)/function o(h){var c,d=0;if(Array.isArray(h[0]))for(c=0;c<h.length;c++)d+=o(h[c]);else for(c=0;c<h.length;c++)d+=1;return d}(this.labels);return i=e[2]-e[0],a=e[3]-e[1],s=i*a,n=Math.pow(s,.5),Math.min(n/r,parseInt(t.config.dataLabels.style.fontSize,10))}},{key:"rotateToFitLabel",value:function(e,t,i,a,s,n){var r=new I(this.ctx),o=r.getTextRects(t);if(o.width+5>s-i&&o.width<=n-a){var h=r.rotateAroundCenter(e.node);e.node.setAttribute("transform","rotate(-90 ".concat(h.x," ").concat(h.y,")"))}}},{key:"animateTreemap",value:function(e,t,i,a){var s=new K(this.ctx);s.animateRect(e,{x:t.x,y:t.y,width:t.width,height:t.height},{x:i.x,y:i.y,width:i.width,height:i.height},a,function(){s.animationCompleted(e)})}}]),v}(),Lt=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w,this.timeScaleArray=[],this.utc=this.w.config.xaxis.labels.datetimeUTC}return E(v,[{key:"calculateTimeScaleTicks",value:function(e,t){var i=this,a=this.w;if(a.globals.allSeriesCollapsed)return a.globals.labels=[],a.globals.timescaleLabels=[],[];var s=new W(this.ctx),n=(t-e)/864e5;this.determineInterval(n),a.globals.disableZoomIn=!1,a.globals.disableZoomOut=!1,n<.00011574074074074075?a.globals.disableZoomIn=!0:n>5e4&&(a.globals.disableZoomOut=!0);var r=s.getTimeUnitsfromTimestamp(e,t,this.utc),o=a.globals.gridWidth/n,h=o/24,c=h/60,d=c/60,g=Math.floor(24*n),f=Math.floor(1440*n),p=Math.floor(86400*n),x=Math.floor(n),b=Math.floor(n/30),y=Math.floor(n/365),k={minMillisecond:r.minMillisecond,minSecond:r.minSecond,minMinute:r.minMinute,minHour:r.minHour,minDate:r.minDate,minMonth:r.minMonth,minYear:r.minYear},A={firstVal:k,currentMillisecond:k.minMillisecond,currentSecond:k.minSecond,currentMinute:k.minMinute,currentHour:k.minHour,currentMonthDate:k.minDate,currentDate:k.minDate,currentMonth:k.minMonth,currentYear:k.minYear,daysWidthOnXAxis:o,hoursWidthOnXAxis:h,minutesWidthOnXAxis:c,secondsWidthOnXAxis:d,numberOfSeconds:p,numberOfMinutes:f,numberOfHours:g,numberOfDays:x,numberOfMonths:b,numberOfYears:y};switch(this.tickInterval){case"years":this.generateYearScale(A);break;case"months":case"half_year":this.generateMonthScale(A);break;case"months_days":case"months_fortnight":case"days":case"week_days":this.generateDayScale(A);break;case"hours":this.generateHourScale(A);break;case"minutes_fives":case"minutes":this.generateMinuteScale(A);break;case"seconds_tens":case"seconds_fives":case"seconds":this.generateSecondScale(A)}var l=this.timeScaleArray.map(function(u){var m={position:u.position,unit:u.unit,year:u.year,day:u.day?u.day:1,hour:u.hour?u.hour:0,month:u.month+1};return u.unit==="month"?F(F({},m),{},{day:1,value:u.value+1}):u.unit==="day"||u.unit==="hour"?F(F({},m),{},{value:u.value}):u.unit==="minute"?F(F({},m),{},{value:u.value,minute:u.value}):u.unit==="second"?F(F({},m),{},{value:u.value,minute:u.minute,second:u.second}):u});return l.filter(function(u){var m=1,w=Math.ceil(a.globals.gridWidth/120),S=u.value;a.config.xaxis.tickAmount!==void 0&&(w=a.config.xaxis.tickAmount),l.length>w&&(m=Math.floor(l.length/w));var P=!1,C=!1;switch(i.tickInterval){case"years":u.unit==="year"&&(P=!0);break;case"half_year":m=7,u.unit==="year"&&(P=!0);break;case"months":m=1,u.unit==="year"&&(P=!0);break;case"months_fortnight":m=15,u.unit!=="year"&&u.unit!=="month"||(P=!0),S===30&&(C=!0);break;case"months_days":m=10,u.unit==="month"&&(P=!0),S===30&&(C=!0);break;case"week_days":m=8,u.unit==="month"&&(P=!0);break;case"days":m=1,u.unit==="month"&&(P=!0);break;case"hours":u.unit==="day"&&(P=!0);break;case"minutes_fives":S%5!=0&&(C=!0);break;case"seconds_tens":S%10!=0&&(C=!0);break;case"seconds_fives":S%5!=0&&(C=!0)}if(i.tickInterval==="hours"||i.tickInterval==="minutes_fives"||i.tickInterval==="seconds_tens"||i.tickInterval==="seconds_fives"){if(!C)return!0}else if((S%m==0||P)&&!C)return!0})}},{key:"recalcDimensionsBasedOnFormat",value:function(e,t){var i=this.w,a=this.formatDates(e),s=this.removeOverlappingTS(a);i.globals.timescaleLabels=s.slice(),new ue(this.ctx).plotCoords()}},{key:"determineInterval",value:function(e){var t=24*e,i=60*t;switch(!0){case e/365>5:this.tickInterval="years";break;case e>800:this.tickInterval="half_year";break;case e>180:this.tickInterval="months";break;case e>90:this.tickInterval="months_fortnight";break;case e>60:this.tickInterval="months_days";break;case e>30:this.tickInterval="week_days";break;case e>2:this.tickInterval="days";break;case t>2.4:this.tickInterval="hours";break;case i>15:this.tickInterval="minutes_fives";break;case i>5:this.tickInterval="minutes";break;case i>1:this.tickInterval="seconds_tens";break;case 60*i>20:this.tickInterval="seconds_fives";break;default:this.tickInterval="seconds"}}},{key:"generateYearScale",value:function(e){var t=e.firstVal,i=e.currentMonth,a=e.currentYear,s=e.daysWidthOnXAxis,n=e.numberOfYears,r=t.minYear,o=0,h=new W(this.ctx),c="year";if(t.minDate>1||t.minMonth>0){var d=h.determineRemainingDaysOfYear(t.minYear,t.minMonth,t.minDate);o=(h.determineDaysOfYear(t.minYear)-d+1)*s,r=t.minYear+1,this.timeScaleArray.push({position:o,value:r,unit:c,year:r,month:L.monthMod(i+1)})}else t.minDate===1&&t.minMonth===0&&this.timeScaleArray.push({position:o,value:r,unit:c,year:a,month:L.monthMod(i+1)});for(var g=r,f=o,p=0;p<n;p++)g++,f=h.determineDaysOfYear(g-1)*s+f,this.timeScaleArray.push({position:f,value:g,unit:c,year:g,month:1})}},{key:"generateMonthScale",value:function(e){var t=e.firstVal,i=e.currentMonthDate,a=e.currentMonth,s=e.currentYear,n=e.daysWidthOnXAxis,r=e.numberOfMonths,o=a,h=0,c=new W(this.ctx),d="month",g=0;if(t.minDate>1){h=(c.determineDaysOfMonths(a+1,t.minYear)-i+1)*n,o=L.monthMod(a+1);var f=s+g,p=L.monthMod(o),x=o;o===0&&(d="year",x=f,p=1,f+=g+=1),this.timeScaleArray.push({position:h,value:x,unit:d,year:f,month:p})}else this.timeScaleArray.push({position:h,value:o,unit:d,year:s,month:L.monthMod(a)});for(var b=o+1,y=h,k=0,A=1;k<r;k++,A++){(b=L.monthMod(b))===0?(d="year",g+=1):d="month";var l=this._getYear(s,b,g);y=c.determineDaysOfMonths(b,l)*n+y;var u=b===0?l:b;this.timeScaleArray.push({position:y,value:u,unit:d,year:l,month:b===0?1:b}),b++}}},{key:"generateDayScale",value:function(e){var t=e.firstVal,i=e.currentMonth,a=e.currentYear,s=e.hoursWidthOnXAxis,n=e.numberOfDays,r=new W(this.ctx),o="day",h=t.minDate+1,c=h,d=function(A,l,u){return A>r.determineDaysOfMonths(l+1,u)&&(c=1,o="month",f=l+=1),l},g=(24-t.minHour)*s,f=h,p=d(c,i,a);t.minHour===0&&t.minDate===1?(g=0,f=L.monthMod(t.minMonth),o="month",c=t.minDate,n++):t.minDate!==1&&t.minHour===0&&t.minMinute===0&&(g=0,h=t.minDate,f=h,p=d(c=h,i,a)),this.timeScaleArray.push({position:g,value:f,unit:o,year:this._getYear(a,p,0),month:L.monthMod(p),day:c});for(var x=g,b=0;b<n;b++){o="day",p=d(c+=1,p,this._getYear(a,p,0));var y=this._getYear(a,p,0);x=24*s+x;var k=c===1?L.monthMod(p):c;this.timeScaleArray.push({position:x,value:k,unit:o,year:y,month:L.monthMod(p),day:k})}}},{key:"generateHourScale",value:function(e){var t=e.firstVal,i=e.currentDate,a=e.currentMonth,s=e.currentYear,n=e.minutesWidthOnXAxis,r=e.numberOfHours,o=new W(this.ctx),h="hour",c=function(m,w){return m>o.determineDaysOfMonths(w+1,s)&&(b=1,w+=1),{month:w,date:b}},d=function(m,w){return m>o.determineDaysOfMonths(w+1,s)?w+=1:w},g=60-(t.minMinute+t.minSecond/60),f=g*n,p=t.minHour+1,x=p+1;g===60&&(f=0,x=(p=t.minHour)+1);var b=i,y=d(b,a);this.timeScaleArray.push({position:f,value:p,unit:h,day:b,hour:x,year:s,month:L.monthMod(y)});for(var k=f,A=0;A<r;A++){h="hour",x>=24&&(x=0,h="day",y=c(b+=1,y).month,y=d(b,y));var l=this._getYear(s,y,0);k=x===0&&A===0?g*n:60*n+k;var u=x===0?b:x;this.timeScaleArray.push({position:k,value:u,unit:h,hour:x,day:b,year:l,month:L.monthMod(y)}),x++}}},{key:"generateMinuteScale",value:function(e){for(var t=e.currentMillisecond,i=e.currentSecond,a=e.currentMinute,s=e.currentHour,n=e.currentDate,r=e.currentMonth,o=e.currentYear,h=e.minutesWidthOnXAxis,c=e.secondsWidthOnXAxis,d=e.numberOfMinutes,g=a+1,f=n,p=r,x=o,b=s,y=(60-i-t/1e3)*c,k=0;k<d;k++)g>=60&&(g=0,(b+=1)===24&&(b=0)),this.timeScaleArray.push({position:y,value:g,unit:"minute",hour:b,minute:g,day:f,year:this._getYear(x,p,0),month:L.monthMod(p)}),y+=h,g++}},{key:"generateSecondScale",value:function(e){for(var t=e.currentMillisecond,i=e.currentSecond,a=e.currentMinute,s=e.currentHour,n=e.currentDate,r=e.currentMonth,o=e.currentYear,h=e.secondsWidthOnXAxis,c=e.numberOfSeconds,d=i+1,g=a,f=n,p=r,x=o,b=s,y=(1e3-t)/1e3*h,k=0;k<c;k++)d>=60&&(d=0,++g>=60&&(g=0,++b===24&&(b=0))),this.timeScaleArray.push({position:y,value:d,unit:"second",hour:b,minute:g,second:d,day:f,year:this._getYear(x,p,0),month:L.monthMod(p)}),y+=h,d++}},{key:"createRawDateString",value:function(e,t){var i=e.year;return e.month===0&&(e.month=1),i+="-"+("0"+e.month.toString()).slice(-2),e.unit==="day"?i+=e.unit==="day"?"-"+("0"+t).slice(-2):"-01":i+="-"+("0"+(e.day?e.day:"1")).slice(-2),e.unit==="hour"?i+=e.unit==="hour"?"T"+("0"+t).slice(-2):"T00":i+="T"+("0"+(e.hour?e.hour:"0")).slice(-2),e.unit==="minute"?i+=":"+("0"+t).slice(-2):i+=":"+(e.minute?("0"+e.minute).slice(-2):"00"),e.unit==="second"?i+=":"+("0"+t).slice(-2):i+=":00",this.utc&&(i+=".000Z"),i}},{key:"formatDates",value:function(e){var t=this,i=this.w;return e.map(function(a){var s=a.value.toString(),n=new W(t.ctx),r=t.createRawDateString(a,s),o=n.getDate(n.parseDate(r));if(t.utc||(o=n.getDate(n.parseDateWithTimezone(r))),i.config.xaxis.labels.format===void 0){var h="dd MMM",c=i.config.xaxis.labels.datetimeFormatter;a.unit==="year"&&(h=c.year),a.unit==="month"&&(h=c.month),a.unit==="day"&&(h=c.day),a.unit==="hour"&&(h=c.hour),a.unit==="minute"&&(h=c.minute),a.unit==="second"&&(h=c.second),s=n.formatDate(o,h)}else s=n.formatDate(o,i.config.xaxis.labels.format);return{dateString:r,position:a.position,value:s,unit:a.unit,year:a.year,month:a.month}})}},{key:"removeOverlappingTS",value:function(e){var t,i=this,a=new I(this.ctx),s=!1;e.length>0&&e[0].value&&e.every(function(o){return o.value.length===e[0].value.length})&&(s=!0,t=a.getTextRects(e[0].value).width);var n=0,r=e.map(function(o,h){if(h>0&&i.w.config.xaxis.labels.hideOverlappingLabels){var c=s?t:a.getTextRects(e[n].value).width,d=e[n].position;return o.position>d+c+10?(n=h,o):null}return o});return r=r.filter(function(o){return o!==null})}},{key:"_getYear",value:function(e,t,i){return e+Math.floor(t/12)+i}}]),v}(),Pt=function(){function v(e,t){X(this,v),this.ctx=t,this.w=t.w,this.el=e}return E(v,[{key:"setupElements",value:function(){var e=this.w.globals,t=this.w.config,i=t.chart.type;e.axisCharts=["line","area","bar","rangeBar","candlestick","boxPlot","scatter","bubble","radar","heatmap","treemap"].indexOf(i)>-1,e.xyCharts=["line","area","bar","rangeBar","candlestick","boxPlot","scatter","bubble"].indexOf(i)>-1,e.isBarHorizontal=(t.chart.type==="bar"||t.chart.type==="rangeBar")&&t.plotOptions.bar.horizontal,e.chartClass=".apexcharts"+e.chartID,e.dom.baseEl=this.el,e.dom.elWrap=document.createElement("div"),I.setAttrs(e.dom.elWrap,{id:e.chartClass.substring(1),class:"apexcharts-canvas "+e.chartClass.substring(1)}),this.el.appendChild(e.dom.elWrap),e.dom.Paper=new window.SVG.Doc(e.dom.elWrap),e.dom.Paper.attr({class:"apexcharts-svg","xmlns:data":"ApexChartsNS",transform:"translate(".concat(t.chart.offsetX,", ").concat(t.chart.offsetY,")")}),e.dom.Paper.node.style.background=t.chart.background,this.setSVGDimensions(),e.dom.elGraphical=e.dom.Paper.group().attr({class:"apexcharts-inner apexcharts-graphical"}),e.dom.elAnnotations=e.dom.Paper.group().attr({class:"apexcharts-annotations"}),e.dom.elDefs=e.dom.Paper.defs(),e.dom.elLegendWrap=document.createElement("div"),e.dom.elLegendWrap.classList.add("apexcharts-legend"),e.dom.elWrap.appendChild(e.dom.elLegendWrap),e.dom.Paper.add(e.dom.elGraphical),e.dom.elGraphical.add(e.dom.elDefs)}},{key:"plotChartType",value:function(e,t){var i=this.w,a=i.config,s=i.globals,n={series:[],i:[]},r={series:[],i:[]},o={series:[],i:[]},h={series:[],i:[]},c={series:[],i:[]},d={series:[],i:[]},g={series:[],i:[]};s.series.map(function(u,m){var w=0;e[m].type!==void 0?(e[m].type==="column"||e[m].type==="bar"?(s.series.length>1&&a.plotOptions.bar.horizontal&&console.warn("Horizontal bars are not supported in a mixed/combo chart. Please turn off `plotOptions.bar.horizontal`"),c.series.push(u),c.i.push(m),w++,i.globals.columnSeries=c.series):e[m].type==="area"?(r.series.push(u),r.i.push(m),w++):e[m].type==="line"?(n.series.push(u),n.i.push(m),w++):e[m].type==="scatter"?(o.series.push(u),o.i.push(m)):e[m].type==="bubble"?(h.series.push(u),h.i.push(m),w++):e[m].type==="candlestick"?(d.series.push(u),d.i.push(m),w++):e[m].type==="boxPlot"?(g.series.push(u),g.i.push(m),w++):console.warn("You have specified an unrecognized chart type. Available types for this property are line/area/column/bar/scatter/bubble"),w>1&&(s.comboCharts=!0)):(n.series.push(u),n.i.push(m))});var f=new Se(this.ctx,t),p=new Ae(this.ctx,t);this.ctx.pie=new Ge(this.ctx);var x=new At(this.ctx);this.ctx.rangeBar=new me(this.ctx,t);var b=new kt(this.ctx),y=[];if(s.comboCharts){if(r.series.length>0&&y.push(f.draw(r.series,"area",r.i)),c.series.length>0)if(i.config.chart.stacked){var k=new We(this.ctx,t);y.push(k.draw(c.series,c.i))}else this.ctx.bar=new te(this.ctx,t),y.push(this.ctx.bar.draw(c.series,c.i));if(n.series.length>0&&y.push(f.draw(n.series,"line",n.i)),d.series.length>0&&y.push(p.draw(d.series,d.i)),g.series.length>0&&y.push(p.draw(g.series,g.i)),o.series.length>0){var A=new Se(this.ctx,t,!0);y.push(A.draw(o.series,"scatter",o.i))}if(h.series.length>0){var l=new Se(this.ctx,t,!0);y.push(l.draw(h.series,"bubble",h.i))}}else switch(a.chart.type){case"line":y=f.draw(s.series,"line");break;case"area":y=f.draw(s.series,"area");break;case"bar":a.chart.stacked?y=new We(this.ctx,t).draw(s.series):(this.ctx.bar=new te(this.ctx,t),y=this.ctx.bar.draw(s.series));break;case"candlestick":y=new Ae(this.ctx,t).draw(s.series);break;case"boxPlot":y=new Ae(this.ctx,t).draw(s.series);break;case"rangeBar":y=this.ctx.rangeBar.draw(s.series);break;case"heatmap":y=new wt(this.ctx,t).draw(s.series);break;case"treemap":y=new Ct(this.ctx,t).draw(s.series);break;case"pie":case"donut":case"polarArea":y=this.ctx.pie.draw(s.series);break;case"radialBar":y=x.draw(s.series);break;case"radar":y=b.draw(s.series);break;default:y=f.draw(s.series)}return y}},{key:"setSVGDimensions",value:function(){var e=this.w.globals,t=this.w.config;e.svgWidth=t.chart.width,e.svgHeight=t.chart.height;var i=L.getDimensions(this.el),a=t.chart.width.toString().split(/[0-9]+/g).pop();a==="%"?L.isNumber(i[0])&&(i[0].width===0&&(i=L.getDimensions(this.el.parentNode)),e.svgWidth=i[0]*parseInt(t.chart.width,10)/100):a!=="px"&&a!==""||(e.svgWidth=parseInt(t.chart.width,10));var s=t.chart.height.toString().split(/[0-9]+/g).pop();if(e.svgHeight!=="auto"&&e.svgHeight!=="")if(s==="%"){var n=L.getDimensions(this.el.parentNode);e.svgHeight=n[1]*parseInt(t.chart.height,10)/100}else e.svgHeight=parseInt(t.chart.height,10);else e.axisCharts?e.svgHeight=e.svgWidth/1.61:e.svgHeight=e.svgWidth/1.2;if(e.svgWidth<0&&(e.svgWidth=0),e.svgHeight<0&&(e.svgHeight=0),I.setAttrs(e.dom.Paper.node,{width:e.svgWidth,height:e.svgHeight}),s!=="%"){var r=t.chart.sparkline.enabled?0:e.axisCharts?t.chart.parentHeightOffset:0;e.dom.Paper.node.parentNode.parentNode.style.minHeight=e.svgHeight+r+"px"}e.dom.elWrap.style.width=e.svgWidth+"px",e.dom.elWrap.style.height=e.svgHeight+"px"}},{key:"shiftGraphPosition",value:function(){var e=this.w.globals,t=e.translateY,i={transform:"translate("+e.translateX+", "+t+")"};I.setAttrs(e.dom.elGraphical.node,i)}},{key:"resizeNonAxisCharts",value:function(){var e=this.w,t=e.globals,i=0,a=e.config.chart.sparkline.enabled?1:15;a+=e.config.grid.padding.bottom,e.config.legend.position!=="top"&&e.config.legend.position!=="bottom"||!e.config.legend.show||e.config.legend.floating||(i=new He(this.ctx).legendHelpers.getLegendBBox().clwh+10);var s=e.globals.dom.baseEl.querySelector(".apexcharts-radialbar, .apexcharts-pie"),n=2.05*e.globals.radialSize;if(s&&!e.config.chart.sparkline.enabled){var r=L.getBoundingClientRect(s);n=r.bottom;var o=r.bottom-r.top;n=Math.max(2.05*e.globals.radialSize,o)}var h=n+t.translateY+i+a;t.dom.elLegendForeign&&t.dom.elLegendForeign.setAttribute("height",h),t.dom.elWrap.style.height=h+"px",I.setAttrs(t.dom.Paper.node,{height:h}),t.dom.Paper.node.parentNode.parentNode.style.minHeight=h+"px"}},{key:"coreCalculations",value:function(){new ye(this.ctx).init()}},{key:"resetGlobals",value:function(){var e=this,t=function(){return e.w.config.series.map(function(s){return[]})},i=new Ye,a=this.w.globals;i.initGlobalVars(a),a.seriesXvalues=t(),a.seriesYvalues=t()}},{key:"isMultipleY",value:function(){if(this.w.config.yaxis.constructor===Array&&this.w.config.yaxis.length>1)return this.w.globals.isMultipleYAxis=!0,!0}},{key:"xySettings",value:function(){var e=null,t=this.w;if(t.globals.axisCharts){if(t.config.xaxis.crosshairs.position==="back"&&new ke(this.ctx).drawXCrosshairs(),t.config.yaxis[0].crosshairs.position==="back"&&new ke(this.ctx).drawYCrosshairs(),t.config.xaxis.type==="datetime"&&t.config.xaxis.labels.formatter===void 0){this.ctx.timeScale=new Lt(this.ctx);var i=[];isFinite(t.globals.minX)&&isFinite(t.globals.maxX)&&!t.globals.isBarHorizontal?i=this.ctx.timeScale.calculateTimeScaleTicks(t.globals.minX,t.globals.maxX):t.globals.isBarHorizontal&&(i=this.ctx.timeScale.calculateTimeScaleTicks(t.globals.minY,t.globals.maxY)),this.ctx.timeScale.recalcDimensionsBasedOnFormat(i)}e=new D(this.ctx).getCalculatedRatios()}return e}},{key:"updateSourceChart",value:function(e){this.ctx.w.globals.selection=void 0,this.ctx.updateHelpers._updateOptions({chart:{selection:{xaxis:{min:e.w.globals.minX,max:e.w.globals.maxX}}}},!1,!1)}},{key:"setupBrushHandler",value:function(){var e=this,t=this.w;if(t.config.chart.brush.enabled&&typeof t.config.chart.events.selection!="function"){var i=t.config.chart.brush.targets||[t.config.chart.brush.target];i.forEach(function(a){var s=ApexCharts.getChartByID(a);s.w.globals.brushSource=e.ctx,typeof s.w.config.chart.events.zoomed!="function"&&(s.w.config.chart.events.zoomed=function(){e.updateSourceChart(s)}),typeof s.w.config.chart.events.scrolled!="function"&&(s.w.config.chart.events.scrolled=function(){e.updateSourceChart(s)})}),t.config.chart.events.selection=function(a,s){i.forEach(function(n){var r=ApexCharts.getChartByID(n),o=L.clone(t.config.yaxis);if(t.config.chart.brush.autoScaleYaxis&&r.w.globals.series.length===1){var h=new ie(r);o=h.autoScaleY(r,o,s)}var c=r.w.config.yaxis.reduce(function(d,g,f){return[].concat(U(d),[F(F({},r.w.config.yaxis[f]),{},{min:o[0].min,max:o[0].max})])},[]);r.ctx.updateHelpers._updateOptions({xaxis:{min:s.xaxis.min,max:s.xaxis.max},yaxis:c},!1,!1,!1,!1)})}}}}]),v}(),Tt=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"_updateOptions",value:function(e){var t=this,i=arguments.length>1&&arguments[1]!==void 0&&arguments[1],a=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],s=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3],n=arguments.length>4&&arguments[4]!==void 0&&arguments[4],r=[this.ctx];s&&(r=this.ctx.getSyncedCharts()),this.ctx.w.globals.isExecCalled&&(r=[this.ctx],this.ctx.w.globals.isExecCalled=!1),r.forEach(function(o){var h=o.w;return h.globals.shouldAnimate=a,i||(h.globals.resized=!0,h.globals.dataChanged=!0,a&&o.series.getPreviousPaths()),e&&N(e)==="object"&&(o.config=new le(e),e=D.extendArrayProps(o.config,e,h),o.w.globals.chartID!==t.ctx.w.globals.chartID&&delete e.series,h.config=L.extend(h.config,e),n&&(h.globals.lastXAxis=e.xaxis?L.clone(e.xaxis):[],h.globals.lastYAxis=e.yaxis?L.clone(e.yaxis):[],h.globals.initialConfig=L.extend({},h.config),h.globals.initialSeries=L.clone(h.config.series))),o.update(e)})}},{key:"_updateSeries",value:function(e,t){var i,a=this,s=arguments.length>2&&arguments[2]!==void 0&&arguments[2],n=this.w;return n.globals.shouldAnimate=t,n.globals.dataChanged=!0,t&&this.ctx.series.getPreviousPaths(),n.globals.axisCharts?((i=e.map(function(r,o){return a._extendSeries(r,o)})).length===0&&(i=[{data:[]}]),n.config.series=i):n.config.series=e.slice(),s&&(n.globals.initialSeries=L.clone(n.config.series)),this.ctx.update()}},{key:"_extendSeries",value:function(e,t){var i=this.w,a=i.config.series[t];return F(F({},i.config.series[t]),{},{name:e.name?e.name:a&&a.name,color:e.color?e.color:a&&a.color,type:e.type?e.type:a&&a.type,data:e.data?e.data:a&&a.data})}},{key:"toggleDataPointSelection",value:function(e,t){var i=this.w,a=null,s=".apexcharts-series[data\\:realIndex='".concat(e,"']");return i.globals.axisCharts?a=i.globals.dom.Paper.select("".concat(s," path[j='").concat(t,"'], ").concat(s," circle[j='").concat(t,"'], ").concat(s," rect[j='").concat(t,"']")).members[0]:t===void 0&&(a=i.globals.dom.Paper.select("".concat(s," path[j='").concat(e,"']")).members[0],i.config.chart.type!=="pie"&&i.config.chart.type!=="polarArea"&&i.config.chart.type!=="donut"||this.ctx.pie.pieClicked(e)),a?(new I(this.ctx).pathMouseDown(a,null),a.node?a.node:null):(console.warn("toggleDataPointSelection: Element not found"),null)}},{key:"forceXAxisUpdate",value:function(e){var t=this.w;if(["min","max"].forEach(function(a){e.xaxis[a]!==void 0&&(t.config.xaxis[a]=e.xaxis[a],t.globals.lastXAxis[a]=e.xaxis[a])}),e.xaxis.categories&&e.xaxis.categories.length&&(t.config.xaxis.categories=e.xaxis.categories),t.config.xaxis.convertedCatToNumeric){var i=new oe(e);e=i.convertCatToNumericXaxis(e,this.ctx)}return e}},{key:"forceYAxisUpdate",value:function(e){var t=this.w;return t.config.chart.stacked&&t.config.chart.stackType==="100%"&&(Array.isArray(e.yaxis)?e.yaxis.forEach(function(i,a){e.yaxis[a].min=0,e.yaxis[a].max=100}):(e.yaxis.min=0,e.yaxis.max=100)),e}},{key:"revertDefaultAxisMinMax",value:function(e){var t=this,i=this.w,a=i.globals.lastXAxis,s=i.globals.lastYAxis;e&&e.xaxis&&(a=e.xaxis),e&&e.yaxis&&(s=e.yaxis),i.config.xaxis.min=a.min,i.config.xaxis.max=a.max;var n=function(r){s[r]!==void 0&&(i.config.yaxis[r].min=s[r].min,i.config.yaxis[r].max=s[r].max)};i.config.yaxis.map(function(r,o){i.globals.zoomed||s[o]!==void 0?n(o):t.ctx.opts.yaxis[o]!==void 0&&(r.min=t.ctx.opts.yaxis[o].min,r.max=t.ctx.opts.yaxis[o].max)})}}]),v}();Q=typeof window<"u"?window:void 0,fe=function(v,e){var t=(this!==void 0?this:v).SVG=function(l){if(t.supported)return l=new t.Doc(l),t.parser.draw||t.prepare(),l};if(t.ns="http://www.w3.org/2000/svg",t.xmlns="http://www.w3.org/2000/xmlns/",t.xlink="http://www.w3.org/1999/xlink",t.svgjs="http://svgjs.dev",t.supported=!0,!t.supported)return!1;t.did=1e3,t.eid=function(l){return"Svgjs"+c(l)+t.did++},t.create=function(l){var u=e.createElementNS(this.ns,l);return u.setAttribute("id",this.eid(l)),u},t.extend=function(){var l,u;u=(l=[].slice.call(arguments)).pop();for(var m=l.length-1;m>=0;m--)if(l[m])for(var w in u)l[m].prototype[w]=u[w];t.Set&&t.Set.inherit&&t.Set.inherit()},t.invent=function(l){var u=typeof l.create=="function"?l.create:function(){this.constructor.call(this,t.create(l.create))};return l.inherit&&(u.prototype=new l.inherit),l.extend&&t.extend(u,l.extend),l.construct&&t.extend(l.parent||t.Container,l.construct),u},t.adopt=function(l){return l?l.instance?l.instance:((u=l.nodeName=="svg"?l.parentNode instanceof v.SVGElement?new t.Nested:new t.Doc:l.nodeName=="linearGradient"?new t.Gradient("linear"):l.nodeName=="radialGradient"?new t.Gradient("radial"):t[c(l.nodeName)]?new t[c(l.nodeName)]:new t.Element(l)).type=l.nodeName,u.node=l,l.instance=u,u instanceof t.Doc&&u.namespace().defs(),u.setData(JSON.parse(l.getAttribute("svgjs:data"))||{}),u):null;var u},t.prepare=function(){var l=e.getElementsByTagName("body")[0],u=(l?new t.Doc(l):t.adopt(e.documentElement).nested()).size(2,0);t.parser={body:l||e.documentElement,draw:u.style("opacity:0;position:absolute;left:-100%;top:-100%;overflow:hidden").node,poly:u.polyline().node,path:u.path().node,native:t.create("svg")}},t.parser={native:t.create("svg")},e.addEventListener("DOMContentLoaded",function(){t.parser.draw||t.prepare()},!1),t.regex={numberAndUnit:/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,hex:/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,rgb:/rgb\((\d+),(\d+),(\d+)\)/,reference:/#([a-z0-9\-_]+)/i,transforms:/\)\s*,?\s*/,whitespace:/\s/g,isHex:/^#[a-f0-9]{3,6}$/i,isRgb:/^rgb\(/,isCss:/[^:]+:[^;]+;?/,isBlank:/^(\s+)?$/,isNumber:/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,isPercent:/^-?[\d\.]+%$/,isImage:/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,delimiter:/[\s,]+/,hyphen:/([^e])\-/gi,pathLetters:/[MLHVCSQTAZ]/gi,isPathLetter:/[MLHVCSQTAZ]/i,numbersWithDots:/((\d?\.\d+(?:e[+-]?\d+)?)((?:\.\d+(?:e[+-]?\d+)?)+))+/gi,dots:/\./g},t.utils={map:function(l,u){for(var m=l.length,w=[],S=0;S<m;S++)w.push(u(l[S]));return w},filter:function(l,u){for(var m=l.length,w=[],S=0;S<m;S++)u(l[S])&&w.push(l[S]);return w},filterSVGElements:function(l){return this.filter(l,function(u){return u instanceof v.SVGElement})}},t.defaults={attrs:{"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","font-size":16,"font-family":"Helvetica, Arial, sans-serif","text-anchor":"start"}},t.Color=function(l){var u,m;this.r=0,this.g=0,this.b=0,l&&(typeof l=="string"?t.regex.isRgb.test(l)?(u=t.regex.rgb.exec(l.replace(t.regex.whitespace,"")),this.r=parseInt(u[1]),this.g=parseInt(u[2]),this.b=parseInt(u[3])):t.regex.isHex.test(l)&&(u=t.regex.hex.exec((m=l).length==4?["#",m.substring(1,2),m.substring(1,2),m.substring(2,3),m.substring(2,3),m.substring(3,4),m.substring(3,4)].join(""):m),this.r=parseInt(u[1],16),this.g=parseInt(u[2],16),this.b=parseInt(u[3],16)):N(l)==="object"&&(this.r=l.r,this.g=l.g,this.b=l.b))},t.extend(t.Color,{toString:function(){return this.toHex()},toHex:function(){return"#"+d(this.r)+d(this.g)+d(this.b)},toRgb:function(){return"rgb("+[this.r,this.g,this.b].join()+")"},brightness:function(){return this.r/255*.3+this.g/255*.59+this.b/255*.11},morph:function(l){return this.destination=new t.Color(l),this},at:function(l){return this.destination?(l=l<0?0:l>1?1:l,new t.Color({r:~~(this.r+(this.destination.r-this.r)*l),g:~~(this.g+(this.destination.g-this.g)*l),b:~~(this.b+(this.destination.b-this.b)*l)})):this}}),t.Color.test=function(l){return l+="",t.regex.isHex.test(l)||t.regex.isRgb.test(l)},t.Color.isRgb=function(l){return l&&typeof l.r=="number"&&typeof l.g=="number"&&typeof l.b=="number"},t.Color.isColor=function(l){return t.Color.isRgb(l)||t.Color.test(l)},t.Array=function(l,u){(l=(l||[]).valueOf()).length==0&&u&&(l=u.valueOf()),this.value=this.parse(l)},t.extend(t.Array,{toString:function(){return this.value.join(" ")},valueOf:function(){return this.value},parse:function(l){return l=l.valueOf(),Array.isArray(l)?l:this.split(l)}}),t.PointArray=function(l,u){t.Array.call(this,l,u||[[0,0]])},t.PointArray.prototype=new t.Array,t.PointArray.prototype.constructor=t.PointArray;for(var i={M:function(l,u,m){return u.x=m.x=l[0],u.y=m.y=l[1],["M",u.x,u.y]},L:function(l,u){return u.x=l[0],u.y=l[1],["L",l[0],l[1]]},H:function(l,u){return u.x=l[0],["H",l[0]]},V:function(l,u){return u.y=l[0],["V",l[0]]},C:function(l,u){return u.x=l[4],u.y=l[5],["C",l[0],l[1],l[2],l[3],l[4],l[5]]},Q:function(l,u){return u.x=l[2],u.y=l[3],["Q",l[0],l[1],l[2],l[3]]},Z:function(l,u,m){return u.x=m.x,u.y=m.y,["Z"]}},a="mlhvqtcsaz".split(""),s=0,n=a.length;s<n;++s)i[a[s]]=function(l){return function(u,m,w){if(l=="H")u[0]=u[0]+m.x;else if(l=="V")u[0]=u[0]+m.y;else if(l=="A")u[5]=u[5]+m.x,u[6]=u[6]+m.y;else for(var S=0,P=u.length;S<P;++S)u[S]=u[S]+(S%2?m.y:m.x);return i[l](u,m,w)}}(a[s].toUpperCase());t.PathArray=function(l,u){t.Array.call(this,l,u||[["M",0,0]])},t.PathArray.prototype=new t.Array,t.PathArray.prototype.constructor=t.PathArray,t.extend(t.PathArray,{toString:function(){return function(l){for(var u=0,m=l.length,w="";u<m;u++)w+=l[u][0],l[u][1]!=null&&(w+=l[u][1],l[u][2]!=null&&(w+=" ",w+=l[u][2],l[u][3]!=null&&(w+=" ",w+=l[u][3],w+=" ",w+=l[u][4],l[u][5]!=null&&(w+=" ",w+=l[u][5],w+=" ",w+=l[u][6],l[u][7]!=null&&(w+=" ",w+=l[u][7])))));return w+" "}(this.value)},move:function(l,u){var m=this.bbox();return m.x,m.y,this},at:function(l){if(!this.destination)return this;for(var u=this.value,m=this.destination.value,w=[],S=new t.PathArray,P=0,C=u.length;P<C;P++){w[P]=[u[P][0]];for(var M=1,T=u[P].length;M<T;M++)w[P][M]=u[P][M]+(m[P][M]-u[P][M])*l;w[P][0]==="A"&&(w[P][4]=+(w[P][4]!=0),w[P][5]=+(w[P][5]!=0))}return S.value=w,S},parse:function(l){if(l instanceof t.PathArray)return l.valueOf();var u,m={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0};l=typeof l=="string"?l.replace(t.regex.numbersWithDots,o).replace(t.regex.pathLetters," $& ").replace(t.regex.hyphen,"$1 -").trim().split(t.regex.delimiter):l.reduce(function(T,z){return[].concat.call(T,z)},[]);var w=[],S=new t.Point,P=new t.Point,C=0,M=l.length;do t.regex.isPathLetter.test(l[C])?(u=l[C],++C):u=="M"?u="L":u=="m"&&(u="l"),w.push(i[u].call(null,l.slice(C,C+=m[u.toUpperCase()]).map(parseFloat),S,P));while(M>C);return w},bbox:function(){return t.parser.draw||t.prepare(),t.parser.path.setAttribute("d",this.toString()),t.parser.path.getBBox()}}),t.Number=t.invent({create:function(l,u){this.value=0,this.unit=u||"",typeof l=="number"?this.value=isNaN(l)?0:isFinite(l)?l:l<0?-34e37:34e37:typeof l=="string"?(u=l.match(t.regex.numberAndUnit))&&(this.value=parseFloat(u[1]),u[5]=="%"?this.value/=100:u[5]=="s"&&(this.value*=1e3),this.unit=u[5]):l instanceof t.Number&&(this.value=l.valueOf(),this.unit=l.unit)},extend:{toString:function(){return(this.unit=="%"?~~(1e8*this.value)/1e6:this.unit=="s"?this.value/1e3:this.value)+this.unit},toJSON:function(){return this.toString()},valueOf:function(){return this.value},plus:function(l){return l=new t.Number(l),new t.Number(this+l,this.unit||l.unit)},minus:function(l){return l=new t.Number(l),new t.Number(this-l,this.unit||l.unit)},times:function(l){return l=new t.Number(l),new t.Number(this*l,this.unit||l.unit)},divide:function(l){return l=new t.Number(l),new t.Number(this/l,this.unit||l.unit)},to:function(l){var u=new t.Number(this);return typeof l=="string"&&(u.unit=l),u},morph:function(l){return this.destination=new t.Number(l),l.relative&&(this.destination.value+=this.value),this},at:function(l){return this.destination?new t.Number(this.destination).minus(this).times(l).plus(this):this}}}),t.Element=t.invent({create:function(l){this._stroke=t.defaults.attrs.stroke,this._event=null,this.dom={},(this.node=l)&&(this.type=l.nodeName,this.node.instance=this,this._stroke=l.getAttribute("stroke")||this._stroke)},extend:{x:function(l){return this.attr("x",l)},y:function(l){return this.attr("y",l)},cx:function(l){return l==null?this.x()+this.width()/2:this.x(l-this.width()/2)},cy:function(l){return l==null?this.y()+this.height()/2:this.y(l-this.height()/2)},move:function(l,u){return this.x(l).y(u)},center:function(l,u){return this.cx(l).cy(u)},width:function(l){return this.attr("width",l)},height:function(l){return this.attr("height",l)},size:function(l,u){var m=g(this,l,u);return this.width(new t.Number(m.width)).height(new t.Number(m.height))},clone:function(l){this.writeDataToDom();var u=x(this.node.cloneNode(!0));return l?l.add(u):this.after(u),u},remove:function(){return this.parent()&&this.parent().removeElement(this),this},replace:function(l){return this.after(l).remove(),l},addTo:function(l){return l.put(this)},putIn:function(l){return l.add(this)},id:function(l){return this.attr("id",l)},show:function(){return this.style("display","")},hide:function(){return this.style("display","none")},visible:function(){return this.style("display")!="none"},toString:function(){return this.attr("id")},classes:function(){var l=this.attr("class");return l==null?[]:l.trim().split(t.regex.delimiter)},hasClass:function(l){return this.classes().indexOf(l)!=-1},addClass:function(l){if(!this.hasClass(l)){var u=this.classes();u.push(l),this.attr("class",u.join(" "))}return this},removeClass:function(l){return this.hasClass(l)&&this.attr("class",this.classes().filter(function(u){return u!=l}).join(" ")),this},toggleClass:function(l){return this.hasClass(l)?this.removeClass(l):this.addClass(l)},reference:function(l){return t.get(this.attr(l))},parent:function(l){var u=this;if(!u.node.parentNode)return null;if(u=t.adopt(u.node.parentNode),!l)return u;for(;u&&u.node instanceof v.SVGElement;){if(typeof l=="string"?u.matches(l):u instanceof l)return u;if(!u.node.parentNode||u.node.parentNode.nodeName=="#document")return null;u=t.adopt(u.node.parentNode)}},doc:function(){return this instanceof t.Doc?this:this.parent(t.Doc)},parents:function(l){var u=[],m=this;do{if(!(m=m.parent(l))||!m.node)break;u.push(m)}while(m.parent);return u},matches:function(l){return function(u,m){return(u.matches||u.matchesSelector||u.msMatchesSelector||u.mozMatchesSelector||u.webkitMatchesSelector||u.oMatchesSelector).call(u,m)}(this.node,l)},native:function(){return this.node},svg:function(l){var u=e.createElement("svg");if(!(l&&this instanceof t.Parent))return u.appendChild(l=e.createElement("svg")),this.writeDataToDom(),l.appendChild(this.node.cloneNode(!0)),u.innerHTML.replace(/^<svg>/,"").replace(/<\/svg>$/,"");u.innerHTML="<svg>"+l.replace(/\n/,"").replace(/<([\w:-]+)([^<]+?)\/>/g,"<$1$2></$1>")+"</svg>";for(var m=0,w=u.firstChild.childNodes.length;m<w;m++)this.node.appendChild(u.firstChild.firstChild);return this},writeDataToDom:function(){return(this.each||this.lines)&&(this.each?this:this.lines()).each(function(){this.writeDataToDom()}),this.node.removeAttribute("svgjs:data"),Object.keys(this.dom).length&&this.node.setAttribute("svgjs:data",JSON.stringify(this.dom)),this},setData:function(l){return this.dom=l,this},is:function(l){return function(u,m){return u instanceof m}(this,l)}}}),t.easing={"-":function(l){return l},"<>":function(l){return-Math.cos(l*Math.PI)/2+.5},">":function(l){return Math.sin(l*Math.PI/2)},"<":function(l){return 1-Math.cos(l*Math.PI/2)}},t.morph=function(l){return function(u,m){return new t.MorphObj(u,m).at(l)}},t.Situation=t.invent({create:function(l){this.init=!1,this.reversed=!1,this.reversing=!1,this.duration=new t.Number(l.duration).valueOf(),this.delay=new t.Number(l.delay).valueOf(),this.start=+new Date+this.delay,this.finish=this.start+this.duration,this.ease=l.ease,this.loop=0,this.loops=!1,this.animations={},this.attrs={},this.styles={},this.transforms=[],this.once={}}}),t.FX=t.invent({create:function(l){this._target=l,this.situations=[],this.active=!1,this.situation=null,this.paused=!1,this.lastPos=0,this.pos=0,this.absPos=0,this._speed=1},extend:{animate:function(l,u,m){N(l)==="object"&&(u=l.ease,m=l.delay,l=l.duration);var w=new t.Situation({duration:l||1e3,delay:m||0,ease:t.easing[u||"-"]||u});return this.queue(w),this},target:function(l){return l&&l instanceof t.Element?(this._target=l,this):this._target},timeToAbsPos:function(l){return(l-this.situation.start)/(this.situation.duration/this._speed)},absPosToTime:function(l){return this.situation.duration/this._speed*l+this.situation.start},startAnimFrame:function(){this.stopAnimFrame(),this.animationFrame=v.requestAnimationFrame((function(){this.step()}).bind(this))},stopAnimFrame:function(){v.cancelAnimationFrame(this.animationFrame)},start:function(){return!this.active&&this.situation&&(this.active=!0,this.startCurrent()),this},startCurrent:function(){return this.situation.start=+new Date+this.situation.delay/this._speed,this.situation.finish=this.situation.start+this.situation.duration/this._speed,this.initAnimations().step()},queue:function(l){return(typeof l=="function"||l instanceof t.Situation)&&this.situations.push(l),this.situation||(this.situation=this.situations.shift()),this},dequeue:function(){return this.stop(),this.situation=this.situations.shift(),this.situation&&(this.situation instanceof t.Situation?this.start():this.situation.call(this)),this},initAnimations:function(){var l,u=this.situation;if(u.init)return this;for(var m in u.animations){l=this.target()[m](),Array.isArray(l)||(l=[l]),Array.isArray(u.animations[m])||(u.animations[m]=[u.animations[m]]);for(var w=l.length;w--;)u.animations[m][w]instanceof t.Number&&(l[w]=new t.Number(l[w])),u.animations[m][w]=l[w].morph(u.animations[m][w])}for(var m in u.attrs)u.attrs[m]=new t.MorphObj(this.target().attr(m),u.attrs[m]);for(var m in u.styles)u.styles[m]=new t.MorphObj(this.target().style(m),u.styles[m]);return u.initialTransformation=this.target().matrixify(),u.init=!0,this},clearQueue:function(){return this.situations=[],this},clearCurrent:function(){return this.situation=null,this},stop:function(l,u){var m=this.active;return this.active=!1,u&&this.clearQueue(),l&&this.situation&&(!m&&this.startCurrent(),this.atEnd()),this.stopAnimFrame(),this.clearCurrent()},after:function(l){var u=this.last();return this.target().on("finished.fx",function m(w){w.detail.situation==u&&(l.call(this,u),this.off("finished.fx",m))}),this._callStart()},during:function(l){var u=this.last(),m=function(w){w.detail.situation==u&&l.call(this,w.detail.pos,t.morph(w.detail.pos),w.detail.eased,u)};return this.target().off("during.fx",m).on("during.fx",m),this.after(function(){this.off("during.fx",m)}),this._callStart()},afterAll:function(l){var u=function m(w){l.call(this),this.off("allfinished.fx",m)};return this.target().off("allfinished.fx",u).on("allfinished.fx",u),this._callStart()},last:function(){return this.situations.length?this.situations[this.situations.length-1]:this.situation},add:function(l,u,m){return this.last()[m||"animations"][l]=u,this._callStart()},step:function(l){var u,m,w;l||(this.absPos=this.timeToAbsPos(+new Date)),this.situation.loops!==!1?(u=Math.max(this.absPos,0),m=Math.floor(u),this.situation.loops===!0||m<this.situation.loops?(this.pos=u-m,w=this.situation.loop,this.situation.loop=m):(this.absPos=this.situation.loops,this.pos=1,w=this.situation.loop-1,this.situation.loop=this.situation.loops),this.situation.reversing&&(this.situation.reversed=this.situation.reversed!=!!((this.situation.loop-w)%2))):(this.absPos=Math.min(this.absPos,1),this.pos=this.absPos),this.pos<0&&(this.pos=0),this.situation.reversed&&(this.pos=1-this.pos);var S=this.situation.ease(this.pos);for(var P in this.situation.once)P>this.lastPos&&P<=S&&(this.situation.once[P].call(this.target(),this.pos,S),delete this.situation.once[P]);return this.active&&this.target().fire("during",{pos:this.pos,eased:S,fx:this,situation:this.situation}),this.situation?(this.eachAt(),this.pos==1&&!this.situation.reversed||this.situation.reversed&&this.pos==0?(this.stopAnimFrame(),this.target().fire("finished",{fx:this,situation:this.situation}),this.situations.length||(this.target().fire("allfinished"),this.situations.length||(this.target().off(".fx"),this.active=!1)),this.active?this.dequeue():this.clearCurrent()):!this.paused&&this.active&&this.startAnimFrame(),this.lastPos=S,this):this},eachAt:function(){var l,u=this,m=this.target(),w=this.situation;for(var S in w.animations)l=[].concat(w.animations[S]).map(function(M){return typeof M!="string"&&M.at?M.at(w.ease(u.pos),u.pos):M}),m[S].apply(m,l);for(var S in w.attrs)l=[S].concat(w.attrs[S]).map(function(T){return typeof T!="string"&&T.at?T.at(w.ease(u.pos),u.pos):T}),m.attr.apply(m,l);for(var S in w.styles)l=[S].concat(w.styles[S]).map(function(T){return typeof T!="string"&&T.at?T.at(w.ease(u.pos),u.pos):T}),m.style.apply(m,l);if(w.transforms.length){l=w.initialTransformation,S=0;for(var P=w.transforms.length;S<P;S++){var C=w.transforms[S];C instanceof t.Matrix?l=C.relative?l.multiply(new t.Matrix().morph(C).at(w.ease(this.pos))):l.morph(C).at(w.ease(this.pos)):(C.relative||C.undo(l.extract()),l=l.multiply(C.at(w.ease(this.pos))))}m.matrix(l)}return this},once:function(l,u,m){var w=this.last();return m||(l=w.ease(l)),w.once[l]=u,this},_callStart:function(){return setTimeout((function(){this.start()}).bind(this),0),this}},parent:t.Element,construct:{animate:function(l,u,m){return(this.fx||(this.fx=new t.FX(this))).animate(l,u,m)},delay:function(l){return(this.fx||(this.fx=new t.FX(this))).delay(l)},stop:function(l,u){return this.fx&&this.fx.stop(l,u),this},finish:function(){return this.fx&&this.fx.finish(),this}}}),t.MorphObj=t.invent({create:function(l,u){return t.Color.isColor(u)?new t.Color(l).morph(u):t.regex.delimiter.test(l)?t.regex.pathLetters.test(l)?new t.PathArray(l).morph(u):new t.Array(l).morph(u):t.regex.numberAndUnit.test(u)?new t.Number(l).morph(u):(this.value=l,void(this.destination=u))},extend:{at:function(l,u){return u<1?this.value:this.destination},valueOf:function(){return this.value}}}),t.extend(t.FX,{attr:function(l,u,m){if(N(l)==="object")for(var w in l)this.attr(w,l[w]);else this.add(l,u,"attrs");return this},plot:function(l,u,m,w){return arguments.length==4?this.plot([l,u,m,w]):this.add("plot",new(this.target()).morphArray(l))}}),t.Box=t.invent({create:function(l,u,m,w){if(!(N(l)!=="object"||l instanceof t.Element))return t.Box.call(this,l.left!=null?l.left:l.x,l.top!=null?l.top:l.y,l.width,l.height);arguments.length==4&&(this.x=l,this.y=u,this.width=m,this.height=w),b(this)}}),t.BBox=t.invent({create:function(l){if(t.Box.apply(this,[].slice.call(arguments)),l instanceof t.Element){var u;try{if(!e.documentElement.contains){for(var m=l.node;m.parentNode;)m=m.parentNode;if(m!=e)throw new Error("Element not in the dom")}u=l.node.getBBox()}catch{if(l instanceof t.Shape){t.parser.draw||t.prepare();var w=l.clone(t.parser.draw.instance).show();u=w.node.getBBox(),w.remove()}else u={x:l.node.clientLeft,y:l.node.clientTop,width:l.node.clientWidth,height:l.node.clientHeight}}t.Box.call(this,u)}},inherit:t.Box,parent:t.Element,construct:{bbox:function(){return new t.BBox(this)}}}),t.BBox.prototype.constructor=t.BBox,t.Matrix=t.invent({create:function(l){var u=p([1,0,0,1,0,0]);l=l===null?u:l instanceof t.Element?l.matrixify():typeof l=="string"?p(l.split(t.regex.delimiter).map(parseFloat)):arguments.length==6?p([].slice.call(arguments)):Array.isArray(l)?p(l):l&&N(l)==="object"?l:u;for(var m=k.length-1;m>=0;--m)this[k[m]]=l[k[m]]!=null?l[k[m]]:u[k[m]]},extend:{extract:function(){var l=f(this,0,1);f(this,1,0);var u=180/Math.PI*Math.atan2(l.y,l.x)-90;return{x:this.e,y:this.f,transformedX:(this.e*Math.cos(u*Math.PI/180)+this.f*Math.sin(u*Math.PI/180))/Math.sqrt(this.a*this.a+this.b*this.b),transformedY:(this.f*Math.cos(u*Math.PI/180)+this.e*Math.sin(-u*Math.PI/180))/Math.sqrt(this.c*this.c+this.d*this.d),rotation:u,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f,matrix:new t.Matrix(this)}},clone:function(){return new t.Matrix(this)},morph:function(l){return this.destination=new t.Matrix(l),this},multiply:function(l){return new t.Matrix(this.native().multiply(function(u){return u instanceof t.Matrix||(u=new t.Matrix(u)),u}(l).native()))},inverse:function(){return new t.Matrix(this.native().inverse())},translate:function(l,u){return new t.Matrix(this.native().translate(l||0,u||0))},native:function(){for(var l=t.parser.native.createSVGMatrix(),u=k.length-1;u>=0;u--)l[k[u]]=this[k[u]];return l},toString:function(){return"matrix("+y(this.a)+","+y(this.b)+","+y(this.c)+","+y(this.d)+","+y(this.e)+","+y(this.f)+")"}},parent:t.Element,construct:{ctm:function(){return new t.Matrix(this.node.getCTM())},screenCTM:function(){if(this instanceof t.Nested){var l=this.rect(1,1),u=l.node.getScreenCTM();return l.remove(),new t.Matrix(u)}return new t.Matrix(this.node.getScreenCTM())}}}),t.Point=t.invent({create:function(l,u){var m;m=Array.isArray(l)?{x:l[0],y:l[1]}:N(l)==="object"?{x:l.x,y:l.y}:l!=null?{x:l,y:u??l}:{x:0,y:0},this.x=m.x,this.y=m.y},extend:{clone:function(){return new t.Point(this)},morph:function(l,u){return this.destination=new t.Point(l,u),this}}}),t.extend(t.Element,{point:function(l,u){return new t.Point(l,u).transform(this.screenCTM().inverse())}}),t.extend(t.Element,{attr:function(l,u,m){if(l==null){for(l={},m=(u=this.node.attributes).length-1;m>=0;m--)l[u[m].nodeName]=t.regex.isNumber.test(u[m].nodeValue)?parseFloat(u[m].nodeValue):u[m].nodeValue;return l}if(N(l)==="object")for(var w in l)this.attr(w,l[w]);else if(u===null)this.node.removeAttribute(l);else{if(u==null)return(u=this.node.getAttribute(l))==null?t.defaults.attrs[l]:t.regex.isNumber.test(u)?parseFloat(u):u;l=="stroke-width"?this.attr("stroke",parseFloat(u)>0?this._stroke:null):l=="stroke"&&(this._stroke=u),l!="fill"&&l!="stroke"||(t.regex.isImage.test(u)&&(u=this.doc().defs().image(u,0,0)),u instanceof t.Image&&(u=this.doc().defs().pattern(0,0,function(){this.add(u)}))),typeof u=="number"?u=new t.Number(u):t.Color.isColor(u)?u=new t.Color(u):Array.isArray(u)&&(u=new t.Array(u)),l=="leading"?this.leading&&this.leading(u):typeof m=="string"?this.node.setAttributeNS(m,l,u.toString()):this.node.setAttribute(l,u.toString()),!this.rebuild||l!="font-size"&&l!="x"||this.rebuild(l,u)}return this}}),t.extend(t.Element,{transform:function(l,u){var m;return N(l)!=="object"?(m=new t.Matrix(this).extract(),typeof l=="string"?m[l]:m):(m=new t.Matrix(this),u=!!u||!!l.relative,l.a!=null&&(m=u?m.multiply(new t.Matrix(l)):new t.Matrix(l)),this.attr("transform",m))}}),t.extend(t.Element,{untransform:function(){return this.attr("transform",null)},matrixify:function(){return(this.attr("transform")||"").split(t.regex.transforms).slice(0,-1).map(function(l){var u=l.trim().split("(");return[u[0],u[1].split(t.regex.delimiter).map(function(m){return parseFloat(m)})]}).reduce(function(l,u){return u[0]=="matrix"?l.multiply(p(u[1])):l[u[0]].apply(l,u[1])},new t.Matrix)},toParent:function(l){if(this==l)return this;var u=this.screenCTM(),m=l.screenCTM().inverse();return this.addTo(l).untransform().transform(m.multiply(u)),this},toDoc:function(){return this.toParent(this.doc())}}),t.Transformation=t.invent({create:function(l,u){if(arguments.length>1&&typeof u!="boolean")return this.constructor.call(this,[].slice.call(arguments));if(Array.isArray(l))for(var m=0,w=this.arguments.length;m<w;++m)this[this.arguments[m]]=l[m];else if(l&&N(l)==="object")for(m=0,w=this.arguments.length;m<w;++m)this[this.arguments[m]]=l[this.arguments[m]];this.inversed=!1,u===!0&&(this.inversed=!0)}}),t.Translate=t.invent({parent:t.Matrix,inherit:t.Transformation,create:function(l,u){this.constructor.apply(this,[].slice.call(arguments))},extend:{arguments:["transformedX","transformedY"],method:"translate"}}),t.extend(t.Element,{style:function(l,u){if(arguments.length==0)return this.node.style.cssText||"";if(arguments.length<2)if(N(l)==="object")for(var m in l)this.style(m,l[m]);else{if(!t.regex.isCss.test(l))return this.node.style[h(l)];for(l=l.split(/\s*;\s*/).filter(function(w){return!!w}).map(function(w){return w.split(/\s*:\s*/)});u=l.pop();)this.style(u[0],u[1])}else this.node.style[h(l)]=u===null||t.regex.isBlank.test(u)?"":u;return this}}),t.Parent=t.invent({create:function(l){this.constructor.call(this,l)},inherit:t.Element,extend:{children:function(){return t.utils.map(t.utils.filterSVGElements(this.node.childNodes),function(l){return t.adopt(l)})},add:function(l,u){return u==null?this.node.appendChild(l.node):l.node!=this.node.childNodes[u]&&this.node.insertBefore(l.node,this.node.childNodes[u]),this},put:function(l,u){return this.add(l,u),l},has:function(l){return this.index(l)>=0},index:function(l){return[].slice.call(this.node.childNodes).indexOf(l.node)},get:function(l){return t.adopt(this.node.childNodes[l])},first:function(){return this.get(0)},last:function(){return this.get(this.node.childNodes.length-1)},each:function(l,u){for(var m=this.children(),w=0,S=m.length;w<S;w++)m[w]instanceof t.Element&&l.apply(m[w],[w,m]),u&&m[w]instanceof t.Container&&m[w].each(l,u);return this},removeElement:function(l){return this.node.removeChild(l.node),this},clear:function(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return delete this._defs,this},defs:function(){return this.doc().defs()}}}),t.extend(t.Parent,{ungroup:function(l,u){return u===0||this instanceof t.Defs||this.node==t.parser.draw||(l=l||(this instanceof t.Doc?this:this.parent(t.Parent)),u=u||1/0,this.each(function(){return this instanceof t.Defs?this:this instanceof t.Parent?this.ungroup(l,u-1):this.toParent(l)}),this.node.firstChild||this.remove()),this},flatten:function(l,u){return this.ungroup(l,u)}}),t.Container=t.invent({create:function(l){this.constructor.call(this,l)},inherit:t.Parent}),t.ViewBox=t.invent({parent:t.Container,construct:{}}),["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","touchstart","touchmove","touchleave","touchend","touchcancel"].forEach(function(l){t.Element.prototype[l]=function(u){return t.on(this.node,l,u),this}}),t.listeners=[],t.handlerMap=[],t.listenerId=0,t.on=function(l,u,m,w,S){var P=m.bind(w||l.instance||l),C=(t.handlerMap.indexOf(l)+1||t.handlerMap.push(l))-1,M=u.split(".")[0],T=u.split(".")[1]||"*";t.listeners[C]=t.listeners[C]||{},t.listeners[C][M]=t.listeners[C][M]||{},t.listeners[C][M][T]=t.listeners[C][M][T]||{},m._svgjsListenerId||(m._svgjsListenerId=++t.listenerId),t.listeners[C][M][T][m._svgjsListenerId]=P,l.addEventListener(M,P,S||{passive:!0})},t.off=function(l,u,m){var w=t.handlerMap.indexOf(l),S=u&&u.split(".")[0],P=u&&u.split(".")[1],C="";if(w!=-1)if(m){if(typeof m=="function"&&(m=m._svgjsListenerId),!m)return;t.listeners[w][S]&&t.listeners[w][S][P||"*"]&&(l.removeEventListener(S,t.listeners[w][S][P||"*"][m],!1),delete t.listeners[w][S][P||"*"][m])}else if(P&&S){if(t.listeners[w][S]&&t.listeners[w][S][P]){for(var M in t.listeners[w][S][P])t.off(l,[S,P].join("."),M);delete t.listeners[w][S][P]}}else if(P)for(var T in t.listeners[w])for(var C in t.listeners[w][T])P===C&&t.off(l,[T,P].join("."));else if(S){if(t.listeners[w][S]){for(var C in t.listeners[w][S])t.off(l,[S,C].join("."));delete t.listeners[w][S]}}else{for(var T in t.listeners[w])t.off(l,T);delete t.listeners[w],delete t.handlerMap[w]}},t.extend(t.Element,{on:function(l,u,m,w){return t.on(this.node,l,u,m,w),this},off:function(l,u){return t.off(this.node,l,u),this},fire:function(l,u){return l instanceof v.Event?this.node.dispatchEvent(l):this.node.dispatchEvent(l=new t.CustomEvent(l,{detail:u,cancelable:!0})),this._event=l,this},event:function(){return this._event}}),t.Defs=t.invent({create:"defs",inherit:t.Container}),t.G=t.invent({create:"g",inherit:t.Container,extend:{x:function(l){return l==null?this.transform("x"):this.transform({x:l-this.x()},!0)}},construct:{group:function(){return this.put(new t.G)}}}),t.Doc=t.invent({create:function(l){l&&((l=typeof l=="string"?e.getElementById(l):l).nodeName=="svg"?this.constructor.call(this,l):(this.constructor.call(this,t.create("svg")),l.appendChild(this.node),this.size("100%","100%")),this.namespace().defs())},inherit:t.Container,extend:{namespace:function(){return this.attr({xmlns:t.ns,version:"1.1"}).attr("xmlns:xlink",t.xlink,t.xmlns).attr("xmlns:svgjs",t.svgjs,t.xmlns)},defs:function(){var l;return this._defs||((l=this.node.getElementsByTagName("defs")[0])?this._defs=t.adopt(l):this._defs=new t.Defs,this.node.appendChild(this._defs.node)),this._defs},parent:function(){return this.node.parentNode&&this.node.parentNode.nodeName!="#document"?this.node.parentNode:null},remove:function(){return this.parent()&&this.parent().removeChild(this.node),this},clear:function(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return delete this._defs,t.parser.draw&&!t.parser.draw.parentNode&&this.node.appendChild(t.parser.draw),this},clone:function(l){this.writeDataToDom();var u=this.node,m=x(u.cloneNode(!0));return l?(l.node||l).appendChild(m.node):u.parentNode.insertBefore(m.node,u.nextSibling),m}}}),t.extend(t.Element,{}),t.Gradient=t.invent({create:function(l){this.constructor.call(this,t.create(l+"Gradient")),this.type=l},inherit:t.Container,extend:{at:function(l,u,m){return this.put(new t.Stop).update(l,u,m)},update:function(l){return this.clear(),typeof l=="function"&&l.call(this,this),this},fill:function(){return"url(#"+this.id()+")"},toString:function(){return this.fill()},attr:function(l,u,m){return l=="transform"&&(l="gradientTransform"),t.Container.prototype.attr.call(this,l,u,m)}},construct:{gradient:function(l,u){return this.defs().gradient(l,u)}}}),t.extend(t.Gradient,t.FX,{from:function(l,u){return(this._target||this).type=="radial"?this.attr({fx:new t.Number(l),fy:new t.Number(u)}):this.attr({x1:new t.Number(l),y1:new t.Number(u)})},to:function(l,u){return(this._target||this).type=="radial"?this.attr({cx:new t.Number(l),cy:new t.Number(u)}):this.attr({x2:new t.Number(l),y2:new t.Number(u)})}}),t.extend(t.Defs,{gradient:function(l,u){return this.put(new t.Gradient(l)).update(u)}}),t.Stop=t.invent({create:"stop",inherit:t.Element,extend:{update:function(l){return(typeof l=="number"||l instanceof t.Number)&&(l={offset:arguments[0],color:arguments[1],opacity:arguments[2]}),l.opacity!=null&&this.attr("stop-opacity",l.opacity),l.color!=null&&this.attr("stop-color",l.color),l.offset!=null&&this.attr("offset",new t.Number(l.offset)),this}}}),t.Pattern=t.invent({create:"pattern",inherit:t.Container,extend:{fill:function(){return"url(#"+this.id()+")"},update:function(l){return this.clear(),typeof l=="function"&&l.call(this,this),this},toString:function(){return this.fill()},attr:function(l,u,m){return l=="transform"&&(l="patternTransform"),t.Container.prototype.attr.call(this,l,u,m)}},construct:{pattern:function(l,u,m){return this.defs().pattern(l,u,m)}}}),t.extend(t.Defs,{pattern:function(l,u,m){return this.put(new t.Pattern).update(m).attr({x:0,y:0,width:l,height:u,patternUnits:"userSpaceOnUse"})}}),t.Shape=t.invent({create:function(l){this.constructor.call(this,l)},inherit:t.Element}),t.Symbol=t.invent({create:"symbol",inherit:t.Container,construct:{symbol:function(){return this.put(new t.Symbol)}}}),t.Use=t.invent({create:"use",inherit:t.Shape,extend:{element:function(l,u){return this.attr("href",(u||"")+"#"+l,t.xlink)}},construct:{use:function(l,u){return this.put(new t.Use).element(l,u)}}}),t.Rect=t.invent({create:"rect",inherit:t.Shape,construct:{rect:function(l,u){return this.put(new t.Rect).size(l,u)}}}),t.Circle=t.invent({create:"circle",inherit:t.Shape,construct:{circle:function(l){return this.put(new t.Circle).rx(new t.Number(l).divide(2)).move(0,0)}}}),t.extend(t.Circle,t.FX,{rx:function(l){return this.attr("r",l)},ry:function(l){return this.rx(l)}}),t.Ellipse=t.invent({create:"ellipse",inherit:t.Shape,construct:{ellipse:function(l,u){return this.put(new t.Ellipse).size(l,u).move(0,0)}}}),t.extend(t.Ellipse,t.Rect,t.FX,{rx:function(l){return this.attr("rx",l)},ry:function(l){return this.attr("ry",l)}}),t.extend(t.Circle,t.Ellipse,{x:function(l){return l==null?this.cx()-this.rx():this.cx(l+this.rx())},y:function(l){return l==null?this.cy()-this.ry():this.cy(l+this.ry())},cx:function(l){return l==null?this.attr("cx"):this.attr("cx",l)},cy:function(l){return l==null?this.attr("cy"):this.attr("cy",l)},width:function(l){return l==null?2*this.rx():this.rx(new t.Number(l).divide(2))},height:function(l){return l==null?2*this.ry():this.ry(new t.Number(l).divide(2))},size:function(l,u){var m=g(this,l,u);return this.rx(new t.Number(m.width).divide(2)).ry(new t.Number(m.height).divide(2))}}),t.Line=t.invent({create:"line",inherit:t.Shape,extend:{array:function(){return new t.PointArray([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])},plot:function(l,u,m,w){return l==null?this.array():(l=u!==void 0?{x1:l,y1:u,x2:m,y2:w}:new t.PointArray(l).toLine(),this.attr(l))},move:function(l,u){return this.attr(this.array().move(l,u).toLine())},size:function(l,u){var m=g(this,l,u);return this.attr(this.array().size(m.width,m.height).toLine())}},construct:{line:function(l,u,m,w){return t.Line.prototype.plot.apply(this.put(new t.Line),l!=null?[l,u,m,w]:[0,0,0,0])}}}),t.Polyline=t.invent({create:"polyline",inherit:t.Shape,construct:{polyline:function(l){return this.put(new t.Polyline).plot(l||new t.PointArray)}}}),t.Polygon=t.invent({create:"polygon",inherit:t.Shape,construct:{polygon:function(l){return this.put(new t.Polygon).plot(l||new t.PointArray)}}}),t.extend(t.Polyline,t.Polygon,{array:function(){return this._array||(this._array=new t.PointArray(this.attr("points")))},plot:function(l){return l==null?this.array():this.clear().attr("points",typeof l=="string"?l:this._array=new t.PointArray(l))},clear:function(){return delete this._array,this},move:function(l,u){return this.attr("points",this.array().move(l,u))},size:function(l,u){var m=g(this,l,u);return this.attr("points",this.array().size(m.width,m.height))}}),t.extend(t.Line,t.Polyline,t.Polygon,{morphArray:t.PointArray,x:function(l){return l==null?this.bbox().x:this.move(l,this.bbox().y)},y:function(l){return l==null?this.bbox().y:this.move(this.bbox().x,l)},width:function(l){var u=this.bbox();return l==null?u.width:this.size(l,u.height)},height:function(l){var u=this.bbox();return l==null?u.height:this.size(u.width,l)}}),t.Path=t.invent({create:"path",inherit:t.Shape,extend:{morphArray:t.PathArray,array:function(){return this._array||(this._array=new t.PathArray(this.attr("d")))},plot:function(l){return l==null?this.array():this.clear().attr("d",typeof l=="string"?l:this._array=new t.PathArray(l))},clear:function(){return delete this._array,this}},construct:{path:function(l){return this.put(new t.Path).plot(l||new t.PathArray)}}}),t.Image=t.invent({create:"image",inherit:t.Shape,extend:{load:function(l){if(!l)return this;var u=this,m=new v.Image;return t.on(m,"load",function(){t.off(m);var w=u.parent(t.Pattern);w!==null&&(u.width()==0&&u.height()==0&&u.size(m.width,m.height),w&&w.width()==0&&w.height()==0&&w.size(u.width(),u.height()),typeof u._loaded=="function"&&u._loaded.call(u,{width:m.width,height:m.height,ratio:m.width/m.height,url:l}))}),t.on(m,"error",function(w){t.off(m),typeof u._error=="function"&&u._error.call(u,w)}),this.attr("href",m.src=this.src=l,t.xlink)},loaded:function(l){return this._loaded=l,this},error:function(l){return this._error=l,this}},construct:{image:function(l,u,m){return this.put(new t.Image).load(l).size(u||0,m||u||0)}}}),t.Text=t.invent({create:function(){this.constructor.call(this,t.create("text")),this.dom.leading=new t.Number(1.3),this._rebuild=!0,this._build=!1,this.attr("font-family",t.defaults.attrs["font-family"])},inherit:t.Shape,extend:{x:function(l){return l==null?this.attr("x"):this.attr("x",l)},text:function(l){if(l===void 0){l="";for(var u=this.node.childNodes,m=0,w=u.length;m<w;++m)m!=0&&u[m].nodeType!=3&&t.adopt(u[m]).dom.newLined==1&&(l+=`
`),l+=u[m].textContent;return l}if(this.clear().build(!0),typeof l=="function")l.call(this,this);else{m=0;for(var S=(l=l.split(`
`)).length;m<S;m++)this.tspan(l[m]).newLine()}return this.build(!1).rebuild()},size:function(l){return this.attr("font-size",l).rebuild()},leading:function(l){return l==null?this.dom.leading:(this.dom.leading=new t.Number(l),this.rebuild())},lines:function(){var l=(this.textPath&&this.textPath()||this).node,u=t.utils.map(t.utils.filterSVGElements(l.childNodes),function(m){return t.adopt(m)});return new t.Set(u)},rebuild:function(l){if(typeof l=="boolean"&&(this._rebuild=l),this._rebuild){var u=this,m=0,w=this.dom.leading*new t.Number(this.attr("font-size"));this.lines().each(function(){this.dom.newLined&&(u.textPath()||this.attr("x",u.attr("x")),this.text()==`
`?m+=w:(this.attr("dy",w+m),m=0))}),this.fire("rebuild")}return this},build:function(l){return this._build=!!l,this},setData:function(l){return this.dom=l,this.dom.leading=new t.Number(l.leading||1.3),this}},construct:{text:function(l){return this.put(new t.Text).text(l)},plain:function(l){return this.put(new t.Text).plain(l)}}}),t.Tspan=t.invent({create:"tspan",inherit:t.Shape,extend:{text:function(l){return l==null?this.node.textContent+(this.dom.newLined?`
`:""):(typeof l=="function"?l.call(this,this):this.plain(l),this)},dx:function(l){return this.attr("dx",l)},dy:function(l){return this.attr("dy",l)},newLine:function(){var l=this.parent(t.Text);return this.dom.newLined=!0,this.dy(l.dom.leading*l.attr("font-size")).attr("x",l.x())}}}),t.extend(t.Text,t.Tspan,{plain:function(l){return this._build===!1&&this.clear(),this.node.appendChild(e.createTextNode(l)),this},tspan:function(l){var u=(this.textPath&&this.textPath()||this).node,m=new t.Tspan;return this._build===!1&&this.clear(),u.appendChild(m.node),m.text(l)},clear:function(){for(var l=(this.textPath&&this.textPath()||this).node;l.hasChildNodes();)l.removeChild(l.lastChild);return this},length:function(){return this.node.getComputedTextLength()}}),t.TextPath=t.invent({create:"textPath",inherit:t.Parent,parent:t.Text,construct:{morphArray:t.PathArray,array:function(){var l=this.track();return l?l.array():null},plot:function(l){var u=this.track(),m=null;return u&&(m=u.plot(l)),l==null?m:this},track:function(){var l=this.textPath();if(l)return l.reference("href")},textPath:function(){if(this.node.firstChild&&this.node.firstChild.nodeName=="textPath")return t.adopt(this.node.firstChild)}}}),t.Nested=t.invent({create:function(){this.constructor.call(this,t.create("svg")),this.style("overflow","visible")},inherit:t.Container,construct:{nested:function(){return this.put(new t.Nested)}}});var r={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(l,u){return u=="color"?l:l+"-"+u}};function o(l,u,m,w){return m+w.replace(t.regex.dots," .")}function h(l){return l.toLowerCase().replace(/-(.)/g,function(u,m){return m.toUpperCase()})}function c(l){return l.charAt(0).toUpperCase()+l.slice(1)}function d(l){var u=l.toString(16);return u.length==1?"0"+u:u}function g(l,u,m){if(u==null||m==null){var w=l.bbox();u==null?u=w.width/w.height*m:m==null&&(m=w.height/w.width*u)}return{width:u,height:m}}function f(l,u,m){return{x:u*l.a+m*l.c+0,y:u*l.b+m*l.d+0}}function p(l){return{a:l[0],b:l[1],c:l[2],d:l[3],e:l[4],f:l[5]}}function x(l){for(var u=l.childNodes.length-1;u>=0;u--)l.childNodes[u]instanceof v.SVGElement&&x(l.childNodes[u]);return t.adopt(l).id(t.eid(l.nodeName))}function b(l){return l.x==null&&(l.x=0,l.y=0,l.width=0,l.height=0),l.w=l.width,l.h=l.height,l.x2=l.x+l.width,l.y2=l.y+l.height,l.cx=l.x+l.width/2,l.cy=l.y+l.height/2,l}function y(l){return Math.abs(l)>1e-37?l:0}["fill","stroke"].forEach(function(l){var u={};u[l]=function(m){if(m===void 0)return this;if(typeof m=="string"||t.Color.isRgb(m)||m&&typeof m.fill=="function")this.attr(l,m);else for(var w=r[l].length-1;w>=0;w--)m[r[l][w]]!=null&&this.attr(r.prefix(l,r[l][w]),m[r[l][w]]);return this},t.extend(t.Element,t.FX,u)}),t.extend(t.Element,t.FX,{translate:function(l,u){return this.transform({x:l,y:u})},matrix:function(l){return this.attr("transform",new t.Matrix(arguments.length==6?[].slice.call(arguments):l))},opacity:function(l){return this.attr("opacity",l)},dx:function(l){return this.x(new t.Number(l).plus(this instanceof t.FX?0:this.x()),!0)},dy:function(l){return this.y(new t.Number(l).plus(this instanceof t.FX?0:this.y()),!0)}}),t.extend(t.Path,{length:function(){return this.node.getTotalLength()},pointAt:function(l){return this.node.getPointAtLength(l)}}),t.Set=t.invent({create:function(l){Array.isArray(l)?this.members=l:this.clear()},extend:{add:function(){for(var l=[].slice.call(arguments),u=0,m=l.length;u<m;u++)this.members.push(l[u]);return this},remove:function(l){var u=this.index(l);return u>-1&&this.members.splice(u,1),this},each:function(l){for(var u=0,m=this.members.length;u<m;u++)l.apply(this.members[u],[u,this.members]);return this},clear:function(){return this.members=[],this},length:function(){return this.members.length},has:function(l){return this.index(l)>=0},index:function(l){return this.members.indexOf(l)},get:function(l){return this.members[l]},first:function(){return this.get(0)},last:function(){return this.get(this.members.length-1)},valueOf:function(){return this.members}},construct:{set:function(l){return new t.Set(l)}}}),t.FX.Set=t.invent({create:function(l){this.set=l}}),t.Set.inherit=function(){var l=[];for(var u in t.Shape.prototype)typeof t.Shape.prototype[u]=="function"&&typeof t.Set.prototype[u]!="function"&&l.push(u);for(var u in l.forEach(function(w){t.Set.prototype[w]=function(){for(var S=0,P=this.members.length;S<P;S++)this.members[S]&&typeof this.members[S][w]=="function"&&this.members[S][w].apply(this.members[S],arguments);return w=="animate"?this.fx||(this.fx=new t.FX.Set(this)):this}}),l=[],t.FX.prototype)typeof t.FX.prototype[u]=="function"&&typeof t.FX.Set.prototype[u]!="function"&&l.push(u);l.forEach(function(m){t.FX.Set.prototype[m]=function(){for(var w=0,S=this.set.members.length;w<S;w++)this.set.members[w].fx[m].apply(this.set.members[w].fx,arguments);return this}})},t.extend(t.Element,{}),t.extend(t.Element,{remember:function(l,u){if(N(arguments[0])==="object")for(var m in l)this.remember(m,l[m]);else{if(arguments.length==1)return this.memory()[l];this.memory()[l]=u}return this},forget:function(){if(arguments.length==0)this._memory={};else for(var l=arguments.length-1;l>=0;l--)delete this.memory()[arguments[l]];return this},memory:function(){return this._memory||(this._memory={})}}),t.get=function(l){var u=e.getElementById(function(m){var w=(m||"").toString().match(t.regex.reference);if(w)return w[1]}(l)||l);return t.adopt(u)},t.select=function(l,u){return new t.Set(t.utils.map((u||e).querySelectorAll(l),function(m){return t.adopt(m)}))},t.extend(t.Parent,{select:function(l){return t.select(l,this.node)}});var k="abcdef".split("");if(typeof v.CustomEvent!="function"){var A=function(l,u){u=u||{bubbles:!1,cancelable:!1,detail:void 0};var m=e.createEvent("CustomEvent");return m.initCustomEvent(l,u.bubbles,u.cancelable,u.detail),m};A.prototype=v.Event.prototype,t.CustomEvent=A}else t.CustomEvent=v.CustomEvent;return t},N(qe)==="object"?pe.exports=Q.document?fe(Q,Q.document):function(v){return fe(v,v.document)}:Q.SVG=fe(Q,Q.document),(function(){SVG.Filter=SVG.invent({create:"filter",inherit:SVG.Parent,extend:{source:"SourceGraphic",sourceAlpha:"SourceAlpha",background:"BackgroundImage",backgroundAlpha:"BackgroundAlpha",fill:"FillPaint",stroke:"StrokePaint",autoSetIn:!0,put:function(r,o){return this.add(r,o),!r.attr("in")&&this.autoSetIn&&r.attr("in",this.source),r.attr("result")||r.attr("result",r),r},blend:function(r,o,h){return this.put(new SVG.BlendEffect(r,o,h))},colorMatrix:function(r,o){return this.put(new SVG.ColorMatrixEffect(r,o))},convolveMatrix:function(r){return this.put(new SVG.ConvolveMatrixEffect(r))},componentTransfer:function(r){return this.put(new SVG.ComponentTransferEffect(r))},composite:function(r,o,h){return this.put(new SVG.CompositeEffect(r,o,h))},flood:function(r,o){return this.put(new SVG.FloodEffect(r,o))},offset:function(r,o){return this.put(new SVG.OffsetEffect(r,o))},image:function(r){return this.put(new SVG.ImageEffect(r))},merge:function(){var r=[void 0];for(var o in arguments)r.push(arguments[o]);return this.put(new(SVG.MergeEffect.bind.apply(SVG.MergeEffect,r)))},gaussianBlur:function(r,o){return this.put(new SVG.GaussianBlurEffect(r,o))},morphology:function(r,o){return this.put(new SVG.MorphologyEffect(r,o))},diffuseLighting:function(r,o,h){return this.put(new SVG.DiffuseLightingEffect(r,o,h))},displacementMap:function(r,o,h,c,d){return this.put(new SVG.DisplacementMapEffect(r,o,h,c,d))},specularLighting:function(r,o,h,c){return this.put(new SVG.SpecularLightingEffect(r,o,h,c))},tile:function(){return this.put(new SVG.TileEffect)},turbulence:function(r,o,h,c,d){return this.put(new SVG.TurbulenceEffect(r,o,h,c,d))},toString:function(){return"url(#"+this.attr("id")+")"}}}),SVG.extend(SVG.Defs,{filter:function(r){var o=this.put(new SVG.Filter);return typeof r=="function"&&r.call(o,o),o}}),SVG.extend(SVG.Container,{filter:function(r){return this.defs().filter(r)}}),SVG.extend(SVG.Element,SVG.G,SVG.Nested,{filter:function(r){return this.filterer=r instanceof SVG.Element?r:this.doc().filter(r),this.doc()&&this.filterer.doc()!==this.doc()&&this.doc().defs().add(this.filterer),this.attr("filter",this.filterer),this.filterer},unfilter:function(r){return this.filterer&&r===!0&&this.filterer.remove(),delete this.filterer,this.attr("filter",null)}}),SVG.Effect=SVG.invent({create:function(){this.constructor.call(this)},inherit:SVG.Element,extend:{in:function(r){return r==null?this.parent()&&this.parent().select('[result="'+this.attr("in")+'"]').get(0)||this.attr("in"):this.attr("in",r)},result:function(r){return r==null?this.attr("result"):this.attr("result",r)},toString:function(){return this.result()}}}),SVG.ParentEffect=SVG.invent({create:function(){this.constructor.call(this)},inherit:SVG.Parent,extend:{in:function(r){return r==null?this.parent()&&this.parent().select('[result="'+this.attr("in")+'"]').get(0)||this.attr("in"):this.attr("in",r)},result:function(r){return r==null?this.attr("result"):this.attr("result",r)},toString:function(){return this.result()}}});var v={blend:function(r,o){return this.parent()&&this.parent().blend(this,r,o)},colorMatrix:function(r,o){return this.parent()&&this.parent().colorMatrix(r,o).in(this)},convolveMatrix:function(r){return this.parent()&&this.parent().convolveMatrix(r).in(this)},componentTransfer:function(r){return this.parent()&&this.parent().componentTransfer(r).in(this)},composite:function(r,o){return this.parent()&&this.parent().composite(this,r,o)},flood:function(r,o){return this.parent()&&this.parent().flood(r,o)},offset:function(r,o){return this.parent()&&this.parent().offset(r,o).in(this)},image:function(r){return this.parent()&&this.parent().image(r)},merge:function(){return this.parent()&&this.parent().merge.apply(this.parent(),[this].concat(arguments))},gaussianBlur:function(r,o){return this.parent()&&this.parent().gaussianBlur(r,o).in(this)},morphology:function(r,o){return this.parent()&&this.parent().morphology(r,o).in(this)},diffuseLighting:function(r,o,h){return this.parent()&&this.parent().diffuseLighting(r,o,h).in(this)},displacementMap:function(r,o,h,c){return this.parent()&&this.parent().displacementMap(this,r,o,h,c)},specularLighting:function(r,o,h,c){return this.parent()&&this.parent().specularLighting(r,o,h,c).in(this)},tile:function(){return this.parent()&&this.parent().tile().in(this)},turbulence:function(r,o,h,c,d){return this.parent()&&this.parent().turbulence(r,o,h,c,d).in(this)}};SVG.extend(SVG.Effect,v),SVG.extend(SVG.ParentEffect,v),SVG.ChildEffect=SVG.invent({create:function(){this.constructor.call(this)},inherit:SVG.Element,extend:{in:function(r){this.attr("in",r)}}});var e={blend:function(r,o,h){this.attr({in:r,in2:o,mode:h||"normal"})},colorMatrix:function(r,o){r=="matrix"&&(o=a(o)),this.attr({type:r,values:o===void 0?null:o})},convolveMatrix:function(r){r=a(r),this.attr({order:Math.sqrt(r.split(" ").length),kernelMatrix:r})},composite:function(r,o,h){this.attr({in:r,in2:o,operator:h})},flood:function(r,o){this.attr("flood-color",r),o!=null&&this.attr("flood-opacity",o)},offset:function(r,o){this.attr({dx:r,dy:o})},image:function(r){this.attr("href",r,SVG.xlink)},displacementMap:function(r,o,h,c,d){this.attr({in:r,in2:o,scale:h,xChannelSelector:c,yChannelSelector:d})},gaussianBlur:function(r,o){r!=null||o!=null?this.attr("stdDeviation",s(Array.prototype.slice.call(arguments))):this.attr("stdDeviation","0 0")},morphology:function(r,o){this.attr({operator:r,radius:o})},tile:function(){},turbulence:function(r,o,h,c,d){this.attr({numOctaves:o,seed:h,stitchTiles:c,baseFrequency:r,type:d})}},t={merge:function(){var r;if(arguments[0]instanceof SVG.Set){var o=this;arguments[0].each(function(c){this instanceof SVG.MergeNode?o.put(this):(this instanceof SVG.Effect||this instanceof SVG.ParentEffect)&&o.put(new SVG.MergeNode(this))})}else{r=Array.isArray(arguments[0])?arguments[0]:arguments;for(var h=0;h<r.length;h++)r[h]instanceof SVG.MergeNode?this.put(r[h]):this.put(new SVG.MergeNode(r[h]))}},componentTransfer:function(r){if(this.rgb=new SVG.Set,["r","g","b","a"].forEach((function(h){this[h]=new SVG["Func"+h.toUpperCase()]("identity"),this.rgb.add(this[h]),this.node.appendChild(this[h].node)}).bind(this)),r)for(var o in r.rgb&&(["r","g","b"].forEach((function(h){this[h].attr(r.rgb)}).bind(this)),delete r.rgb),r)this[o].attr(r[o])},diffuseLighting:function(r,o,h){this.attr({surfaceScale:r,diffuseConstant:o,kernelUnitLength:h})},specularLighting:function(r,o,h,c){this.attr({surfaceScale:r,diffuseConstant:o,specularExponent:h,kernelUnitLength:c})}},i={distantLight:function(r,o){this.attr({azimuth:r,elevation:o})},pointLight:function(r,o,h){this.attr({x:r,y:o,z:h})},spotLight:function(r,o,h,c,d,g){this.attr({x:r,y:o,z:h,pointsAtX:c,pointsAtY:d,pointsAtZ:g})},mergeNode:function(r){this.attr("in",r)}};function a(r){return Array.isArray(r)&&(r=new SVG.Array(r)),r.toString().replace(/^\s+/,"").replace(/\s+$/,"").replace(/\s+/g," ")}function s(r){if(!Array.isArray(r))return r;for(var o=0,h=r.length,c=[];o<h;o++)c.push(r[o]);return c.join(" ")}function n(){var r=function(){};for(var o in typeof arguments[arguments.length-1]=="function"&&(r=arguments[arguments.length-1],Array.prototype.splice.call(arguments,arguments.length-1,1)),arguments)for(var h in arguments[o])r(arguments[o][h],h,arguments[o])}["r","g","b","a"].forEach(function(r){i["Func"+r.toUpperCase()]=function(o){switch(this.attr("type",o),o){case"table":this.attr("tableValues",arguments[1]);break;case"linear":this.attr("slope",arguments[1]),this.attr("intercept",arguments[2]);break;case"gamma":this.attr("amplitude",arguments[1]),this.attr("exponent",arguments[2]),this.attr("offset",arguments[2])}}}),n(e,function(r,o){var h=o.charAt(0).toUpperCase()+o.slice(1);SVG[h+"Effect"]=SVG.invent({create:function(){this.constructor.call(this,SVG.create("fe"+h)),r.apply(this,arguments),this.result(this.attr("id")+"Out")},inherit:SVG.Effect,extend:{}})}),n(t,function(r,o){var h=o.charAt(0).toUpperCase()+o.slice(1);SVG[h+"Effect"]=SVG.invent({create:function(){this.constructor.call(this,SVG.create("fe"+h)),r.apply(this,arguments),this.result(this.attr("id")+"Out")},inherit:SVG.ParentEffect,extend:{}})}),n(i,function(r,o){var h=o.charAt(0).toUpperCase()+o.slice(1);SVG[h]=SVG.invent({create:function(){this.constructor.call(this,SVG.create("fe"+h)),r.apply(this,arguments)},inherit:SVG.ChildEffect,extend:{}})}),SVG.extend(SVG.MergeEffect,{in:function(r){return r instanceof SVG.MergeNode?this.add(r,0):this.add(new SVG.MergeNode(r),0),this}}),SVG.extend(SVG.CompositeEffect,SVG.BlendEffect,SVG.DisplacementMapEffect,{in2:function(r){return r==null?this.parent()&&this.parent().select('[result="'+this.attr("in2")+'"]').get(0)||this.attr("in2"):this.attr("in2",r)}}),SVG.filter={sepiatone:[.343,.669,.119,0,0,.249,.626,.13,0,0,.172,.334,.111,0,0,0,0,0,1,0]}}).call(void 0),function(){function v(s,n,r,o,h,c,d){for(var g=s.slice(n,r||d),f=o.slice(h,c||d),p=0,x={pos:[0,0],start:[0,0]},b={pos:[0,0],start:[0,0]};g[p]=e.call(x,g[p]),f[p]=e.call(b,f[p]),g[p][0]!=f[p][0]||g[p][0]=="M"||g[p][0]=="A"&&(g[p][4]!=f[p][4]||g[p][5]!=f[p][5])?(Array.prototype.splice.apply(g,[p,1].concat(i.call(x,g[p]))),Array.prototype.splice.apply(f,[p,1].concat(i.call(b,f[p])))):(g[p]=t.call(x,g[p]),f[p]=t.call(b,f[p])),!(++p==g.length&&p==f.length);)p==g.length&&g.push(["C",x.pos[0],x.pos[1],x.pos[0],x.pos[1],x.pos[0],x.pos[1]]),p==f.length&&f.push(["C",b.pos[0],b.pos[1],b.pos[0],b.pos[1],b.pos[0],b.pos[1]]);return{start:g,dest:f}}function e(s){switch(s[0]){case"z":case"Z":s[0]="L",s[1]=this.start[0],s[2]=this.start[1];break;case"H":s[0]="L",s[2]=this.pos[1];break;case"V":s[0]="L",s[2]=s[1],s[1]=this.pos[0];break;case"T":s[0]="Q",s[3]=s[1],s[4]=s[2],s[1]=this.reflection[1],s[2]=this.reflection[0];break;case"S":s[0]="C",s[6]=s[4],s[5]=s[3],s[4]=s[2],s[3]=s[1],s[2]=this.reflection[1],s[1]=this.reflection[0]}return s}function t(s){var n=s.length;return this.pos=[s[n-2],s[n-1]],"SCQT".indexOf(s[0])!=-1&&(this.reflection=[2*this.pos[0]-s[n-4],2*this.pos[1]-s[n-3]]),s}function i(s){var n=[s];switch(s[0]){case"M":return this.pos=this.start=[s[1],s[2]],n;case"L":s[5]=s[3]=s[1],s[6]=s[4]=s[2],s[1]=this.pos[0],s[2]=this.pos[1];break;case"Q":s[6]=s[4],s[5]=s[3],s[4]=1*s[4]/3+2*s[2]/3,s[3]=1*s[3]/3+2*s[1]/3,s[2]=1*this.pos[1]/3+2*s[2]/3,s[1]=1*this.pos[0]/3+2*s[1]/3;break;case"A":s=(n=function(r,o){var h,c,d,g,f,p,x,b,y,k,A,l,u,m,w,S,P,C,M,T,z,Y,H,R,V,G,Z=Math.abs(o[1]),ae=Math.abs(o[2]),ce=o[3]%360,Mt=o[4],Ce=o[5],Le=o[6],Pe=o[7],_=new SVG.Point(r),B=new SVG.Point(Le,Pe),Ue=[];if(Z===0||ae===0||_.x===B.x&&_.y===B.y)return[["C",_.x,_.y,B.x,B.y,B.x,B.y]];for(h=new SVG.Point((_.x-B.x)/2,(_.y-B.y)/2).transform(new SVG.Matrix().rotate(ce)),(c=h.x*h.x/(Z*Z)+h.y*h.y/(ae*ae))>1&&(Z*=c=Math.sqrt(c),ae*=c),d=new SVG.Matrix().rotate(ce).scale(1/Z,1/ae).rotate(-ce),_=_.transform(d),B=B.transform(d),g=[B.x-_.x,B.y-_.y],p=g[0]*g[0]+g[1]*g[1],f=Math.sqrt(p),g[0]/=f,g[1]/=f,x=p<4?Math.sqrt(1-p/4):0,Mt===Ce&&(x*=-1),b=new SVG.Point((B.x+_.x)/2+x*-g[1],(B.y+_.y)/2+x*g[0]),y=new SVG.Point(_.x-b.x,_.y-b.y),k=new SVG.Point(B.x-b.x,B.y-b.y),A=Math.acos(y.x/Math.sqrt(y.x*y.x+y.y*y.y)),y.y<0&&(A*=-1),l=Math.acos(k.x/Math.sqrt(k.x*k.x+k.y*k.y)),k.y<0&&(l*=-1),Ce&&A>l&&(l+=2*Math.PI),!Ce&&A<l&&(l-=2*Math.PI),m=Math.ceil(2*Math.abs(A-l)/Math.PI),S=[],P=A,u=(l-A)/m,w=4*Math.tan(u/4)/3,z=0;z<=m;z++)M=Math.cos(P),C=Math.sin(P),T=new SVG.Point(b.x+M,b.y+C),S[z]=[new SVG.Point(T.x+w*C,T.y-w*M),T,new SVG.Point(T.x-w*C,T.y+w*M)],P+=u;for(S[0][0]=S[0][1].clone(),S[S.length-1][2]=S[S.length-1][1].clone(),d=new SVG.Matrix().rotate(ce).scale(Z,ae).rotate(-ce),z=0,Y=S.length;z<Y;z++)S[z][0]=S[z][0].transform(d),S[z][1]=S[z][1].transform(d),S[z][2]=S[z][2].transform(d);for(z=1,Y=S.length;z<Y;z++)H=(T=S[z-1][2]).x,R=T.y,V=(T=S[z][0]).x,G=T.y,Le=(T=S[z][1]).x,Pe=T.y,Ue.push(["C",H,R,V,G,Le,Pe]);return Ue}(this.pos,s))[0]}return s[0]="C",this.pos=[s[5],s[6]],this.reflection=[2*s[5]-s[3],2*s[6]-s[4]],n}function a(s,n){if(n===!1)return!1;for(var r=n,o=s.length;r<o;++r)if(s[r][0]=="M")return r;return!1}SVG.extend(SVG.PathArray,{morph:function(s){for(var n=this.value,r=this.parse(s),o=0,h=0,c=!1,d=!1;o!==!1||h!==!1;){var g;c=a(n,o!==!1&&o+1),d=a(r,h!==!1&&h+1),o===!1&&(o=(g=new SVG.PathArray(f.start).bbox()).height==0||g.width==0?n.push(n[0])-1:n.push(["M",g.x+g.width/2,g.y+g.height/2])-1),h===!1&&(h=(g=new SVG.PathArray(f.dest).bbox()).height==0||g.width==0?r.push(r[0])-1:r.push(["M",g.x+g.width/2,g.y+g.height/2])-1);var f=v(n,o,c,r,h,d);n=n.slice(0,o).concat(f.start,c===!1?[]:n.slice(c)),r=r.slice(0,h).concat(f.dest,d===!1?[]:r.slice(d)),o=c!==!1&&o+f.start.length,h=d!==!1&&h+f.dest.length}return this.value=n,this.destination=new SVG.PathArray,this.destination.value=r,this}})}(),(function(){function v(e){e.remember("_draggable",this),this.el=e}v.prototype.init=function(e,t){var i=this;this.constraint=e,this.value=t,this.el.on("mousedown.drag",function(a){i.start(a)}),this.el.on("touchstart.drag",function(a){i.start(a)})},v.prototype.transformPoint=function(e,t){var i=(e=e||window.event).changedTouches&&e.changedTouches[0]||e;return this.p.x=i.clientX-(t||0),this.p.y=i.clientY,this.p.matrixTransform(this.m)},v.prototype.getBBox=function(){var e=this.el.bbox();return this.el instanceof SVG.Nested&&(e=this.el.rbox()),(this.el instanceof SVG.G||this.el instanceof SVG.Use||this.el instanceof SVG.Nested)&&(e.x=this.el.x(),e.y=this.el.y()),e},v.prototype.start=function(e){if(e.type!="click"&&e.type!="mousedown"&&e.type!="mousemove"||(e.which||e.buttons)==1){var t=this;if(this.el.fire("beforedrag",{event:e,handler:this}),!this.el.event().defaultPrevented){e.preventDefault(),e.stopPropagation(),this.parent=this.parent||this.el.parent(SVG.Nested)||this.el.parent(SVG.Doc),this.p=this.parent.node.createSVGPoint(),this.m=this.el.node.getScreenCTM().inverse();var i,a=this.getBBox();if(this.el instanceof SVG.Text)switch(i=this.el.node.getComputedTextLength(),this.el.attr("text-anchor")){case"middle":i/=2;break;case"start":i=0}this.startPoints={point:this.transformPoint(e,i),box:a,transform:this.el.transform()},SVG.on(window,"mousemove.drag",function(s){t.drag(s)}),SVG.on(window,"touchmove.drag",function(s){t.drag(s)}),SVG.on(window,"mouseup.drag",function(s){t.end(s)}),SVG.on(window,"touchend.drag",function(s){t.end(s)}),this.el.fire("dragstart",{event:e,p:this.startPoints.point,m:this.m,handler:this})}}},v.prototype.drag=function(e){var t=this.getBBox(),i=this.transformPoint(e),a=this.startPoints.box.x+i.x-this.startPoints.point.x,s=this.startPoints.box.y+i.y-this.startPoints.point.y,n=this.constraint,r=i.x-this.startPoints.point.x,o=i.y-this.startPoints.point.y;if(this.el.fire("dragmove",{event:e,p:i,m:this.m,handler:this}),this.el.event().defaultPrevented)return i;if(typeof n=="function"){var h=n.call(this.el,a,s,this.m);typeof h=="boolean"&&(h={x:h,y:h}),h.x===!0?this.el.x(a):h.x!==!1&&this.el.x(h.x),h.y===!0?this.el.y(s):h.y!==!1&&this.el.y(h.y)}else typeof n=="object"&&(n.minX!=null&&a<n.minX?r=(a=n.minX)-this.startPoints.box.x:n.maxX!=null&&a>n.maxX-t.width&&(r=(a=n.maxX-t.width)-this.startPoints.box.x),n.minY!=null&&s<n.minY?o=(s=n.minY)-this.startPoints.box.y:n.maxY!=null&&s>n.maxY-t.height&&(o=(s=n.maxY-t.height)-this.startPoints.box.y),n.snapToGrid!=null&&(a-=a%n.snapToGrid,s-=s%n.snapToGrid,r-=r%n.snapToGrid,o-=o%n.snapToGrid),this.el instanceof SVG.G?this.el.matrix(this.startPoints.transform).transform({x:r,y:o},!0):this.el.move(a,s));return i},v.prototype.end=function(e){var t=this.drag(e);this.el.fire("dragend",{event:e,p:t,m:this.m,handler:this}),SVG.off(window,"mousemove.drag"),SVG.off(window,"touchmove.drag"),SVG.off(window,"mouseup.drag"),SVG.off(window,"touchend.drag")},SVG.extend(SVG.Element,{draggable:function(e,t){typeof e!="function"&&typeof e!="object"||(t=e,e=!0);var i=this.remember("_draggable")||new v(this);return(e=e===void 0||e)?i.init(t||{},e):(this.off("mousedown.drag"),this.off("touchstart.drag")),this}})}).call(void 0),function(){function v(e){this.el=e,e.remember("_selectHandler",this),this.pointSelection={isSelected:!1},this.rectSelection={isSelected:!1},this.pointsList={lt:[0,0],rt:["width",0],rb:["width","height"],lb:[0,"height"],t:["width",0],r:["width","height"],b:["width","height"],l:[0,"height"]},this.pointCoord=function(t,i,a){var s=typeof t!="string"?t:i[t];return a?s/2:s},this.pointCoords=function(t,i){var a=this.pointsList[t];return{x:this.pointCoord(a[0],i,t==="t"||t==="b"),y:this.pointCoord(a[1],i,t==="r"||t==="l")}}}v.prototype.init=function(e,t){var i=this.el.bbox();this.options={};var a=this.el.selectize.defaults.points;for(var s in this.el.selectize.defaults)this.options[s]=this.el.selectize.defaults[s],t[s]!==void 0&&(this.options[s]=t[s]);var n=["points","pointsExclude"];for(var s in n){var r=this.options[n[s]];typeof r=="string"?r=r.length>0?r.split(/\s*,\s*/i):[]:typeof r=="boolean"&&n[s]==="points"&&(r=r?a:[]),this.options[n[s]]=r}this.options.points=[a,this.options.points].reduce(function(o,h){return o.filter(function(c){return h.indexOf(c)>-1})}),this.options.points=[this.options.points,this.options.pointsExclude].reduce(function(o,h){return o.filter(function(c){return h.indexOf(c)<0})}),this.parent=this.el.parent(),this.nested=this.nested||this.parent.group(),this.nested.matrix(new SVG.Matrix(this.el).translate(i.x,i.y)),this.options.deepSelect&&["line","polyline","polygon"].indexOf(this.el.type)!==-1?this.selectPoints(e):this.selectRect(e),this.observe(),this.cleanup()},v.prototype.selectPoints=function(e){return this.pointSelection.isSelected=e,this.pointSelection.set||(this.pointSelection.set=this.parent.set(),this.drawPoints()),this},v.prototype.getPointArray=function(){var e=this.el.bbox();return this.el.array().valueOf().map(function(t){return[t[0]-e.x,t[1]-e.y]})},v.prototype.drawPoints=function(){for(var e=this,t=this.getPointArray(),i=0,a=t.length;i<a;++i){var s=function(r){return function(o){(o=o||window.event).preventDefault?o.preventDefault():o.returnValue=!1,o.stopPropagation();var h=o.pageX||o.touches[0].pageX,c=o.pageY||o.touches[0].pageY;e.el.fire("point",{x:h,y:c,i:r,event:o})}}(i),n=this.drawPoint(t[i][0],t[i][1]).addClass(this.options.classPoints).addClass(this.options.classPoints+"_point").on("touchstart",s).on("mousedown",s);this.pointSelection.set.add(n)}},v.prototype.drawPoint=function(e,t){var i=this.options.pointType;switch(i){case"circle":return this.drawCircle(e,t);case"rect":return this.drawRect(e,t);default:if(typeof i=="function")return i.call(this,e,t);throw new Error("Unknown "+i+" point type!")}},v.prototype.drawCircle=function(e,t){return this.nested.circle(this.options.pointSize).center(e,t)},v.prototype.drawRect=function(e,t){return this.nested.rect(this.options.pointSize,this.options.pointSize).center(e,t)},v.prototype.updatePointSelection=function(){var e=this.getPointArray();this.pointSelection.set.each(function(t){this.cx()===e[t][0]&&this.cy()===e[t][1]||this.center(e[t][0],e[t][1])})},v.prototype.updateRectSelection=function(){var e=this,t=this.el.bbox();if(this.rectSelection.set.get(0).attr({width:t.width,height:t.height}),this.options.points.length&&this.options.points.map(function(a,s){var n=e.pointCoords(a,t);e.rectSelection.set.get(s+1).center(n.x,n.y)}),this.options.rotationPoint){var i=this.rectSelection.set.length();this.rectSelection.set.get(i-1).center(t.width/2,20)}},v.prototype.selectRect=function(e){var t=this,i=this.el.bbox();function a(r){return function(o){(o=o||window.event).preventDefault?o.preventDefault():o.returnValue=!1,o.stopPropagation();var h=o.pageX||o.touches[0].pageX,c=o.pageY||o.touches[0].pageY;t.el.fire(r,{x:h,y:c,event:o})}}if(this.rectSelection.isSelected=e,this.rectSelection.set=this.rectSelection.set||this.parent.set(),this.rectSelection.set.get(0)||this.rectSelection.set.add(this.nested.rect(i.width,i.height).addClass(this.options.classRect)),this.options.points.length&&this.rectSelection.set.length()<2&&(this.options.points.map(function(r,o){var h=t.pointCoords(r,i),c=t.drawPoint(h.x,h.y).attr("class",t.options.classPoints+"_"+r).on("mousedown",a(r)).on("touchstart",a(r));t.rectSelection.set.add(c)}),this.rectSelection.set.each(function(){this.addClass(t.options.classPoints)})),this.options.rotationPoint&&(this.options.points&&!this.rectSelection.set.get(9)||!this.options.points&&!this.rectSelection.set.get(1))){var s=function(r){(r=r||window.event).preventDefault?r.preventDefault():r.returnValue=!1,r.stopPropagation();var o=r.pageX||r.touches[0].pageX,h=r.pageY||r.touches[0].pageY;t.el.fire("rot",{x:o,y:h,event:r})},n=this.drawPoint(i.width/2,20).attr("class",this.options.classPoints+"_rot").on("touchstart",s).on("mousedown",s);this.rectSelection.set.add(n)}},v.prototype.handler=function(){var e=this.el.bbox();this.nested.matrix(new SVG.Matrix(this.el).translate(e.x,e.y)),this.rectSelection.isSelected&&this.updateRectSelection(),this.pointSelection.isSelected&&this.updatePointSelection()},v.prototype.observe=function(){var e=this;if(MutationObserver)if(this.rectSelection.isSelected||this.pointSelection.isSelected)this.observerInst=this.observerInst||new MutationObserver(function(){e.handler()}),this.observerInst.observe(this.el.node,{attributes:!0});else try{this.observerInst.disconnect(),delete this.observerInst}catch{}else this.el.off("DOMAttrModified.select"),(this.rectSelection.isSelected||this.pointSelection.isSelected)&&this.el.on("DOMAttrModified.select",function(){e.handler()})},v.prototype.cleanup=function(){!this.rectSelection.isSelected&&this.rectSelection.set&&(this.rectSelection.set.each(function(){this.remove()}),this.rectSelection.set.clear(),delete this.rectSelection.set),!this.pointSelection.isSelected&&this.pointSelection.set&&(this.pointSelection.set.each(function(){this.remove()}),this.pointSelection.set.clear(),delete this.pointSelection.set),this.pointSelection.isSelected||this.rectSelection.isSelected||(this.nested.remove(),delete this.nested)},SVG.extend(SVG.Element,{selectize:function(e,t){return typeof e=="object"&&(t=e,e=!0),(this.remember("_selectHandler")||new v(this)).init(e===void 0||e,t||{}),this}}),SVG.Element.prototype.selectize.defaults={points:["lt","rt","rb","lb","t","r","b","l"],pointsExclude:[],classRect:"svg_select_boundingRect",classPoints:"svg_select_points",pointSize:7,rotationPoint:!0,deepSelect:!1,pointType:"circle"}}(),function(){(function(){function v(e){e.remember("_resizeHandler",this),this.el=e,this.parameters={},this.lastUpdateCall=null,this.p=e.doc().node.createSVGPoint()}v.prototype.transformPoint=function(e,t,i){return this.p.x=e-(this.offset.x-window.pageXOffset),this.p.y=t-(this.offset.y-window.pageYOffset),this.p.matrixTransform(i||this.m)},v.prototype._extractPosition=function(e){return{x:e.clientX!=null?e.clientX:e.touches[0].clientX,y:e.clientY!=null?e.clientY:e.touches[0].clientY}},v.prototype.init=function(e){var t=this;if(this.stop(),e!=="stop"){for(var i in this.options={},this.el.resize.defaults)this.options[i]=this.el.resize.defaults[i],e[i]!==void 0&&(this.options[i]=e[i]);this.el.on("lt.resize",function(a){t.resize(a||window.event)}),this.el.on("rt.resize",function(a){t.resize(a||window.event)}),this.el.on("rb.resize",function(a){t.resize(a||window.event)}),this.el.on("lb.resize",function(a){t.resize(a||window.event)}),this.el.on("t.resize",function(a){t.resize(a||window.event)}),this.el.on("r.resize",function(a){t.resize(a||window.event)}),this.el.on("b.resize",function(a){t.resize(a||window.event)}),this.el.on("l.resize",function(a){t.resize(a||window.event)}),this.el.on("rot.resize",function(a){t.resize(a||window.event)}),this.el.on("point.resize",function(a){t.resize(a||window.event)}),this.update()}},v.prototype.stop=function(){return this.el.off("lt.resize"),this.el.off("rt.resize"),this.el.off("rb.resize"),this.el.off("lb.resize"),this.el.off("t.resize"),this.el.off("r.resize"),this.el.off("b.resize"),this.el.off("l.resize"),this.el.off("rot.resize"),this.el.off("point.resize"),this},v.prototype.resize=function(e){var t=this;this.m=this.el.node.getScreenCTM().inverse(),this.offset={x:window.pageXOffset,y:window.pageYOffset};var i=this._extractPosition(e.detail.event);if(this.parameters={type:this.el.type,p:this.transformPoint(i.x,i.y),x:e.detail.x,y:e.detail.y,box:this.el.bbox(),rotation:this.el.transform().rotation},this.el.type==="text"&&(this.parameters.fontSize=this.el.attr()["font-size"]),e.detail.i!==void 0){var a=this.el.array().valueOf();this.parameters.i=e.detail.i,this.parameters.pointCoords=[a[e.detail.i][0],a[e.detail.i][1]]}switch(e.type){case"lt":this.calc=function(s,n){var r=this.snapToGrid(s,n);if(this.parameters.box.width-r[0]>0&&this.parameters.box.height-r[1]>0){if(this.parameters.type==="text")return this.el.move(this.parameters.box.x+r[0],this.parameters.box.y),void this.el.attr("font-size",this.parameters.fontSize-r[0]);r=this.checkAspectRatio(r),this.el.move(this.parameters.box.x+r[0],this.parameters.box.y+r[1]).size(this.parameters.box.width-r[0],this.parameters.box.height-r[1])}};break;case"rt":this.calc=function(s,n){var r=this.snapToGrid(s,n,2);if(this.parameters.box.width+r[0]>0&&this.parameters.box.height-r[1]>0){if(this.parameters.type==="text")return this.el.move(this.parameters.box.x-r[0],this.parameters.box.y),void this.el.attr("font-size",this.parameters.fontSize+r[0]);r=this.checkAspectRatio(r,!0),this.el.move(this.parameters.box.x,this.parameters.box.y+r[1]).size(this.parameters.box.width+r[0],this.parameters.box.height-r[1])}};break;case"rb":this.calc=function(s,n){var r=this.snapToGrid(s,n,0);if(this.parameters.box.width+r[0]>0&&this.parameters.box.height+r[1]>0){if(this.parameters.type==="text")return this.el.move(this.parameters.box.x-r[0],this.parameters.box.y),void this.el.attr("font-size",this.parameters.fontSize+r[0]);r=this.checkAspectRatio(r),this.el.move(this.parameters.box.x,this.parameters.box.y).size(this.parameters.box.width+r[0],this.parameters.box.height+r[1])}};break;case"lb":this.calc=function(s,n){var r=this.snapToGrid(s,n,1);if(this.parameters.box.width-r[0]>0&&this.parameters.box.height+r[1]>0){if(this.parameters.type==="text")return this.el.move(this.parameters.box.x+r[0],this.parameters.box.y),void this.el.attr("font-size",this.parameters.fontSize-r[0]);r=this.checkAspectRatio(r,!0),this.el.move(this.parameters.box.x+r[0],this.parameters.box.y).size(this.parameters.box.width-r[0],this.parameters.box.height+r[1])}};break;case"t":this.calc=function(s,n){var r=this.snapToGrid(s,n,2);if(this.parameters.box.height-r[1]>0){if(this.parameters.type==="text")return;this.el.move(this.parameters.box.x,this.parameters.box.y+r[1]).height(this.parameters.box.height-r[1])}};break;case"r":this.calc=function(s,n){var r=this.snapToGrid(s,n,0);if(this.parameters.box.width+r[0]>0){if(this.parameters.type==="text")return;this.el.move(this.parameters.box.x,this.parameters.box.y).width(this.parameters.box.width+r[0])}};break;case"b":this.calc=function(s,n){var r=this.snapToGrid(s,n,0);if(this.parameters.box.height+r[1]>0){if(this.parameters.type==="text")return;this.el.move(this.parameters.box.x,this.parameters.box.y).height(this.parameters.box.height+r[1])}};break;case"l":this.calc=function(s,n){var r=this.snapToGrid(s,n,1);if(this.parameters.box.width-r[0]>0){if(this.parameters.type==="text")return;this.el.move(this.parameters.box.x+r[0],this.parameters.box.y).width(this.parameters.box.width-r[0])}};break;case"rot":this.calc=function(s,n){var r=s+this.parameters.p.x,o=n+this.parameters.p.y,h=Math.atan2(this.parameters.p.y-this.parameters.box.y-this.parameters.box.height/2,this.parameters.p.x-this.parameters.box.x-this.parameters.box.width/2),c=Math.atan2(o-this.parameters.box.y-this.parameters.box.height/2,r-this.parameters.box.x-this.parameters.box.width/2),d=this.parameters.rotation+180*(c-h)/Math.PI+this.options.snapToAngle/2;this.el.center(this.parameters.box.cx,this.parameters.box.cy).rotate(d-d%this.options.snapToAngle,this.parameters.box.cx,this.parameters.box.cy)};break;case"point":this.calc=function(s,n){var r=this.snapToGrid(s,n,this.parameters.pointCoords[0],this.parameters.pointCoords[1]),o=this.el.array().valueOf();o[this.parameters.i][0]=this.parameters.pointCoords[0]+r[0],o[this.parameters.i][1]=this.parameters.pointCoords[1]+r[1],this.el.plot(o)}}this.el.fire("resizestart",{dx:this.parameters.x,dy:this.parameters.y,event:e}),SVG.on(window,"touchmove.resize",function(s){t.update(s||window.event)}),SVG.on(window,"touchend.resize",function(){t.done()}),SVG.on(window,"mousemove.resize",function(s){t.update(s||window.event)}),SVG.on(window,"mouseup.resize",function(){t.done()})},v.prototype.update=function(e){if(e){var t=this._extractPosition(e),i=this.transformPoint(t.x,t.y),a=i.x-this.parameters.p.x,s=i.y-this.parameters.p.y;this.lastUpdateCall=[a,s],this.calc(a,s),this.el.fire("resizing",{dx:a,dy:s,event:e})}else this.lastUpdateCall&&this.calc(this.lastUpdateCall[0],this.lastUpdateCall[1])},v.prototype.done=function(){this.lastUpdateCall=null,SVG.off(window,"mousemove.resize"),SVG.off(window,"mouseup.resize"),SVG.off(window,"touchmove.resize"),SVG.off(window,"touchend.resize"),this.el.fire("resizedone")},v.prototype.snapToGrid=function(e,t,i,a){var s;return a!==void 0?s=[(i+e)%this.options.snapToGrid,(a+t)%this.options.snapToGrid]:(i=i??3,s=[(this.parameters.box.x+e+(1&i?0:this.parameters.box.width))%this.options.snapToGrid,(this.parameters.box.y+t+(2&i?0:this.parameters.box.height))%this.options.snapToGrid]),e<0&&(s[0]-=this.options.snapToGrid),t<0&&(s[1]-=this.options.snapToGrid),e-=Math.abs(s[0])<this.options.snapToGrid/2?s[0]:s[0]-(e<0?-this.options.snapToGrid:this.options.snapToGrid),t-=Math.abs(s[1])<this.options.snapToGrid/2?s[1]:s[1]-(t<0?-this.options.snapToGrid:this.options.snapToGrid),this.constraintToBox(e,t,i,a)},v.prototype.constraintToBox=function(e,t,i,a){var s,n,r=this.options.constraint||{};return a!==void 0?(s=i,n=a):(s=this.parameters.box.x+(1&i?0:this.parameters.box.width),n=this.parameters.box.y+(2&i?0:this.parameters.box.height)),r.minX!==void 0&&s+e<r.minX&&(e=r.minX-s),r.maxX!==void 0&&s+e>r.maxX&&(e=r.maxX-s),r.minY!==void 0&&n+t<r.minY&&(t=r.minY-n),r.maxY!==void 0&&n+t>r.maxY&&(t=r.maxY-n),[e,t]},v.prototype.checkAspectRatio=function(e,t){if(!this.options.saveAspectRatio)return e;var i=e.slice(),a=this.parameters.box.width/this.parameters.box.height,s=this.parameters.box.width+e[0],n=this.parameters.box.height-e[1],r=s/n;return r<a?(i[1]=s/a-this.parameters.box.height,t&&(i[1]=-i[1])):r>a&&(i[0]=this.parameters.box.width-n*a,t&&(i[0]=-i[0])),i},SVG.extend(SVG.Element,{resize:function(e){return(this.remember("_resizeHandler")||new v(this)).init(e||{}),this}}),SVG.Element.prototype.resize.defaults={snapToAngle:.1,snapToGrid:1,constraint:{},saveAspectRatio:!1}}).call(this)}(),function(v,e){e===void 0&&(e={});var t=e.insertAt;if(typeof document<"u"){var i=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",t==="top"&&i.firstChild?i.insertBefore(a,i.firstChild):i.appendChild(a),a.styleSheet?a.styleSheet.cssText=v:a.appendChild(document.createTextNode(v))}}(`.apexcharts-canvas {
  position: relative;
  user-select: none;
  /* cannot give overflow: hidden as it will crop tooltips which overflow outside chart area */
}


/* scrollbar is not visible by default for legend, hence forcing the visibility */
.apexcharts-canvas ::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 6px;
}

.apexcharts-canvas ::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, .5);
  box-shadow: 0 0 1px rgba(255, 255, 255, .5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, .5);
}


.apexcharts-inner {
  position: relative;
}

.apexcharts-text tspan {
  font-family: inherit;
}

.legend-mouseover-inactive {
  transition: 0.15s ease all;
  opacity: 0.20;
}

.apexcharts-series-collapsed {
  opacity: 0;
}

.apexcharts-tooltip {
  border-radius: 5px;
  box-shadow: 2px 2px 6px -4px #999;
  cursor: default;
  font-size: 14px;
  left: 62px;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  top: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  white-space: nowrap;
  z-index: 12;
  transition: 0.15s ease all;
}

.apexcharts-tooltip.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-tooltip.apexcharts-theme-light {
  border: 1px solid #e3e3e3;
  background: rgba(255, 255, 255, 0.96);
}

.apexcharts-tooltip.apexcharts-theme-dark {
  color: #fff;
  background: rgba(30, 30, 30, 0.8);
}

.apexcharts-tooltip * {
  font-family: inherit;
}


.apexcharts-tooltip-title {
  padding: 6px;
  font-size: 15px;
  margin-bottom: 4px;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  background: #ECEFF1;
  border-bottom: 1px solid #ddd;
}

.apexcharts-tooltip.apexcharts-theme-dark .apexcharts-tooltip-title {
  background: rgba(0, 0, 0, 0.7);
  border-bottom: 1px solid #333;
}

.apexcharts-tooltip-text-y-value,
.apexcharts-tooltip-text-goals-value,
.apexcharts-tooltip-text-z-value {
  display: inline-block;
  font-weight: 600;
  margin-left: 5px;
}

.apexcharts-tooltip-text-y-label:empty,
.apexcharts-tooltip-text-y-value:empty,
.apexcharts-tooltip-text-goals-label:empty,
.apexcharts-tooltip-text-goals-value:empty,
.apexcharts-tooltip-text-z-value:empty {
  display: none;
}

.apexcharts-tooltip-text-y-value,
.apexcharts-tooltip-text-goals-value,
.apexcharts-tooltip-text-z-value {
  font-weight: 600;
}

.apexcharts-tooltip-text-goals-label, 
.apexcharts-tooltip-text-goals-value {
  padding: 6px 0 5px;
}

.apexcharts-tooltip-goals-group, 
.apexcharts-tooltip-text-goals-label, 
.apexcharts-tooltip-text-goals-value {
  display: flex;
}
.apexcharts-tooltip-text-goals-label:not(:empty),
.apexcharts-tooltip-text-goals-value:not(:empty) {
  margin-top: -6px;
}

.apexcharts-tooltip-marker {
  width: 12px;
  height: 12px;
  position: relative;
  top: 0px;
  margin-right: 10px;
  border-radius: 50%;
}

.apexcharts-tooltip-series-group {
  padding: 0 10px;
  display: none;
  text-align: left;
  justify-content: left;
  align-items: center;
}

.apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-marker {
  opacity: 1;
}

.apexcharts-tooltip-series-group.apexcharts-active,
.apexcharts-tooltip-series-group:last-child {
  padding-bottom: 4px;
}

.apexcharts-tooltip-series-group-hidden {
  opacity: 0;
  height: 0;
  line-height: 0;
  padding: 0 !important;
}

.apexcharts-tooltip-y-group {
  padding: 6px 0 5px;
}

.apexcharts-tooltip-box, .apexcharts-custom-tooltip {
  padding: 4px 8px;
}

.apexcharts-tooltip-boxPlot {
  display: flex;
  flex-direction: column-reverse;
}

.apexcharts-tooltip-box>div {
  margin: 4px 0;
}

.apexcharts-tooltip-box span.value {
  font-weight: bold;
}

.apexcharts-tooltip-rangebar {
  padding: 5px 8px;
}

.apexcharts-tooltip-rangebar .category {
  font-weight: 600;
  color: #777;
}

.apexcharts-tooltip-rangebar .series-name {
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.apexcharts-xaxistooltip {
  opacity: 0;
  padding: 9px 10px;
  pointer-events: none;
  color: #373d3f;
  font-size: 13px;
  text-align: center;
  border-radius: 2px;
  position: absolute;
  z-index: 10;
  background: #ECEFF1;
  border: 1px solid #90A4AE;
  transition: 0.15s ease all;
}

.apexcharts-xaxistooltip.apexcharts-theme-dark {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #fff;
}

.apexcharts-xaxistooltip:after,
.apexcharts-xaxistooltip:before {
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.apexcharts-xaxistooltip:after {
  border-color: rgba(236, 239, 241, 0);
  border-width: 6px;
  margin-left: -6px;
}

.apexcharts-xaxistooltip:before {
  border-color: rgba(144, 164, 174, 0);
  border-width: 7px;
  margin-left: -7px;
}

.apexcharts-xaxistooltip-bottom:after,
.apexcharts-xaxistooltip-bottom:before {
  bottom: 100%;
}

.apexcharts-xaxistooltip-top:after,
.apexcharts-xaxistooltip-top:before {
  top: 100%;
}

.apexcharts-xaxistooltip-bottom:after {
  border-bottom-color: #ECEFF1;
}

.apexcharts-xaxistooltip-bottom:before {
  border-bottom-color: #90A4AE;
}

.apexcharts-xaxistooltip-bottom.apexcharts-theme-dark:after {
  border-bottom-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip-bottom.apexcharts-theme-dark:before {
  border-bottom-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip-top:after {
  border-top-color: #ECEFF1
}

.apexcharts-xaxistooltip-top:before {
  border-top-color: #90A4AE;
}

.apexcharts-xaxistooltip-top.apexcharts-theme-dark:after {
  border-top-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip-top.apexcharts-theme-dark:before {
  border-top-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-yaxistooltip {
  opacity: 0;
  padding: 4px 10px;
  pointer-events: none;
  color: #373d3f;
  font-size: 13px;
  text-align: center;
  border-radius: 2px;
  position: absolute;
  z-index: 10;
  background: #ECEFF1;
  border: 1px solid #90A4AE;
}

.apexcharts-yaxistooltip.apexcharts-theme-dark {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #fff;
}

.apexcharts-yaxistooltip:after,
.apexcharts-yaxistooltip:before {
  top: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.apexcharts-yaxistooltip:after {
  border-color: rgba(236, 239, 241, 0);
  border-width: 6px;
  margin-top: -6px;
}

.apexcharts-yaxistooltip:before {
  border-color: rgba(144, 164, 174, 0);
  border-width: 7px;
  margin-top: -7px;
}

.apexcharts-yaxistooltip-left:after,
.apexcharts-yaxistooltip-left:before {
  left: 100%;
}

.apexcharts-yaxistooltip-right:after,
.apexcharts-yaxistooltip-right:before {
  right: 100%;
}

.apexcharts-yaxistooltip-left:after {
  border-left-color: #ECEFF1;
}

.apexcharts-yaxistooltip-left:before {
  border-left-color: #90A4AE;
}

.apexcharts-yaxistooltip-left.apexcharts-theme-dark:after {
  border-left-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip-left.apexcharts-theme-dark:before {
  border-left-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip-right:after {
  border-right-color: #ECEFF1;
}

.apexcharts-yaxistooltip-right:before {
  border-right-color: #90A4AE;
}

.apexcharts-yaxistooltip-right.apexcharts-theme-dark:after {
  border-right-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip-right.apexcharts-theme-dark:before {
  border-right-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip.apexcharts-active {
  opacity: 1;
}

.apexcharts-yaxistooltip-hidden {
  display: none;
}

.apexcharts-xcrosshairs,
.apexcharts-ycrosshairs {
  pointer-events: none;
  opacity: 0;
  transition: 0.15s ease all;
}

.apexcharts-xcrosshairs.apexcharts-active,
.apexcharts-ycrosshairs.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-ycrosshairs-hidden {
  opacity: 0;
}

.apexcharts-selection-rect {
  cursor: move;
}

.svg_select_boundingRect, .svg_select_points_rot {
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
}
.apexcharts-selection-rect + g .svg_select_boundingRect,
.apexcharts-selection-rect + g .svg_select_points_rot {
  opacity: 0;
  visibility: hidden;
}

.apexcharts-selection-rect + g .svg_select_points_l,
.apexcharts-selection-rect + g .svg_select_points_r {
  cursor: ew-resize;
  opacity: 1;
  visibility: visible;
}

.svg_select_points {
  fill: #efefef;
  stroke: #333;
  rx: 2;
}

.apexcharts-svg.apexcharts-zoomable.hovering-zoom {
  cursor: crosshair
}

.apexcharts-svg.apexcharts-zoomable.hovering-pan {
  cursor: move
}

.apexcharts-zoom-icon,
.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon,
.apexcharts-reset-icon,
.apexcharts-pan-icon,
.apexcharts-selection-icon,
.apexcharts-menu-icon,
.apexcharts-toolbar-custom-icon {
  cursor: pointer;
  width: 20px;
  height: 20px;
  line-height: 24px;
  color: #6E8192;
  text-align: center;
}

.apexcharts-zoom-icon svg,
.apexcharts-zoomin-icon svg,
.apexcharts-zoomout-icon svg,
.apexcharts-reset-icon svg,
.apexcharts-menu-icon svg {
  fill: #6E8192;
}

.apexcharts-selection-icon svg {
  fill: #444;
  transform: scale(0.76)
}

.apexcharts-theme-dark .apexcharts-zoom-icon svg,
.apexcharts-theme-dark .apexcharts-zoomin-icon svg,
.apexcharts-theme-dark .apexcharts-zoomout-icon svg,
.apexcharts-theme-dark .apexcharts-reset-icon svg,
.apexcharts-theme-dark .apexcharts-pan-icon svg,
.apexcharts-theme-dark .apexcharts-selection-icon svg,
.apexcharts-theme-dark .apexcharts-menu-icon svg,
.apexcharts-theme-dark .apexcharts-toolbar-custom-icon svg {
  fill: #f3f4f5;
}

.apexcharts-canvas .apexcharts-zoom-icon.apexcharts-selected svg,
.apexcharts-canvas .apexcharts-selection-icon.apexcharts-selected svg,
.apexcharts-canvas .apexcharts-reset-zoom-icon.apexcharts-selected svg {
  fill: #008FFB;
}

.apexcharts-theme-light .apexcharts-selection-icon:not(.apexcharts-selected):hover svg,
.apexcharts-theme-light .apexcharts-zoom-icon:not(.apexcharts-selected):hover svg,
.apexcharts-theme-light .apexcharts-zoomin-icon:hover svg,
.apexcharts-theme-light .apexcharts-zoomout-icon:hover svg,
.apexcharts-theme-light .apexcharts-reset-icon:hover svg,
.apexcharts-theme-light .apexcharts-menu-icon:hover svg {
  fill: #333;
}

.apexcharts-selection-icon,
.apexcharts-menu-icon {
  position: relative;
}

.apexcharts-reset-icon {
  margin-left: 5px;
}

.apexcharts-zoom-icon,
.apexcharts-reset-icon,
.apexcharts-menu-icon {
  transform: scale(0.85);
}

.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
  transform: scale(0.7)
}

.apexcharts-zoomout-icon {
  margin-right: 3px;
}

.apexcharts-pan-icon {
  transform: scale(0.62);
  position: relative;
  left: 1px;
  top: 0px;
}

.apexcharts-pan-icon svg {
  fill: #fff;
  stroke: #6E8192;
  stroke-width: 2;
}

.apexcharts-pan-icon.apexcharts-selected svg {
  stroke: #008FFB;
}

.apexcharts-pan-icon:not(.apexcharts-selected):hover svg {
  stroke: #333;
}

.apexcharts-toolbar {
  position: absolute;
  z-index: 11;
  max-width: 176px;
  text-align: right;
  border-radius: 3px;
  padding: 0px 6px 2px 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.apexcharts-menu {
  background: #fff;
  position: absolute;
  top: 100%;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 3px;
  right: 10px;
  opacity: 0;
  min-width: 110px;
  transition: 0.15s ease all;
  pointer-events: none;
}

.apexcharts-menu.apexcharts-menu-open {
  opacity: 1;
  pointer-events: all;
  transition: 0.15s ease all;
}

.apexcharts-menu-item {
  padding: 6px 7px;
  font-size: 12px;
  cursor: pointer;
}

.apexcharts-theme-light .apexcharts-menu-item:hover {
  background: #eee;
}

.apexcharts-theme-dark .apexcharts-menu {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
}

@media screen and (min-width: 768px) {
  .apexcharts-canvas:hover .apexcharts-toolbar {
    opacity: 1;
  }
}

.apexcharts-datalabel.apexcharts-element-hidden {
  opacity: 0;
}

.apexcharts-pie-label,
.apexcharts-datalabels,
.apexcharts-datalabel,
.apexcharts-datalabel-label,
.apexcharts-datalabel-value {
  cursor: default;
  pointer-events: none;
}

.apexcharts-pie-label-delay {
  opacity: 0;
  animation-name: opaque;
  animation-duration: 0.3s;
  animation-fill-mode: forwards;
  animation-timing-function: ease;
}

.apexcharts-canvas .apexcharts-element-hidden {
  opacity: 0;
}

.apexcharts-hide .apexcharts-series-points {
  opacity: 0;
}

.apexcharts-gridline,
.apexcharts-annotation-rect,
.apexcharts-tooltip .apexcharts-marker,
.apexcharts-area-series .apexcharts-area,
.apexcharts-line,
.apexcharts-zoom-rect,
.apexcharts-toolbar svg,
.apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-radar-series path,
.apexcharts-radar-series polygon {
  pointer-events: none;
}


/* markers */

.apexcharts-marker {
  transition: 0.15s ease all;
}

@keyframes opaque {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}


/* Resize generated styles */

@keyframes resizeanim {
  from {
    opacity: 0;
  }
  to {
    opacity: 0;
  }
}

.resize-triggers {
  animation: 1ms resizeanim;
  visibility: hidden;
  opacity: 0;
}

.resize-triggers,
.resize-triggers>div,
.contract-trigger:before {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.resize-triggers>div {
  background: #eee;
  overflow: auto;
}

.contract-trigger:before {
  width: 200%;
  height: 200%;
}`),function(){function v(g){var f=g.__resizeTriggers__,p=f.firstElementChild,x=f.lastElementChild,b=p?p.firstElementChild:null;x&&(x.scrollLeft=x.scrollWidth,x.scrollTop=x.scrollHeight),b&&(b.style.width=p.offsetWidth+1+"px",b.style.height=p.offsetHeight+1+"px"),p&&(p.scrollLeft=p.scrollWidth,p.scrollTop=p.scrollHeight)}function e(g){var f=this;v(this),this.__resizeRAF__&&s(this.__resizeRAF__),this.__resizeRAF__=a(function(){(function(p){return p.offsetWidth!=p.__resizeLast__.width||p.offsetHeight!=p.__resizeLast__.height})(f)&&(f.__resizeLast__.width=f.offsetWidth,f.__resizeLast__.height=f.offsetHeight,f.__resizeListeners__.forEach(function(p){p.call(g)}))})}var t,i,a=(t=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||function(g){return window.setTimeout(g,20)},function(g){return t(g)}),s=(i=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.webkitCancelAnimationFrame||window.clearTimeout,function(g){return i(g)}),n=!1,r="animationstart",o="Webkit Moz O ms".split(" "),h="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),c=document.createElement("fakeelement");if(c.style.animationName!==void 0&&(n=!0),n===!1){for(var d=0;d<o.length;d++)if(c.style[o[d]+"AnimationName"]!==void 0){r=h[d];break}}window.addResizeListener=function(g,f){g.__resizeTriggers__||(getComputedStyle(g).position=="static"&&(g.style.position="relative"),g.__resizeLast__={},g.__resizeListeners__=[],(g.__resizeTriggers__=document.createElement("div")).className="resize-triggers",g.__resizeTriggers__.innerHTML='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>',g.appendChild(g.__resizeTriggers__),v(g),g.addEventListener("scroll",e,!0),r&&g.__resizeTriggers__.addEventListener(r,function(p){p.animationName=="resizeanim"&&v(g)})),g.__resizeListeners__.push(f)},window.removeResizeListener=function(g,f){g&&(g.__resizeListeners__.splice(g.__resizeListeners__.indexOf(f),1),g.__resizeListeners__.length||(g.removeEventListener("scroll",e),g.__resizeTriggers__.parentNode&&(g.__resizeTriggers__=!g.removeChild(g.__resizeTriggers__))))}}(),window.Apex===void 0&&(window.Apex={});var _e=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"initModules",value:function(){this.ctx.publicMethods=["updateOptions","updateSeries","appendData","appendSeries","toggleSeries","showSeries","hideSeries","setLocale","resetSeries","zoomX","toggleDataPointSelection","dataURI","addXaxisAnnotation","addYaxisAnnotation","addPointAnnotation","clearAnnotations","removeAnnotation","paper","destroy"],this.ctx.eventList=["click","mousedown","mousemove","mouseleave","touchstart","touchmove","touchleave","mouseup","touchend"],this.ctx.animations=new K(this.ctx),this.ctx.axes=new nt(this.ctx),this.ctx.core=new Pt(this.ctx.el,this.ctx),this.ctx.config=new le({}),this.ctx.data=new Fe(this.ctx),this.ctx.grid=new Re(this.ctx),this.ctx.graphics=new I(this.ctx),this.ctx.coreUtils=new D(this.ctx),this.ctx.crosshairs=new ke(this.ctx),this.ctx.events=new st(this.ctx),this.ctx.exports=new ve(this.ctx),this.ctx.localization=new rt(this.ctx),this.ctx.options=new $,this.ctx.responsive=new ot(this.ctx),this.ctx.series=new j(this.ctx),this.ctx.theme=new lt(this.ctx),this.ctx.formatters=new ge(this.ctx),this.ctx.titleSubtitle=new ht(this.ctx),this.ctx.legend=new He(this.ctx),this.ctx.toolbar=new De(this.ctx),this.ctx.dimensions=new ue(this.ctx),this.ctx.updateHelpers=new Tt(this.ctx),this.ctx.zoomPanSelection=new pt(this.ctx),this.ctx.w.globals.tooltip=new yt(this.ctx)}}]),v}(),je=function(){function v(e){X(this,v),this.ctx=e,this.w=e.w}return E(v,[{key:"clear",value:function(e){var t=e.isUpdating;this.ctx.zoomPanSelection&&this.ctx.zoomPanSelection.destroy(),this.ctx.toolbar&&this.ctx.toolbar.destroy(),this.ctx.animations=null,this.ctx.axes=null,this.ctx.annotations=null,this.ctx.core=null,this.ctx.data=null,this.ctx.grid=null,this.ctx.series=null,this.ctx.responsive=null,this.ctx.theme=null,this.ctx.formatters=null,this.ctx.titleSubtitle=null,this.ctx.legend=null,this.ctx.dimensions=null,this.ctx.options=null,this.ctx.crosshairs=null,this.ctx.zoomPanSelection=null,this.ctx.updateHelpers=null,this.ctx.toolbar=null,this.ctx.localization=null,this.ctx.w.globals.tooltip=null,this.clearDomElements({isUpdating:t})}},{key:"killSVG",value:function(e){e.each(function(t,i){this.removeClass("*"),this.off(),this.stop()},!0),e.ungroup(),e.clear()}},{key:"clearDomElements",value:function(e){var t=this,i=e.isUpdating,a=this.w.globals.dom.Paper.node;a.parentNode&&a.parentNode.parentNode&&!i&&(a.parentNode.parentNode.style.minHeight="unset");var s=this.w.globals.dom.baseEl;s&&this.ctx.eventList.forEach(function(r){s.removeEventListener(r,t.ctx.events.documentEvent)});var n=this.w.globals.dom;if(this.ctx.el!==null)for(;this.ctx.el.firstChild;)this.ctx.el.removeChild(this.ctx.el.firstChild);this.killSVG(n.Paper),n.Paper.remove(),n.elWrap=null,n.elGraphical=null,n.elAnnotations=null,n.elLegendWrap=null,n.baseEl=null,n.elGridRect=null,n.elGridRectMask=null,n.elGridRectMarkerMask=null,n.elForecastMask=null,n.elNonForecastMask=null,n.elDefs=null}}]),v}(),It=function(){function v(e,t){X(this,v),this.opts=t,this.ctx=this,this.w=new at(t).init(),this.el=e,this.w.globals.cuid=L.randomId(),this.w.globals.chartID=this.w.config.chart.id?L.escapeString(this.w.config.chart.id):this.w.globals.cuid,new _e(this).initModules(),this.create=L.bind(this.create,this),this.windowResizeHandler=this._windowResizeHandler.bind(this),this.parentResizeHandler=this._parentResizeCallback.bind(this)}return E(v,[{key:"render",value:function(){var e=this;return new Promise(function(t,i){if(e.el!==null){Apex._chartInstances===void 0&&(Apex._chartInstances=[]),e.w.config.chart.id&&Apex._chartInstances.push({id:e.w.globals.chartID,group:e.w.config.chart.group,chart:e}),e.setLocale(e.w.config.chart.defaultLocale);var a=e.w.config.chart.events.beforeMount;typeof a=="function"&&a(e,e.w),e.events.fireEvent("beforeMount",[e,e.w]),window.addEventListener("resize",e.windowResizeHandler),window.addResizeListener(e.el.parentNode,e.parentResizeHandler);var s=e.create(e.w.config.series,{});if(!s)return t(e);e.mount(s).then(function(){typeof e.w.config.chart.events.mounted=="function"&&e.w.config.chart.events.mounted(e,e.w),e.events.fireEvent("mounted",[e,e.w]),t(s)}).catch(function(n){i(n)})}else i(new Error("Element not found"))})}},{key:"create",value:function(e,t){var i=this.w;new _e(this).initModules();var a=this.w.globals;if(a.noData=!1,a.animationEnded=!1,this.responsive.checkResponsiveConfig(t),i.config.xaxis.convertedCatToNumeric&&new oe(i.config).convertCatToNumericXaxis(i.config,this.ctx),this.el===null||(this.core.setupElements(),i.config.chart.type==="treemap"&&(i.config.grid.show=!1,i.config.yaxis[0].show=!1),a.svgWidth===0))return a.animationEnded=!0,null;var s=D.checkComboSeries(e);a.comboCharts=s.comboCharts,a.comboBarCount=s.comboBarCount;var n=e.every(function(d){return d.data&&d.data.length===0});(e.length===0||n)&&this.series.handleNoData(),this.events.setupEventHandlers(),this.data.parseData(e),this.theme.init(),new ne(this).setGlobalMarkerSize(),this.formatters.setLabelFormatters(),this.titleSubtitle.draw(),a.noData&&a.collapsedSeries.length!==a.series.length&&!i.config.legend.showForSingleSeries||this.legend.init(),this.series.hasAllSeriesEqualX(),a.axisCharts&&(this.core.coreCalculations(),i.config.xaxis.type!=="category"&&this.formatters.setLabelFormatters(),this.ctx.toolbar.minX=i.globals.minX,this.ctx.toolbar.maxX=i.globals.maxX),this.formatters.heatmapLabelFormatters(),this.dimensions.plotCoords();var r=this.core.xySettings();this.grid.createGridMask();var o=this.core.plotChartType(e,r),h=new ee(this);h.bringForward(),i.config.dataLabels.background.enabled&&h.dataLabelsBackground(),this.core.shiftGraphPosition();var c={plot:{left:i.globals.translateX,top:i.globals.translateY,width:i.globals.gridWidth,height:i.globals.gridHeight}};return{elGraph:o,xyRatios:r,elInner:i.globals.dom.elGraphical,dimensions:c}}},{key:"mount",value:function(){var e=this,t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,i=this,a=i.w;return new Promise(function(s,n){if(i.el===null)return n(new Error("Not enough data to display or target element not found"));(t===null||a.globals.allSeriesCollapsed)&&i.series.handleNoData(),a.config.chart.type!=="treemap"&&i.axes.drawAxis(a.config.chart.type,t.xyRatios),i.grid=new Re(i);var r=i.grid.drawGrid();i.annotations=new et(i),i.annotations.drawImageAnnos(),i.annotations.drawTextAnnos(),a.config.grid.position==="back"&&r&&a.globals.dom.elGraphical.add(r.el);var o=new he(e.ctx),h=new we(e.ctx);if(r!==null&&(o.xAxisLabelCorrections(r.xAxisTickWidth),h.setYAxisTextAlignments(),a.config.yaxis.map(function(g,f){a.globals.ignoreYAxisIndexes.indexOf(f)===-1&&h.yAxisTitleRotate(f,g.opposite)})),a.config.annotations.position==="back"&&(a.globals.dom.Paper.add(a.globals.dom.elAnnotations),i.annotations.drawAxesAnnotations()),Array.isArray(t.elGraph))for(var c=0;c<t.elGraph.length;c++)a.globals.dom.elGraphical.add(t.elGraph[c]);else a.globals.dom.elGraphical.add(t.elGraph);if(a.config.grid.position==="front"&&r&&a.globals.dom.elGraphical.add(r.el),a.config.xaxis.crosshairs.position==="front"&&i.crosshairs.drawXCrosshairs(),a.config.yaxis[0].crosshairs.position==="front"&&i.crosshairs.drawYCrosshairs(),a.config.annotations.position==="front"&&(a.globals.dom.Paper.add(a.globals.dom.elAnnotations),i.annotations.drawAxesAnnotations()),!a.globals.noData){if(a.config.tooltip.enabled&&!a.globals.noData&&i.w.globals.tooltip.drawTooltip(t.xyRatios),a.globals.axisCharts&&(a.globals.isXNumeric||a.config.xaxis.convertedCatToNumeric||a.globals.isTimelineBar))(a.config.chart.zoom.enabled||a.config.chart.selection&&a.config.chart.selection.enabled||a.config.chart.pan&&a.config.chart.pan.enabled)&&i.zoomPanSelection.init({xyRatios:t.xyRatios});else{var d=a.config.chart.toolbar.tools;["zoom","zoomin","zoomout","selection","pan","reset"].forEach(function(g){d[g]=!1})}a.config.chart.toolbar.show&&!a.globals.allSeriesCollapsed&&i.toolbar.createToolbar()}a.globals.memory.methodsToExec.length>0&&a.globals.memory.methodsToExec.forEach(function(g){g.method(g.params,!1,g.context)}),a.globals.axisCharts||a.globals.noData||i.core.resizeNonAxisCharts(),s(i)})}},{key:"destroy",value:function(){window.removeEventListener("resize",this.windowResizeHandler),window.removeResizeListener(this.el.parentNode,this.parentResizeHandler);var e=this.w.config.chart.id;e&&Apex._chartInstances.forEach(function(t,i){t.id===L.escapeString(e)&&Apex._chartInstances.splice(i,1)}),new je(this.ctx).clear({isUpdating:!1})}},{key:"updateOptions",value:function(e){var t=this,i=arguments.length>1&&arguments[1]!==void 0&&arguments[1],a=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],s=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3],n=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],r=this.w;return r.globals.selection=void 0,e.series&&(this.series.resetSeries(!1,!0,!1),e.series.length&&e.series[0].data&&(e.series=e.series.map(function(o,h){return t.updateHelpers._extendSeries(o,h)})),this.updateHelpers.revertDefaultAxisMinMax()),e.xaxis&&(e=this.updateHelpers.forceXAxisUpdate(e)),e.yaxis&&(e=this.updateHelpers.forceYAxisUpdate(e)),r.globals.collapsedSeriesIndices.length>0&&this.series.clearPreviousPaths(),e.theme&&(e=this.theme.updateThemeOptions(e)),this.updateHelpers._updateOptions(e,i,a,s,n)}},{key:"updateSeries",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2];return this.series.resetSeries(!1),this.updateHelpers.revertDefaultAxisMinMax(),this.updateHelpers._updateSeries(e,t,i)}},{key:"appendSeries",value:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],a=this.w.config.series.slice();return a.push(e),this.series.resetSeries(!1),this.updateHelpers.revertDefaultAxisMinMax(),this.updateHelpers._updateSeries(a,t,i)}},{key:"appendData",value:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=this;i.w.globals.dataChanged=!0,i.series.getPreviousPaths();for(var a=i.w.config.series.slice(),s=0;s<a.length;s++)if(e[s]!==null&&e[s]!==void 0)for(var n=0;n<e[s].data.length;n++)a[s].data.push(e[s].data[n]);return i.w.config.series=a,t&&(i.w.globals.initialSeries=L.clone(i.w.config.series)),this.update()}},{key:"update",value:function(e){var t=this;return new Promise(function(i,a){new je(t.ctx).clear({isUpdating:!0});var s=t.create(t.w.config.series,e);if(!s)return i(t);t.mount(s).then(function(){typeof t.w.config.chart.events.updated=="function"&&t.w.config.chart.events.updated(t,t.w),t.events.fireEvent("updated",[t,t.w]),t.w.globals.isDirty=!0,i(t)}).catch(function(n){a(n)})})}},{key:"getSyncedCharts",value:function(){var e=this.getGroupedCharts(),t=[this];return e.length&&(t=[],e.forEach(function(i){t.push(i)})),t}},{key:"getGroupedCharts",value:function(){var e=this;return Apex._chartInstances.filter(function(t){if(t.group)return!0}).map(function(t){return e.w.config.chart.group===t.group?t.chart:e})}},{key:"toggleSeries",value:function(e){return this.series.toggleSeries(e)}},{key:"showSeries",value:function(e){this.series.showSeries(e)}},{key:"hideSeries",value:function(e){this.series.hideSeries(e)}},{key:"resetSeries",value:function(){var e=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];this.series.resetSeries(e,t)}},{key:"addEventListener",value:function(e,t){this.events.addEventListener(e,t)}},{key:"removeEventListener",value:function(e,t){this.events.removeEventListener(e,t)}},{key:"addXaxisAnnotation",value:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,a=this;i&&(a=i),a.annotations.addXaxisAnnotationExternal(e,t,a)}},{key:"addYaxisAnnotation",value:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,a=this;i&&(a=i),a.annotations.addYaxisAnnotationExternal(e,t,a)}},{key:"addPointAnnotation",value:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,a=this;i&&(a=i),a.annotations.addPointAnnotationExternal(e,t,a)}},{key:"clearAnnotations",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0,t=this;e&&(t=e),t.annotations.clearAnnotations(t)}},{key:"removeAnnotation",value:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,i=this;t&&(i=t),i.annotations.removeAnnotation(i,e)}},{key:"getChartArea",value:function(){return this.w.globals.dom.baseEl.querySelector(".apexcharts-inner")}},{key:"getSeriesTotalXRange",value:function(e,t){return this.coreUtils.getSeriesTotalsXRange(e,t)}},{key:"getHighestValueInSeries",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=new ye(this.ctx);return t.getMinYMaxY(e).highestY}},{key:"getLowestValueInSeries",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=new ye(this.ctx);return t.getMinYMaxY(e).lowestY}},{key:"getSeriesTotal",value:function(){return this.w.globals.seriesTotals}},{key:"toggleDataPointSelection",value:function(e,t){return this.updateHelpers.toggleDataPointSelection(e,t)}},{key:"zoomX",value:function(e,t){this.ctx.toolbar.zoomUpdateOptions(e,t)}},{key:"setLocale",value:function(e){this.localization.setCurrentLocaleValues(e)}},{key:"dataURI",value:function(e){return new ve(this.ctx).dataURI(e)}},{key:"paper",value:function(){return this.w.globals.dom.Paper}},{key:"_parentResizeCallback",value:function(){this.w.globals.animationEnded&&this.w.config.chart.redrawOnParentResize&&this._windowResize()}},{key:"_windowResize",value:function(){var e=this;clearTimeout(this.w.globals.resizeTimer),this.w.globals.resizeTimer=window.setTimeout(function(){e.w.globals.resized=!0,e.w.globals.dataChanged=!1,e.ctx.update()},150)}},{key:"_windowResizeHandler",value:function(){var e=this.w.config.chart.redrawOnWindowResize;typeof e=="function"&&(e=e()),e&&this._windowResize()}}],[{key:"getChartByID",value:function(e){var t=L.escapeString(e),i=Apex._chartInstances.filter(function(a){return a.id===t})[0];return i&&i.chart}},{key:"initOnLoad",value:function(){for(var e=document.querySelectorAll("[data-apexcharts]"),t=0;t<e.length;t++)new v(e[t],JSON.parse(e[t].getAttribute("data-options"))).render()}},{key:"exec",value:function(e,t){var i=this.getChartByID(e);if(i){i.w.globals.isExecCalled=!0;var a=null;if(i.publicMethods.indexOf(t)!==-1){for(var s=arguments.length,n=new Array(s>2?s-2:0),r=2;r<s;r++)n[r-2]=arguments[r];a=i[t].apply(i,n)}return a}}},{key:"merge",value:function(e,t){return L.extend(e,t)}}]),v}();pe.exports=It})(Te,Te.exports);var Xt=Te.exports;const Et=zt(Xt);try{window.ApexCharts=Et}catch{}
