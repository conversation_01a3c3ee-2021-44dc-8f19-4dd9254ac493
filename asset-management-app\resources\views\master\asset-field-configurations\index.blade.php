@extends('layouts.contentNavbarLayout')

@section('title', 'Konfigurasi Field Asset - Asset Management System')

@section('content')
<style>
.field-type-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.field-group-badge {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
}

.table-responsive {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.btn-action {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-settings-3-line me-2"></i>
              Konfigurasi Field Asset
            </h5>
            <small class="text-muted">Kelola field dinamis untuk form input asset</small>
          </div>
          <div class="d-flex gap-2">
            <a href="{{ route('master.lookups.index', ['lookup_code' => 'FIELD_GROUP']) }}" class="btn btn-outline-info">
              <i class="ri-list-settings-line me-1"></i>
              Kelola Field Groups
            </a>
            <a href="{{ route('master.asset-field-configurations.create') }}" class="btn btn-primary">
              <i class="ri-add-line me-1"></i>
              Tambah Field
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" action="{{ route('master.asset-field-configurations.index') }}">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Kategori Asset</label>
                <select name="category_id" class="form-select">
                  <option value="">Semua Kategori</option>
                  @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                      {{ $category->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">Grup Field</label>
                <select name="field_group" class="form-select">
                  <option value="">Semua Grup</option>
                  @foreach($fieldGroups as $key => $group)
                    <option value="{{ $key }}" {{ request('field_group') == $key ? 'selected' : '' }}>
                      {{ $group }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-4">
                <label class="form-label">Pencarian</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="Cari nama field atau label..." 
                       value="{{ request('search') }}">
              </div>
              <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line"></i>
                  </button>
                  <a href="{{ route('master.asset-field-configurations.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line"></i>
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Field Configurations List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body p-0">
          @if($fieldConfigurations->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover mb-0">
                <thead class="table-light">
                  <tr>
                    <th>Field Name</th>
                    <th>Label</th>
                    <th>Type</th>
                    <th>Grup</th>
                    <th>Kategori</th>
                    <th>Required</th>
                    <th>Status</th>
                    <th>Order</th>
                    <th width="120">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($fieldConfigurations as $field)
                    <tr>
                      <td>
                        <code class="text-primary">{{ $field->field_name }}</code>
                      </td>
                      <td>
                        <strong>{{ $field->field_label }}</strong>
                        @if($field->help_text)
                          <br><small class="text-muted">{{ Str::limit($field->help_text, 50) }}</small>
                        @endif
                      </td>
                      <td>
                        <span class="badge bg-info field-type-badge">
                          {{ $field->field_type_display }}
                        </span>
                      </td>
                      <td>
                        <span class="badge bg-secondary field-group-badge">
                          {{ $fieldGroups[$field->field_group] ?? $field->field_group }}
                        </span>
                      </td>
                      <td>
                        @if($field->assetCategory)
                          <span class="badge bg-primary">{{ $field->assetCategory->name }}</span>
                        @else
                          <span class="badge bg-light text-dark">Global</span>
                        @endif
                      </td>
                      <td>
                        @if($field->is_required)
                          <span class="badge bg-danger">Required</span>
                        @else
                          <span class="badge bg-light text-muted">Optional</span>
                        @endif
                      </td>
                      <td>
                        @if($field->is_active)
                          <span class="badge bg-success">Active</span>
                        @else
                          <span class="badge bg-secondary">Inactive</span>
                        @endif
                      </td>
                      <td>
                        <span class="badge bg-light text-dark">{{ $field->sort_order }}</span>
                      </td>
                      <td>
                        <div class="dropdown">
                          <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="ri-more-2-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="{{ route('master.asset-field-configurations.show', $field) }}">
                                <i class="ri-eye-line me-1"></i> Lihat Detail
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="{{ route('master.asset-field-configurations.edit', $field) }}">
                                <i class="ri-pencil-line me-1"></i> Edit
                              </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                              <form action="{{ route('master.asset-field-configurations.destroy', $field) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="dropdown-item text-danger" 
                                        onclick="return confirm('Yakin ingin menghapus field ini?\n\nTindakan ini tidak dapat dibatalkan!')">
                                  <i class="ri-delete-bin-line me-1"></i> Hapus
                                </button>
                              </form>
                            </li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            @if($fieldConfigurations->hasPages())
              <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="text-muted">
                    Menampilkan {{ $fieldConfigurations->firstItem() }} - {{ $fieldConfigurations->lastItem() }} 
                    dari {{ $fieldConfigurations->total() }} field
                  </div>
                  {{ $fieldConfigurations->links() }}
                </div>
              </div>
            @endif
          @else
            <!-- Empty State -->
            <div class="text-center py-5">
              <div class="mb-3">
                <i class="ri-settings-3-line ri-48px text-muted opacity-50"></i>
              </div>
              <h5 class="text-muted">Belum Ada Konfigurasi Field</h5>
              <p class="text-muted mb-4">Mulai dengan menambahkan field konfigurasi untuk asset.</p>
              <a href="{{ route('master.asset-field-configurations.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>
                Tambah Field Pertama
              </a>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h6 class="card-title">Quick Actions</h6>
          <div class="d-flex gap-2 flex-wrap">
            <a href="{{ route('master.asset-field-configurations.preview') }}" class="btn btn-outline-info btn-sm">
              <i class="ri-eye-line me-1"></i>
              Preview Form
            </a>
            <button class="btn btn-outline-secondary btn-sm" onclick="exportFields()">
              <i class="ri-download-line me-1"></i>
              Export Config
            </button>
            <button class="btn btn-outline-warning btn-sm" onclick="importFields()">
              <i class="ri-upload-line me-1"></i>
              Import Config
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportFields() {
  // TODO: Implement export functionality
  alert('Export functionality will be implemented soon');
}

function importFields() {
  // TODO: Implement import functionality
  alert('Import functionality will be implemented soon');
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
  const filterSelects = document.querySelectorAll('select[name="category_id"], select[name="field_group"]');
  filterSelects.forEach(select => {
    select.addEventListener('change', function() {
      this.form.submit();
    });
  });
});
</script>

@endsection
