<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\StockOpname;

class CheckStockOpnameLock
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the current route name
        $routeName = $request->route()->getName();

        // Define routes that should be blocked during stock opname
        $blockedRoutes = [
            'assets.create',
            'assets.store',
            'assets.edit',
            'assets.update',
            'assets.destroy',
            'asset-categories.create',
            'asset-categories.store',
            'asset-categories.edit',
            'asset-categories.update',
            'asset-categories.destroy'
        ];

        // Check if current route is in blocked routes
        if (in_array($routeName, $blockedRoutes)) {
            // Get branch ID from request or asset
            $branchId = $this->getBranchIdFromRequest($request);

            if ($branchId) {
                // Check if there's an active stock opname for this specific branch
                $activeOpname = StockOpname::where('status', 'in_progress')
                                          ->where('is_locked', true)
                                          ->where('branch_id', $branchId)
                                          ->with('branch')
                                          ->first();

                if ($activeOpname) {
                    // If it's an AJAX request, return JSON response
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => "Operasi asset dikunci karena sedang ada stock opname yang berjalan di cabang {$activeOpname->branch->name}.",
                            'stock_opname' => [
                                'title' => $activeOpname->title,
                                'opname_number' => $activeOpname->opname_number,
                                'branch' => $activeOpname->branch->name,
                                'branch_id' => $activeOpname->branch_id
                            ]
                        ], 423); // 423 Locked
                    }

                    // For regular requests, redirect with error message
                    return redirect()->back()->with('error',
                        "Operasi asset dikunci karena sedang ada stock opname yang berjalan di cabang {$activeOpname->branch->name}: " .
                        $activeOpname->title . ' (' . $activeOpname->opname_number . ')'
                    )->with('stock_opname_info', [
                        'title' => $activeOpname->title,
                        'opname_number' => $activeOpname->opname_number,
                        'branch' => $activeOpname->branch->name,
                        'branch_id' => $activeOpname->branch_id,
                        'url' => route('stock-opnames.show', $activeOpname)
                    ]);
                }
            }
        }

        return $next($request);
    }

    /**
     * Get branch ID from request context
     */
    private function getBranchIdFromRequest(Request $request): ?int
    {
        // Try to get branch_id from form data
        if ($request->has('branch_id')) {
            return $request->input('branch_id');
        }

        // Try to get from asset being edited
        if ($request->route('asset')) {
            $asset = $request->route('asset');
            return $asset->branch_id ?? null;
        }

        // Try to get from asset_category being edited
        if ($request->route('asset_category')) {
            $assetCategory = $request->route('asset_category');
            return $assetCategory->branch_id ?? null;
        }

        // For create operations, try to get from user's default branch
        if (auth()->check()) {
            $user = auth()->user();
            if ($user->branch_id) {
                return $user->branch_id;
            }
        }

        return null;
    }
}
