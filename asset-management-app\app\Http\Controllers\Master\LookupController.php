<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Lookup;
use Illuminate\Http\Request;

class LookupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Lookup::query();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('lookup_code', 'like', "%{$search}%")
                  ->orWhere('lookup_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('lookup_code')) {
            $query->where('lookup_code', $request->lookup_code);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $lookups = $query->ordered()->paginate(15);
        $categories = Lookup::getCategories();
        $codes = Lookup::getCodes();

        return view('master.lookups.index', compact('lookups', 'categories', 'codes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Lookup::getCategories();
        $codes = Lookup::getCodes();
        return view('master.lookups.create', compact('categories', 'codes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Debug: Log request data
            \Log::info('Lookup store request data:', $request->all());
            // Custom validation rules
            $validated = $request->validate([
                'lookup_code' => [
                    'required',
                    'string',
                    'max:255',
                    'regex:/^[A-Z0-9_]+$/', // Only uppercase letters, numbers, and underscores
                ],
                'lookup_name' => [
                    'required',
                    'string',
                    'max:255',
                    'min:2',
                ],
                'description' => 'nullable|string|max:1000',
                'category' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\s_-]*$/',
                'sort_order' => 'required|integer|min:0|max:9999',
                'is_active' => 'boolean',
                'metadata' => 'nullable',
            ], [
                'lookup_code.required' => 'Kode lookup wajib diisi.',
                'lookup_code.regex' => 'Kode lookup hanya boleh mengandung huruf besar, angka, dan underscore.',
                'lookup_name.required' => 'Nama lookup wajib diisi.',
                'lookup_name.min' => 'Nama lookup minimal 2 karakter.',
                'description.max' => 'Deskripsi maksimal 1000 karakter.',
                'category.regex' => 'Kategori hanya boleh mengandung huruf, angka, spasi, underscore, dan dash.',
                'sort_order.required' => 'Urutan tampilan wajib diisi.',
                'sort_order.min' => 'Urutan tampilan tidak boleh negatif.',
                'sort_order.max' => 'Urutan tampilan maksimal 9999.',
            ]);

            // Additional business logic validation
            $this->validateBusinessRules($validated);

            // Process metadata if provided
            if ($request->has('metadata')) {
                if (is_string($request->metadata)) {
                    $metadata = json_decode($request->metadata, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new \InvalidArgumentException('Format metadata tidak valid.');
                    }
                    $validated['metadata'] = $metadata;
                } elseif (is_array($request->metadata)) {
                    $validated['metadata'] = $request->metadata;
                } else {
                    $validated['metadata'] = null;
                }
            } else {
                $validated['metadata'] = null;
            }

            // Set default values
            $validated['is_active'] = $request->has('is_active') ? true : false;
            $validated['lookup_code'] = strtoupper($validated['lookup_code']);

            // Create lookup
            $lookup = Lookup::create($validated);

            return redirect()->route('master.lookups.index')
                ->with('success', "Lookup '{$lookup->lookup_name}' berhasil dibuat dengan kode '{$lookup->lookup_code}'.");

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Terdapat kesalahan dalam data yang diinput. Silakan periksa kembali.');

        } catch (\InvalidArgumentException $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            \Log::error('Error creating lookup: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan sistem. Silakan coba lagi atau hubungi administrator.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Lookup $lookup)
    {
        $relatedLookups = Lookup::where('lookup_code', $lookup->lookup_code)
            ->ordered()
            ->get();

        return view('master.lookups.show', compact('lookup', 'relatedLookups'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Lookup $lookup)
    {
        $categories = Lookup::getCategories();
        $codes = Lookup::getCodes();
        return view('master.lookups.edit', compact('lookup', 'categories', 'codes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Lookup $lookup)
    {
        try {
            // Custom validation rules
            $validated = $request->validate([
                'lookup_code' => [
                    'required',
                    'string',
                    'max:255',
                    'regex:/^[A-Z0-9_]+$/', // Only uppercase letters, numbers, and underscores
                ],
                'lookup_name' => [
                    'required',
                    'string',
                    'max:255',
                    'min:2',
                ],
                'description' => 'nullable|string|max:1000',
                'category' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\s_-]*$/',
                'sort_order' => 'required|integer|min:0|max:9999',
                'is_active' => 'boolean',
                'metadata' => 'nullable',
            ], [
                'lookup_code.required' => 'Kode lookup wajib diisi.',
                'lookup_code.regex' => 'Kode lookup hanya boleh mengandung huruf besar, angka, dan underscore.',
                'lookup_name.required' => 'Nama lookup wajib diisi.',
                'lookup_name.min' => 'Nama lookup minimal 2 karakter.',
                'description.max' => 'Deskripsi maksimal 1000 karakter.',
                'category.regex' => 'Kategori hanya boleh mengandung huruf, angka, spasi, underscore, dan dash.',
                'sort_order.required' => 'Urutan tampilan wajib diisi.',
                'sort_order.min' => 'Urutan tampilan tidak boleh negatif.',
                'sort_order.max' => 'Urutan tampilan maksimal 9999.',
            ]);

            // Additional business logic validation
            $this->validateBusinessRules($validated, $lookup->id);

            // Process metadata if provided
            if ($request->has('metadata')) {
                if (is_string($request->metadata)) {
                    $metadata = json_decode($request->metadata, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        throw new \InvalidArgumentException('Format metadata tidak valid.');
                    }
                    $validated['metadata'] = $metadata;
                } elseif (is_array($request->metadata)) {
                    $validated['metadata'] = $request->metadata;
                } else {
                    $validated['metadata'] = null;
                }
            } else {
                $validated['metadata'] = null;
            }

            // Set default values
            $validated['is_active'] = $request->has('is_active') ? true : false;
            $validated['lookup_code'] = strtoupper($validated['lookup_code']);

            // Update lookup
            $lookup->update($validated);

            return redirect()->route('master.lookups.index')
                ->with('success', "Lookup '{$lookup->lookup_name}' berhasil diperbarui.");

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Terdapat kesalahan dalam data yang diinput. Silakan periksa kembali.');

        } catch (\InvalidArgumentException $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            \Log::error('Error updating lookup: ' . $e->getMessage(), [
                'lookup_id' => $lookup->id,
                'request_data' => $request->all(),
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan sistem. Silakan coba lagi atau hubungi administrator.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Lookup $lookup)
    {
        try {
            // Check if lookup is being used (you can add more checks here)
            // For example, check if it's referenced in other tables

            $lookupName = $lookup->lookup_name;
            $lookupCode = $lookup->lookup_code;

            $lookup->delete();

            return redirect()->route('master.lookups.index')
                ->with('success', "Lookup '{$lookupName}' dengan kode '{$lookupCode}' berhasil dihapus.");

        } catch (\Exception $e) {
            \Log::error('Error deleting lookup: ' . $e->getMessage(), [
                'lookup_id' => $lookup->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('master.lookups.index')
                ->with('error', 'Terjadi kesalahan saat menghapus lookup. Silakan coba lagi atau hubungi administrator.');
        }
    }

    /**
     * Validate business rules for lookup
     */
    private function validateBusinessRules(array $data, $excludeId = null)
    {
        // Check for duplicate combination of code + name in same category (optional business rule)
        $query = Lookup::where('lookup_code', $data['lookup_code'])
            ->where('lookup_name', $data['lookup_name']);

        if (isset($data['category'])) {
            $query->where('category', $data['category']);
        } else {
            $query->whereNull('category');
        }

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        if ($query->exists()) {
            throw new \InvalidArgumentException(
                "Kombinasi kode '{$data['lookup_code']}' dan nama '{$data['lookup_name']}' " .
                "sudah ada dalam kategori yang sama."
            );
        }

        // Validate metadata structure if provided
        if (isset($data['metadata']) && is_array($data['metadata'])) {
            foreach ($data['metadata'] as $key => $value) {
                if (empty($key) || !is_string($key)) {
                    throw new \InvalidArgumentException('Key metadata tidak boleh kosong dan harus berupa string.');
                }

                if (strlen($key) > 100) {
                    throw new \InvalidArgumentException("Key metadata '{$key}' terlalu panjang (maksimal 100 karakter).");
                }

                // Validate value is not too complex
                if (is_array($value) && count($value) > 50) {
                    throw new \InvalidArgumentException("Array metadata untuk key '{$key}' terlalu besar (maksimal 50 item).");
                }
            }
        }

        // Validate sort_order is reasonable
        if (isset($data['sort_order']) && $data['sort_order'] > 9999) {
            throw new \InvalidArgumentException('Urutan tampilan terlalu besar (maksimal 9999).');
        }
    }

    /**
     * Get lookups by code (AJAX)
     */
    public function getByCode(Request $request)
    {
        $code = $request->get('code');
        $lookups = Lookup::getByCode($code);

        return response()->json($lookups);
    }

    /**
     * Get lookups by category (AJAX)
     */
    public function getByCategory(Request $request)
    {
        $category = $request->get('category');
        $lookups = Lookup::getByCategory($category);

        return response()->json($lookups);
    }
}
