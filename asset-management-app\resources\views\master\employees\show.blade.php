@extends('layouts.contentNavbarLayout')

@section('title', 'Detail <PERSON>wan - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('master.employees.index') }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-user-line me-2"></i>
                  Detail Ka<PERSON>wan
                </h5>
                <small class="text-muted">{{ $employee->full_name }} - {{ $employee->nik }}</small>
              </div>
            </div>
            <div>
              <a href="{{ route('master.employees.edit', $employee) }}" class="btn btn-primary">
                <i class="ri-edit-line me-2"></i>
                Edit Karyawan
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Employee Information -->
    <div class="col-lg-8">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Karyawan
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Dasar</h6>
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">NIK:</td>
                  <td><span class="badge bg-primary">{{ $employee->nik }}</span></td>
                </tr>
                <tr>
                  <td class="text-muted">Nama Lengkap:</td>
                  <td><strong>{{ $employee->full_name }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Email:</td>
                  <td>{{ $employee->email ?: '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">No. Telepon:</td>
                  <td>{{ $employee->phone ?: '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Jenis Kelamin:</td>
                  <td>{{ $employee->gender_text }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Tanggal Lahir:</td>
                  <td>
                    @if($employee->birth_date)
                      {{ $employee->birth_date->format('d/m/Y') }}
                      <small class="text-muted">({{ $employee->age }} tahun)</small>
                    @else
                      -
                    @endif
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Alamat:</td>
                  <td>{{ $employee->address ?: '-' }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Pekerjaan</h6>
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Cabang:</td>
                  <td><strong>{{ $employee->branch->name }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Divisi:</td>
                  <td>{{ $employee->division->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Bagian:</td>
                  <td>{{ $employee->department ?: '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Jabatan:</td>
                  <td><strong>{{ $employee->position }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Status Karyawan:</td>
                  <td>
                    <span class="badge bg-{{ $employee->employee_status === 'permanent' ? 'success' : ($employee->employee_status === 'contract' ? 'warning' : 'info') }}">
                      {{ $employee->employee_status_text }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Tanggal Bergabung:</td>
                  <td>
                    @if($employee->join_date)
                      {{ $employee->join_date->format('d/m/Y') }}
                      <small class="text-muted">({{ $employee->work_duration }})</small>
                    @else
                      -
                    @endif
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Status:</td>
                  <td>
                    <span class="badge bg-{{ $employee->is_active ? 'success' : 'danger' }}">
                      {{ $employee->status_text }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>
          </div>
          
          @if($employee->notes)
            <div class="row mt-4">
              <div class="col-12">
                <h6 class="text-muted mb-2">Catatan</h6>
                <div class="alert alert-info">
                  {{ $employee->notes }}
                </div>
              </div>
            </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Statistics & Quick Actions -->
    <div class="col-lg-4">
      <!-- Statistics -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-bar-chart-line me-2"></i>
            Statistik Asset
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-6">
              <div class="border-end">
                <h4 class="mb-1 text-primary">{{ $employee->getAssignedAssetsCount() }}</h4>
                <small class="text-muted">Total Asset</small>
              </div>
            </div>
            <div class="col-6">
              <h4 class="mb-1 text-success">{{ $employee->getActiveAssetsCount() }}</h4>
              <small class="text-muted">Asset Aktif</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-settings-line me-2"></i>
            Aksi Cepat
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.employees.edit', $employee) }}" class="btn btn-outline-primary">
              <i class="ri-edit-line me-2"></i>
              Edit Data Karyawan
            </a>
            <button type="button" class="btn btn-outline-info" onclick="toggleStatus()">
              <i class="ri-toggle-line me-2"></i>
              {{ $employee->is_active ? 'Nonaktifkan' : 'Aktifkan' }} Karyawan
            </button>
            @if($employee->getAssignedAssetsCount() == 0)
              <form action="{{ route('master.employees.destroy', $employee) }}" method="POST" 
                    onsubmit="return confirm('Yakin ingin menghapus karyawan ini?')" class="d-inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-outline-danger w-100">
                  <i class="ri-delete-bin-line me-2"></i>
                  Hapus Karyawan
                </button>
              </form>
            @endif
          </div>
        </div>
      </div>

      <!-- Employee Timeline -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-time-line me-2"></i>
            Timeline
          </h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-point timeline-point-primary"></div>
              <div class="timeline-event">
                <div class="timeline-header">
                  <h6 class="timeline-title">Karyawan Dibuat</h6>
                  <small class="text-muted">{{ $employee->created_at->format('d/m/Y H:i') }}</small>
                </div>
                <p class="timeline-text">Data karyawan ditambahkan ke sistem</p>
              </div>
            </div>
            
            @if($employee->join_date)
              <div class="timeline-item">
                <div class="timeline-point timeline-point-success"></div>
                <div class="timeline-event">
                  <div class="timeline-header">
                    <h6 class="timeline-title">Bergabung</h6>
                    <small class="text-muted">{{ $employee->join_date->format('d/m/Y') }}</small>
                  </div>
                  <p class="timeline-text">Mulai bekerja di {{ $employee->branch->name }}</p>
                </div>
              </div>
            @endif

            @if($employee->updated_at != $employee->created_at)
              <div class="timeline-item">
                <div class="timeline-point timeline-point-info"></div>
                <div class="timeline-event">
                  <div class="timeline-header">
                    <h6 class="timeline-title">Data Diperbarui</h6>
                    <small class="text-muted">{{ $employee->updated_at->format('d/m/Y H:i') }}</small>
                  </div>
                  <p class="timeline-text">Data karyawan terakhir diperbarui</p>
                </div>
              </div>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Assigned Assets -->
  @if($employee->assets->count() > 0)
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-archive-line me-2"></i>
              Asset yang Di-assign ({{ $employee->assets->count() }})
            </h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Kode Asset</th>
                    <th>Nama Asset</th>
                    <th>Kategori</th>
                    <th>Status</th>
                    <th>Tanggal Assignment</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($employee->assets as $asset)
                    <tr>
                      <td><span class="badge bg-primary">{{ $asset->asset_code }}</span></td>
                      <td>{{ $asset->name }}</td>
                      <td>{{ $asset->assetCategory->name ?? '-' }}</td>
                      <td>
                        <span class="badge bg-{{ $asset->status === 'active' ? 'success' : 'warning' }}">
                          {{ ucfirst($asset->status) }}
                        </span>
                      </td>
                      <td>{{ $asset->assigned_at ? $asset->assigned_at->format('d/m/Y') : '-' }}</td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  @endif
</div>

<script>
function toggleStatus() {
  if (confirm('Yakin ingin mengubah status karyawan ini?')) {
    fetch(`/master/employees/{{ $employee->id }}/toggle-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('Gagal mengubah status karyawan');
      }
    })
    .catch(error => {
      alert('Terjadi kesalahan saat mengubah status');
    });
  }
}
</script>

@endsection
