@extends('layouts/contentNavbarLayout')

@section('title', 'Tracking Maintenance - ' . $maintenance->maintenance_number)

@section('page-meta')
<meta name="description" content="Tracking status maintenance {{ $maintenance->maintenance_number }} untuk asset {{ $maintenance->asset->asset_code }} - {{ $maintenance->asset->name }}">
<meta name="keywords" content="maintenance tracking, asset management, {{ $maintenance->maintenance_number }}">
<meta name="robots" content="noindex, nofollow">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:title" content="Tracking Maintenance - {{ $maintenance->maintenance_number }}">
<meta property="og:description" content="Status maintenance untuk asset {{ $maintenance->asset->asset_code }} - {{ $maintenance->asset->name }}">

<!-- Twitter -->
<meta property="twitter:card" content="summary">
<meta property="twitter:title" content="Tracking Maintenance - {{ $maintenance->maintenance_number }}">
<meta property="twitter:description" content="Status maintenance untuk asset {{ $maintenance->asset->asset_code }}">
@endsection

@section('page-style')
<style>
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 5px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid #fff;
  background: #6c757d;
}

.timeline-marker.active {
  background: #28a745;
}

.timeline-marker.current {
  background: #007bff;
}

.status-card {
  border-left: 4px solid #dee2e6;
  transition: all 0.3s ease;
}

.status-card.active {
  border-left-color: #28a745;
  background-color: #f8fff9;
}

.status-card.current {
  border-left-color: #007bff;
  background-color: #f8f9ff;
}

.qr-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
}
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h4 class="mb-1">
                <i class="ri-tools-line me-2"></i>
                Tracking Maintenance
                @if(!$currentUser)
                  <span class="badge bg-info ms-2">Public View</span>
                @endif
              </h4>
              <p class="text-muted mb-0">
                Nomor: <strong>{{ $maintenance->maintenance_number }}</strong>
                @if(!$currentUser)
                  <br><small class="text-info">
                    <i class="ri-information-line me-1"></i>
                    Anda dapat melihat status maintenance secara real-time tanpa perlu login
                  </small>
                @endif
              </p>
            </div>
            <div class="col-md-4 text-end">
              <span class="badge {{ $maintenance->status_badge_class }} fs-6">
                {{ $maintenance->status_text }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
      
      <!-- Asset Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-computer-line me-2"></i>
            Informasi Asset
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Asset</label>
                <p class="fw-bold text-primary">{{ $maintenance->asset->asset_code }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Asset</label>
                <p class="fw-bold">{{ $maintenance->asset->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kategori</label>
                <p>{{ $maintenance->asset->assetCategory->name ?? '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Cabang</label>
                <p>{{ $maintenance->asset->branch->name ?? '-' }}</p>
              </div>
            </div>
            @if($maintenance->asset->assignedEmployee)
            <div class="col-12">
              <div class="alert alert-info">
                <h6 class="alert-heading">
                  <i class="ri-user-line me-2"></i>
                  Karyawan yang Menggunakan Asset
                </h6>
                <div class="row">
                  <div class="col-md-4">
                    <strong>NIK:</strong> {{ $maintenance->asset->assignedEmployee->nik ?? '-' }}
                  </div>
                  <div class="col-md-4">
                    <strong>Nama:</strong> {{ $maintenance->asset->assignedEmployee->full_name ?? '-' }}
                  </div>
                  <div class="col-md-4">
                    <strong>Jabatan:</strong> {{ $maintenance->asset->assignedEmployee->position ?? '-' }}
                  </div>
                </div>
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>

      <!-- Maintenance Details -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-file-text-line me-2"></i>
            Detail Maintenance
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Judul</label>
                <p class="fw-bold">{{ $maintenance->title }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tipe Maintenance</label>
                <p><span class="badge bg-info">{{ $maintenance->maintenance_type_text }}</span></p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Prioritas</label>
                <p><span class="badge {{ $maintenance->priority_badge_class }}">{{ $maintenance->priority_text }}</span></p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tanggal Jadwal</label>
                <p>{{ $maintenance->scheduled_date ? $maintenance->scheduled_date->format('d/m/Y') : '-' }}</p>
              </div>
            </div>
            @if($maintenance->description)
            <div class="col-12">
              <div class="mb-3">
                <label class="form-label text-muted">Deskripsi Masalah</label>
                <p>{{ $maintenance->description }}</p>
              </div>
            </div>
            @endif
            @if($maintenance->solution_description)
            <div class="col-12">
              <div class="mb-3">
                <label class="form-label text-muted">Solusi yang Dilakukan</label>
                <p>{{ $maintenance->solution_description }}</p>
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>

      <!-- Supplier Information -->
      @if($maintenance->supplier)
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-building-line me-2"></i>
            Informasi Supplier
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Supplier</label>
                <p class="fw-bold text-primary">{{ $maintenance->supplier->supplier_code }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Supplier</label>
                <p class="fw-bold">{{ $maintenance->supplier->name }}</p>
              </div>
            </div>
            @if($maintenance->supplier->phone)
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Telepon</label>
                <p>
                  {{ $maintenance->supplier->phone }}
                  <a href="tel:{{ $maintenance->supplier->phone }}" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="ri-phone-line"></i>
                  </a>
                </p>
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>
      @endif

      <!-- Receipt Information -->
      @if($receipt)
      <div class="card mb-4">
        <div class="card-header bg-success text-white">
          <h6 class="card-title mb-0 text-white">
            <i class="ri-check-line me-2"></i>
            Penerimaan Maintenance
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Diterima Tanggal</label>
                <p class="fw-bold">{{ $receipt->received_date->format('d/m/Y') }} {{ $receipt->received_time }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Diterima Oleh</label>
                <p class="fw-bold">{{ $receipt->receiver_name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Jabatan</label>
                <p>{{ $receipt->receiver_position }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kondisi Asset</label>
                <p><span class="badge {{ $receipt->condition_badge_class }}">{{ $receipt->condition_text }}</span></p>
              </div>
            </div>
            @if($receipt->notes)
            <div class="col-12">
              <div class="mb-3">
                <label class="form-label text-muted">Catatan</label>
                <p>{{ $receipt->notes }}</p>
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>
      @endif

    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
      
      <!-- QR Code Info -->
      <div class="card mb-4">
        <div class="card-body qr-info">
          <h6 class="text-white mb-3">
            <i class="ri-qr-code-line me-2"></i>
            QR Code Tracking
          </h6>
          <p class="text-white-50 mb-3">
            Scan QR code pada form maintenance untuk mengakses halaman tracking ini
          </p>
          @if(!$currentUser)
          <div class="alert alert-light">
            <small class="text-dark">
              <i class="ri-shield-check-line me-1"></i>
              <strong>Akses Public:</strong> Halaman ini dapat diakses tanpa login untuk transparansi tracking maintenance.
            </small>
          </div>
          @endif
        </div>
      </div>

      <!-- Status Timeline -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-time-line me-2"></i>
            Status Timeline
          </h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-marker active"></div>
              <div class="status-card card active">
                <div class="card-body py-2">
                  <h6 class="mb-1">Dijadwalkan</h6>
                  <small class="text-muted">{{ $maintenance->created_at->format('d/m/Y H:i') }}</small>
                </div>
              </div>
            </div>
            
            @if($maintenance->started_date)
            <div class="timeline-item">
              <div class="timeline-marker active"></div>
              <div class="status-card card active">
                <div class="card-body py-2">
                  <h6 class="mb-1">Sedang Dikerjakan</h6>
                  <small class="text-muted">{{ $maintenance->started_date->format('d/m/Y') }}</small>
                </div>
              </div>
            </div>
            @endif
            
            @if($maintenance->completed_date)
            <div class="timeline-item">
              <div class="timeline-marker active"></div>
              <div class="status-card card active">
                <div class="card-body py-2">
                  <h6 class="mb-1">Selesai</h6>
                  <small class="text-muted">{{ $maintenance->completed_date->format('d/m/Y') }}</small>
                </div>
              </div>
            </div>
            @endif
            
            @if($receipt)
            <div class="timeline-item">
              <div class="timeline-marker active"></div>
              <div class="status-card card active">
                <div class="card-body py-2">
                  <h6 class="mb-1">Diterima</h6>
                  <small class="text-muted">{{ $receipt->received_date->format('d/m/Y') }}</small>
                </div>
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>

      <!-- Actions -->
      @if($currentUser)
        @if($canReceive && !$receipt && $maintenance->status === 'completed')
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h6 class="card-title mb-0 text-white">
              <i class="ri-file-edit-line me-2"></i>
              Penerimaan Asset
            </h6>
          </div>
          <div class="card-body">
            <p class="text-muted mb-3">
              Maintenance telah selesai. Silakan isi form penerimaan asset.
            </p>
            <a href="{{ route('maintenance.receipt.form', $maintenance->id) }}" class="btn btn-primary w-100">
              <i class="ri-edit-line me-2"></i>
              Isi Form Penerimaan
            </a>
          </div>
        </div>
        @endif
      @else
        <!-- Login prompt for guest users -->
        @if(!$receipt && $maintenance->status === 'completed')
        <div class="card">
          <div class="card-header bg-info text-white">
            <h6 class="card-title mb-0 text-white">
              <i class="ri-login-box-line me-2"></i>
              Akses Terbatas
            </h6>
          </div>
          <div class="card-body">
            <p class="text-muted mb-3">
              Untuk mengisi form penerimaan maintenance, silakan login terlebih dahulu.
            </p>
            <a href="{{ route('login') }}" class="btn btn-info w-100">
              <i class="ri-login-box-line me-2"></i>
              Login untuk Akses Lengkap
            </a>
          </div>
        </div>
        @endif
      @endif

    </div>
  </div>

</div>
@endsection
