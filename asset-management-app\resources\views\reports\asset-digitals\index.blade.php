@extends('layouts/contentNavbarLayout')

@section('title', 'Laporan Asset Digital')

@section('page-style')
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}">
@endsection

@section('vendor-script')
<script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
<script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-0">
              <i class="ri-computer-line me-2"></i>
              Laporan Asset Digital
            </h5>
            <small class="text-muted">Laporan data asset digital dengan filter cabang, tipe lisensi, tanggal, dan status</small>
          </div>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
              <i class="ri-file-excel-line me-1"></i>
              Export Excel
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
              <i class="ri-refresh-line me-1"></i>
              Reset Filter
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-primary">
                <i class="ri-computer-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['total_assets']) }}</h4>
              <small class="text-muted">Total Asset Digital</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-success">
                <i class="ri-check-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['active_assets']) }}</h4>
              <small class="text-muted">Asset Aktif</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-warning">
                <i class="ri-time-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['expiring_soon']) }}</h4>
              <small class="text-muted">Akan Expired (30 hari)</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-info">
                <i class="ri-money-dollar-circle-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">Rp {{ number_format($stats['total_value'], 0, ',', '.') }}</h5>
              <small class="text-muted">Total Nilai Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Statistics Row -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-danger">
                <i class="ri-close-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['expired_assets']) }}</h4>
              <small class="text-muted">Asset Expired</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-secondary">
                <i class="ri-pause-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['suspended_assets']) }}</h4>
              <small class="text-muted">Asset Suspended</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-dark">
                <i class="ri-user-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['total_users']) }}</h4>
              <small class="text-muted">Total Users Aktif</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-light text-dark">
                <i class="ri-group-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['total_max_users']) }}</h4>
              <small class="text-muted">Total Kapasitas Users</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Form -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="ri-filter-line me-2"></i>
            Filter Laporan
          </h6>
        </div>
        <div class="card-body">
          <form method="GET" action="{{ route('reports.asset-digitals.index') }}" id="filterForm">
            <div class="row">
              <div class="col-md-3 mb-3">
                <label class="form-label">Cabang</label>
                <select name="branch_id" class="form-select">
                  <option value="">Semua Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tipe Lisensi</label>
                <select name="license_type" class="form-select">
                  <option value="">Semua Tipe</option>
                  @foreach($licenseTypes as $licenseType)
                    <option value="{{ $licenseType->name }}" {{ request('license_type') == $licenseType->name ? 'selected' : '' }}>
                      {{ $licenseType->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                  <option value="">Semua Status</option>
                  @foreach($statusOptions as $value => $label)
                    <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                      {{ $label }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Dari</label>
                <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Sampai</label>
                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Expired Dari</label>
                <input type="date" name="expiry_from" class="form-control" value="{{ request('expiry_from') }}">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Expired Sampai</label>
                <input type="date" name="expiry_to" class="form-control" value="{{ request('expiry_to') }}">
              </div>
              <div class="col-md-3 mb-3 d-flex align-items-end">
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line me-1"></i>
                    Filter
                  </button>
                  <a href="{{ route('reports.asset-digitals.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line me-1"></i>
                    Reset
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Asset Digital Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="ri-table-line me-2"></i>
            Data Asset Digital ({{ $assetDigitals->total() }} asset)
          </h6>
          <div class="d-flex gap-2">
            <span class="badge bg-primary">{{ $assetDigitals->count() }} ditampilkan</span>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Kode Asset</th>
                  <th>Nama Asset</th>
                  <th>Tipe Lisensi</th>
                  <th>Cabang</th>
                  <th>Supplier</th>
                  <th>Status</th>
                  <th>Users</th>
                  <th>Tanggal Expired</th>
                  <th>Harga</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                @forelse($assetDigitals as $assetDigital)
                <tr>
                  <td>
                    <strong>{{ $assetDigital->asset_code }}</strong>
                  </td>
                  <td>
                    <div>
                      <strong>{{ $assetDigital->name }}</strong>
                      @if($assetDigital->version)
                        <br><small class="text-muted">v{{ $assetDigital->version }}</small>
                      @endif
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-info">{{ $assetDigital->license_type }}</span>
                  </td>
                  <td>{{ $assetDigital->branch->name ?? '-' }}</td>
                  <td>{{ $assetDigital->supplier->name ?? '-' }}</td>
                  <td>
                    @if($assetDigital->status == 'active')
                      <span class="badge bg-success">Aktif</span>
                    @elseif($assetDigital->status == 'inactive')
                      <span class="badge bg-secondary">Non-Aktif</span>
                    @elseif($assetDigital->status == 'expired')
                      <span class="badge bg-danger">Expired</span>
                    @elseif($assetDigital->status == 'suspended')
                      <span class="badge bg-warning">Suspended</span>
                    @endif
                  </td>
                  <td>
                    <span class="badge bg-dark">
                      {{ $assetDigital->current_users ?? 0 }}/{{ $assetDigital->max_users ?? '∞' }}
                    </span>
                  </td>
                  <td>
                    @if($assetDigital->expiry_date)
                      {{ $assetDigital->expiry_date->format('d/m/Y') }}
                      @if($assetDigital->expiry_date->isPast())
                        <br><small class="text-danger">Sudah Expired</small>
                      @elseif($assetDigital->expiry_date->diffInDays() <= 30)
                        <br><small class="text-warning">{{ $assetDigital->expiry_date->diffInDays() }} hari lagi</small>
                      @endif
                    @else
                      -
                    @endif
                  </td>
                  <td>
                    @if($assetDigital->purchase_price)
                      Rp {{ number_format($assetDigital->purchase_price, 0, ',', '.') }}
                    @else
                      -
                    @endif
                  </td>
                  <td>
                    <div class="dropdown">
                      <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        Aksi
                      </button>
                      <ul class="dropdown-menu">
                        <li>
                          <a href="{{ route('asset-digitals.show', $assetDigital) }}" class="dropdown-item">
                            <i class="ri-eye-line me-2"></i>
                            Detail
                          </a>
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
                @empty
                <tr>
                  <td colspan="10" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                      <i class="ri-computer-line ri-48px text-muted mb-2"></i>
                      <span class="text-muted">Tidak ada data asset digital</span>
                    </div>
                  </td>
                </tr>
                @endforelse
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          @if($assetDigitals->hasPages())
          <div class="d-flex justify-content-center mt-4">
            {{ $assetDigitals->appends(request()->query())->links() }}
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportToExcel() {
    // Get current filter values
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    
    // Redirect to export URL with current filters
    window.location.href = '{{ route("reports.asset-digitals.export") }}?' + params.toString();
}

function resetFilters() {
    window.location.href = '{{ route("reports.asset-digitals.index") }}';
}
</script>
@endsection
