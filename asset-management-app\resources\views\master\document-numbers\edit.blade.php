@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Konfigurasi Nomor Dokumen - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Nomor Dokumen /</span> Edit Konfigurasi
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Edit Konfigurasi Nomor Dokumen</h5>
          <a href="{{ route('master.document-numbers.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.document-numbers.update', $documentNumber) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="document_type">Tipe Dokumen <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('document_type') is-invalid @enderror" 
                       id="document_type" name="document_type" value="{{ old('document_type', $documentNumber->document_type) }}" 
                       placeholder="Contoh: asset, purchase_order, invoice">
                @error('document_type')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Tipe dokumen yang akan menggunakan nomor ini</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="prefix">Prefix <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('prefix') is-invalid @enderror" 
                       id="prefix" name="prefix" value="{{ old('prefix', $documentNumber->prefix) }}" 
                       placeholder="Contoh: AST, PO, INV" maxlength="10">
                @error('prefix')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Prefix untuk nomor dokumen (maksimal 10 karakter)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="format">Format Nomor <span class="text-danger">*</span></label>
              <input type="text" class="form-control @error('format') is-invalid @enderror" 
                     id="format" name="format" value="{{ old('format', $documentNumber->format) }}" 
                     placeholder="{prefix}-{branch}-{year}-{number}">
              @error('format')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">
                Format nomor dokumen. Gunakan placeholder: {prefix}, {branch}, {year}, {number}
                <br>Contoh: {prefix}-{branch}-{year}-{number} → AST-HQ-2025-0001
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="current_number">Nomor Terakhir <span class="text-danger">*</span></label>
                <input type="number" class="form-control @error('current_number') is-invalid @enderror" 
                       id="current_number" name="current_number" value="{{ old('current_number', $documentNumber->current_number) }}" 
                       min="0">
                @error('current_number')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Nomor terakhir yang telah digunakan</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="year">Tahun <span class="text-danger">*</span></label>
                <select class="form-select @error('year') is-invalid @enderror" id="year" name="year">
                  @for($year = 2020; $year <= date('Y') + 5; $year++)
                    <option value="{{ $year }}" {{ old('year', $documentNumber->year) == $year ? 'selected' : '' }}>
                      {{ $year }}
                    </option>
                  @endfor
                </select>
                @error('year')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="branch_id">Cabang</label>
              <select class="form-select @error('branch_id') is-invalid @enderror" id="branch_id" name="branch_id">
                <option value="">Global (Semua Cabang)</option>
                @foreach($branches as $branch)
                  <option value="{{ $branch->id }}" {{ old('branch_id', $documentNumber->branch_id) == $branch->id ? 'selected' : '' }}>
                    {{ $branch->name }} ({{ $branch->code }})
                  </option>
                @endforeach
              </select>
              @error('branch_id')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Pilih cabang spesifik atau biarkan kosong untuk global</div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi konfigurasi nomor dokumen">{{ old('description', $documentNumber->description) }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', $documentNumber->is_active) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.document-numbers.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Update
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Konfigurasi</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Detail Saat Ini:</h6>
            <p class="mb-2">Tipe: <strong>{{ $documentNumber->document_type }}</strong></p>
            <p class="mb-2">Prefix: <strong>{{ $documentNumber->prefix }}</strong></p>
            <p class="mb-2">Cabang: <strong>{{ $documentNumber->branch ? $documentNumber->branch->name : 'Global' }}</strong></p>
            <p class="mb-2">Tahun: <strong>{{ $documentNumber->year }}</strong></p>
            <p class="mb-0">Nomor Terakhir: <span class="badge bg-secondary">{{ str_pad($documentNumber->current_number, 4, '0', STR_PAD_LEFT) }}</span></p>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Peringatan:</h6>
            <ul class="mb-0">
              <li>Hati-hati mengubah nomor terakhir</li>
              <li>Pastikan tidak ada duplikasi nomor</li>
              <li>Perubahan format akan mempengaruhi nomor selanjutnya</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase prefix
  const prefixInput = document.getElementById('prefix');
  prefixInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase();
  });
});
</script>
@endsection
