@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Kontrak - ' . $contract->contract_number)

@section('page-style')
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="ri-file-edit-line me-2"></i>
            Edit Kontrak
          </h5>
          <div class="d-flex gap-2">
            <a href="{{ route('contracts.show', $contract) }}" class="btn btn-outline-info">
              <i class="ri-eye-line me-1"></i>Detail
            </a>
            <a href="{{ route('contracts.index') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i><PERSON><PERSON><PERSON>
            </a>
          </div>
        </div>

        <form action="{{ route('contracts.update', $contract) }}" method="POST" enctype="multipart/form-data">
          @csrf
          @method('PUT')
          <div class="card-body">
            <div class="row">
              <!-- Contract Number -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="contract_number" class="form-label">Nomor Kontrak <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('contract_number') is-invalid @enderror" 
                         id="contract_number" name="contract_number" value="{{ old('contract_number', $contract->contract_number) }}" required>
                  @error('contract_number')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Contract Name -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="contract_name" class="form-label">Nama Kontrak <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('contract_name') is-invalid @enderror" 
                         id="contract_name" name="contract_name" value="{{ old('contract_name', $contract->contract_name) }}" required>
                  @error('contract_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Description -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="description" class="form-label">Deskripsi</label>
                  <textarea class="form-control @error('description') is-invalid @enderror" 
                            id="description" name="description" rows="3">{{ old('description', $contract->description) }}</textarea>
                  @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Supplier -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="supplier_id" class="form-label">Supplier <span class="text-danger">*</span></label>
                  <div class="input-group">
                    <input type="hidden" name="supplier_id" id="supplier_id" value="{{ old('supplier_id', $contract->supplier_id) }}">
                    <input type="text" id="supplier_name" class="form-control @error('supplier_id') is-invalid @enderror"
                           placeholder="Pilih supplier..." readonly required
                           value="{{ old('supplier_name', $contract->supplier ? $contract->supplier->name . ($contract->supplier->company_name ? ' (' . $contract->supplier->company_name . ')' : '') : '') }}">
                    <button type="button" class="btn btn-outline-primary" onclick="openSupplierLookup()">
                      <i class="ri-search-line"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSupplier()" title="Clear">
                      <i class="ri-close-line"></i>
                    </button>
                  </div>
                  @error('supplier_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Division -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="division_id" class="form-label">Divisi</label>
                  <select class="form-select @error('division_id') is-invalid @enderror" id="division_id" name="division_id">
                    <option value="">Pilih Divisi</option>
                    @foreach($divisions as $division)
                      <option value="{{ $division->id }}" {{ old('division_id', $contract->division_id) == $division->id ? 'selected' : '' }}>
                        {{ $division->name }}
                      </option>
                    @endforeach
                  </select>
                  @error('division_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Branch -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="branch_id" class="form-label">Cabang</label>
                  <select class="form-select @error('branch_id') is-invalid @enderror" id="branch_id" name="branch_id">
                    <option value="">Pilih Cabang</option>
                    @foreach($branches as $branch)
                      <option value="{{ $branch->id }}" {{ old('branch_id', $contract->branch_id) == $branch->id ? 'selected' : '' }}>
                        {{ $branch->name }}
                      </option>
                    @endforeach
                  </select>
                  @error('branch_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Contract Date -->
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="contract_date" class="form-label">Tanggal Kontrak <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('contract_date') is-invalid @enderror"
                         id="contract_date" name="contract_date" value="{{ old('contract_date', $contract->contract_date->format('Y-m-d')) }}" required>
                  @error('contract_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Start Date -->
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="start_date" class="form-label">Tanggal Mulai <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('start_date') is-invalid @enderror"
                         id="start_date" name="start_date" value="{{ old('start_date', $contract->start_date->format('Y-m-d')) }}" required>
                  @error('start_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- End Date -->
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="end_date" class="form-label">Tanggal Berakhir <span class="text-danger">*</span></label>
                  <input type="date" class="form-control @error('end_date') is-invalid @enderror"
                         id="end_date" name="end_date" value="{{ old('end_date', $contract->end_date->format('Y-m-d')) }}" required>
                  @error('end_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Contract Value -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="contract_value" class="form-label">Nilai Kontrak</label>
                  <div class="input-group">
                    <span class="input-group-text">Rp</span>
                    <input type="number" class="form-control @error('contract_value') is-invalid @enderror" 
                           id="contract_value" name="contract_value" value="{{ old('contract_value', $contract->contract_value) }}" step="0.01" min="0">
                  </div>
                  @error('contract_value')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Contract Type -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="contract_type" class="form-label">Tipe Kontrak <span class="text-danger">*</span></label>
                  <select class="form-select @error('contract_type') is-invalid @enderror" id="contract_type" name="contract_type" required>
                    <option value="">Pilih Tipe</option>
                    @foreach(\App\Models\Contract::getContractTypes() as $key => $value)
                      <option value="{{ $key }}" {{ old('contract_type', $contract->contract_type) == $key ? 'selected' : '' }}>
                        {{ $value }}
                      </option>
                    @endforeach
                  </select>
                  @error('contract_type')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Status -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                  <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                    @foreach(\App\Models\Contract::getStatuses() as $key => $value)
                      <option value="{{ $key }}" {{ old('status', $contract->status) == $key ? 'selected' : '' }}>
                        {{ $value }}
                      </option>
                    @endforeach
                  </select>
                  @error('status')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Notification Days -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="notification_days" class="form-label">Notifikasi Kadaluarsa (Hari) <span class="text-danger">*</span></label>
                  <input type="number" class="form-control @error('notification_days') is-invalid @enderror" 
                         id="notification_days" name="notification_days" value="{{ old('notification_days', $contract->notification_days) }}" required min="1" max="365">
                  @error('notification_days')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <small class="text-muted">Sistem akan memberikan notifikasi sebelum kontrak kadaluarsa</small>
                </div>
              </div>

              <!-- Auto Renewal -->
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="auto_renewal" name="auto_renewal" value="1" 
                           {{ old('auto_renewal', $contract->auto_renewal) ? 'checked' : '' }}>
                    <label class="form-check-label" for="auto_renewal">
                      Perpanjangan Otomatis
                    </label>
                  </div>
                </div>
              </div>

              <!-- Renewal Period -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="renewal_period_months" class="form-label">Periode Perpanjangan (Bulan)</label>
                  <input type="number" class="form-control @error('renewal_period_months') is-invalid @enderror" 
                         id="renewal_period_months" name="renewal_period_months" value="{{ old('renewal_period_months', $contract->renewal_period_months) }}" min="1" max="120">
                  @error('renewal_period_months')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <small class="text-muted">Hanya berlaku jika perpanjangan otomatis diaktifkan</small>
                </div>
              </div>

              <!-- Terms & Conditions -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="terms_conditions" class="form-label">Syarat & Ketentuan</label>
                  <textarea class="form-control @error('terms_conditions') is-invalid @enderror" 
                            id="terms_conditions" name="terms_conditions" rows="4">{{ old('terms_conditions', $contract->terms_conditions) }}</textarea>
                  @error('terms_conditions')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Contract File -->
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="contract_file" class="form-label">File Kontrak</label>
                  @if($contract->contract_file)
                    <div class="mb-2">
                      <small class="text-muted">File saat ini: </small>
                      <a href="{{ route('contracts.download', $contract) }}" class="text-primary">
                        {{ basename($contract->contract_file) }}
                      </a>
                    </div>
                  @endif
                  <input type="file" class="form-control @error('contract_file') is-invalid @enderror" 
                         id="contract_file" name="contract_file" accept=".pdf,.doc,.docx">
                  @error('contract_file')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <small class="text-muted">Format: PDF, DOC, DOCX. Maksimal 10MB. Kosongkan jika tidak ingin mengubah file.</small>
                </div>
              </div>

              <!-- Notes -->
              <div class="col-12">
                <div class="mb-3">
                  <label for="notes" class="form-label">Catatan</label>
                  <textarea class="form-control @error('notes') is-invalid @enderror" 
                            id="notes" name="notes" rows="3">{{ old('notes', $contract->notes) }}</textarea>
                  @error('notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('contracts.show', $contract) }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Perbarui Kontrak
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>


@endsection

<!-- Include Supplier Lookup Component -->
@include('components.supplier-lookup')

@section('page-script')
<script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
<script>
// Auto renewal toggle
document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('auto_renewal').addEventListener('change', function() {
    const renewalPeriod = document.getElementById('renewal_period_months');
    if (this.checked) {
      renewalPeriod.required = true;
    } else {
      renewalPeriod.required = false;
      renewalPeriod.value = '';
    }
  });

  // Wait for supplier lookup to be initialized
  function initSupplierLookup() {
    if (window.supplierLookup) {
      // Override supplier lookup onSelect function
      window.supplierLookup.options.onSelect = function(id, name, companyName) {
        document.getElementById('supplier_id').value = id;
        document.getElementById('supplier_name').value = name + (companyName ? ' (' + companyName + ')' : '');
      };
    } else {
      // Retry after 100ms if not loaded yet
      setTimeout(initSupplierLookup, 100);
    }
  }

  initSupplierLookup();
});

function openSupplierLookup() {
  if (window.supplierLookup) {
    window.supplierLookup.openModal();
  } else {
    alert('Supplier lookup belum dimuat. Silakan coba lagi.');
  }
}

function clearSupplier() {
  document.getElementById('supplier_id').value = '';
  document.getElementById('supplier_name').value = '';
}
</script>
@endsection
