@extends('layouts.contentNavbarLayout')

@section('title', 'Asset Assignment - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-user-settings-line me-2"></i>
                Asset Assignment
              </h5>
              <small class="text-muted">Kelola assignment asset ke karyawan</small>
            </div>
            <div>
              <a href="{{ route('asset-assignments.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-2"></i>
                Assign Asset
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" action="{{ route('asset-assignments.index') }}" id="filterForm">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Pencarian</label>
                <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                       placeholder="Kode Asset, Nama Asset, NIK, Nama Karyawan...">
              </div>
              <div class="col-md-2">
                <label class="form-label">Status</label>
                <select class="form-select" name="status">
                  <option value="">Semua Status</option>
                  <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Aktif</option>
                  <option value="returned" {{ request('status') === 'returned' ? 'selected' : '' }}>Dikembalikan</option>
                  <option value="transferred" {{ request('status') === 'transferred' ? 'selected' : '' }}>Dipindahkan</option>
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Karyawan</label>
                <select class="form-select" name="employee_id">
                  <option value="">Semua Karyawan</option>
                  @foreach($employees as $employee)
                    <option value="{{ $employee->id }}" {{ request('employee_id') == $employee->id ? 'selected' : '' }}>
                      {{ $employee->full_name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-2">
                <label class="form-label">Cabang</label>
                <select class="form-select" name="branch_id">
                  <option value="">Semua Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line me-1"></i>
                    Cari
                  </button>
                  <a href="{{ route('asset-assignments.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line me-1"></i>
                    Reset
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Assignments Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">
              Daftar Assignment ({{ $assignments->total() }})
            </h6>
          </div>
        </div>
        <div class="card-body">
          @if($assignments->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Asset</th>
                    <th>Karyawan</th>
                    <th>Cabang</th>
                    <th>Tanggal Assignment</th>
                    <th>Status</th>
                    <th>Durasi</th>
                    <th>Kondisi</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($assignments as $assignment)
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar avatar-sm me-3">
                            <span class="avatar-initial rounded bg-label-primary">
                              <i class="ri-archive-line"></i>
                            </span>
                          </div>
                          <div>
                            <h6 class="mb-0">{{ $assignment->asset->asset_code }}</h6>
                            <small class="text-muted">{{ $assignment->asset->name }}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar avatar-sm me-3">
                            <span class="avatar-initial rounded bg-label-info">
                              {{ strtoupper(substr($assignment->employee->full_name, 0, 2)) }}
                            </span>
                          </div>
                          <div>
                            <h6 class="mb-0">{{ $assignment->employee->full_name }}</h6>
                            <small class="text-muted">{{ $assignment->employee->nik }}</small>
                          </div>
                        </div>
                      </td>
                      <td>{{ $assignment->employee->branch->name }}</td>
                      <td>{{ $assignment->assigned_at->format('d/m/Y') }}</td>
                      <td>
                        <span class="badge bg-{{ $assignment->status === 'active' ? 'success' : ($assignment->status === 'returned' ? 'info' : 'warning') }}">
                          {{ $assignment->status_text }}
                        </span>
                      </td>
                      <td>{{ $assignment->duration }}</td>
                      <td>
                        <span class="badge bg-{{ $assignment->condition_when_assigned === 'excellent' ? 'success' : ($assignment->condition_when_assigned === 'good' ? 'info' : ($assignment->condition_when_assigned === 'fair' ? 'warning' : 'danger')) }}">
                          {{ $assignment->condition_when_assigned_text }}
                        </span>
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="ri-more-2-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="{{ route('asset-assignments.show', $assignment) }}">
                                <i class="ri-eye-line me-2"></i>
                                Detail
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="{{ route('asset-assignments.print-receipt', $assignment) }}" target="_blank">
                                <i class="ri-printer-line me-2"></i>
                                Cetak Tanda Terima
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="{{ route('asset-assignments.download-pdf', $assignment) }}" target="_blank">
                                <i class="ri-file-pdf-line me-2"></i>
                                Download PDF
                              </a>
                            </li>
                            @if($assignment->status === 'active')
                              <li>
                                <a class="dropdown-item" href="{{ route('asset-assignments.return-form', $assignment) }}">
                                  <i class="ri-arrow-go-back-line me-2"></i>
                                  Return Asset
                                </a>
                              </li>
                              <li>
                                <a class="dropdown-item" href="{{ route('asset-assignments.transfer-form', $assignment) }}">
                                  <i class="ri-exchange-line me-2"></i>
                                  Transfer Asset
                                </a>
                              </li>
                            @endif
                          </ul>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
              <div>
                <small class="text-muted">
                  Menampilkan {{ $assignments->firstItem() }} - {{ $assignments->lastItem() }} 
                  dari {{ $assignments->total() }} assignment
                </small>
              </div>
              <div>
                {{ $assignments->links() }}
              </div>
            </div>
          @else
            <div class="text-center py-5">
              <i class="ri-user-settings-line ri-48px text-muted mb-3"></i>
              <h6 class="text-muted">Tidak ada data assignment</h6>
              <p class="text-muted">Belum ada asset yang di-assign ke karyawan atau sesuai dengan filter.</p>
              <a href="{{ route('asset-assignments.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-2"></i>
                Assign Asset Pertama
              </a>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

@endsection
