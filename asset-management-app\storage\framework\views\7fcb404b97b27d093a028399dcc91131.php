<?php $__env->startSection('title', 'Master Kontrak'); ?>

<?php $__env->startSection('page-style'); ?>
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
<style>
.expiry-status {
  font-size: 0.75rem;
  font-weight: 600;
}
.expiry-normal { color: #28a745; }
.expiry-expiring { color: #ffc107; }
.expiry-expired { color: #dc3545; }
.btn-action {
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.btn-action:hover {
  transform: scale(1.05);
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="ri-file-text-line me-2"></i>
            Master Kontrak
          </h5>
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('contracts.create')): ?>
          <a href="<?php echo e(route('contracts.create')); ?>" class="btn btn-primary">
            <i class="ri-add-line me-1"></i>
            Tambah Kontrak
          </a>
          <?php endif; ?>
        </div>

        <!-- Filters -->
        <div class="card-body border-bottom">
          <form method="GET" action="<?php echo e(route('contracts.index')); ?>" class="row g-3">
            <div class="col-md-3">
              <label class="form-label">Pencarian</label>
              <input type="text" name="search" class="form-control" placeholder="Nomor/Nama kontrak, Supplier..." value="<?php echo e(request('search')); ?>">
            </div>
            
            <div class="col-md-2">
              <label class="form-label">Supplier</label>
              <select name="supplier_id" class="form-select">
                <option value="">Semua Supplier</option>
                <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($supplier->id); ?>" <?php echo e(request('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                    <?php echo e($supplier->name); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">Divisi</label>
              <select name="division_id" class="form-select">
                <option value="">Semua Divisi</option>
                <?php $__currentLoopData = $divisions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $division): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($division->id); ?>" <?php echo e(request('division_id') == $division->id ? 'selected' : ''); ?>>
                    <?php echo e($division->name); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">Status</label>
              <select name="status" class="form-select">
                <option value="">Semua Status</option>
                <?php $__currentLoopData = \App\Models\Contract::getStatuses(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>" <?php echo e(request('status') == $key ? 'selected' : ''); ?>>
                    <?php echo e($value); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">Tipe</label>
              <select name="contract_type" class="form-select">
                <option value="">Semua Tipe</option>
                <?php $__currentLoopData = \App\Models\Contract::getContractTypes(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>" <?php echo e(request('contract_type') == $key ? 'selected' : ''); ?>>
                    <?php echo e($value); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>

            <div class="col-md-1">
              <label class="form-label">&nbsp;</label>
              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="ri-search-line"></i>
                </button>
                <a href="<?php echo e(route('contracts.index')); ?>" class="btn btn-outline-secondary">
                  <i class="ri-refresh-line"></i>
                </a>
              </div>
            </div>
          </form>
        </div>

        <div class="card-body">
          <?php if($contracts->count() > 0): ?>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Nomor Kontrak</th>
                  <th>Nama Kontrak</th>
                  <th>Supplier</th>
                  <th>Divisi</th>
                  <th>Tipe</th>
                  <th>Tanggal Mulai</th>
                  <th>Tanggal Berakhir</th>
                  <th>Status Kadaluarsa</th>
                  <th>Status</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                <?php $__currentLoopData = $contracts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contract): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                  <td>
                    <strong><?php echo e($contract->contract_number); ?></strong>
                  </td>
                  <td>
                    <div>
                      <strong><?php echo e($contract->contract_name); ?></strong>
                      <?php if($contract->description): ?>
                        <br><small class="text-muted"><?php echo e(Str::limit($contract->description, 50)); ?></small>
                      <?php endif; ?>
                    </div>
                  </td>
                  <td>
                    <div>
                      <strong><?php echo e($contract->supplier->name); ?></strong>
                      <br><small class="text-muted"><?php echo e($contract->supplier->supplier_code); ?></small>
                    </div>
                  </td>
                  <td>
                    <?php echo e($contract->division->name ?? '-'); ?>

                  </td>
                  <td>
                    <span class="badge bg-info"><?php echo e($contract->contract_type_text); ?></span>
                  </td>
                  <td><?php echo e($contract->start_date->format('d/m/Y')); ?></td>
                  <td><?php echo e($contract->end_date->format('d/m/Y')); ?></td>
                  <td>
                    <div class="expiry-status expiry-<?php echo e($contract->expiry_status); ?>">
                      <?php echo e($contract->expiry_status_text); ?>

                    </div>
                  </td>
                  <td>
                    <span class="badge bg-<?php echo e($contract->status_badge); ?>"><?php echo e($contract->status_text); ?></span>
                  </td>
                  <td>
                    <div class="d-flex gap-1">
                      <!-- Detail Button -->
                      <a href="<?php echo e(route('contracts.show', $contract)); ?>" class="btn btn-sm btn-outline-info btn-action" title="Detail">
                        <i class="ri-eye-line"></i>
                      </a>

                      <!-- Edit Button -->
                      <a href="<?php echo e(route('contracts.edit', $contract)); ?>" class="btn btn-sm btn-outline-primary btn-action" title="Edit">
                        <i class="ri-edit-line"></i>
                      </a>

                      <!-- Download Button -->
                      <?php if($contract->contract_file): ?>
                      <a href="<?php echo e(route('contracts.download', $contract)); ?>" class="btn btn-sm btn-outline-success btn-action" title="Download File">
                        <i class="ri-download-line"></i>
                      </a>
                      <?php endif; ?>

                      <!-- Delete Button -->
                      <form action="<?php echo e(route('contracts.destroy', $contract)); ?>" method="POST" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-sm btn-outline-danger btn-action" title="Hapus" onclick="return confirm('Yakin ingin menghapus kontrak ini?')">
                          <i class="ri-delete-bin-line"></i>
                        </button>
                      </form>
                    </div>
                  </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
              Menampilkan <?php echo e($contracts->firstItem() ?? 0); ?> sampai <?php echo e($contracts->lastItem() ?? 0); ?> 
              dari <?php echo e($contracts->total()); ?> kontrak
            </div>
            <?php echo e($contracts->links()); ?>

          </div>
          <?php else: ?>
          <div class="text-center py-4">
            <i class="ri-file-text-line" style="font-size: 3rem; color: #ccc;"></i>
            <h5 class="mt-2">Belum ada kontrak</h5>
            <p class="text-muted">Silakan tambah kontrak baru untuk memulai.</p>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('contracts.create')): ?>
            <a href="<?php echo e(route('contracts.create')); ?>" class="btn btn-primary">
              <i class="ri-add-line me-1"></i>Tambah Kontrak
            </a>
            <?php endif; ?>
          </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-script'); ?>
<script>
// Auto submit form on filter change
document.querySelectorAll('select[name="supplier_id"], select[name="division_id"], select[name="status"], select[name="contract_type"]').forEach(function(select) {
  select.addEventListener('change', function() {
    this.form.submit();
  });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/master/contracts/index.blade.php ENDPATH**/ ?>