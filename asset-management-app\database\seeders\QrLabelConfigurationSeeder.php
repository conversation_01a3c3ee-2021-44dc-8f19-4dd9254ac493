<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\QrLabelConfiguration;

class QrLabelConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configurations = [
            [
                'name' => 'Small Label (30x20mm)',
                'description' => 'Label kecil untuk asset portable',
                'width' => 30,
                'height' => 20,
                'qr_size' => 15,
                'margin_top' => 2,
                'margin_bottom' => 2,
                'margin_left' => 2,
                'margin_right' => 2,
                'font_family' => 'Arial',
                'font_size_title' => 8,
                'font_size_content' => 6,
                'show_asset_name' => true,
                'show_asset_code' => true,
                'show_category' => false,
                'show_branch' => true,
                'show_location' => false,
                'is_default' => false,
                'is_active' => true
            ],
            [
                'name' => 'Medium Label (50x30mm)',
                'description' => 'Label standar untuk kebanyakan asset',
                'width' => 50,
                'height' => 30,
                'qr_size' => 25,
                'margin_top' => 3,
                'margin_bottom' => 3,
                'margin_left' => 3,
                'margin_right' => 3,
                'font_family' => 'Arial',
                'font_size_title' => 10,
                'font_size_content' => 8,
                'show_asset_name' => true,
                'show_asset_code' => true,
                'show_category' => true,
                'show_branch' => true,
                'show_location' => false,
                'is_default' => true,
                'is_active' => true
            ],
            [
                'name' => 'Large Label (70x40mm)',
                'description' => 'Label besar untuk asset besar',
                'width' => 70,
                'height' => 40,
                'qr_size' => 35,
                'margin_top' => 5,
                'margin_bottom' => 5,
                'margin_left' => 5,
                'margin_right' => 5,
                'font_family' => 'Arial',
                'font_size_title' => 12,
                'font_size_content' => 10,
                'show_asset_name' => true,
                'show_asset_code' => true,
                'show_category' => true,
                'show_branch' => true,
                'show_location' => true,
                'is_default' => false,
                'is_active' => true
            ],
            [
                'name' => 'Extra Large Label (100x60mm)',
                'description' => 'Label sangat besar dengan informasi lengkap',
                'width' => 100,
                'height' => 60,
                'qr_size' => 50,
                'margin_top' => 8,
                'margin_bottom' => 8,
                'margin_left' => 8,
                'margin_right' => 8,
                'font_family' => 'Arial',
                'font_size_title' => 14,
                'font_size_content' => 12,
                'show_asset_name' => true,
                'show_asset_code' => true,
                'show_category' => true,
                'show_branch' => true,
                'show_location' => true,
                'is_default' => false,
                'is_active' => true
            ]
        ];

        foreach ($configurations as $config) {
            QrLabelConfiguration::create($config);
        }

        $this->command->info('QR Label configurations created successfully!');
    }
}
