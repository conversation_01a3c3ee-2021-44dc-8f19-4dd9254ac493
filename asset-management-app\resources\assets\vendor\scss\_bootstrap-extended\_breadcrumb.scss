// Breadcrumbs
// *******************************************************************************

.breadcrumb-item,
.breadcrumb-item a {
  color: $breadcrumb-color;
  line-height: $line-height-lg;
  &:hover,
  &:focus {
    color: $breadcrumb-active-color;
  }
}

.breadcrumb-item.active a {
  &,
  &:hover,
  &:focus,
  &:active {
    color: inherit;
  }
}

// Breadcrumb divider styles
.breadcrumb-style1,
.breadcrumb-style2 {
  .breadcrumb-item + .breadcrumb-item::before {
    font-family: 'remixicon';
  }
}
.breadcrumb-style1 .breadcrumb-item + .breadcrumb-item::before {
  content: '\EA6E';
}
.breadcrumb-style2 .breadcrumb-item + .breadcrumb-item::before {
  content: '\EA68';
}
