@extends('layouts/contentNavbarLayout')

@section('title', 'Master Ko<PERSON>rak')

@section('page-style')
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
<style>
.expiry-status {
  font-size: 0.75rem;
  font-weight: 600;
}
.expiry-normal { color: #28a745; }
.expiry-expiring { color: #ffc107; }
.expiry-expired { color: #dc3545; }
.btn-action {
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.btn-action:hover {
  transform: scale(1.05);
}
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="ri-file-text-line me-2"></i>
            Master Kontrak
          </h5>
          @can('contracts.create')
          <a href="{{ route('contracts.create') }}" class="btn btn-primary">
            <i class="ri-add-line me-1"></i>
            Tambah Kontrak
          </a>
          @endcan
        </div>

        <!-- Filters -->
        <div class="card-body border-bottom">
          <form method="GET" action="{{ route('contracts.index') }}" class="row g-3">
            <div class="col-md-3">
              <label class="form-label">Pencarian</label>
              <input type="text" name="search" class="form-control" placeholder="Nomor/Nama kontrak, Supplier..." value="{{ request('search') }}">
            </div>
            
            <div class="col-md-2">
              <label class="form-label">Supplier</label>
              <select name="supplier_id" class="form-select">
                <option value="">Semua Supplier</option>
                @foreach($suppliers as $supplier)
                  <option value="{{ $supplier->id }}" {{ request('supplier_id') == $supplier->id ? 'selected' : '' }}>
                    {{ $supplier->name }}
                  </option>
                @endforeach
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">Divisi</label>
              <select name="division_id" class="form-select">
                <option value="">Semua Divisi</option>
                @foreach($divisions as $division)
                  <option value="{{ $division->id }}" {{ request('division_id') == $division->id ? 'selected' : '' }}>
                    {{ $division->name }}
                  </option>
                @endforeach
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">Status</label>
              <select name="status" class="form-select">
                <option value="">Semua Status</option>
                @foreach(\App\Models\Contract::getStatuses() as $key => $value)
                  <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                    {{ $value }}
                  </option>
                @endforeach
              </select>
            </div>

            <div class="col-md-2">
              <label class="form-label">Tipe</label>
              <select name="contract_type" class="form-select">
                <option value="">Semua Tipe</option>
                @foreach(\App\Models\Contract::getContractTypes() as $key => $value)
                  <option value="{{ $key }}" {{ request('contract_type') == $key ? 'selected' : '' }}>
                    {{ $value }}
                  </option>
                @endforeach
              </select>
            </div>

            <div class="col-md-1">
              <label class="form-label">&nbsp;</label>
              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="ri-search-line"></i>
                </button>
                <a href="{{ route('contracts.index') }}" class="btn btn-outline-secondary">
                  <i class="ri-refresh-line"></i>
                </a>
              </div>
            </div>
          </form>
        </div>

        <div class="card-body">
          @if($contracts->count() > 0)
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Nomor Kontrak</th>
                  <th>Nama Kontrak</th>
                  <th>Supplier</th>
                  <th>Divisi</th>
                  <th>Tipe</th>
                  <th>Tanggal Mulai</th>
                  <th>Tanggal Berakhir</th>
                  <th>Status Kadaluarsa</th>
                  <th>Status</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                @foreach($contracts as $contract)
                <tr>
                  <td>
                    <strong>{{ $contract->contract_number }}</strong>
                  </td>
                  <td>
                    <div>
                      <strong>{{ $contract->contract_name }}</strong>
                      @if($contract->description)
                        <br><small class="text-muted">{{ Str::limit($contract->description, 50) }}</small>
                      @endif
                    </div>
                  </td>
                  <td>
                    <div>
                      <strong>{{ $contract->supplier->name }}</strong>
                      <br><small class="text-muted">{{ $contract->supplier->supplier_code }}</small>
                    </div>
                  </td>
                  <td>
                    {{ $contract->division->name ?? '-' }}
                  </td>
                  <td>
                    <span class="badge bg-info">{{ $contract->contract_type_text }}</span>
                  </td>
                  <td>{{ $contract->start_date->format('d/m/Y') }}</td>
                  <td>{{ $contract->end_date->format('d/m/Y') }}</td>
                  <td>
                    <div class="expiry-status expiry-{{ $contract->expiry_status }}">
                      {{ $contract->expiry_status_text }}
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-{{ $contract->status_badge }}">{{ $contract->status_text }}</span>
                  </td>
                  <td>
                    <div class="d-flex gap-1">
                      <!-- Detail Button -->
                      <a href="{{ route('contracts.show', $contract) }}" class="btn btn-sm btn-outline-info btn-action" title="Detail">
                        <i class="ri-eye-line"></i>
                      </a>

                      <!-- Edit Button -->
                      <a href="{{ route('contracts.edit', $contract) }}" class="btn btn-sm btn-outline-primary btn-action" title="Edit">
                        <i class="ri-edit-line"></i>
                      </a>

                      <!-- Download Button -->
                      @if($contract->contract_file)
                      <a href="{{ route('contracts.download', $contract) }}" class="btn btn-sm btn-outline-success btn-action" title="Download File">
                        <i class="ri-download-line"></i>
                      </a>
                      @endif

                      <!-- Delete Button -->
                      <form action="{{ route('contracts.destroy', $contract) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-sm btn-outline-danger btn-action" title="Hapus" onclick="return confirm('Yakin ingin menghapus kontrak ini?')">
                          <i class="ri-delete-bin-line"></i>
                        </button>
                      </form>
                    </div>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
              Menampilkan {{ $contracts->firstItem() ?? 0 }} sampai {{ $contracts->lastItem() ?? 0 }} 
              dari {{ $contracts->total() }} kontrak
            </div>
            {{ $contracts->links() }}
          </div>
          @else
          <div class="text-center py-4">
            <i class="ri-file-text-line" style="font-size: 3rem; color: #ccc;"></i>
            <h5 class="mt-2">Belum ada kontrak</h5>
            <p class="text-muted">Silakan tambah kontrak baru untuk memulai.</p>
            @can('contracts.create')
            <a href="{{ route('contracts.create') }}" class="btn btn-primary">
              <i class="ri-add-line me-1"></i>Tambah Kontrak
            </a>
            @endcan
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@section('page-script')
<script>
// Auto submit form on filter change
document.querySelectorAll('select[name="supplier_id"], select[name="division_id"], select[name="status"], select[name="contract_type"]').forEach(function(select) {
  select.addEventListener('change', function() {
    this.form.submit();
  });
});
</script>
@endsection
