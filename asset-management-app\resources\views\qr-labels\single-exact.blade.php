<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QR Label - {{ $asset->asset_code }}</title>
    <style>
        @page {
            margin: 0;
            size: {{ $config->width }}mm {{ $config->height }}mm;
        }
        
        html, body {
            margin: 0;
            padding: 0;
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: {{ $config->font_size_content }}pt;
            overflow: hidden;
        }
        
        .label {
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            border: 1px solid #000;
            display: table;
            table-layout: fixed;
            background: white;
        }
        
        .label-row {
            display: table-row;
        }
        
        .qr-cell {
            display: table-cell;
            width: {{ $config->qr_size + $config->margin_left + 1 }}mm;
            vertical-align: middle;
            text-align: center;
            padding: {{ $config->margin_top }}mm 0 {{ $config->margin_bottom }}mm {{ $config->margin_left }}mm;
        }
        
        .qr-img {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
        }
        
        .separator-cell {
            display: table-cell;
            width: 3mm;
            vertical-align: middle;
            text-align: center;
            padding: {{ $config->margin_top + 2 }}mm 0 {{ $config->margin_bottom + 2 }}mm 0;
        }
        
        .separator-line {
            width: 1px;
            height: 100%;
            background: #000;
            margin: 0 auto;
        }
        
        .text-cell {
            display: table-cell;
            vertical-align: middle;
            padding: {{ $config->margin_top }}mm {{ $config->margin_right }}mm {{ $config->margin_bottom }}mm 1mm;
            line-height: 1.1;
        }
        
        .name {
            font-size: {{ $config->font_size_title }}pt;
            font-weight: bold;
            margin-bottom: 1mm;
            color: #000;
        }
        
        .code {
            font-size: {{ $config->font_size_content }}pt;
            font-weight: bold;
            margin-bottom: 0.8mm;
            color: #000;
        }
        
        .info {
            font-size: {{ max($config->font_size_content - 1, 6) }}pt;
            margin-bottom: 0.5mm;
            color: #333;
        }
        
        .info:last-child {
            margin-bottom: 0;
        }
        
        .category {
            font-style: italic;
            color: #666;
        }
        
        .branch {
            font-weight: 600;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="label">
        <div class="label-row">
            <div class="qr-cell">
                <img src="{{ $qrCodeBase64 }}" class="qr-img" alt="QR">
            </div>
            <div class="separator-cell">
                <div class="separator-line"></div>
            </div>
            <div class="text-cell">
                @if($config->show_asset_name)
                    <div class="name">{{ Str::limit($asset->name, 20) }}</div>
                @endif
                
                @if($config->show_asset_code)
                    <div class="code">{{ $asset->asset_code }}</div>
                @endif
                
                @if($config->show_category && $asset->assetCategory)
                    <div class="info category">{{ Str::limit($asset->assetCategory->name, 18) }}</div>
                @endif
                
                @if($config->show_branch && $asset->branch)
                    <div class="info branch">{{ Str::limit($asset->branch->name, 18) }}</div>
                @endif
                
                @if($config->show_location && isset($asset->dynamic_fields['location']))
                    <div class="info">{{ Str::limit($asset->dynamic_fields['location'], 18) }}</div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
