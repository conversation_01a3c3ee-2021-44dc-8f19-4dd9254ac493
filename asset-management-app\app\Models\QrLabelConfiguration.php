<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QrLabelConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'width',
        'height',
        'qr_size',
        'margin_top',
        'margin_bottom',
        'margin_left',
        'margin_right',
        'font_family',
        'font_size_title',
        'font_size_content',
        'show_asset_name',
        'show_asset_code',
        'show_category',
        'show_branch',
        'show_location',
        'is_default',
        'is_active'
    ];

    protected $casts = [
        'show_asset_name' => 'boolean',
        'show_asset_code' => 'boolean',
        'show_category' => 'boolean',
        'show_branch' => 'boolean',
        'show_location' => 'boolean',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get active configurations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get default configuration
     */
    public static function getDefault()
    {
        return self::where('is_default', true)->first() ?? self::active()->first();
    }

    /**
     * Set as default configuration
     */
    public function setAsDefault()
    {
        // Remove default from all others
        self::where('is_default', true)->update(['is_default' => false]);

        // Set this as default
        $this->update(['is_default' => true]);
    }

    /**
     * Get predefined label sizes
     */
    public static function getPredefinedSizes()
    {
        return [
            'small' => [
                'name' => 'Small (30x20mm)',
                'width' => 30,
                'height' => 20,
                'qr_size' => 15
            ],
            'medium' => [
                'name' => 'Medium (50x30mm)',
                'width' => 50,
                'height' => 30,
                'qr_size' => 25
            ],
            'large' => [
                'name' => 'Large (70x40mm)',
                'width' => 70,
                'height' => 40,
                'qr_size' => 35
            ],
            'extra_large' => [
                'name' => 'Extra Large (100x60mm)',
                'width' => 100,
                'height' => 60,
                'qr_size' => 50
            ]
        ];
    }
}
