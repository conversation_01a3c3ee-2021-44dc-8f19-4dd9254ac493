<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lookups', function (Blueprint $table) {
            $table->id();
            $table->string('lookup_code'); // Kode lookup (bisa duplikat)
            $table->string('lookup_name'); // Nama lookup
            $table->text('description')->nullable(); // Deskripsi lookup
            $table->string('category')->nullable(); // Kategori lookup untuk grouping
            $table->integer('sort_order')->default(0); // Urutan tampilan
            $table->boolean('is_active')->default(true); // Status aktif
            $table->json('metadata')->nullable(); // Data tambahan dalam JSON
            $table->timestamps();

            // Index untuk performa (tanpa unique constraint)
            $table->index(['lookup_code', 'is_active']);
            $table->index(['category', 'sort_order']);
            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lookups');
    }
};
