<?php $__env->startSection('title', 'Import Data Asset'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="ri-upload-2-line me-2"></i>
                        Import Data Asset
                    </h5>
                    <a href="<?php echo e(route('assets.index')); ?>" class="btn btn-secondary">
                        <i class="ri-arrow-left-line me-1"></i>
                        Kembali
                    </a>
                </div>
                <div class="card-body">
                    <!-- Download Template Section -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="ri-information-line me-2"></i>
                            Langkah-langkah Import Data Asset
                        </h6>
                        <ol class="mb-0">
                            <li>Download template Excel dengan klik tombol "Download Template" di bawah</li>
                            <li>Isi data asset sesuai dengan format yang tersedia di template</li>
                            <li>Pastikan mengikuti petunjuk pengisian di sheet "Petunjuk Pengisian"</li>
                            <li>Gunakan data referensi di sheet "Data Referensi" untuk mengisi kode-kode yang diperlukan</li>
                            <li>Upload file Excel yang sudah diisi menggunakan form di bawah</li>
                        </ol>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="card-title mb-0">
                                        <i class="ri-download-2-line me-2"></i>
                                        Download Template
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        Download template Excel untuk import data asset. Template sudah dilengkapi dengan:
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="ri-check-line text-success me-2"></i>Format kolom yang sesuai</li>
                                        <li><i class="ri-check-line text-success me-2"></i>Petunjuk pengisian lengkap</li>
                                        <li><i class="ri-check-line text-success me-2"></i>Data referensi (kategori, cabang, dll)</li>
                                        <li><i class="ri-check-line text-success me-2"></i>Contoh pengisian data</li>
                                    </ul>
                                    <a href="<?php echo e(route('assets.import.template')); ?>" class="btn btn-primary">
                                        <i class="ri-download-2-line me-1"></i>
                                        Download Template Excel
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="card-title mb-0">
                                        <i class="ri-upload-2-line me-2"></i>
                                        Upload File Import
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form action="<?php echo e(route('assets.import.process')); ?>" method="POST" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <div class="mb-3">
                                            <label for="file" class="form-label">Pilih File Excel</label>
                                            <input type="file" class="form-control <?php $__errorArgs = ['file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="file" name="file" accept=".xlsx,.xls" required>
                                            <?php $__errorArgs = ['file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <div class="form-text">
                                                <i class="ri-information-line me-1"></i>
                                                Format: .xlsx atau .xls, Maksimal: 10MB
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-warning">
                                            <h6 class="alert-heading">
                                                <i class="ri-alert-line me-2"></i>
                                                Perhatian
                                            </h6>
                                            <ul class="mb-0">
                                                <li>Pastikan file menggunakan template yang sudah didownload</li>
                                                <li>Maksimal 1000 baris data per import</li>
                                                <li>Proses import tidak dapat dibatalkan</li>
                                                <li>Data yang error akan dilewati dan dilaporkan</li>
                                            </ul>
                                        </div>

                                        <button type="submit" class="btn btn-success">
                                            <i class="ri-upload-2-line me-1"></i>
                                            Proses Import
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Import Results -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show mt-4" role="alert">
                            <h6 class="alert-heading">
                                <i class="ri-check-circle-line me-2"></i>
                                Import Berhasil
                            </h6>
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('import_errors') && count(session('import_errors')) > 0): ?>
                        <div class="alert alert-warning alert-dismissible fade show mt-4" role="alert">
                            <h6 class="alert-heading">
                                <i class="ri-error-warning-line me-2"></i>
                                Data yang Gagal Diimport
                            </h6>
                            <div class="mt-2" style="max-height: 300px; overflow-y: auto;">
                                <?php $__currentLoopData = session('import_errors'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="small"><?php echo e($error); ?></div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Format Requirements -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="ri-file-list-3-line me-2"></i>
                                Format Data yang Diperlukan
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger">Kolom Wajib Diisi:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="ri-arrow-right-s-line text-danger me-1"></i><strong>name</strong> - Nama asset</li>
                                        <li><i class="ri-arrow-right-s-line text-danger me-1"></i><strong>asset_category_name</strong> - Nama kategori asset</li>
                                        <li><i class="ri-arrow-right-s-line text-danger me-1"></i><strong>branch_code</strong> - Kode cabang</li>
                                        <li><i class="ri-arrow-right-s-line text-danger me-1"></i><strong>status</strong> - Status asset (active/inactive/maintenance/disposed)</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-info">Kolom Opsional:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>asset_type_name - Nama tipe asset</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>supplier_code - Kode supplier</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>purchase_order_number - Nomor PO</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>description - Deskripsi</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>brand, model, serial_number</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>purchase_date (dd/mm/yyyy)</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>purchase_price (angka)</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>condition (excellent/good/fair/poor)</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>location - Lokasi asset</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>assigned_employee_nik - NIK karyawan</li>
                                        <li><i class="ri-arrow-right-s-line text-info me-1"></i>notes - Catatan</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File input validation
    const fileInput = document.getElementById('file');
    const form = fileInput.closest('form');
    
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const fileSize = file.size / 1024 / 1024; // Convert to MB
            const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
            
            if (fileSize > 10) {
                alert('Ukuran file terlalu besar. Maksimal 10MB.');
                this.value = '';
                return;
            }
            
            if (!allowedTypes.includes(file.type)) {
                alert('Format file tidak didukung. Gunakan file .xlsx atau .xls');
                this.value = '';
                return;
            }
        }
    });
    
    // Form submission confirmation
    form.addEventListener('submit', function(e) {
        if (!confirm('Apakah Anda yakin ingin memproses import data? Proses ini tidak dapat dibatalkan.')) {
            e.preventDefault();
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/assets/import.blade.php ENDPATH**/ ?>