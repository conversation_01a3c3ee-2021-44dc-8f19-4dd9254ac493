<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assets', function (Blueprint $table) {
            // Check if columns don't exist before adding
            if (!Schema::hasColumn('assets', 'assigned_at')) {
                $table->datetime('assigned_at')->nullable()->comment('Tanggal assignment');
            }
            if (!Schema::hasColumn('assets', 'assigned_by')) {
                $table->foreignId('assigned_by')->nullable()->constrained('users')->comment('User yang melakukan assignment');
            }
            if (!Schema::hasColumn('assets', 'assignment_notes')) {
                $table->text('assignment_notes')->nullable()->comment('Catatan assignment');
            }
            if (!Schema::hasColumn('assets', 'assignment_status')) {
                $table->enum('assignment_status', ['assigned', 'returned', 'transferred'])->default('assigned')->comment('Status assignment');
            }
        });

        // Add indexes if they don't exist
        if (!Schema::hasIndex('assets', 'assets_assigned_to_assignment_status_index')) {
            Schema::table('assets', function (Blueprint $table) {
                $table->index(['assigned_to', 'assignment_status']);
            });
        }

        if (!Schema::hasIndex('assets', 'assets_assigned_at_index')) {
            Schema::table('assets', function (Blueprint $table) {
                $table->index(['assigned_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assets', function (Blueprint $table) {
            $table->dropForeign(['assigned_to']);
            $table->dropForeign(['assigned_by']);
            $table->dropColumn([
                'assigned_to',
                'assigned_at',
                'assigned_by',
                'assignment_notes',
                'assignment_status'
            ]);
        });
    }
};
