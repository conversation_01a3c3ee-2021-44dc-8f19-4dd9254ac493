<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ApprovalRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $approvalRoles = [
            [
                'name' => 'Supervisor',
                'slug' => 'supervisor',
                'description' => 'Supervisor role for first level approval',
                'is_active' => true,
            ],
            [
                'name' => 'IT Admin',
                'slug' => 'it_admin',
                'description' => 'IT Administrator for IT related approvals',
                'is_active' => true,
            ],
            [
                'name' => 'HRD Admin',
                'slug' => 'hrd_admin',
                'description' => 'HRD Administrator for HR related approvals',
                'is_active' => true,
            ],
            [
                'name' => 'Finance Admin',
                'slug' => 'finance_admin',
                'description' => 'Finance Administrator for budget approvals',
                'is_active' => true,
            ],
            [
                'name' => 'Finance Manager',
                'slug' => 'finance_manager',
                'description' => 'Finance Manager for high-value budget approvals',
                'is_active' => true,
            ],
        ];

        foreach ($approvalRoles as $roleData) {
            // Check if role already exists
            $existingRole = \App\Models\Role::where('slug', $roleData['slug'])->first();

            if (!$existingRole) {
                \App\Models\Role::create($roleData);
                $this->command->info("Created role: {$roleData['name']}");
            } else {
                $this->command->info("Role already exists: {$roleData['name']}");
            }
        }

        $this->command->info('Approval roles seeding completed.');
    }
}
