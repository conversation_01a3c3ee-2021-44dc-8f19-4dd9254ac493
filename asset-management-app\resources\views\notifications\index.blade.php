@extends('layouts.contentNavbarLayout')

@section('title', 'Notifikasi - Asset Management System')

@section('content')
<style>
<style>
.notification-item {
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.notification-item:hover {
  background-color: rgba(105, 108, 255, 0.04);
  border-left-color: #696cff;
  transform: translateX(2px);
}

.notification-unread {
  background-color: rgba(105, 108, 255, 0.02);
  border-left-color: #696cff;
}

.notification-read {
  opacity: 0.7;
}

.notification-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 16px;
}
</style>
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-notification-2-line me-2"></i>
              Notifikasi
            </h5>
            <small class="text-muted">Kelola semua notifikasi Anda</small>
          </div>
          <div>
            @if($notifications->where('read_at', null)->count() > 0)
              <form method="POST" action="{{ route('notifications.mark-all-read') }}" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-primary btn-sm">
                  <i class="ri-check-double-line me-1"></i>
                  Tandai Semua Dibaca
                </button>
              </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Notifications List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body p-0">
          @if($notifications->count() > 0)
            <div class="list-group list-group-flush">
              @foreach($notifications as $notification)
                <div class="list-group-item notification-item {{ $notification->read_at ? 'notification-read' : 'notification-unread' }} border-0">
                  <div class="d-flex align-items-start">
                    <!-- Icon -->
                    <div class="flex-shrink-0 me-3">
                      @php
                        $notificationService = app(\App\Services\NotificationService::class);
                        $iconClass = $notificationService->getNotificationIcon($notification->type);
                        $colorClass = $notificationService->getNotificationColor($notification->type);
                      @endphp
                      <div class="notification-icon bg-{{ $colorClass }} text-white">
                        <i class="{{ $iconClass }}"></i>
                      </div>
                    </div>

                    <!-- Content -->
                    <div class="flex-grow-1">
                      <div class="d-flex justify-content-between align-items-start mb-1">
                        <h6 class="mb-0 fw-semibold">{{ $notification->title }}</h6>
                        <div class="d-flex align-items-center">
                          @if(!$notification->read_at)
                            <span class="badge bg-primary me-2">Baru</span>
                          @endif
                          <small class="text-muted">{{ $notification->created_at->diffForHumans() }}</small>
                        </div>
                      </div>
                      
                      <p class="mb-2 text-muted">{{ $notification->message }}</p>
                      
                      <div class="d-flex justify-content-between align-items-center">
                        <div>
                          @if($notification->data && isset($notification->data['request_number']))
                            <small class="text-info">
                              <i class="ri-file-text-line me-1"></i>
                              {{ $notification->data['request_number'] }}
                            </small>
                          @endif
                        </div>
                        
                        <div>
                          @if($notification->data && isset($notification->data['request_id']))
                            <a href="{{ route('notifications.read', $notification->id) }}" 
                               class="btn btn-sm btn-outline-primary">
                              <i class="ri-eye-line me-1"></i>
                              Lihat Detail
                            </a>
                          @endif
                          
                          @if(!$notification->read_at)
                            <form method="POST" action="{{ route('notifications.read', $notification->id) }}" class="d-inline ms-1">
                              @csrf
                              <button type="submit" class="btn btn-sm btn-outline-success">
                                <i class="ri-check-line me-1"></i>
                                Tandai Dibaca
                              </button>
                            </form>
                          @endif
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              @endforeach
            </div>

            <!-- Pagination -->
            @if($notifications->hasPages())
              <div class="card-footer">
                <div class="d-flex justify-content-center">
                  {{ $notifications->links() }}
                </div>
              </div>
            @endif
          @else
            <!-- Empty State -->
            <div class="text-center py-5">
              <div class="mb-3">
                <i class="ri-notification-off-line ri-48px text-muted opacity-50"></i>
              </div>
              <h5 class="text-muted">Tidak Ada Notifikasi</h5>
              <p class="text-muted mb-0">Anda belum memiliki notifikasi apapun.</p>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto refresh page every 2 minutes
    setInterval(function() {
        if (!document.hidden) {
            window.location.reload();
        }
    }, 120000);

    // Add click animation
    document.querySelectorAll('.notification-item').forEach(item => {
        item.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
});
</script>

@endsection
