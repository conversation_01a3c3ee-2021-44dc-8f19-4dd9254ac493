<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_field_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('field_name'); // Nama field (snake_case)
            $table->string('field_label'); // Label yang ditampilkan
            $table->string('field_type'); // text, number, date, select, textarea, file, etc.
            $table->json('field_options')->nullable(); // Options untuk select, validation rules, etc.
            $table->boolean('is_required')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->string('field_group')->nullable(); // Grouping fields (Basic Info, Technical Specs, etc.)
            $table->foreignId('asset_category_id')->nullable()->constrained()->onDelete('cascade'); // Specific to category
            $table->text('help_text')->nullable(); // Help text untuk field
            $table->json('validation_rules')->nullable(); // Custom validation rules
            $table->timestamps();

            // Indexes
            $table->index(['asset_category_id', 'is_active', 'sort_order'], 'afc_category_active_sort_idx');
            $table->index(['field_name', 'asset_category_id'], 'afc_name_category_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_field_configurations');
    }
};
