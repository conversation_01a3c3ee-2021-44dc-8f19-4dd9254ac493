<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class MaintenanceReportPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create maintenance report permissions
        $permissions = [
            [
                'name' => 'View Maintenance Reports',
                'slug' => 'reports.maintenance.view',
                'module' => 'Reports'
            ],
            [
                'name' => 'Export Maintenance Reports',
                'slug' => 'reports.maintenance.export',
                'module' => 'Reports'
            ]
        ];

        foreach ($permissions as $permissionData) {
            Permission::updateOrInsert(
                ['slug' => $permissionData['slug']],
                [
                    'name' => $permissionData['name'],
                    'module' => $permissionData['module'],
                    'description' => $permissionData['name']
                ]
            );
        }

        // Assign permissions to roles
        $roles = ['staff', 'viewer', 'manager', 'admin'];
        
        foreach ($roles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            
            if ($role) {
                foreach ($permissions as $permissionData) {
                    $permission = Permission::where('slug', $permissionData['slug'])->first();
                    
                    if ($permission && !$role->permissions()->where('permission_id', $permission->id)->exists()) {
                        $role->permissions()->attach($permission->id);
                        $this->command->info("Assigned permission '{$permission->slug}' to role '{$role->name}'");
                    }
                }
            }
        }
    }
}
