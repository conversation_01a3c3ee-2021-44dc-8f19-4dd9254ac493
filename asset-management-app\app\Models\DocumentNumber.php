<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentNumber extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_type',
        'format',
        'current_number',
        'year',
        'branch_id',
        'asset_category_id',
        'asset_type_id',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function category()
    {
        return $this->belongsTo(AssetCategory::class, 'asset_category_id');
    }

    public function assetType()
    {
        return $this->belongsTo(AssetType::class, 'asset_type_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeCurrentYear($query)
    {
        return $query->where('year', date('Y'));
    }

    // Helper methods
    public function getNextNumber()
    {
        // For request asset, we need to check current month
        if ($this->document_type === 'request_asset') {
            $currentMonth = date('m');
            $currentYear = date('Y');

            // If year has changed, reset counter
            if ($this->year != $currentYear) {
                $this->update([
                    'year' => $currentYear,
                    'current_number' => 0
                ]);
            }

            // Get the last number for current month
            $lastNumber = \App\Models\RequestAsset::where('request_number', 'like',
                    "%-{$this->branch->code}-REQ-{$currentYear}{$currentMonth}-%")
                ->count();

            $nextNumber = $lastNumber + 1;

            // Update current number
            $this->increment('current_number');

            return $nextNumber;
        }

        // For other document types, use existing logic
        $this->increment('current_number');
        return $this->current_number;
    }

    public function generateDocumentNumber()
    {
        try {
            $nextNumber = $this->getNextNumber();
            $companyCode = CompanySetting::getCompanyCode() ?? 'COMP';
            $branchCode = $this->branch ? $this->branch->code : 'HQ';
            $categoryCode = $this->category ? $this->category->code : '000';
            $assetTypeCode = $this->assetType ? $this->assetType->code : '00';

            return str_replace([
                '{company_code}',
                '{branch_code}',
                '{category_code}',
                '{asset_type_code}',
                '{year}',
                '{month}',
                '{number}'
            ], [
                $companyCode,
                $branchCode,
                $categoryCode,
                $assetTypeCode,
                substr($this->year, -2), // Last 2 digits of year
                date('m'), // Current month
                str_pad($nextNumber, 4, '0', STR_PAD_LEFT)
            ], $this->format);
        } catch (\Exception $e) {
            \Log::error('Error generating document number: ' . $e->getMessage(), [
                'document_id' => $this->id,
                'format' => $this->format,
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback simple number generation
            $nextNumber = $this->getNextNumber();
            return 'REQ-' . date('Ym') . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
        }
    }

    public static function getDocumentNumber($type, $branchId, $categoryId = null, $assetTypeId = null)
    {
        $year = date('Y');

        $docNumber = self::where('document_type', $type)
            ->where('branch_id', $branchId)
            ->where('asset_category_id', $categoryId)
            ->where('asset_type_id', $assetTypeId)
            ->where('year', $year)
            ->where('is_active', true)
            ->first();

        if (!$docNumber) {
            // Create new document number for this type, branch, category, asset type, and year
            $docNumber = self::create([
                'document_type' => $type,
                'format' => '{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}',
                'current_number' => 0,
                'year' => $year,
                'branch_id' => $branchId,
                'asset_category_id' => $categoryId,
                'asset_type_id' => $assetTypeId,
                'description' => "Auto-generated for {$type}",
                'is_active' => true,
            ]);
        }

        return $docNumber->generateDocumentNumber();
    }

    public static function getRequestAssetNumber($branchId)
    {
        try {
            $year = date('Y');

            $docNumber = self::where('document_type', 'request_asset')
                ->where('branch_id', $branchId)
                ->where('year', $year)
                ->where('is_active', true)
                ->first();

            if (!$docNumber) {
                // Create new document number for request asset
                $docNumber = self::create([
                    'document_type' => 'request_asset',
                    'format' => '{company_code}-{branch_code}-REQ-{year}{month}-{number}',
                    'current_number' => 0,
                    'year' => $year,
                    'branch_id' => $branchId,
                    'asset_category_id' => null,
                    'asset_type_id' => null,
                    'description' => "Auto-generated for request asset",
                    'is_active' => true,
                ]);
            }

            return $docNumber->generateDocumentNumber();
        } catch (\Exception $e) {
            \Log::error('Error getting request asset number: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback simple number generation
            $timestamp = time();
            return 'REQ-' . date('Ym') . '-' . str_pad($timestamp % 10000, 4, '0', STR_PAD_LEFT);
        }
    }

    public static function getMaintenanceNumber($branchId, $categoryId = null)
    {
        try {
            $year = date('Y');

            $docNumber = self::where('document_type', 'maintenance')
                ->where('branch_id', $branchId)
                ->where('asset_category_id', $categoryId)
                ->where('year', $year)
                ->where('is_active', true)
                ->first();

            if (!$docNumber) {
                // Create new document number for maintenance
                $docNumber = self::create([
                    'document_type' => 'maintenance',
                    'format' => '{company_code}-{branch_code}-MTC-{category_code}-{year}{month}-{number}',
                    'current_number' => 0,
                    'year' => $year,
                    'branch_id' => $branchId,
                    'asset_category_id' => $categoryId,
                    'asset_type_id' => null,
                    'description' => "Auto-generated for asset maintenance",
                    'is_active' => true,
                ]);
            }

            return $docNumber->generateDocumentNumber();
        } catch (\Exception $e) {
            \Log::error('Error getting maintenance number: ' . $e->getMessage(), [
                'branch_id' => $branchId,
                'category_id' => $categoryId,
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback simple number generation
            $timestamp = time();
            $categoryCode = $categoryId ? \App\Models\AssetCategory::find($categoryId)?->code ?? '000' : '000';
            return 'MTC-' . $categoryCode . '-' . date('Ym') . '-' . str_pad($timestamp % 10000, 4, '0', STR_PAD_LEFT);
        }
    }
}
