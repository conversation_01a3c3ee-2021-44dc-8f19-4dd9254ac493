<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApprovalWorkflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'module',
        'conditions',
        'is_active',
        'priority',
    ];

    protected $casts = [
        'conditions' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function levels()
    {
        return $this->hasMany(ApprovalLevel::class)->orderBy('level_order');
    }

    public function histories()
    {
        return $this->hasMany(ApprovalHistory::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForModule($query, $module)
    {
        return $query->where('module', $module);
    }

    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    // Methods
    public static function findWorkflowForRequest($request)
    {
        $workflows = self::active()
            ->forModule('asset_requests')
            ->byPriority()
            ->get();

        foreach ($workflows as $workflow) {
            if ($workflow->matchesConditions($request)) {
                return $workflow;
            }
        }

        return null;
    }

    public function matchesConditions($request)
    {
        $conditions = $this->conditions;

        foreach ($conditions as $condition) {
            if (!$this->evaluateCondition($condition, $request)) {
                return false;
            }
        }

        return true;
    }

    private function evaluateCondition($condition, $request)
    {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];

        $requestValue = $this->getRequestValue($field, $request);

        switch ($operator) {
            case 'equals':
                return $requestValue == $value;
            case 'not_equals':
                return $requestValue != $value;
            case 'greater_than':
                return $requestValue > $value;
            case 'greater_than_or_equal':
                return $requestValue >= $value;
            case 'less_than':
                return $requestValue < $value;
            case 'less_than_or_equal':
                return $requestValue <= $value;
            case 'in':
                return in_array($requestValue, $value);
            case 'not_in':
                return !in_array($requestValue, $value);
            case 'contains':
                return str_contains(strtolower($requestValue), strtolower($value));
            default:
                return false;
        }
    }

    private function getRequestValue($field, $request)
    {
        switch ($field) {
            case 'estimated_price':
                return $request->estimated_price * $request->quantity;
            case 'quantity':
                return $request->quantity;
            case 'priority':
                return $request->priority;
            case 'category_id':
                return $request->asset_category_id;
            case 'branch_id':
                return $request->branch_id;
            case 'division_id':
                return $request->division_id;
            case 'user_role':
                return $request->user->role->slug;
            default:
                return null;
        }
    }

    public function getNextLevel($currentLevel = 0)
    {
        return $this->levels()
            ->where('level_order', '>', $currentLevel)
            ->where('is_active', true)
            ->first();
    }

    public function getApproversForLevel($levelOrder)
    {
        $level = $this->levels()
            ->where('level_order', $levelOrder)
            ->where('is_active', true)
            ->first();

        if (!$level) {
            return collect();
        }

        return $level->getApprovers();
    }
}
