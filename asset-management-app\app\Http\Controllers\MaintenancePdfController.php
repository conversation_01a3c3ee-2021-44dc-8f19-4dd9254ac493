<?php

namespace App\Http\Controllers;

use App\Models\AssetMaintenance;
use App\Models\CompanySetting;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class MaintenancePdfController extends Controller
{
    /**
     * Generate PDF for maintenance form
     */
    public function generatePdf(AssetMaintenance $assetMaintenance)
    {
        // Load relationships
        $assetMaintenance->load([
            'asset.branch',
            'asset.assetCategory',
            'asset.assignedEmployee.branch',
            'asset.assignedEmployee.division',
            'supplier',
            'requestedBy',
            'assignedTo',
            'approvedBy'
        ]);

        // Get company settings
        $companySetting = CompanySetting::first();

        // Generate QR Code for maintenance tracking
        $qrCodeUrl = route('maintenance.track', $assetMaintenance->id);

        // Generate QR Code for PDF
        try {
            // Try to get QR Code from Google Charts API and convert to base64
            $qrImageUrl = 'https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=' . urlencode($qrCodeUrl);

            // Try to fetch the image
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);

            $qrImageData = @file_get_contents($qrImageUrl, false, $context);

            if ($qrImageData !== false) {
                // Convert to base64 data URI
                $qrCode = '<img src="data:image/png;base64,' . base64_encode($qrImageData) . '" style="width:100px;height:100px;display:block;margin:0 auto;" alt="QR Code">';
            } else {
                throw new \Exception('Failed to fetch QR Code from Google Charts');
            }
        } catch (\Exception $e) {
            // Fallback: Create a styled placeholder
            $qrCode = '
                <div style="width:100px;height:100px;border:2px solid #000;background:#fff;position:relative;margin:0 auto;">
                    <div style="position:absolute;top:8px;left:8px;width:12px;height:12px;background:#000;"></div>
                    <div style="position:absolute;top:8px;right:8px;width:12px;height:12px;background:#000;"></div>
                    <div style="position:absolute;bottom:8px;left:8px;width:12px;height:12px;background:#000;"></div>
                    <div style="position:absolute;top:35px;left:35px;width:30px;height:30px;border:2px solid #000;text-align:center;line-height:26px;font-size:8px;font-weight:bold;">QR</div>
                    <div style="position:absolute;top:20px;left:25px;width:8px;height:8px;background:#000;"></div>
                    <div style="position:absolute;top:20px;right:25px;width:8px;height:8px;background:#000;"></div>
                    <div style="position:absolute;bottom:20px;left:25px;width:8px;height:8px;background:#000;"></div>
                    <div style="position:absolute;bottom:20px;right:25px;width:8px;height:8px;background:#000;"></div>
                </div>';
            \Log::warning('QR Code generation failed, using placeholder: ' . $e->getMessage());
        }

        $data = [
            'maintenance' => $assetMaintenance,
            'company' => $companySetting,
            'qrCode' => $qrCode,
            'qrCodeUrl' => $qrCodeUrl,
            'printDate' => now()->format('d/m/Y H:i:s'),
            'printedBy' => auth()->user()->name
        ];

        $pdf = Pdf::loadView('maintenance.pdf.form', $data)
                  ->setPaper('a4', 'portrait')
                  ->setOptions([
                      'dpi' => 150,
                      'defaultFont' => 'sans-serif',
                      'isHtml5ParserEnabled' => true,
                      'isRemoteEnabled' => true,
                      'chroot' => public_path(),
                      'enable_remote' => true
                  ]);

        $filename = 'Form_Maintenance_' . $assetMaintenance->maintenance_number . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Generate PDF for maintenance receipt
     */
    public function generateReceipt(AssetMaintenance $assetMaintenance)
    {
        // Load relationships
        $assetMaintenance->load([
            'asset.branch',
            'asset.assetCategory',
            'asset.assignedEmployee',
            'supplier',
            'requestedBy',
            'assignedTo',
            'approvedBy'
        ]);

        // Get company settings
        $companySetting = CompanySetting::first();

        // Generate QR Code
        $qrCodeUrl = route('maintenance.track', $assetMaintenance->id);

        // Generate QR Code for receipt
        try {
            $qrImageUrl = 'https://chart.googleapis.com/chart?chs=80x80&cht=qr&chl=' . urlencode($qrCodeUrl);

            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);

            $qrImageData = @file_get_contents($qrImageUrl, false, $context);

            if ($qrImageData !== false) {
                $qrCode = '<img src="data:image/png;base64,' . base64_encode($qrImageData) . '" style="width:80px;height:80px;display:block;margin:0 auto;" alt="QR Code">';
            } else {
                throw new \Exception('Failed to fetch QR Code');
            }
        } catch (\Exception $e) {
            $qrCode = '<div style="width:80px;height:80px;border:2px solid #000;background:#fff;text-align:center;line-height:76px;font-size:10px;font-weight:bold;margin:0 auto;">QR CODE</div>';
        }

        $data = [
            'maintenance' => $assetMaintenance,
            'company' => $companySetting,
            'qrCode' => $qrCode,
            'printDate' => now()->format('d/m/Y H:i:s'),
            'printedBy' => auth()->user()->name
        ];

        $pdf = Pdf::loadView('maintenance.pdf.receipt', $data)
                  ->setPaper('a4', 'portrait')
                  ->setOptions([
                      'dpi' => 150,
                      'defaultFont' => 'sans-serif',
                      'isHtml5ParserEnabled' => true,
                      'isRemoteEnabled' => true,
                      'enable_remote' => true
                  ]);

        $filename = 'Receipt_Maintenance_' . $assetMaintenance->maintenance_number . '.pdf';

        return $pdf->download($filename);
    }
}
