@extends('layouts/contentNavbarLayout')

@section('title', 'Remix - Icons')

@section('page-style')
@vite( 'resources/assets/vendor/scss/pages/page-icons.scss')
@endsection

@section('content')
<p>You can check complete list of Remix Icons from <a href="https://remixicon.com/" target="_blank">https://remixicon.com/</a></p>

<!-- Icon Container -->
<div class="d-flex flex-wrap" id="icons-container">
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-arrow-left-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">Arrow left</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-arrow-right-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">Arrow right</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-home-4-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">home</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-home-gear-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">home gear</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-mail-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">Mail</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-mail-open-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">open mail</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-brush-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">Brush</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-palette-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">palette</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-line-chart-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">chart</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-pie-chart-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">pie chart</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-chat-4-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">chat</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-message-2-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">message proccessing</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-bold ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">bold</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-italic ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">Italic</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-html5-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">html 5</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-code-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">code</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-wifi-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">wifi</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-signal-wifi-error-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">signal error</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-mac-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">mac</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-save-3-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">save</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-volume-up-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">volume up</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-volume-mute-line ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">volume mute</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-linkedin-box-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">linkedin</p>
    </div>
  </div>
  <div class="card icon-card cursor-pointer text-center mb-6 mx-3">
    <div class="card-body">
      <i class="ri-twitter-fill ri-36px"></i>
      <p class="icon-name text-capitalize text-truncate mb-0 mt-2">twitter</p>
    </div>
  </div>
</div>

<!-- Buttons -->
<div class="d-flex justify-content-center mx-auto gap-4">
  <a href="https://remixicon.com/" target="_blank" class="btn btn-primary">View All Icons</a>
  <a href="{{config('variables.documentation')}}/Icons.html" class="btn btn-primary" target="_blank">How to use icons?</a>
</div>
@endsection
