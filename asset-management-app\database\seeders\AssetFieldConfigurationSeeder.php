<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AssetFieldConfiguration;

class AssetFieldConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Default field configurations for all asset categories
        $defaultFields = [
            // Basic Information
            [
                'field_name' => 'warranty_period',
                'field_label' => 'Periode Garansi',
                'field_type' => 'text',
                'field_group' => 'basic_info',
                'sort_order' => 1,
                'help_text' => 'Contoh: 2 tahun, 36 bulan',
                'is_required' => false,
            ],
            [
                'field_name' => 'warranty_expiry',
                'field_label' => 'Tanggal Berakhir Garansi',
                'field_type' => 'date',
                'field_group' => 'basic_info',
                'sort_order' => 2,
                'is_required' => false,
            ],

            // Technical Specifications
            [
                'field_name' => 'processor',
                'field_label' => 'Processor',
                'field_type' => 'text',
                'field_group' => 'technical_specs',
                'sort_order' => 1,
                'help_text' => 'Contoh: Intel Core i5-10400, AMD Ryzen 5 3600',
                'is_required' => false,
            ],
            [
                'field_name' => 'memory_ram',
                'field_label' => 'Memory (RAM)',
                'field_type' => 'text',
                'field_group' => 'technical_specs',
                'sort_order' => 2,
                'help_text' => 'Contoh: 8GB DDR4, 16GB DDR4',
                'is_required' => false,
            ],
            [
                'field_name' => 'storage',
                'field_label' => 'Storage',
                'field_type' => 'text',
                'field_group' => 'technical_specs',
                'sort_order' => 3,
                'help_text' => 'Contoh: 256GB SSD, 1TB HDD',
                'is_required' => false,
            ],
            [
                'field_name' => 'operating_system',
                'field_label' => 'Operating System',
                'field_type' => 'select',
                'field_group' => 'technical_specs',
                'field_options' => [
                    'options' => [
                        'windows_10' => 'Windows 10',
                        'windows_11' => 'Windows 11',
                        'macos' => 'macOS',
                        'ubuntu' => 'Ubuntu',
                        'centos' => 'CentOS',
                        'other' => 'Other'
                    ]
                ],
                'sort_order' => 4,
                'is_required' => false,
            ],

            // Purchase Information
            [
                'field_name' => 'supplier',
                'field_label' => 'Supplier',
                'field_type' => 'text',
                'field_group' => 'purchase_info',
                'sort_order' => 1,
                'is_required' => false,
            ],
            [
                'field_name' => 'invoice_number',
                'field_label' => 'Nomor Invoice',
                'field_type' => 'text',
                'field_group' => 'purchase_info',
                'sort_order' => 2,
                'is_required' => false,
            ],
            [
                'field_name' => 'po_number',
                'field_label' => 'Nomor PO',
                'field_type' => 'text',
                'field_group' => 'purchase_info',
                'sort_order' => 3,
                'is_required' => false,
            ],

            // Maintenance
            [
                'field_name' => 'last_maintenance',
                'field_label' => 'Maintenance Terakhir',
                'field_type' => 'date',
                'field_group' => 'maintenance',
                'sort_order' => 1,
                'is_required' => false,
            ],
            [
                'field_name' => 'next_maintenance',
                'field_label' => 'Maintenance Berikutnya',
                'field_type' => 'date',
                'field_group' => 'maintenance',
                'sort_order' => 2,
                'is_required' => false,
            ],
            [
                'field_name' => 'maintenance_notes',
                'field_label' => 'Catatan Maintenance',
                'field_type' => 'textarea',
                'field_group' => 'maintenance',
                'sort_order' => 3,
                'is_required' => false,
            ],

            // Location & Assignment
            [
                'field_name' => 'room_number',
                'field_label' => 'Nomor Ruangan',
                'field_type' => 'text',
                'field_group' => 'location',
                'sort_order' => 1,
                'is_required' => false,
            ],
            [
                'field_name' => 'floor',
                'field_label' => 'Lantai',
                'field_type' => 'number',
                'field_group' => 'location',
                'sort_order' => 2,
                'is_required' => false,
            ],
            [
                'field_name' => 'building',
                'field_label' => 'Gedung',
                'field_type' => 'text',
                'field_group' => 'location',
                'sort_order' => 3,
                'is_required' => false,
            ],
        ];

        foreach ($defaultFields as $field) {
            AssetFieldConfiguration::create($field);
        }

        // Create dependent dropdown examples
        $this->createDependentDropdownExamples();
    }

    private function createDependentDropdownExamples()
    {
        // Parent dropdown: Brand
        $brandField = AssetFieldConfiguration::create([
            'field_name' => 'brand_category',
            'field_label' => 'Brand Category',
            'field_type' => 'select',
            'field_group' => 'technical_specs',
            'field_options' => [
                'options' => [
                    'laptop' => 'Laptop Brands',
                    'server' => 'Server Brands',
                    'network' => 'Network Equipment Brands'
                ]
            ],
            'sort_order' => 10,
            'is_required' => false,
            'data_source_type' => 'manual',
        ]);

        // Child dropdown: Specific Brand (depends on Brand Category)
        AssetFieldConfiguration::create([
            'field_name' => 'specific_brand',
            'field_label' => 'Specific Brand',
            'field_type' => 'select',
            'field_group' => 'technical_specs',
            'parent_field_id' => $brandField->id,
            'field_options' => [
                'dependent_options' => [
                    'laptop' => [
                        'dell' => 'Dell',
                        'hp' => 'HP',
                        'lenovo' => 'Lenovo',
                        'asus' => 'ASUS',
                        'acer' => 'Acer'
                    ],
                    'server' => [
                        'dell_server' => 'Dell PowerEdge',
                        'hp_server' => 'HP ProLiant',
                        'ibm' => 'IBM',
                        'supermicro' => 'Supermicro'
                    ],
                    'network' => [
                        'cisco' => 'Cisco',
                        'juniper' => 'Juniper',
                        'mikrotik' => 'MikroTik',
                        'ubiquiti' => 'Ubiquiti'
                    ]
                ]
            ],
            'sort_order' => 11,
            'is_required' => false,
            'data_source_type' => 'manual',
        ]);

        // Example using database source for dependent dropdown
        // Parent: Asset Category (using existing asset_categories table)
        $categoryField = AssetFieldConfiguration::create([
            'field_name' => 'asset_category_db',
            'field_label' => 'Asset Category (DB)',
            'field_type' => 'select',
            'field_group' => 'basic_info',
            'sort_order' => 20,
            'is_required' => false,
            'data_source_type' => 'database',
            'data_source_table' => 'asset_categories',
            'data_source_value_column' => 'id',
            'data_source_label_column' => 'name',
            'data_source_filter' => '{"is_active": 1}',
        ]);

        // Child: Asset Type (depends on Asset Category)
        AssetFieldConfiguration::create([
            'field_name' => 'asset_type_db',
            'field_label' => 'Asset Type (DB)',
            'field_type' => 'select',
            'field_group' => 'basic_info',
            'parent_field_id' => $categoryField->id,
            'parent_field_conditions' => [
                [
                    'column' => 'asset_category_id',
                    'operator' => '=',
                    'value' => '{parent_value}'
                ]
            ],
            'sort_order' => 21,
            'is_required' => false,
            'data_source_type' => 'database',
            'data_source_table' => 'asset_types',
            'data_source_value_column' => 'id',
            'data_source_label_column' => 'name',
            'data_source_filter' => '{"is_active": 1}',
        ]);

        // Example using lookup source
        AssetFieldConfiguration::create([
            'field_name' => 'condition_lookup',
            'field_label' => 'Condition (Lookup)',
            'field_type' => 'select',
            'field_group' => 'basic_info',
            'sort_order' => 30,
            'is_required' => false,
            'data_source_type' => 'lookup',
            'data_source_filter' => 'CONDITION',
        ]);
    }
}
