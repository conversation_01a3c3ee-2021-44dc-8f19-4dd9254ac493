<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AssetCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Komputer & Laptop',
                'code' => 'PC-LAPTOP',
                'description' => 'PC Desktop, Laptop, dan perangkat komputer',
                'is_active' => true,
            ],
            [
                'name' => 'Tools Bengkel',
                'code' => 'TOOLS',
                'description' => 'Peralatan dan tools untuk bengkel',
                'is_active' => true,
            ],
            [
                'name' => 'Furniture Kantor',
                'code' => 'FURNITURE',
                'description' => 'Meja, kursi, lemari, dan furniture kantor lainnya',
                'is_active' => true,
            ],
            [
                'name' => 'Elektronik',
                'code' => 'ELECTRONIC',
                'description' => 'Peralatan elektronik kantor seperti printer, scanner, dll',
                'is_active' => true,
            ],
            [
                'name' => 'Kendaraan',
                'code' => 'VEHICLE',
                'description' => 'Mobil, motor, dan kendaraan operasional',
                'is_active' => true,
            ],
            [
                'name' => 'Peralatan Kantor',
                'code' => 'OFFICE-EQUIP',
                'description' => 'Peralatan kantor umum dan supplies',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('asset_categories')->insert([
                'name' => $category['name'],
                'code' => $category['code'],
                'description' => $category['description'],
                'is_active' => $category['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
