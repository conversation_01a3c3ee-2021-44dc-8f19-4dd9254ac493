<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetMaintenance;

class MaintenanceNumberController extends Controller
{
    /**
     * Preview maintenance number for given asset
     */
    public function previewNumber($assetId)
    {
        try {
            // Validate asset exists
            $asset = Asset::find($assetId);
            if (!$asset) {
                return response()->json([
                    'success' => false,
                    'message' => 'Asset tidak ditemukan'
                ], 404);
            }

            // Generate preview number
            $previewNumber = AssetMaintenance::generateMaintenanceNumber($assetId);

            return response()->json([
                'success' => true,
                'number' => $previewNumber,
                'asset' => [
                    'id' => $asset->id,
                    'code' => $asset->asset_code,
                    'name' => $asset->name,
                    'branch' => $asset->branch ? $asset->branch->name : '-',
                    'category' => $asset->assetCategory ? $asset->assetCategory->name : '-'
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error generating maintenance number preview: ' . $e->getMessage(), [
                'asset_id' => $assetId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat generate preview nomor maintenance'
            ], 500);
        }
    }
}
