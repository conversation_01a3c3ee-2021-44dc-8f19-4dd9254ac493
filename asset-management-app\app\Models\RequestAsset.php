<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequestAsset extends Model
{
    use HasFactory;

    protected $fillable = [
        'request_number',
        'request_date',
        'needed_date',
        'request_category',
        'requested_by',
        'position',
        'division_id',
        'item_type',
        'purpose',
        'items',
        'status',
        'approved_by',
        'approved_at',
        'approval_notes',
        'estimated_total',
        'notes',
    ];

    protected $casts = [
        'request_date' => 'date',
        'needed_date' => 'date',
        'approved_at' => 'datetime',
        'items' => 'array',
        'estimated_total' => 'decimal:2',
    ];

    // Relationships
    public function requestedByUser()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedByUser()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function approvalHistories()
    {
        return $this->morphMany(\App\Models\ApprovalHistory::class, 'approvable');
    }

    public function currentApprovalHistory()
    {
        return $this->morphOne(\App\Models\ApprovalHistory::class, 'approvable')
            ->where('status', 'pending')
            ->orderBy('id');
    }

    public function division()
    {
        return $this->belongsTo(Division::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('requested_by', $userId);
    }

    public function scopeByDivision($query, $divisionId)
    {
        return $query->where('division_id', $divisionId);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['submitted', 'reviewed']);
    }

    // Accessors & Mutators
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => 'secondary',
            'submitted' => 'info',
            'reviewed' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'completed' => 'primary',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getStatusLabelAttribute()
    {
        $labels = [
            'draft' => 'Draft',
            'submitted' => 'Diajukan',
            'reviewed' => 'Dalam Review',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            'completed' => 'Selesai',
        ];

        return $labels[$this->status] ?? 'Unknown';
    }

    public function getTotalItemsAttribute()
    {
        return is_array($this->items) ? count($this->items) : 0;
    }

    // Static methods
    public static function generateRequestNumber($branchId)
    {
        return \App\Models\DocumentNumber::getRequestAssetNumber($branchId);
    }

    // Helper methods
    public function canBeEdited()
    {
        return in_array($this->status, ['draft', 'rejected']);
    }

    public function canBeSubmitted()
    {
        return $this->status === 'draft';
    }

    public function canBeApproved()
    {
        return in_array($this->status, ['submitted', 'reviewed']);
    }

    public function canBeRejected()
    {
        return in_array($this->status, ['submitted', 'reviewed']);
    }
}
