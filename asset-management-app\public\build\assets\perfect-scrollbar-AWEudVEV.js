import{c as rt,g as lt}from"./_commonjsHelpers-Cpj98o6Y.js";var j={exports:{}};/*!
 * perfect-scrollbar v1.5.6
 * Copyright 2024 <PERSON><PERSON><PERSON>, MDBootstrap and Contributors
 * Licensed under MIT
 */(function(C,st){(function(w,b){C.exports=b()})(rt,function(){function w(t){return getComputedStyle(t)}function b(t,e){for(var r in e){var o=e[r];typeof o=="number"&&(o=o+"px"),t.style[r]=o}return t}function M(t){var e=document.createElement("div");return e.className=t,e}var D=typeof Element<"u"&&(Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector);function W(t,e){if(!D)throw new Error("No element matching method supported");return D.call(t,e)}function T(t){t.remove?t.remove():t.parentNode&&t.parentNode.removeChild(t)}function k(t,e){return Array.prototype.filter.call(t.children,function(r){return W(r,e)})}var v={main:"ps",rtl:"ps__rtl",element:{thumb:function(t){return"ps__thumb-"+t},rail:function(t){return"ps__rail-"+t},consuming:"ps__child--consume"},state:{focus:"ps--focus",clicking:"ps--clicking",active:function(t){return"ps--active-"+t},scrolling:function(t){return"ps--scrolling-"+t}}},B={x:null,y:null};function O(t,e){var r=t.element.classList,o=v.state.scrolling(e);r.contains(o)?clearTimeout(B[e]):r.add(o)}function I(t,e){B[e]=setTimeout(function(){return t.isAlive&&t.element.classList.remove(v.state.scrolling(e))},t.settings.scrollingThreshold)}function U(t,e){O(t,e),I(t,e)}var R=function(e){this.element=e,this.handlers={}},K={isEmpty:{configurable:!0}};R.prototype.bind=function(e,r){typeof this.handlers[e]>"u"&&(this.handlers[e]=[]),this.handlers[e].push(r),this.element.addEventListener(e,r,!1)},R.prototype.unbind=function(e,r){var o=this;this.handlers[e]=this.handlers[e].filter(function(a){return r&&a!==r?!0:(o.element.removeEventListener(e,a,!1),!1)})},R.prototype.unbindAll=function(){for(var e in this.handlers)this.unbind(e)},K.isEmpty.get=function(){var t=this;return Object.keys(this.handlers).every(function(e){return t.handlers[e].length===0})},Object.defineProperties(R.prototype,K);var E=function(){this.eventElements=[]};E.prototype.eventElement=function(e){var r=this.eventElements.filter(function(o){return o.element===e})[0];return r||(r=new R(e),this.eventElements.push(r)),r},E.prototype.bind=function(e,r,o){this.eventElement(e).bind(r,o)},E.prototype.unbind=function(e,r,o){var a=this.eventElement(e);a.unbind(r,o),a.isEmpty&&this.eventElements.splice(this.eventElements.indexOf(a),1)},E.prototype.unbindAll=function(){this.eventElements.forEach(function(e){return e.unbindAll()}),this.eventElements=[]},E.prototype.once=function(e,r,o){var a=this.eventElement(e),h=function(s){a.unbind(r,h),o(s)};a.bind(r,h)};function A(t){if(typeof window.CustomEvent=="function")return new CustomEvent(t);var e=document.createEvent("CustomEvent");return e.initCustomEvent(t,!1,!1,void 0),e}function P(t,e,r,o,a){o===void 0&&(o=!0),a===void 0&&(a=!1);var h;if(e==="top")h=["contentHeight","containerHeight","scrollTop","y","up","down"];else if(e==="left")h=["contentWidth","containerWidth","scrollLeft","x","left","right"];else throw new Error("A proper axis should be provided");$(t,r,h,o,a)}function $(t,e,r,o,a){var h=r[0],s=r[1],i=r[2],l=r[3],c=r[4],p=r[5];o===void 0&&(o=!0),a===void 0&&(a=!1);var n=t.element;t.reach[l]=null,n[i]<1&&(t.reach[l]="start"),n[i]>t[h]-t[s]-1&&(t.reach[l]="end"),e&&(n.dispatchEvent(A("ps-scroll-"+l)),e<0?n.dispatchEvent(A("ps-scroll-"+c)):e>0&&n.dispatchEvent(A("ps-scroll-"+p)),o&&U(t,l)),t.reach[l]&&(e||a)&&n.dispatchEvent(A("ps-"+l+"-reach-"+t.reach[l]))}function d(t){return parseInt(t,10)||0}function q(t){return W(t,"input,[contenteditable]")||W(t,"select,[contenteditable]")||W(t,"textarea,[contenteditable]")||W(t,"button,[contenteditable]")}function z(t){var e=w(t);return d(e.width)+d(e.paddingLeft)+d(e.paddingRight)+d(e.borderLeftWidth)+d(e.borderRightWidth)}var H={isWebKit:typeof document<"u"&&"WebkitAppearance"in document.documentElement.style,supportsTouch:typeof window<"u"&&("ontouchstart"in window||"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>0||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:typeof navigator<"u"&&navigator.msMaxTouchPoints,isChrome:typeof navigator<"u"&&/Chrome/i.test(navigator&&navigator.userAgent)};function L(t){var e=t.element,r=Math.floor(e.scrollTop),o=e.getBoundingClientRect();t.containerWidth=Math.floor(o.width),t.containerHeight=Math.floor(o.height),t.contentWidth=e.scrollWidth,t.contentHeight=e.scrollHeight,e.contains(t.scrollbarXRail)||(k(e,v.element.rail("x")).forEach(function(a){return T(a)}),e.appendChild(t.scrollbarXRail)),e.contains(t.scrollbarYRail)||(k(e,v.element.rail("y")).forEach(function(a){return T(a)}),e.appendChild(t.scrollbarYRail)),!t.settings.suppressScrollX&&t.containerWidth+t.settings.scrollXMarginOffset<t.contentWidth?(t.scrollbarXActive=!0,t.railXWidth=t.containerWidth-t.railXMarginWidth,t.railXRatio=t.containerWidth/t.railXWidth,t.scrollbarXWidth=N(t,d(t.railXWidth*t.containerWidth/t.contentWidth)),t.scrollbarXLeft=d((t.negativeScrollAdjustment+e.scrollLeft)*(t.railXWidth-t.scrollbarXWidth)/(t.contentWidth-t.containerWidth))):t.scrollbarXActive=!1,!t.settings.suppressScrollY&&t.containerHeight+t.settings.scrollYMarginOffset<t.contentHeight?(t.scrollbarYActive=!0,t.railYHeight=t.containerHeight-t.railYMarginHeight,t.railYRatio=t.containerHeight/t.railYHeight,t.scrollbarYHeight=N(t,d(t.railYHeight*t.containerHeight/t.contentHeight)),t.scrollbarYTop=d(r*(t.railYHeight-t.scrollbarYHeight)/(t.contentHeight-t.containerHeight))):t.scrollbarYActive=!1,t.scrollbarXLeft>=t.railXWidth-t.scrollbarXWidth&&(t.scrollbarXLeft=t.railXWidth-t.scrollbarXWidth),t.scrollbarYTop>=t.railYHeight-t.scrollbarYHeight&&(t.scrollbarYTop=t.railYHeight-t.scrollbarYHeight),F(e,t),t.scrollbarXActive?e.classList.add(v.state.active("x")):(e.classList.remove(v.state.active("x")),t.scrollbarXWidth=0,t.scrollbarXLeft=0,e.scrollLeft=t.isRtl===!0?t.contentWidth:0),t.scrollbarYActive?e.classList.add(v.state.active("y")):(e.classList.remove(v.state.active("y")),t.scrollbarYHeight=0,t.scrollbarYTop=0,e.scrollTop=0)}function N(t,e){return t.settings.minScrollbarLength&&(e=Math.max(e,t.settings.minScrollbarLength)),t.settings.maxScrollbarLength&&(e=Math.min(e,t.settings.maxScrollbarLength)),e}function F(t,e){var r={width:e.railXWidth},o=Math.floor(t.scrollTop);e.isRtl?r.left=e.negativeScrollAdjustment+t.scrollLeft+e.containerWidth-e.contentWidth:r.left=t.scrollLeft,e.isScrollbarXUsingBottom?r.bottom=e.scrollbarXBottom-o:r.top=e.scrollbarXTop+o,b(e.scrollbarXRail,r);var a={top:o,height:e.railYHeight};e.isScrollbarYUsingRight?e.isRtl?a.right=e.contentWidth-(e.negativeScrollAdjustment+t.scrollLeft)-e.scrollbarYRight-e.scrollbarYOuterWidth-9:a.right=e.scrollbarYRight-t.scrollLeft:e.isRtl?a.left=e.negativeScrollAdjustment+t.scrollLeft+e.containerWidth*2-e.contentWidth-e.scrollbarYLeft-e.scrollbarYOuterWidth:a.left=e.scrollbarYLeft+t.scrollLeft,b(e.scrollbarYRail,a),b(e.scrollbarX,{left:e.scrollbarXLeft,width:e.scrollbarXWidth-e.railBorderXWidth}),b(e.scrollbarY,{top:e.scrollbarYTop,height:e.scrollbarYHeight-e.railBorderYWidth})}function G(t){t.event.bind(t.scrollbarY,"mousedown",function(e){return e.stopPropagation()}),t.event.bind(t.scrollbarYRail,"mousedown",function(e){var r=e.pageY-window.pageYOffset-t.scrollbarYRail.getBoundingClientRect().top,o=r>t.scrollbarYTop?1:-1;t.element.scrollTop+=o*t.containerHeight,L(t),e.stopPropagation()}),t.event.bind(t.scrollbarX,"mousedown",function(e){return e.stopPropagation()}),t.event.bind(t.scrollbarXRail,"mousedown",function(e){var r=e.pageX-window.pageXOffset-t.scrollbarXRail.getBoundingClientRect().left,o=r>t.scrollbarXLeft?1:-1;t.element.scrollLeft+=o*t.containerWidth,L(t),e.stopPropagation()})}var x=null;function J(t){_(t,["containerHeight","contentHeight","pageY","railYHeight","scrollbarY","scrollbarYHeight","scrollTop","y","scrollbarYRail"]),_(t,["containerWidth","contentWidth","pageX","railXWidth","scrollbarX","scrollbarXWidth","scrollLeft","x","scrollbarXRail"])}function _(t,e){var r=e[0],o=e[1],a=e[2],h=e[3],s=e[4],i=e[5],l=e[6],c=e[7],p=e[8],n=t.element,f=null,g=null,u=null;function y(m){m.touches&&m.touches[0]&&(m[a]=m.touches[0]["page"+c.toUpperCase()]),x===s&&(n[l]=f+u*(m[a]-g),O(t,c),L(t),m.stopPropagation(),m.preventDefault())}function Y(){I(t,c),t[p].classList.remove(v.state.clicking),document.removeEventListener("mousemove",y),document.removeEventListener("mouseup",Y),document.removeEventListener("touchmove",y),document.removeEventListener("touchend",Y),x=null}function X(m){x===null&&(x=s,f=n[l],m.touches&&(m[a]=m.touches[0]["page"+c.toUpperCase()]),g=m[a],u=(t[o]-t[r])/(t[h]-t[i]),m.touches?(document.addEventListener("touchmove",y,{passive:!1}),document.addEventListener("touchend",Y)):(document.addEventListener("mousemove",y),document.addEventListener("mouseup",Y)),t[p].classList.add(v.state.clicking)),m.stopPropagation(),m.cancelable&&m.preventDefault()}t[s].addEventListener("mousedown",X),t[s].addEventListener("touchstart",X)}function Q(t){var e=t.element,r=function(){return W(e,":hover")},o=function(){return W(t.scrollbarX,":focus")||W(t.scrollbarY,":focus")};function a(h,s){var i=Math.floor(e.scrollTop);if(h===0){if(!t.scrollbarYActive)return!1;if(i===0&&s>0||i>=t.contentHeight-t.containerHeight&&s<0)return!t.settings.wheelPropagation}var l=e.scrollLeft;if(s===0){if(!t.scrollbarXActive)return!1;if(l===0&&h<0||l>=t.contentWidth-t.containerWidth&&h>0)return!t.settings.wheelPropagation}return!0}t.event.bind(t.ownerDocument,"keydown",function(h){if(!(h.isDefaultPrevented&&h.isDefaultPrevented()||h.defaultPrevented)&&!(!r()&&!o())){var s=document.activeElement?document.activeElement:t.ownerDocument.activeElement;if(s){if(s.tagName==="IFRAME")s=s.contentDocument.activeElement;else for(;s.shadowRoot;)s=s.shadowRoot.activeElement;if(q(s))return}var i=0,l=0;switch(h.which){case 37:h.metaKey?i=-t.contentWidth:h.altKey?i=-t.containerWidth:i=-30;break;case 38:h.metaKey?l=t.contentHeight:h.altKey?l=t.containerHeight:l=30;break;case 39:h.metaKey?i=t.contentWidth:h.altKey?i=t.containerWidth:i=30;break;case 40:h.metaKey?l=-t.contentHeight:h.altKey?l=-t.containerHeight:l=-30;break;case 32:h.shiftKey?l=t.containerHeight:l=-t.containerHeight;break;case 33:l=t.containerHeight;break;case 34:l=-t.containerHeight;break;case 36:l=t.contentHeight;break;case 35:l=-t.contentHeight;break;default:return}t.settings.suppressScrollX&&i!==0||t.settings.suppressScrollY&&l!==0||(e.scrollTop-=l,e.scrollLeft+=i,L(t),a(i,l)&&h.preventDefault())}})}function V(t){var e=t.element;function r(s,i){var l=Math.floor(e.scrollTop),c=e.scrollTop===0,p=l+e.offsetHeight===e.scrollHeight,n=e.scrollLeft===0,f=e.scrollLeft+e.offsetWidth===e.scrollWidth,g;return Math.abs(i)>Math.abs(s)?g=c||p:g=n||f,g?!t.settings.wheelPropagation:!0}function o(s){var i=s.deltaX,l=-1*s.deltaY;return(typeof i>"u"||typeof l>"u")&&(i=-1*s.wheelDeltaX/6,l=s.wheelDeltaY/6),s.deltaMode&&s.deltaMode===1&&(i*=10,l*=10),i!==i&&l!==l&&(i=0,l=s.wheelDelta),s.shiftKey?[-l,-i]:[i,l]}function a(s,i,l){if(!H.isWebKit&&e.querySelector("select:focus"))return!0;if(!e.contains(s))return!1;for(var c=s;c&&c!==e;){if(c.classList.contains(v.element.consuming))return!0;var p=w(c);if(l&&p.overflowY.match(/(scroll|auto)/)){var n=c.scrollHeight-c.clientHeight;if(n>0&&(c.scrollTop>0&&l<0||c.scrollTop<n&&l>0))return!0}if(i&&p.overflowX.match(/(scroll|auto)/)){var f=c.scrollWidth-c.clientWidth;if(f>0&&(c.scrollLeft>0&&i<0||c.scrollLeft<f&&i>0))return!0}c=c.parentNode}return!1}function h(s){var i=o(s),l=i[0],c=i[1];if(!a(s.target,l,c)){var p=!1;t.settings.useBothWheelAxes?t.scrollbarYActive&&!t.scrollbarXActive?(c?e.scrollTop-=c*t.settings.wheelSpeed:e.scrollTop+=l*t.settings.wheelSpeed,p=!0):t.scrollbarXActive&&!t.scrollbarYActive&&(l?e.scrollLeft+=l*t.settings.wheelSpeed:e.scrollLeft-=c*t.settings.wheelSpeed,p=!0):(e.scrollTop-=c*t.settings.wheelSpeed,e.scrollLeft+=l*t.settings.wheelSpeed),L(t),p=p||r(l,c),p&&!s.ctrlKey&&(s.stopPropagation(),s.preventDefault())}}typeof window.onwheel<"u"?t.event.bind(e,"wheel",h):typeof window.onmousewheel<"u"&&t.event.bind(e,"mousewheel",h)}function Z(t){if(!H.supportsTouch&&!H.supportsIePointer)return;var e=t.element,r={startOffset:{},startTime:0,speed:{},easingLoop:null};function o(n,f){var g=Math.floor(e.scrollTop),u=e.scrollLeft,y=Math.abs(n),Y=Math.abs(f);if(Y>y){if(f<0&&g===t.contentHeight-t.containerHeight||f>0&&g===0)return window.scrollY===0&&f>0&&H.isChrome}else if(y>Y&&(n<0&&u===t.contentWidth-t.containerWidth||n>0&&u===0))return!0;return!0}function a(n,f){e.scrollTop-=f,e.scrollLeft-=n,L(t)}function h(n){return n.targetTouches?n.targetTouches[0]:n}function s(n){return n.target===t.scrollbarX||n.target===t.scrollbarY||n.pointerType&&n.pointerType==="pen"&&n.buttons===0?!1:!!(n.targetTouches&&n.targetTouches.length===1||n.pointerType&&n.pointerType!=="mouse"&&n.pointerType!==n.MSPOINTER_TYPE_MOUSE)}function i(n){if(s(n)){var f=h(n);r.startOffset.pageX=f.pageX,r.startOffset.pageY=f.pageY,r.startTime=new Date().getTime(),r.easingLoop!==null&&clearInterval(r.easingLoop)}}function l(n,f,g){if(!e.contains(n))return!1;for(var u=n;u&&u!==e;){if(u.classList.contains(v.element.consuming))return!0;var y=w(u);if(g&&y.overflowY.match(/(scroll|auto)/)){var Y=u.scrollHeight-u.clientHeight;if(Y>0&&(u.scrollTop>0&&g<0||u.scrollTop<Y&&g>0))return!0}if(f&&y.overflowX.match(/(scroll|auto)/)){var X=u.scrollWidth-u.clientWidth;if(X>0&&(u.scrollLeft>0&&f<0||u.scrollLeft<X&&f>0))return!0}u=u.parentNode}return!1}function c(n){if(s(n)){var f=h(n),g={pageX:f.pageX,pageY:f.pageY},u=g.pageX-r.startOffset.pageX,y=g.pageY-r.startOffset.pageY;if(l(n.target,u,y))return;a(u,y),r.startOffset=g;var Y=new Date().getTime(),X=Y-r.startTime;X>0&&(r.speed.x=u/X,r.speed.y=y/X,r.startTime=Y),o(u,y)&&n.cancelable&&n.preventDefault()}}function p(){t.settings.swipeEasing&&(clearInterval(r.easingLoop),r.easingLoop=setInterval(function(){if(t.isInitialized){clearInterval(r.easingLoop);return}if(!r.speed.x&&!r.speed.y){clearInterval(r.easingLoop);return}if(Math.abs(r.speed.x)<.01&&Math.abs(r.speed.y)<.01){clearInterval(r.easingLoop);return}a(r.speed.x*30,r.speed.y*30),r.speed.x*=.8,r.speed.y*=.8},10))}H.supportsTouch?(t.event.bind(e,"touchstart",i),t.event.bind(e,"touchmove",c),t.event.bind(e,"touchend",p)):H.supportsIePointer&&(window.PointerEvent?(t.event.bind(e,"pointerdown",i),t.event.bind(e,"pointermove",c),t.event.bind(e,"pointerup",p)):window.MSPointerEvent&&(t.event.bind(e,"MSPointerDown",i),t.event.bind(e,"MSPointerMove",c),t.event.bind(e,"MSPointerUp",p)))}var tt=function(){return{handlers:["click-rail","drag-thumb","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollingThreshold:1e3,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!0,wheelSpeed:1}},et={"click-rail":G,"drag-thumb":J,keyboard:Q,wheel:V,touch:Z},S=function(e,r){var o=this;if(r===void 0&&(r={}),typeof e=="string"&&(e=document.querySelector(e)),!e||!e.nodeName)throw new Error("no element is specified to initialize PerfectScrollbar");this.element=e,e.classList.add(v.main),this.settings=tt();for(var a in r)this.settings[a]=r[a];this.containerWidth=null,this.containerHeight=null,this.contentWidth=null,this.contentHeight=null;var h=function(){return e.classList.add(v.state.focus)},s=function(){return e.classList.remove(v.state.focus)};this.isRtl=w(e).direction==="rtl",this.isRtl===!0&&e.classList.add(v.rtl),this.isNegativeScroll=function(){var c=e.scrollLeft,p=null;return e.scrollLeft=-1,p=e.scrollLeft<0,e.scrollLeft=c,p}(),this.negativeScrollAdjustment=this.isNegativeScroll?e.scrollWidth-e.clientWidth:0,this.event=new E,this.ownerDocument=e.ownerDocument||document,this.scrollbarXRail=M(v.element.rail("x")),e.appendChild(this.scrollbarXRail),this.scrollbarX=M(v.element.thumb("x")),this.scrollbarXRail.appendChild(this.scrollbarX),this.scrollbarX.setAttribute("tabindex",0),this.event.bind(this.scrollbarX,"focus",h),this.event.bind(this.scrollbarX,"blur",s),this.scrollbarXActive=null,this.scrollbarXWidth=null,this.scrollbarXLeft=null;var i=w(this.scrollbarXRail);this.scrollbarXBottom=parseInt(i.bottom,10),isNaN(this.scrollbarXBottom)?(this.isScrollbarXUsingBottom=!1,this.scrollbarXTop=d(i.top)):this.isScrollbarXUsingBottom=!0,this.railBorderXWidth=d(i.borderLeftWidth)+d(i.borderRightWidth),b(this.scrollbarXRail,{display:"block"}),this.railXMarginWidth=d(i.marginLeft)+d(i.marginRight),b(this.scrollbarXRail,{display:""}),this.railXWidth=null,this.railXRatio=null,this.scrollbarYRail=M(v.element.rail("y")),e.appendChild(this.scrollbarYRail),this.scrollbarY=M(v.element.thumb("y")),this.scrollbarYRail.appendChild(this.scrollbarY),this.scrollbarY.setAttribute("tabindex",0),this.event.bind(this.scrollbarY,"focus",h),this.event.bind(this.scrollbarY,"blur",s),this.scrollbarYActive=null,this.scrollbarYHeight=null,this.scrollbarYTop=null;var l=w(this.scrollbarYRail);this.scrollbarYRight=parseInt(l.right,10),isNaN(this.scrollbarYRight)?(this.isScrollbarYUsingRight=!1,this.scrollbarYLeft=d(l.left)):this.isScrollbarYUsingRight=!0,this.scrollbarYOuterWidth=this.isRtl?z(this.scrollbarY):null,this.railBorderYWidth=d(l.borderTopWidth)+d(l.borderBottomWidth),b(this.scrollbarYRail,{display:"block"}),this.railYMarginHeight=d(l.marginTop)+d(l.marginBottom),b(this.scrollbarYRail,{display:""}),this.railYHeight=null,this.railYRatio=null,this.reach={x:e.scrollLeft<=0?"start":e.scrollLeft>=this.contentWidth-this.containerWidth?"end":null,y:e.scrollTop<=0?"start":e.scrollTop>=this.contentHeight-this.containerHeight?"end":null},this.isAlive=!0,this.settings.handlers.forEach(function(c){return et[c](o)}),this.lastScrollTop=Math.floor(e.scrollTop),this.lastScrollLeft=e.scrollLeft,this.event.bind(this.element,"scroll",function(c){return o.onScroll(c)}),L(this)};return S.prototype.update=function(){this.isAlive&&(this.negativeScrollAdjustment=this.isNegativeScroll?this.element.scrollWidth-this.element.clientWidth:0,b(this.scrollbarXRail,{display:"block"}),b(this.scrollbarYRail,{display:"block"}),this.railXMarginWidth=d(w(this.scrollbarXRail).marginLeft)+d(w(this.scrollbarXRail).marginRight),this.railYMarginHeight=d(w(this.scrollbarYRail).marginTop)+d(w(this.scrollbarYRail).marginBottom),b(this.scrollbarXRail,{display:"none"}),b(this.scrollbarYRail,{display:"none"}),L(this),P(this,"top",0,!1,!0),P(this,"left",0,!1,!0),b(this.scrollbarXRail,{display:""}),b(this.scrollbarYRail,{display:""}))},S.prototype.onScroll=function(e){this.isAlive&&(L(this),P(this,"top",this.element.scrollTop-this.lastScrollTop),P(this,"left",this.element.scrollLeft-this.lastScrollLeft),this.lastScrollTop=Math.floor(this.element.scrollTop),this.lastScrollLeft=this.element.scrollLeft)},S.prototype.destroy=function(){this.isAlive&&(this.event.unbindAll(),T(this.scrollbarX),T(this.scrollbarY),T(this.scrollbarXRail),T(this.scrollbarYRail),this.removePsClasses(),this.element=null,this.scrollbarX=null,this.scrollbarY=null,this.scrollbarXRail=null,this.scrollbarYRail=null,this.isAlive=!1)},S.prototype.removePsClasses=function(){this.element.className=this.element.className.split(" ").filter(function(e){return!e.match(/^ps([-_].+|)$/)}).join(" ")},S})})(j);var nt=j.exports;const ot=lt(nt);try{window.PerfectScrollbar=ot}catch{}
