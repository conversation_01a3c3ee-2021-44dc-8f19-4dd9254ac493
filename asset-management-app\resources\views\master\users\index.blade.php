@extends('layouts.contentNavbarLayout')

@section('title', 'Master User - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Master User
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('master.users.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah User
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('master.users.index') }}">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                   placeholder="Nama, username, atau email...">
          </div>
          <div class="col-md-2">
            <label class="form-label">Role</label>
            <select class="form-select" name="role_id">
              <option value="">Semua Role</option>
              @foreach($roles as $role)
                <option value="{{ $role->id }}" {{ request('role_id') == $role->id ? 'selected' : '' }}>
                  {{ $role->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Cabang</label>
            <select class="form-select" name="branch_id">
              <option value="">Semua Cabang</option>
              @foreach($branches as $branch)
                <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                  {{ $branch->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Divisi</label>
            <select class="form-select" name="division_id">
              <option value="">Semua Divisi</option>
              @foreach($divisions as $division)
                <option value="{{ $division->id }}" {{ request('division_id') == $division->id ? 'selected' : '' }}>
                  {{ $division->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" name="is_active">
              <option value="">Semua Status</option>
              <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Aktif</option>
              <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Non-Aktif</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('master.users.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Users Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar User ({{ $users->total() }} user)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>User</th>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th>Cabang</th>
            <th>Divisi</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($users as $user)
          <tr>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar avatar-sm me-2">
                  <span class="avatar-initial rounded-circle bg-label-primary">
                    {{ strtoupper(substr($user->name, 0, 2)) }}
                  </span>
                </div>
                <div>
                  <strong>{{ $user->name }}</strong>
                  @if($user->phone)
                    <br><small class="text-muted">{{ $user->phone }}</small>
                  @endif
                </div>
              </div>
            </td>
            <td>
              <span class="badge bg-info">{{ $user->username }}</span>
            </td>
            <td>{{ $user->email }}</td>
            <td>
              <span class="badge bg-{{ $user->role->slug === 'super-admin' ? 'danger' : ($user->role->slug === 'admin' ? 'warning' : 'primary') }}">
                {{ $user->role->name }}
              </span>
            </td>
            <td>{{ $user->branch->name }}</td>
            <td>
              @if($user->division)
                <span class="badge bg-secondary">{{ $user->division->name }}</span>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                {{ $user->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('master.users.show', $user) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('master.users.edit', $user) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  @if($user->id !== auth()->id())
                  <div class="dropdown-divider"></div>
                  <form action="{{ route('master.users.destroy', $user) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus user ini?\n\nTindakan ini tidak dapat dibatalkan!')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                  @endif
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="8" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-user-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada data user</h6>
                <p class="text-muted mb-3">Belum ada user yang terdaftar atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('master.users.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah User Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($users->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $users->firstItem() }} - {{ $users->lastItem() }} dari {{ $users->total() }} data
        </div>
        {{ $users->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
