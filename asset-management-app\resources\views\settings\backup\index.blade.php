@extends('layouts/contentNavbarLayout')

@section('title', 'Backup & Restore Database')

@section('page-style')
<style>
.backup-card {
  transition: all 0.3s ease;
}

.backup-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.backup-item {
  border-left: 4px solid #007bff;
  background: #f8f9fa;
}

.backup-item:hover {
  background: #e9ecef;
}

.danger-zone {
  border: 2px dashed #dc3545;
  background: #fff5f5;
}

.upload-zone {
  border: 2px dashed #28a745;
  background: #f8fff9;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-zone:hover {
  border-color: #20c997;
  background: #f0fff4;
}

.upload-zone.dragover {
  border-color: #007bff;
  background: #f0f8ff;
}
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="ri-database-2-line me-2"></i>
              Backup & Restore Database
            </h5>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBackupModal">
                <i class="ri-add-line me-1"></i>
                Buat Backup
              </button>
              <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadBackupModal">
                <i class="ri-upload-line me-1"></i>
                Upload Backup
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<br>
  <!-- Backup Statistics -->
  <div class="row mb-6">
    <div class="col-md-3">
      <div class="card backup-card">
        <div class="card-body text-center">
          <div class="avatar avatar-lg mx-auto mb-3">
            <span class="avatar-initial rounded-circle bg-primary">
              <i class="ri-file-list-3-line ri-24px"></i>
            </span>
          </div>
          <h5 class="mb-1">{{ count($backups) }}</h5>
          <p class="mb-0 text-muted">Total Backup</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card backup-card">
        <div class="card-body text-center">
          <div class="avatar avatar-lg mx-auto mb-3">
            <span class="avatar-initial rounded-circle bg-success">
              <i class="ri-hard-drive-line ri-24px"></i>
            </span>
          </div>
          <h5 class="mb-1">
            @php
              $totalSize = array_sum(array_column($backups, 'size'));
              echo $totalSize > 0 ? number_format($totalSize / 1024 / 1024, 2) . ' MB' : '0 MB';
            @endphp
          </h5>
          <p class="mb-0 text-muted">Total Size</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card backup-card">
        <div class="card-body text-center">
          <div class="avatar avatar-lg mx-auto mb-3">
            <span class="avatar-initial rounded-circle bg-info">
              <i class="ri-calendar-line ri-24px"></i>
            </span>
          </div>
          <h5 class="mb-1">
            @if(isset($stats['last_backup']) && $stats['last_backup'])
              {{ \Carbon\Carbon::parse($stats['last_backup'])->diffForHumans() }}
            @else
              -
            @endif
          </h5>
          <p class="mb-0 text-muted">Backup Terakhir</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card backup-card">
        <div class="card-body text-center">
          <div class="avatar avatar-lg mx-auto mb-3">
            <span class="avatar-initial rounded-circle bg-secondary">
              <i class="ri-pie-chart-line ri-24px"></i>
            </span>
          </div>
          <h5 class="mb-1">
            @if(isset($stats['backup_types']) && count($stats['backup_types']) > 0)
              {{ count($stats['backup_types']) }} Types
            @else
              0 Types
            @endif
          </h5>
          <p class="mb-0 text-muted">Backup Types</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Backup List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-file-list-line me-2"></i>
            Daftar Backup
          </h6>
        </div>
        <div class="card-body">
          @if(count($backups) > 0)
            <div class="row">
              @foreach($backups as $backup)
                <div class="col-md-6 col-lg-6 mb-5">
                  <div class="backup-item p-3 rounded"
                       data-bs-toggle="tooltip"
                       data-bs-placement="top"
                       title="File: {{ $backup['filename'] }} | Created: {{ \Carbon\Carbon::parse($backup['created_at'])->format('d/m/Y H:i:s') }} | By: {{ $backup['created_by'] }}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                      <div>
                        <h6 class="mb-1">
                          <i class="ri-file-3-line me-1"></i>
                          {{ $backup['backup_name'] ?? $backup['filename'] }}
                        </h6>
                        <small class="text-muted d-block">
                          <i class="ri-time-line me-1"></i>
                          {{ \Carbon\Carbon::parse($backup['created_at'])->format('d/m/Y H:i') }}
                        </small>
                        <small class="text-muted d-block">
                          <i class="ri-user-line me-1"></i>
                          Created by: <strong>{{ $backup['created_by'] }}</strong>
                        </small>
                        <small class="text-muted d-block">
                        <small class="text-muted">{{ \Carbon\Carbon::parse($backup['created_at'])->diffForHumans() }}</small>
                        </small>
                        @if($backup['type'] !== 'manual')
                          <small class="text-muted d-block">
                            <i class="ri-upload-line me-1"></i>
                            Type: <span class="badge bg-info">{{ ucfirst($backup['type']) }}</span>
                          </small>
                        @endif
                      </div>
                      <div class="dropdown">
                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                          <i class="ri-more-2-line"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="{{ route('backup.download', $backup['filename']) }}">
                              <i class="ri-download-line me-2"></i>
                              Download
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item text-warning" href="#" onclick="showRestoreModal('{{ $backup['filename'] }}')">
                              <i class="ri-refresh-line me-2"></i>
                              Restore
                            </a>
                          </li>
                          <li><hr class="dropdown-divider"></li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ $backup['filename'] }}')">
                              <i class="ri-delete-bin-line me-2"></i>
                              Hapus
                            </a>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                      <div class="d-flex gap-2">
                        <span class="badge bg-primary">{{ $backup['formatted_size'] }}</span>
                        @if($backup['include_data'])
                          <span class="badge bg-success">Full</span>
                        @else
                          <span class="badge bg-warning">Structure Only</span>
                        @endif
                      </div>
                      
                    </div>
                  </div>
                </div>
              @endforeach
            </div>
          @else
            <div class="text-center py-5">
              <div class="avatar avatar-xl mx-auto mb-3">
                <span class="avatar-initial rounded-circle bg-light">
                  <i class="ri-database-2-line ri-36px text-muted"></i>
                </span>
              </div>
              <h6 class="text-muted">Belum ada backup</h6>
              <p class="text-muted">Buat backup pertama Anda untuk mengamankan data.</p>
              <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBackupModal">
                <i class="ri-add-line me-1"></i>
                Buat Backup Sekarang
              </button>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Backup Modal -->
<div class="modal fade" id="createBackupModal" tabindex="-1" aria-labelledby="createBackupModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createBackupModalLabel">
          <i class="ri-add-line me-2"></i>
          Buat Backup Database
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('backup.create') }}" method="POST">
        @csrf
        <div class="modal-body">
          <div class="mb-3">
            <label for="backup_name" class="form-label">Nama Backup</label>
            <input type="text" class="form-control" id="backup_name" name="backup_name"
                   value="backup_{{ date('Y-m-d_H-i-s') }}" required>
            <div class="form-text">Nama file backup (tanpa ekstensi .sql)</div>
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="include_data" name="include_data" value="1" checked>
              <label class="form-check-label" for="include_data">
                Sertakan Data
              </label>
            </div>
            <div class="form-text">Jika tidak dicentang, hanya struktur tabel yang akan di-backup</div>
          </div>
          <div class="alert alert-info">
            <i class="ri-information-line me-2"></i>
            <strong>Informasi:</strong> Proses backup mungkin membutuhkan waktu beberapa menit tergantung ukuran database.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="ri-close-line me-1"></i>
            Batal
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            Buat Backup
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Upload Backup Modal -->
<div class="modal fade" id="uploadBackupModal" tabindex="-1" aria-labelledby="uploadBackupModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="uploadBackupModalLabel">
          <i class="ri-upload-line me-2"></i>
          Upload File Backup
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('backup.upload') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="modal-body">
          <div class="mb-3">
            <label for="backup_upload" class="form-label">File Backup (.sql)</label>
            <div class="upload-zone" onclick="document.getElementById('backup_upload').click()">
              <div class="text-center">
                <i class="ri-upload-cloud-line ri-48px text-muted mb-2"></i>
                <p class="mb-1">Klik untuk pilih file atau drag & drop</p>
                <small class="text-muted">File .sql maksimal 100MB</small>
              </div>
            </div>
            <input type="file" class="form-control d-none" id="backup_upload" name="backup_upload"
                   accept=".sql" required>
            <div id="file-info" class="mt-2 d-none">
              <div class="alert alert-success">
                <i class="ri-file-line me-2"></i>
                <span id="file-name"></span>
                <small class="text-muted">(<span id="file-size"></span>)</small>
              </div>
            </div>
          </div>
          <div class="alert alert-warning">
            <i class="ri-alert-line me-2"></i>
            <strong>Peringatan:</strong> Pastikan file backup kompatibel dengan versi database yang digunakan.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="ri-close-line me-1"></i>
            Batal
          </button>
          <button type="submit" class="btn btn-success">
            <i class="ri-upload-line me-1"></i>
            Upload
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Restore Confirmation Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1" aria-labelledby="restoreModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-warning">
        <h5 class="modal-title text-white" id="restoreModalLabel">
          <i class="ri-alert-line me-2"></i>
          Konfirmasi Restore Database
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('backup.restore') }}" method="POST">
        @csrf
        <div class="modal-body">
          <div class="danger-zone p-3 rounded mb-3">
            <h6 class="text-danger mb-2">
              <i class="ri-error-warning-line me-2"></i>
              PERINGATAN PENTING!
            </h6>
            <ul class="text-danger mb-0">
              <li>Semua data saat ini akan <strong>DIGANTI</strong> dengan data dari backup</li>
              <li>Perubahan ini <strong>TIDAK DAPAT DIBATALKAN</strong></li>
              <li>Pastikan Anda telah membuat backup terbaru sebelum melanjutkan</li>
            </ul>
          </div>

          <div class="mb-3">
            <label class="form-label">File Backup yang akan di-restore:</label>
            <div class="alert alert-info">
              <i class="ri-file-line me-2"></i>
              <strong id="restore-filename"></strong>
            </div>
          </div>

          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="confirm_restore" name="confirm_restore" value="1" required>
              <label class="form-check-label text-danger" for="confirm_restore">
                <strong>Saya memahami risiko dan ingin melanjutkan restore</strong>
              </label>
            </div>
          </div>

          <input type="hidden" id="backup_file" name="backup_file">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="ri-close-line me-1"></i>
            Batal
          </button>
          <button type="submit" class="btn btn-danger">
            <i class="ri-refresh-line me-1"></i>
            Restore Database
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
// File upload handling
document.getElementById('backup_upload').addEventListener('change', function(e) {
  const file = e.target.files[0];
  if (file) {
    document.getElementById('file-name').textContent = file.name;
    document.getElementById('file-size').textContent = formatBytes(file.size);
    document.getElementById('file-info').classList.remove('d-none');
  }
});

// Drag and drop handling
const uploadZone = document.querySelector('.upload-zone');
uploadZone.addEventListener('dragover', function(e) {
  e.preventDefault();
  this.classList.add('dragover');
});

uploadZone.addEventListener('dragleave', function(e) {
  e.preventDefault();
  this.classList.remove('dragover');
});

uploadZone.addEventListener('drop', function(e) {
  e.preventDefault();
  this.classList.remove('dragover');

  const files = e.dataTransfer.files;
  if (files.length > 0) {
    document.getElementById('backup_upload').files = files;
    document.getElementById('backup_upload').dispatchEvent(new Event('change'));
  }
});

// Show restore modal
function showRestoreModal(filename) {
  document.getElementById('backup_file').value = filename;
  document.getElementById('restore-filename').textContent = filename;
  document.getElementById('confirm_restore').checked = false;
  new bootstrap.Modal(document.getElementById('restoreModal')).show();
}

// Confirm delete
function confirmDelete(filename) {
  if (confirm('Apakah Anda yakin ingin menghapus backup: ' + filename + '?')) {
    window.location.href = '{{ route("backup.delete", "") }}/' + filename;
  }
}

// Format bytes
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
  // Initialize Bootstrap tooltips
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Add hover effects for backup items
  document.querySelectorAll('.backup-item').forEach(function(item) {
    item.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px)';
      this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    });

    item.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = 'none';
    });
  });
});
</script>
@endsection
