<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class PurchaseOrderPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create purchase order permissions
        $permissions = [
            [
                'name' => 'View Purchase Orders',
                'slug' => 'purchase-orders.view',
                'module' => 'Purchase Order',
                'description' => 'Can view purchase orders'
            ],
            [
                'name' => 'Create Purchase Orders',
                'slug' => 'purchase-orders.create',
                'module' => 'Purchase Order',
                'description' => 'Can create new purchase orders'
            ],
            [
                'name' => 'Edit Purchase Orders',
                'slug' => 'purchase-orders.edit',
                'module' => 'Purchase Order',
                'description' => 'Can edit purchase orders'
            ],
            [
                'name' => 'Delete Purchase Orders',
                'slug' => 'purchase-orders.delete',
                'module' => 'Purchase Order',
                'description' => 'Can delete purchase orders'
            ],
            [
                'name' => 'Submit Purchase Orders',
                'slug' => 'purchase-orders.submit',
                'module' => 'Purchase Order',
                'description' => 'Can submit purchase orders for approval'
            ],
            [
                'name' => 'Approve Purchase Orders',
                'slug' => 'purchase-orders.approve',
                'module' => 'Purchase Order',
                'description' => 'Can approve purchase orders'
            ],
            [
                'name' => 'Print Purchase Orders',
                'slug' => 'purchase-orders.print',
                'module' => 'Purchase Order',
                'description' => 'Can print purchase orders'
            ]
        ];

        foreach ($permissions as $permissionData) {
            Permission::updateOrInsert(
                ['slug' => $permissionData['slug']],
                [
                    'name' => $permissionData['name'],
                    'module' => $permissionData['module'],
                    'description' => $permissionData['description']
                ]
            );
        }

        // Assign permissions to roles
        $rolePermissions = [
            'super-admin' => [
                'purchase-orders.view',
                'purchase-orders.create',
                'purchase-orders.edit',
                'purchase-orders.delete',
                'purchase-orders.submit',
                'purchase-orders.approve',
                'purchase-orders.print'
            ],
            'admin' => [
                'purchase-orders.view',
                'purchase-orders.create',
                'purchase-orders.edit',
                'purchase-orders.submit',
                'purchase-orders.approve',
                'purchase-orders.print'
            ],
            'manager' => [
                'purchase-orders.view',
                'purchase-orders.create',
                'purchase-orders.edit',
                'purchase-orders.submit',
                'purchase-orders.approve',
                'purchase-orders.print'
            ],
            'staff' => [
                'purchase-orders.view',
                'purchase-orders.create',
                'purchase-orders.edit',
                'purchase-orders.submit',
                'purchase-orders.print'
            ],
            'viewer' => [
                'purchase-orders.view',
                'purchase-orders.print'
            ]
        ];

        foreach ($rolePermissions as $roleSlug => $permissionSlugs) {
            $role = Role::where('slug', $roleSlug)->first();
            
            if ($role) {
                foreach ($permissionSlugs as $permissionSlug) {
                    $permission = Permission::where('slug', $permissionSlug)->first();
                    
                    if ($permission && !$role->permissions()->where('permission_id', $permission->id)->exists()) {
                        $role->permissions()->attach($permission->id);
                        $this->command->info("Assigned permission '{$permission->slug}' to role '{$role->name}'");
                    }
                }
            }
        }

        $this->command->info('Purchase Order permissions created and assigned successfully!');
    }
}
