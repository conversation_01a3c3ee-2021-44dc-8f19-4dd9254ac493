<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApprovalHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'approval_workflow_id',
        'approval_level_id',
        'approvable_type',
        'approvable_id',
        'approver_id',
        'status',
        'notes',
        'approved_at',
        'timeout_at',
        'metadata',
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'timeout_at' => 'datetime',
        'metadata' => 'array',
    ];

    // Relationships
    public function workflow()
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'approval_workflow_id');
    }

    public function level()
    {
        return $this->belongsTo(ApprovalLevel::class, 'approval_level_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function approvable()
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeForApprover($query, $approverId)
    {
        return $query->where('approver_id', $approverId);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'skipped' => 'info',
            'timeout' => 'secondary',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Menunggu',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            'skipped' => 'Dilewati',
            'timeout' => 'Timeout',
        ];

        return $labels[$this->status] ?? 'Unknown';
    }

    // Methods
    public function isOverdue()
    {
        return $this->status === 'pending' &&
               $this->timeout_at &&
               now()->isAfter($this->timeout_at);
    }

    public function approve($approverId, $notes = null)
    {
        $this->update([
            'status' => 'approved',
            'approver_id' => $approverId,
            'notes' => $notes,
            'approved_at' => now(),
        ]);
    }

    public function reject($approverId, $notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'approver_id' => $approverId,
            'notes' => $notes,
            'approved_at' => now(),
        ]);
    }

    public function skip($reason = null)
    {
        $this->update([
            'status' => 'skipped',
            'notes' => $reason,
            'approved_at' => now(),
        ]);
    }
}
