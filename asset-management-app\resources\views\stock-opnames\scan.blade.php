@extends('layouts.contentNavbarLayout')

@section('title', 'Scan Asset - Stock Opname')

@section('vendor-script')
<!-- QR Code Scanner Library -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
@endsection

@section('vendor-style')
<style>
/* Camera Preview Container */
.camera-preview-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.camera-frame {
  position: relative;
  width: 100%;
  min-height: 300px;
  background: #000;
}

#qr-reader {
  width: 100% !important;
  height: auto !important;
  border: none !important;
  border-radius: 0 !important;
}

#qr-reader video {
  width: 100% !important;
  height: auto !important;
  object-fit: cover;
  border-radius: 0 !important;
}

#qr-reader canvas {
  display: none !important;
}

/* <PERSON>an Overlay */
.scan-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 250px;
  height: 250px;
  border: 2px solid rgba(40, 167, 69, 0.8);
  border-radius: 12px;
  background: rgba(40, 167, 69, 0.1);
  animation: scanPulse 2s ease-in-out infinite;
}

@keyframes scanPulse {
  0%, 100% {
    border-color: rgba(40, 167, 69, 0.8);
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  50% {
    border-color: rgba(40, 167, 69, 1);
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
}

/* Scan Corners */
.scan-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #28a745;
}

.scan-corner.top-left {
  top: -2px;
  left: -2px;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 8px;
}

.scan-corner.top-right {
  top: -2px;
  right: -2px;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 8px;
}

.scan-corner.bottom-left {
  bottom: -2px;
  left: -2px;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 8px;
}

.scan-corner.bottom-right {
  bottom: -2px;
  right: -2px;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 8px;
}

/* Scan Instruction */
.scan-instruction {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.scan-instruction i {
  color: #28a745;
}

.scan-instruction p {
  font-size: 14px;
  margin: 0;
}

/* Camera Controls */
.camera-controls {
  padding: 0 15px;
}

.camera-controls .btn {
  border-radius: 8px;
  font-weight: 500;
}

/* Camera Stats */
.camera-stat {
  padding: 10px;
  background: rgba(108, 117, 125, 0.1);
  border-radius: 8px;
  margin: 0 5px;
}

.camera-stat i {
  display: block;
  margin-bottom: 5px;
}

/* Camera Status Indicators */
.camera-status-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .camera-preview-container {
    max-width: 100%;
    margin: 0 -15px;
    border-radius: 0;
  }

  .scan-frame {
    width: 200px;
    height: 200px;
  }

  .camera-controls {
    padding: 0;
  }

  .camera-controls .btn {
    font-size: 14px;
    padding: 8px 12px;
  }
}

/* Loading Animation */
.camera-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 5;
}

.camera-loading .spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

/* Success Animation */
@keyframes scanSuccess {
  0% {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
  }
  50% {
    border-color: #20c997;
    background: rgba(32, 201, 151, 0.3);
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    transform: translate(-50%, -50%) scale(1);
  }
}

.scan-frame.success {
  animation: scanSuccess 0.6s ease-in-out;
}
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('stock-opnames.show', $stockOpname) }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-qr-scan-line me-2"></i>
                  Scan Asset - {{ $stockOpname->title }}
                </h5>
                <small class="text-muted">{{ $stockOpname->opname_number }}</small>
              </div>
            </div>
            <div>
              <span class="badge bg-warning">
                <i class="ri-play-line me-1"></i>
                Stock Opname Aktif
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Progress Card -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h6 class="mb-2">Progress Scanning</h6>
              <div class="progress mb-2" style="height: 10px;">
                <div class="progress-bar" role="progressbar" id="progressBar"
                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <small class="text-muted">
                <span id="scannedCount">0</span> dari <span id="totalCount">{{ $stockOpname->total_assets }}</span> asset telah di-scan
              </small>
            </div>
            <div class="col-md-4 text-md-end">
              <div class="d-flex justify-content-md-end gap-2">
                <div class="text-center">
                  <div class="badge bg-success fs-6" id="foundCount">0</div>
                  <small class="d-block text-muted">Ditemukan</small>
                </div>
                <div class="text-center">
                  <div class="badge bg-danger fs-6" id="missingCount">0</div>
                  <small class="d-block text-muted">Hilang</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scan Interface -->
  <div class="row">
    <!-- Left Column - Scanner -->
    <div class="col-lg-6">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-qr-code-line me-2"></i>
            QR Code Scanner
          </h6>
        </div>
        <div class="card-body">
          <!-- Manual Input -->
          <div class="mb-4">
            <label for="assetCodeInput" class="form-label">Scan QR Code atau Input Manual</label>
            <div class="input-group">
              <input type="text" class="form-control" id="assetCodeInput" 
                     placeholder="Scan QR Code atau ketik kode asset..." autofocus>
              <button class="btn btn-primary" type="button" id="searchAssetBtn">
                <i class="ri-search-line"></i>
              </button>
            </div>
            <small class="text-muted">Arahkan kamera ke QR Code asset atau ketik kode asset secara manual</small>
          </div>

          <!-- Camera Scanner -->
          <div class="text-center">
            <button type="button" class="btn btn-outline-info" id="toggleCameraBtn">
              <i class="ri-camera-line me-2"></i>
              Aktifkan Kamera Scanner
            </button>
          </div>

          <!-- Camera Preview (Hidden by default) -->
          <div id="cameraContainer" class="mt-3" style="display: none;">
            <!-- Camera Status -->
            <div class="alert alert-success d-flex align-items-center mb-3" role="alert">
              <i class="ri-camera-line ri-24px me-3"></i>
              <div>
                <h6 class="alert-heading mb-1">Kamera Aktif</h6>
                <small class="mb-0">Arahkan kamera ke QR Code asset untuk scan otomatis</small>
              </div>
              <div class="ms-auto">
                <span class="badge bg-success">
                  <i class="ri-record-circle-line me-1"></i>
                  LIVE
                </span>
              </div>
            </div>

            <!-- Camera Preview Container -->
            <div class="camera-preview-container">
              <div class="camera-frame">
                <div id="qr-reader"></div>

                <!-- Scan Overlay -->
                <div class="scan-overlay">
                  <div class="scan-frame">
                    <div class="scan-corner top-left"></div>
                    <div class="scan-corner top-right"></div>
                    <div class="scan-corner bottom-left"></div>
                    <div class="scan-corner bottom-right"></div>
                  </div>
                  <div class="scan-instruction">
                    <i class="ri-qr-code-line ri-24px mb-2"></i>
                    <p class="mb-0">Posisikan QR Code di dalam frame</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Camera Controls -->
            <div class="camera-controls mt-3">
              <div class="row">
                <div class="col-6">
                  <button type="button" class="btn btn-outline-secondary w-100" id="stopCameraBtn">
                    <i class="ri-camera-off-line me-2"></i>
                    Matikan Kamera
                  </button>
                </div>
                <div class="col-6">
                  <button type="button" class="btn btn-outline-info w-100" id="switchCameraBtn">
                    <i class="ri-camera-switch-line me-2"></i>
                    Ganti Kamera
                  </button>
                </div>
              </div>
            </div>

            <!-- Scan Status -->
            <div id="scanStatus" class="mt-3" style="display: none;">
              <div class="alert alert-warning d-flex align-items-center" role="alert">
                <div class="spinner-border spinner-border-sm me-3" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <div>
                  <strong>Memproses QR Code...</strong>
                  <br><small>Sedang mengambil data asset</small>
                </div>
              </div>
            </div>

            <!-- Camera Info -->
            <div class="camera-info mt-3">
              <div class="row text-center">
                <div class="col-4">
                  <div class="camera-stat">
                    <i class="ri-focus-3-line ri-20px text-primary"></i>
                    <small class="d-block text-muted">Auto Focus</small>
                  </div>
                </div>
                <div class="col-4">
                  <div class="camera-stat">
                    <i class="ri-flashlight-line ri-20px text-warning"></i>
                    <small class="d-block text-muted">Auto Flash</small>
                  </div>
                </div>
                <div class="col-4">
                  <div class="camera-stat">
                    <i class="ri-scan-line ri-20px text-success"></i>
                    <small class="d-block text-muted">Scan Ready</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Asset Info -->
    <div class="col-lg-6">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Asset
          </h6>
        </div>
        <div class="card-body">
          <div id="assetInfoContainer">
            <div class="text-center py-5">
              <i class="ri-qr-scan-line ri-48px text-muted mb-3"></i>
              <h6 class="text-muted">Scan QR Code Asset</h6>
              <p class="text-muted mb-0">Informasi asset akan muncul di sini setelah QR Code di-scan</p>
            </div>
          </div>

          <!-- Asset Form (Hidden by default) -->
          <div id="assetFormContainer" style="display: none;">
            <form id="scanForm">
              <input type="hidden" id="assetId" name="asset_id">
              
              <div class="mb-3">
                <label for="foundStatus" class="form-label">Status Ditemukan <span class="text-danger">*</span></label>
                <select class="form-select" id="foundStatus" name="found_status" required>
                  <option value="">Pilih Status</option>
                  <option value="found">Ditemukan</option>
                  <option value="not_found">Tidak Ditemukan</option>
                  <option value="damaged">Rusak</option>
                </select>
              </div>

              <div class="mb-3">
                <label for="physicalCondition" class="form-label">Kondisi Fisik <span class="text-danger">*</span></label>
                <select class="form-select" id="physicalCondition" name="physical_condition" required>
                  <option value="">Pilih Kondisi</option>
                  <option value="excellent">Sangat Baik</option>
                  <option value="good">Baik</option>
                  <option value="fair">Cukup</option>
                  <option value="poor">Buruk</option>
                  <option value="damaged">Rusak</option>
                </select>
              </div>

              <div class="mb-3">
                <label for="locationFound" class="form-label">Lokasi Ditemukan</label>
                <input type="text" class="form-control" id="locationFound" name="location_found" 
                       placeholder="Lokasi dimana asset ditemukan">
              </div>

              <div class="mb-3">
                <label for="conditionNotes" class="form-label">Catatan Kondisi</label>
                <textarea class="form-control" id="conditionNotes" name="condition_notes" rows="3"
                          placeholder="Catatan tambahan tentang kondisi asset..."></textarea>
              </div>

              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-success flex-fill">
                  <i class="ri-save-line me-2"></i>
                  Simpan Hasil Scan
                </button>
                <button type="button" class="btn btn-outline-secondary" id="resetFormBtn">
                  <i class="ri-refresh-line me-2"></i>
                  Reset
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Scans -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-history-line me-2"></i>
            Scan Terakhir
          </h6>
        </div>
        <div class="card-body">
          <div id="recentScansContainer">
            <div class="text-center py-3">
              <small class="text-muted">Belum ada asset yang di-scan</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const assetCodeInput = document.getElementById('assetCodeInput');
  const searchAssetBtn = document.getElementById('searchAssetBtn');
  const assetInfoContainer = document.getElementById('assetInfoContainer');
  const assetFormContainer = document.getElementById('assetFormContainer');
  const scanForm = document.getElementById('scanForm');
  const resetFormBtn = document.getElementById('resetFormBtn');
  const recentScansContainer = document.getElementById('recentScansContainer');
  const toggleCameraBtn = document.getElementById('toggleCameraBtn');
  const stopCameraBtn = document.getElementById('stopCameraBtn');
  const switchCameraBtn = document.getElementById('switchCameraBtn');
  const cameraContainer = document.getElementById('cameraContainer');
  const scanStatus = document.getElementById('scanStatus');

  // QR Scanner variables
  let html5QrCode = null;
  let isCameraActive = false;
  let availableCameras = [];
  let currentCameraIndex = 0;

  // Initialize statistics
  updateStatistics();

  // Camera functions
  function startCamera(cameraIndex = 0) {
    if (isCameraActive) return;

    // Check if browser supports camera
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      showToast('Browser Anda tidak mendukung akses kamera. Gunakan input manual.', 'warning');
      return;
    }

    // Show loading state
    toggleCameraBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Mengaktifkan Kamera...';
    toggleCameraBtn.disabled = true;

    html5QrCode = new Html5Qrcode("qr-reader");

    Html5Qrcode.getCameras().then(devices => {
      if (devices && devices.length) {
        availableCameras = devices;
        currentCameraIndex = cameraIndex;

        // Use specified camera index or find back camera
        let selectedCamera;
        if (cameraIndex < devices.length) {
          selectedCamera = devices[cameraIndex];
        } else {
          selectedCamera = devices.find(device =>
            device.label.toLowerCase().includes('back') ||
            device.label.toLowerCase().includes('rear')
          ) || devices[0];
          currentCameraIndex = devices.indexOf(selectedCamera);
        }

        const cameraId = selectedCamera.id;

        const config = {
          fps: 10,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0
        };

        html5QrCode.start(
          cameraId,
          config,
          async (decodedText, decodedResult) => {
            // QR Code detected - show processing state
            console.log('QR Code detected:', decodedText);
            showScanProcessing();

            // Add success animation to scan frame
            const scanFrame = document.querySelector('.scan-frame');
            if (scanFrame) {
              scanFrame.classList.add('success');
              setTimeout(() => scanFrame.classList.remove('success'), 600);
            }

            // Extract asset code from QR URL
            const assetCode = await extractAssetCodeFromQR(decodedText);
            if (assetCode) {
              assetCodeInput.value = assetCode;
              hideScanProcessing();
              searchAsset();
              stopCamera(); // Stop camera after successful scan
              showToast('QR Code berhasil di-scan!', 'success');
            } else {
              hideScanProcessing();
              showToast('QR Code tidak valid atau bukan QR Code asset', 'warning');
            }
          },
          (errorMessage) => {
            // QR Code scan error (can be ignored for continuous scanning)
            console.log('QR scan error:', errorMessage);
          }
        ).then(() => {
          isCameraActive = true;
          cameraContainer.style.display = 'block';
          toggleCameraBtn.innerHTML = '<i class="ri-camera-off-line me-2"></i>Matikan Kamera';
          toggleCameraBtn.className = 'btn btn-outline-danger';
          toggleCameraBtn.disabled = false;

          // Update switch camera button
          if (availableCameras.length > 1) {
            switchCameraBtn.style.display = 'block';
            switchCameraBtn.innerHTML = `<i class="ri-camera-switch-line me-2"></i>Kamera ${currentCameraIndex + 1}/${availableCameras.length}`;
          } else {
            switchCameraBtn.style.display = 'none';
          }

          showToast('Kamera berhasil diaktifkan. Arahkan ke QR Code asset.', 'success');
        }).catch(err => {
          console.error('Error starting camera:', err);
          toggleCameraBtn.innerHTML = '<i class="ri-camera-line me-2"></i>Aktifkan Kamera Scanner';
          toggleCameraBtn.className = 'btn btn-outline-info';
          toggleCameraBtn.disabled = false;

          let errorMessage = 'Gagal mengaktifkan kamera';
          if (err.toString().includes('NotAllowedError')) {
            errorMessage = 'Akses kamera ditolak. Silakan izinkan akses kamera di browser.';
          } else if (err.toString().includes('NotFoundError')) {
            errorMessage = 'Kamera tidak ditemukan. Pastikan perangkat memiliki kamera.';
          } else if (err.toString().includes('NotReadableError')) {
            errorMessage = 'Kamera sedang digunakan aplikasi lain.';
          }

          showToast(errorMessage, 'error');
        });
      } else {
        showToast('Tidak ada kamera yang tersedia', 'error');
      }
    }).catch(err => {
      console.error('Error getting cameras:', err);
      showToast('Gagal mengakses kamera: ' + err, 'error');
    });
  }

  function stopCamera() {
    if (html5QrCode && isCameraActive) {
      html5QrCode.stop().then(() => {
        isCameraActive = false;
        cameraContainer.style.display = 'none';
        toggleCameraBtn.innerHTML = '<i class="ri-camera-line me-2"></i>Aktifkan Kamera Scanner';
        toggleCameraBtn.className = 'btn btn-outline-info';
        html5QrCode = null;
      }).catch(err => {
        console.error('Error stopping camera:', err);
      });
    }
  }

  async function extractAssetCodeFromQR(qrText) {
    try {
      // Check if it's a URL (like our QR codes)
      if (qrText.includes('/assets/') && qrText.includes('/qr')) {
        // Extract asset ID from URL
        const matches = qrText.match(/\/assets\/(\d+)\/qr/);
        if (matches && matches[1]) {
          const assetId = matches[1];

          // Fetch asset code from server using asset ID
          try {
            const response = await fetch(`/api/assets/${assetId}/code`);
            if (response.ok) {
              const data = await response.json();
              return data.asset_code;
            }
          } catch (fetchError) {
            console.error('Error fetching asset code:', fetchError);
            // Fallback: use asset ID as code for now
            return `ASSET-${assetId}`;
          }
        }
      }

      // If it's already an asset code (direct text)
      if (qrText.match(/^[A-Z0-9-]+$/)) {
        return qrText;
      }

      // Try to extract any alphanumeric code
      const codeMatch = qrText.match(/([A-Z0-9-]{3,})/i);
      if (codeMatch) {
        return codeMatch[1].toUpperCase();
      }

      return null;
    } catch (error) {
      console.error('Error extracting asset code:', error);
      return null;
    }
  }

  function showToast(message, type = 'info') {
    const toastClass = type === 'error' ? 'bg-danger' :
                      type === 'warning' ? 'bg-warning' :
                      type === 'success' ? 'bg-success' : 'bg-info';
    const iconClass = type === 'error' ? 'ri-error-warning-line' :
                     type === 'warning' ? 'ri-alert-line' :
                     type === 'success' ? 'ri-check-line' : 'ri-information-line';

    const toast = document.createElement('div');
    toast.className = `bs-toast toast toast-placement-ex m-2 fade ${toastClass} show top-0 end-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
      <div class="toast-header">
        <i class="${iconClass} me-2"></i>
        <div class="me-auto fw-semibold">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body">${message}</div>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 5000);
  }

  function showScanProcessing() {
    scanStatus.style.display = 'block';
  }

  function hideScanProcessing() {
    scanStatus.style.display = 'none';
  }

  function switchCamera() {
    if (availableCameras.length <= 1) return;

    stopCamera();
    currentCameraIndex = (currentCameraIndex + 1) % availableCameras.length;
    setTimeout(() => {
      startCamera(currentCameraIndex);
    }, 500);
  }

  // Camera event listeners
  toggleCameraBtn.addEventListener('click', function() {
    if (isCameraActive) {
      stopCamera();
    } else {
      startCamera();
    }
  });

  stopCameraBtn.addEventListener('click', function() {
    stopCamera();
  });

  switchCameraBtn.addEventListener('click', function() {
    switchCamera();
  });

  // Search asset function
  function searchAsset() {
    const assetCode = assetCodeInput.value.trim();
    if (!assetCode) {
      alert('Masukkan kode asset atau scan QR Code');
      return;
    }

    // Show loading
    assetInfoContainer.innerHTML = `
      <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Mencari asset...</p>
      </div>
    `;

    // Find asset by code
    fetch(`{{ route('stock-opnames.asset-info', $stockOpname) }}?asset_code=${encodeURIComponent(assetCode)}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showAssetInfo(data.asset, data.detail, data.already_scanned);
        } else {
          showError(data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showError('Terjadi kesalahan saat mencari asset');
      });
  }

  // Show asset information
  function showAssetInfo(asset, detail, alreadyScanned) {
    assetInfoContainer.innerHTML = `
      <div class="border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-start mb-2">
          <div>
            <h6 class="mb-1">${asset.name}</h6>
            <span class="badge bg-primary">${asset.asset_code}</span>
          </div>
          ${alreadyScanned ? '<span class="badge bg-warning">Sudah di-scan</span>' : '<span class="badge bg-success">Siap di-scan</span>'}
        </div>
        <div class="row">
          <div class="col-6">
            <small class="text-muted">Kategori:</small>
            <div>${asset.asset_category?.name || '-'}</div>
          </div>
          <div class="col-6">
            <small class="text-muted">Status:</small>
            <div><span class="badge bg-info">${asset.status}</span></div>
          </div>
        </div>
      </div>
    `;

    if (alreadyScanned) {
      assetFormContainer.style.display = 'none';
      assetInfoContainer.innerHTML += `
        <div class="alert alert-warning">
          <i class="ri-error-warning-line me-2"></i>
          Asset ini sudah pernah di-scan sebelumnya pada ${detail.scanned_at}
        </div>
      `;
    } else {
      // Show form
      document.getElementById('assetId').value = asset.id;
      assetFormContainer.style.display = 'block';
    }
  }

  // Show error
  function showError(message) {
    assetInfoContainer.innerHTML = `
      <div class="alert alert-danger">
        <i class="ri-error-warning-line me-2"></i>
        ${message}
      </div>
    `;
    assetFormContainer.style.display = 'none';
  }

  // Event listeners
  searchAssetBtn.addEventListener('click', searchAsset);
  
  assetCodeInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
      searchAsset();
    }
  });

  resetFormBtn.addEventListener('click', function() {
    scanForm.reset();
    assetFormContainer.style.display = 'none';
    assetInfoContainer.innerHTML = `
      <div class="text-center py-5">
        <i class="ri-qr-scan-line ri-48px text-muted mb-3"></i>
        <h6 class="text-muted">Scan QR Code Asset</h6>
        <p class="text-muted mb-0">Informasi asset akan muncul di sini setelah QR Code di-scan</p>
      </div>
    `;
    assetCodeInput.value = '';
    assetCodeInput.focus();
  });

  // Handle form submission
  scanForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    // Debug: Log form data
    console.log('Form data being sent:');
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }

    // Ensure all required fields are present
    if (!formData.get('asset_id')) {
      alert('Asset ID tidak ditemukan. Silakan scan ulang asset.');
      return;
    }

    if (!formData.get('found_status')) {
      alert('Status ditemukan harus dipilih.');
      return;
    }

    if (!formData.get('physical_condition')) {
      alert('Kondisi fisik harus dipilih.');
      return;
    }

    fetch(`{{ route('stock-opnames.process-scan', $stockOpname) }}`, {
      method: 'POST',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'Accept': 'application/json'
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success message
        showSuccessToast(data.message);

        // Update statistics
        updateStatistics(data.statistics);

        // Add to recent scans
        addToRecentScans(data.detail);

        // Reset form
        resetFormBtn.click();
      } else {
        alert(data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Terjadi kesalahan saat menyimpan data');
    });
  });

  // Update statistics
  function updateStatistics(stats = null) {
    if (stats) {
      document.getElementById('scannedCount').textContent = stats.scanned;
      document.getElementById('progressBar').style.width = stats.progress + '%';
      document.getElementById('progressBar').setAttribute('aria-valuenow', stats.progress);
    }
  }

  // Add to recent scans
  function addToRecentScans(detail) {
    const recentItem = `
      <div class="d-flex justify-content-between align-items-center border-bottom py-2">
        <div>
          <strong>${detail.asset.name}</strong>
          <br><small class="text-muted">${detail.asset_code}</small>
        </div>
        <div class="text-end">
          <span class="badge ${getStatusBadgeClass(detail.found_status)}">${detail.found_status}</span>
          <br><small class="text-muted">${new Date().toLocaleTimeString()}</small>
        </div>
      </div>
    `;
    
    if (recentScansContainer.innerHTML.includes('Belum ada asset')) {
      recentScansContainer.innerHTML = recentItem;
    } else {
      recentScansContainer.insertAdjacentHTML('afterbegin', recentItem);
    }
  }

  // Get status badge class
  function getStatusBadgeClass(status) {
    switch(status) {
      case 'found': return 'bg-success';
      case 'not_found': return 'bg-danger';
      case 'damaged': return 'bg-warning';
      default: return 'bg-secondary';
    }
  }

  // Show success toast
  function showSuccessToast(message) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
      <div class="toast-header">
        <i class="ri-check-line me-2"></i>
        <div class="me-auto fw-semibold">Success</div>
        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body">${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      toast.remove();
    }, 3000);
  }
});
</script>

@endsection
