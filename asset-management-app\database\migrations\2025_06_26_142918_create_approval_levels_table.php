<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_levels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_workflow_id')->constrained()->onDelete('cascade'); // Workflow yang memiliki level ini
            $table->integer('level_order'); // Urutan level (1, 2, 3, dst)
            $table->string('level_name'); // Nama level (Supervisor, Manager, Director, dst)
            $table->text('description')->nullable(); // Deskripsi level
            $table->enum('approver_type', ['role', 'user', 'position']); // Tipe approver
            $table->json('approver_config'); // Konfigurasi approver (role_id, user_id, atau position)
            $table->boolean('is_required')->default(true); // Apakah level ini wajib
            $table->boolean('can_skip')->default(false); // Apakah level ini bisa di-skip
            $table->integer('timeout_hours')->nullable(); // Timeout dalam jam (auto approve jika tidak ada respon)
            $table->enum('timeout_action', ['approve', 'reject', 'escalate'])->default('escalate'); // Aksi jika timeout
            $table->boolean('is_active')->default(true); // Status aktif
            $table->timestamps();

            $table->unique(['approval_workflow_id', 'level_order']);
            $table->index(['approval_workflow_id', 'level_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_levels');
    }
};
