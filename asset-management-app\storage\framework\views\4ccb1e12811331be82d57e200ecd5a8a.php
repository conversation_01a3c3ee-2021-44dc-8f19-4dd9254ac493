<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Purchase Order - <?php echo e($purchaseOrder->po_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .document-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 15px;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        
        .info-label {
            width: 150px;
            font-weight: bold;
        }
        
        .info-value {
            flex: 1;
        }
        
        .two-column {
            display: flex;
            gap: 40px;
            margin-bottom: 20px;
        }
        
        .column {
            flex: 1;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .totals-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 5px 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .totals-table .total-row {
            font-weight: bold;
            border-top: 2px solid #333;
            border-bottom: 2px solid #333;
        }
        
        .terbilang {
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #333;
            background-color: #f9f9f9;
        }
        
        .signatures {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            height: 60px;
            margin-bottom: 5px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        
        .status-draft { background-color: #6c757d; }
        .status-submitted { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #198754; }
        .status-rejected { background-color: #dc3545; }
        .status-sent_to_supplier { background-color: #0d6efd; }
        .status-partially_received { background-color: #fd7e14; }
        .status-completed { background-color: #20c997; }
        .status-cancelled { background-color: #6f42c1; }
        
        @media print {
            body { margin: 0; padding: 15px; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name"><?php echo e(config('app.name', 'Asset Management System')); ?></div>
        <div><?php echo e($purchaseOrder->branch->address ?? 'Alamat Perusahaan'); ?></div>
        <div class="document-title">PURCHASE ORDER</div>
        <div>
            <span class="status-badge status-<?php echo e(str_replace(' ', '_', strtolower($purchaseOrder->status))); ?>">
                <?php echo e($purchaseOrder->status_label); ?>

            </span>
        </div>
    </div>

    <!-- PO Information -->
    <div class="two-column">
        <div class="column">
            <div class="info-section">
                <h4>Informasi Purchase Order</h4>
                <div class="info-row">
                    <div class="info-label">Nomor PO:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->po_number); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Tanggal PO:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->po_date->format('d/m/Y')); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Tanggal Pengiriman:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->delivery_date ? $purchaseOrder->delivery_date->format('d/m/Y') : '-'); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Cabang:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->branch->name); ?></div>
                </div>
            </div>
        </div>
        
        <div class="column">
            <div class="info-section">
                <h4>Informasi Supplier</h4>
                <div class="info-row">
                    <div class="info-label">Nama:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->supplier->name); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Kode:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->supplier->supplier_code); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Telepon:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->supplier->phone ?? '-'); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Email:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->supplier->email ?? '-'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="25%">Nama Item</th>
                <th width="10%">Kode</th>
                <th width="15%">Merk/Model</th>
                <th width="15%">Spesifikasi</th>
                <th width="8%">Qty</th>
                <th width="7%">Satuan</th>
                <th width="15%">Harga Satuan</th>
                <th width="15%">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $purchaseOrder->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td class="text-center"><?php echo e($index + 1); ?></td>
                    <td>
                        <strong><?php echo e($item->item_name); ?></strong>
                        <?php if($item->item_description): ?>
                            <br><small><?php echo e($item->item_description); ?></small>
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($item->item_code ?: '-'); ?></td>
                    <td>
                        <?php if($item->brand || $item->model): ?>
                            <?php echo e($item->brand); ?><?php echo e($item->brand && $item->model ? ' / ' : ''); ?><?php echo e($item->model); ?>

                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($item->specification ?: '-'); ?></td>
                    <td class="text-center"><?php echo e(number_format($item->quantity, 0, ',', '.')); ?></td>
                    <td class="text-center"><?php echo e($item->unit); ?></td>
                    <td class="text-right">Rp <?php echo e(number_format($item->unit_price, 0, ',', '.')); ?></td>
                    <td class="text-right">Rp <?php echo e(number_format($item->total_price, 0, ',', '.')); ?></td>
                </tr>
                <?php if($item->notes): ?>
                    <tr>
                        <td></td>
                        <td colspan="8"><em>Catatan: <?php echo e($item->notes); ?></em></td>
                    </tr>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <!-- Totals -->
    <table class="totals-table">
        <tr>
            <td><strong>Subtotal:</strong></td>
            <td class="text-right"><strong>Rp <?php echo e(number_format($purchaseOrder->subtotal_amount, 0, ',', '.')); ?></strong></td>
        </tr>
        <?php if($purchaseOrder->discount_amount > 0): ?>
            <tr>
                <td>Diskon (<?php echo e($purchaseOrder->discount_percentage); ?>%):</td>
                <td class="text-right">Rp <?php echo e(number_format($purchaseOrder->discount_amount, 0, ',', '.')); ?></td>
            </tr>
        <?php endif; ?>
        <?php if($purchaseOrder->tax_amount > 0): ?>
            <tr>
                <td>Pajak (<?php echo e($purchaseOrder->tax_percentage); ?>%):</td>
                <td class="text-right">Rp <?php echo e(number_format($purchaseOrder->tax_amount, 0, ',', '.')); ?></td>
            </tr>
        <?php endif; ?>
        <tr class="total-row">
            <td><strong>TOTAL:</strong></td>
            <td class="text-right"><strong>Rp <?php echo e(number_format($purchaseOrder->total_amount, 0, ',', '.')); ?></strong></td>
        </tr>
    </table>

    <!-- Terbilang -->
    <div class="terbilang">
        <strong>Terbilang:</strong> <?php echo e(terbilang_rupiah($purchaseOrder->total_amount)); ?>

    </div>

    <!-- Additional Information -->
    <?php if($purchaseOrder->payment_terms || $purchaseOrder->delivery_address || $purchaseOrder->terms_conditions || $purchaseOrder->notes): ?>
        <div style="margin-top: 20px;">
            <?php if($purchaseOrder->payment_terms): ?>
                <div class="info-row">
                    <div class="info-label">Syarat Pembayaran:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->payment_terms); ?></div>
                </div>
            <?php endif; ?>
            <?php if($purchaseOrder->delivery_address): ?>
                <div class="info-row">
                    <div class="info-label">Alamat Pengiriman:</div>
                    <div class="info-value"><?php echo e($purchaseOrder->delivery_address); ?></div>
                </div>
            <?php endif; ?>
            <?php if($purchaseOrder->terms_conditions): ?>
                <div style="margin-top: 15px;">
                    <strong>Syarat & Ketentuan:</strong><br>
                    <?php echo e($purchaseOrder->terms_conditions); ?>

                </div>
            <?php endif; ?>
            <?php if($purchaseOrder->notes): ?>
                <div style="margin-top: 15px;">
                    <strong>Catatan:</strong><br>
                    <?php echo e($purchaseOrder->notes); ?>

                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Signatures -->
    <div class="signatures">
        <div class="signature-box">
            <div>Dibuat Oleh:</div>
            <div class="signature-line"></div>
            <div><?php echo e($purchaseOrder->createdBy->name); ?></div>
            <div><?php echo e($purchaseOrder->created_at->format('d/m/Y')); ?></div>
        </div>
        
        <?php if($purchaseOrder->approved_by): ?>
            <div class="signature-box">
                <div>Disetujui Oleh:</div>
                <div class="signature-line"></div>
                <div><?php echo e($purchaseOrder->approvedBy->name); ?></div>
                <div><?php echo e($purchaseOrder->approved_at->format('d/m/Y')); ?></div>
            </div>
        <?php else: ?>
            <div class="signature-box">
                <div>Disetujui Oleh:</div>
                <div class="signature-line"></div>
                <div>(...........................)</div>
                <div>Tanggal: ................</div>
            </div>
        <?php endif; ?>
        
        <div class="signature-box">
            <div>Supplier:</div>
            <div class="signature-line"></div>
            <div>(...........................)</div>
            <div>Tanggal: ................</div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/purchase-orders/print.blade.php ENDPATH**/ ?>