@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Konfigurasi Nomor Dokumen - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Nomor Dokumen /</span> Detail Konfigurasi
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Detail Konfigurasi Nomor Dokumen</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.document-numbers.edit', $documentNumber) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.document-numbers.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tipe Dokumen</label>
                <p class="fw-bold">{{ ucfirst($documentNumber->document_type) }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Prefix</label>
                <p><span class="badge bg-info fs-6">{{ $documentNumber->prefix }}</span></p>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label text-muted">Format Nomor</label>
            <p><code class="fs-6">{{ $documentNumber->format }}</code></p>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label text-muted">Cabang</label>
                <p class="fw-bold">
                  {{ $documentNumber->branch ? $documentNumber->branch->name : 'Global (Semua Cabang)' }}
                  @if($documentNumber->branch)
                    <br><small class="text-muted">{{ $documentNumber->branch->code }}</small>
                  @endif
                </p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label text-muted">Tahun</label>
                <p class="fw-bold">{{ $documentNumber->year }}</p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label text-muted">Nomor Terakhir</label>
                <p><span class="badge bg-secondary fs-6">{{ str_pad($documentNumber->current_number, 4, '0', STR_PAD_LEFT) }}</span></p>
              </div>
            </div>
          </div>

          @if($documentNumber->description)
          <div class="mb-3">
            <label class="form-label text-muted">Deskripsi</label>
            <p>{{ $documentNumber->description }}</p>
          </div>
          @endif

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $documentNumber->is_active ? 'success' : 'secondary' }} fs-6">
                    {{ $documentNumber->is_active ? 'Aktif' : 'Non-Aktif' }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Dibuat</label>
                <p>{{ $documentNumber->created_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Preview Nomor Dokumen</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-primary">
            <h6 class="alert-heading">Contoh Nomor Berikutnya:</h6>
            @php
              $nextNumber = $documentNumber->current_number + 1;
              $branchCode = $documentNumber->branch ? $documentNumber->branch->code : 'HQ';
              $previewNumber = str_replace([
                '{prefix}',
                '{branch}',
                '{year}',
                '{number}'
              ], [
                $documentNumber->prefix,
                $branchCode,
                $documentNumber->year,
                str_pad($nextNumber, 4, '0', STR_PAD_LEFT)
              ], $documentNumber->format);
            @endphp
            <p class="mb-0 text-center">
              <span class="badge bg-primary fs-5">{{ $previewNumber }}</span>
            </p>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Informasi:</h6>
            <ul class="mb-0 small">
              <li>Nomor akan otomatis increment setiap kali digunakan</li>
              <li>Format dapat disesuaikan sesuai kebutuhan</li>
              <li>Setiap kombinasi tipe, cabang, dan tahun harus unik</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.document-numbers.edit', $documentNumber) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Konfigurasi
            </a>
            
            @if($documentNumber->is_active)
              <button class="btn btn-warning" onclick="toggleStatus(false)">
                <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
              </button>
            @else
              <button class="btn btn-success" onclick="toggleStatus(true)">
                <i class="ri-play-circle-line me-1"></i>Aktifkan
              </button>
            @endif
            
            <form action="{{ route('master.document-numbers.destroy', $documentNumber) }}" method="POST" id="deleteForm">
              @csrf
              @method('DELETE')
              <button type="button" class="btn btn-danger w-100" onclick="confirmDelete()">
                <i class="ri-delete-bin-line me-1"></i>Hapus Konfigurasi
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStatus(status) {
  if (confirm('Yakin ingin mengubah status konfigurasi ini?')) {
    // Create form to toggle status
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("master.document-numbers.update", $documentNumber) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'is_active';
    statusField.value = status ? '1' : '0';
    
    // Copy other fields
    const fields = ['document_type', 'prefix', 'format', 'current_number', 'year', 'branch_id', 'description'];
    fields.forEach(field => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = field;
      input.value = documentNumberData[field];
      form.appendChild(input);
    });
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    form.appendChild(statusField);
    
    document.body.appendChild(form);
    form.submit();
  }
}

function confirmDelete() {
  if (confirm('Yakin ingin menghapus konfigurasi nomor dokumen ini?\n\nTindakan ini tidak dapat dibatalkan!')) {
    document.getElementById('deleteForm').submit();
  }
}
</script>
@endsection
