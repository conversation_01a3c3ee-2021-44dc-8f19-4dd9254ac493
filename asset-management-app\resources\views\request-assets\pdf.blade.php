<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Permintaan Asset - {{ $requestAsset->request_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
            background: #fff;
        }

        .container {
            width: 100%;
            max-width: 190mm;
            margin: 0 auto;
            padding: 8mm;
        }
        
        /* Header */
        .header {
            display: table;
            width: 100%;
            margin-bottom: 12px;
            border-bottom: 1px solid #2c5aa0;
            padding-bottom: 8px;
        }
        
        .header-left {
            display: table-cell;
            width: 65%;
            vertical-align: top;
        }

        .header-right {
            display: table-cell;
            width: 35%;
            vertical-align: top;
            text-align: right;
        }
        
        .company-name {
            font-size: 15px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 4px;
        }

        .company-address {
            font-size: 9px;
            color: #666;
            line-height: 1.3;
        }

        .document-number {
            font-size: 13px;
            font-weight: bold;
            color: #2c5aa0;
            background: #f8f9fa;
            padding: 6px 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        
        /* Title */
        .title {
            text-align: center;
            margin: 10px 0;
            padding: 6px;
            background: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
            color: white;
            border-radius: 4px;
        }

        .title h1 {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .title p {
            font-size: 10px;
            opacity: 0.9;
        }
        
        /* Info Section */
        .info-section {
            margin-bottom: 10px;
        }
        
        .info-grid {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        
        .info-row {
            display: table-row;
        }
        
        .info-cell {
            display: table-cell;
            padding: 4px 6px;
            border: 1px solid #dee2e6;
            vertical-align: top;
            font-size: 10px;
        }
        
        .info-label {
            background: #f8f9fa;
            font-weight: bold;
            width: 22%;
            color: #495057;
        }

        .info-value {
            width: 28%;
        }
        
        /* Purpose Section */
        .purpose-section {
            margin: 8px 0;
            padding: 6px;
            background: #f8f9fa;
            border-left: 3px solid #2c5aa0;
            border-radius: 0 3px 3px 0;
        }

        .purpose-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
            font-size: 11px;
        }

        .purpose-text {
            text-align: justify;
            line-height: 1.4;
            font-size: 10px;
        }
        
        /* Items Table */
        .items-section {
            margin: 8px 0;
        }

        .section-title {
            font-size: 12px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 6px;
            padding-bottom: 4px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .items-table th {
            background: #2c5aa0;
            color: white;
            padding: 6px 4px;
            text-align: left;
            font-weight: bold;
            font-size: 10px;
        }

        .items-table td {
            padding: 4px;
            border: 1px solid #dee2e6;
            vertical-align: top;
            font-size: 9px;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .item-number {
            text-align: center;
            font-weight: bold;
            width: 8%;
        }
        
        .item-name {
            width: 45%;
            font-weight: bold;
        }
        
        .item-qty {
            text-align: center;
            width: 12%;
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .item-desc {
            width: 35%;
            font-size: 9px;
        }

        /* Notes Section */
        .notes-section {
            margin: 6px 0;
            padding: 5px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 3px;
        }

        .notes-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 4px;
            font-size: 10px;
        }
        
        /* Disclaimer */
        .disclaimer {
            margin: 6px 0;
            padding: 6px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 3px;
            text-align: center;
        }

        .disclaimer-title {
            font-weight: bold;
            color: #721c24;
            margin-bottom: 4px;
            font-size: 10px;
        }

        .disclaimer-text {
            color: #721c24;
            font-size: 9px;
            line-height: 1.3;
        }
        
        /* Signatures */
        .signatures {
            margin-top: 15px;
            display: table;
            width: 100%;
        }
        
        .signature-row {
            display: table-row;
        }
        
        .signature-cell {
            display: table-cell;
            width: 25%;
            text-align: center;
            vertical-align: top;
            padding: 0 5px;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            height: 60px;
            margin-bottom: 6px;
            background: #fafafa;
            position: relative;
        }

        .signature-label {
            font-weight: bold;
            font-size: 10px;
            color: #495057;
        }

        .signature-name {
            font-size: 9px;
            color: #6c757d;
            margin-top: 3px;
        }

        .signature-date {
            position: absolute;
            bottom: 3px;
            left: 4px;
            font-size: 8px;
            color: #6c757d;
        }
        
        /* Footer */
        .footer {
            margin-top: 12px;
            text-align: center;
            font-size: 8px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 6px;
        }
        
        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-draft { background: #6c757d; color: white; }
        .status-submitted { background: #17a2b8; color: white; }
        .status-reviewed { background: #ffc107; color: #212529; }
        .status-approved { background: #28a745; color: white; }
        .status-completed { background: #007bff; color: white; }
        
        /* Print specific */
        @media print {
            .container {
                padding: 5mm;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-size: 9px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-left">
                <div class="company-name">{{ $companySetting->company_name ?? 'PT. Asset Management' }}</div>
                <div class="company-address">
                    {{ $companySetting->address ?? 'Alamat Perusahaan' }}<br>
                    {{ $companySetting->phone ?? 'Telepon' }} | {{ $companySetting->email ?? 'Email' }}
                    @if($branch)
                        <br><strong>Cabang:</strong> {{ $branch->name }}
                    @endif
                </div>
            </div>
            <div class="header-right">
                <div class="document-number">{{ $requestAsset->request_number }}</div>
                <div style="margin-top: 8px; font-size: 10px;">
                    <span class="status-badge status-{{ $requestAsset->status }}">{{ $requestAsset->status_label }}</span>
                </div>
            </div>
        </div>

        <!-- Title -->
        <div class="title">
            <h1>FORM PERMINTAAN ASSET</h1>
            <p>Formulir Permohonan Pengadaan/Perbaikan Asset Perusahaan</p>
        </div>

        <!-- Request Information -->
        <div class="info-section">
            <div class="info-grid">
                <div class="info-row">
                    <div class="info-cell info-label">Nama Pemohon</div>
                    <div class="info-cell info-value">{{ $requestAsset->requestedByUser->name }}</div>
                    <div class="info-cell info-label">Tanggal Permohonan</div>
                    <div class="info-cell info-value">{{ $requestAsset->request_date->format('d/m/Y') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-cell info-label">Divisi Pemohon</div>
                    <div class="info-cell info-value">{{ $requestAsset->division->name }}</div>
                    <div class="info-cell info-label">Tanggal Dibutuhkan</div>
                    <div class="info-cell info-value">{{ $requestAsset->needed_date->format('d/m/Y') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-cell info-label">Jabatan Pemohon</div>
                    <div class="info-cell info-value">{{ $requestAsset->position }}</div>
                    <div class="info-cell info-label">Kategori Permintaan</div>
                    <div class="info-cell info-value">{{ $requestAsset->request_category }}</div>
                </div>
                <div class="info-row">
                    <div class="info-cell info-label">Cabang Pemohon</div>
                    <div class="info-cell info-value">{{ $branch->name ?? 'Pusat' }}</div>
                    <div class="info-cell info-label">Item Permintaan</div>
                    <div class="info-cell info-value">{{ $requestAsset->item_type }}</div>
                </div>
            </div>
        </div>

        <!-- Purpose -->
        <div class="purpose-section">
            <div class="purpose-title">Tujuan Permintaan</div>
            <div class="purpose-text">{{ $requestAsset->purpose }}</div>
        </div>

        <!-- Items Detail -->
        <div class="items-section">
            <div class="section-title">Detail Item Permintaan</div>
            <table class="items-table">
                <thead>
                    <tr>
                        <th class="item-number">No</th>
                        <th class="item-name">Nama Barang/Jasa</th>
                        <th class="item-qty">Jumlah</th>
                        <th class="item-desc">Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    @if($requestAsset->items && count($requestAsset->items) > 0)
                        @foreach($requestAsset->items as $index => $item)
                        <tr>
                            <td class="item-number">{{ $index + 1 }}</td>
                            <td class="item-name">{{ $item['name'] }}</td>
                            <td class="item-qty">{{ $item['quantity'] }}</td>
                            <td class="item-desc">{{ $item['description'] ?? '-' }}</td>
                        </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="4" style="text-align: center; color: #6c757d; font-style: italic;">
                                Tidak ada item yang tercatat
                            </td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Additional Notes -->
        @if($requestAsset->notes)
        <div class="notes-section">
            <div class="notes-title">Catatan Tambahan</div>
            <div style="font-size: 8px;">{{ $requestAsset->notes }}</div>
        </div>
        @endif

        <!-- Disclaimer -->
        <div class="disclaimer">
            <div class="disclaimer-title">⚠️ PENTING - PERHATIAN</div>
            <div class="disclaimer-text">
                Permintaan ini belum 100% disetujui. Permintaan akan di-review oleh divisi terkait 
                serta bagian keuangan serta atasan pemohon sebelum dapat dieksekusi.
            </div>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-row">
                <div class="signature-cell">
                    <div class="signature-box">
                        @if($requestAsset->status !== 'draft')
                            <div class="signature-date">{{ $requestAsset->created_at->format('d/m/Y') }}</div>
                        @endif
                    </div>
                    <div class="signature-label">Diminta Oleh</div>
                    <div class="signature-name">{{ $requestAsset->requestedByUser->name }}</div>
                </div>
                <div class="signature-cell">
                    <div class="signature-box">
                        @if(in_array($requestAsset->status, ['approved', 'completed']))
                            <div class="signature-date">{{ $requestAsset->approved_at->format('d/m/Y') }}</div>
                        @endif
                    </div>
                    <div class="signature-label">Atasan Pemohon</div>
                    <div class="signature-name">(...........................)</div>
                </div>
                <div class="signature-cell">
                    <div class="signature-box">
                        @if(in_array($requestAsset->status, ['approved', 'completed']) && $requestAsset->approvedByUser)
                            <div class="signature-date">{{ $requestAsset->approved_at->format('d/m/Y') }}</div>
                        @endif
                    </div>
                    <div class="signature-label">IT/HRD-GA</div>
                    <div class="signature-name">
                        @if($requestAsset->approvedByUser)
                            {{ $requestAsset->approvedByUser->name }}
                        @else
                            (...........................)
                        @endif
                    </div>
                </div>
                <div class="signature-cell">
                    <div class="signature-box"></div>
                    <div class="signature-label">FAD</div>
                    <div class="signature-name">(...........................)</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>Dokumen ini dicetak pada {{ $printDate }} | {{ $requestAsset->request_number }}</div>
            <div style="margin-top: 5px;">
                @if($requestAsset->estimated_total)
                    Estimasi Total Biaya: Rp {{ number_format($requestAsset->estimated_total, 0, ',', '.') }}
                @endif
            </div>
        </div>
    </div>
</body>
</html>
