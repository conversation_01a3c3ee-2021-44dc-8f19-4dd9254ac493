@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Karyawan - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex align-items-center">
            <a href="{{ route('master.employees.index') }}" class="btn btn-outline-secondary btn-sm me-3">
              <i class="ri-arrow-left-line"></i>
            </a>
            <div>
              <h5 class="card-title mb-0">
                <i class="ri-user-add-line me-2"></i>
                Tambah Karyawan Baru
              </h5>
              <small class="text-muted">Tambah data karyawan untuk assignment asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">Informasi Karyawan</h6>
        </div>
        <div class="card-body">
          <form action="{{ route('master.employees.store') }}" method="POST">
            @csrf
            
            <div class="row">
              <!-- Basic Information -->
              <div class="col-md-6">
                <h6 class="text-muted mb-3">Informasi Dasar</h6>
                
                <div class="mb-3">
                  <label class="form-label">NIK Karyawan <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('nik') is-invalid @enderror" 
                         name="nik" value="{{ old('nik') }}" placeholder="Masukkan NIK karyawan">
                  @error('nik')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('full_name') is-invalid @enderror" 
                         name="full_name" value="{{ old('full_name') }}" placeholder="Masukkan nama lengkap">
                  @error('full_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Email</label>
                  <input type="email" class="form-control @error('email') is-invalid @enderror" 
                         name="email" value="{{ old('email') }}" placeholder="Masukkan email">
                  @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">No. Telepon</label>
                  <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                         name="phone" value="{{ old('phone') }}" placeholder="Masukkan no. telepon">
                  @error('phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Jenis Kelamin</label>
                  <select class="form-select @error('gender') is-invalid @enderror" name="gender">
                    <option value="">Pilih jenis kelamin</option>
                    <option value="male" {{ old('gender') === 'male' ? 'selected' : '' }}>Laki-laki</option>
                    <option value="female" {{ old('gender') === 'female' ? 'selected' : '' }}>Perempuan</option>
                  </select>
                  @error('gender')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Tanggal Lahir</label>
                  <input type="date" class="form-control @error('birth_date') is-invalid @enderror" 
                         name="birth_date" value="{{ old('birth_date') }}">
                  @error('birth_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <!-- Work Information -->
              <div class="col-md-6">
                <h6 class="text-muted mb-3">Informasi Pekerjaan</h6>
                
                <div class="mb-3">
                  <label class="form-label">Cabang <span class="text-danger">*</span></label>
                  <select class="form-select @error('branch_id') is-invalid @enderror" name="branch_id">
                    <option value="">Pilih cabang</option>
                    @foreach($branches as $branch)
                      <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                        {{ $branch->name }}
                      </option>
                    @endforeach
                  </select>
                  @error('branch_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Divisi <span class="text-danger">*</span></label>
                  <select class="form-select @error('division_id') is-invalid @enderror" name="division_id">
                    <option value="">Pilih divisi</option>
                    @foreach($divisions as $division)
                      <option value="{{ $division->id }}" {{ old('division_id') == $division->id ? 'selected' : '' }}>
                        {{ $division->name }}
                      </option>
                    @endforeach
                  </select>
                  @error('division_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Bagian/Departemen</label>
                  <input type="text" class="form-control @error('department') is-invalid @enderror" 
                         name="department" value="{{ old('department') }}" placeholder="Masukkan bagian/departemen">
                  @error('department')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Jabatan <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('position') is-invalid @enderror" 
                         name="position" value="{{ old('position') }}" placeholder="Masukkan jabatan">
                  @error('position')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Status Karyawan <span class="text-danger">*</span></label>
                  <select class="form-select @error('employee_status') is-invalid @enderror" name="employee_status">
                    <option value="">Pilih status karyawan</option>
                    <option value="permanent" {{ old('employee_status') === 'permanent' ? 'selected' : '' }}>Tetap</option>
                    <option value="contract" {{ old('employee_status') === 'contract' ? 'selected' : '' }}>Kontrak</option>
                    <option value="intern" {{ old('employee_status') === 'intern' ? 'selected' : '' }}>Magang</option>
                  </select>
                  @error('employee_status')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Tanggal Bergabung</label>
                  <input type="date" class="form-control @error('join_date') is-invalid @enderror" 
                         name="join_date" value="{{ old('join_date') }}">
                  @error('join_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_active" value="1" 
                           {{ old('is_active', true) ? 'checked' : '' }}>
                    <label class="form-check-label">
                      Status Aktif
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div class="row mt-4">
              <div class="col-12">
                <h6 class="text-muted mb-3">Informasi Tambahan</h6>
                
                <div class="mb-3">
                  <label class="form-label">Alamat</label>
                  <textarea class="form-control @error('address') is-invalid @enderror" 
                            name="address" rows="3" placeholder="Masukkan alamat lengkap">{{ old('address') }}</textarea>
                  @error('address')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>

                <div class="mb-3">
                  <label class="form-label">Catatan</label>
                  <textarea class="form-control @error('notes') is-invalid @enderror" 
                            name="notes" rows="3" placeholder="Catatan tambahan (opsional)">{{ old('notes') }}</textarea>
                  @error('notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-end gap-3">
                  <a href="{{ route('master.employees.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-close-line me-2"></i>
                    Batal
                  </a>
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-2"></i>
                    Simpan Karyawan
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

@endsection
