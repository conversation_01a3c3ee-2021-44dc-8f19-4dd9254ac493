<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use App\Models\AssetMaintenance;
use App\Models\AssetCategory;
use App\Models\Branch;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\MaintenanceReportExport;

class MaintenanceReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:reports.maintenance.view')->only(['index']);
        $this->middleware('permission:reports.maintenance.export')->only(['export']);
    }
    
    /**
     * Display maintenance report with filters
     */
    public function index(Request $request)
    {
        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $categories = AssetCategory::active()->orderBy('name')->get();
        
        // Initialize query
        $query = AssetMaintenance::with(['asset.category', 'asset.branch', 'supplier', 'requestedBy', 'assignedTo']);
        
        // Apply filters
        $filters = [];
        
        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->whereHas('asset', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
            $filters['branch_id'] = $request->branch_id;
        }
        
        // Filter by asset category
        if ($request->filled('category_id')) {
            $query->whereHas('asset', function($q) use ($request) {
                $q->where('asset_category_id', $request->category_id);
            });
            $filters['category_id'] = $request->category_id;
        }
        
        // Filter by maintenance status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
            $filters['status'] = $request->status;
        }
        
        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
            $filters['priority'] = $request->priority;
        }
        
        // Filter by maintenance type
        if ($request->filled('maintenance_type')) {
            $query->where('maintenance_type', $request->maintenance_type);
            $filters['maintenance_type'] = $request->maintenance_type;
        }
        
        // Filter by date range (scheduled_date)
        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
            $filters['date_from'] = $request->date_from;
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
            $filters['date_to'] = $request->date_to;
        }
        
        // Get maintenances with pagination
        $maintenances = $query->orderBy('scheduled_date', 'desc')->paginate(20);
        
        // Get statistics
        $stats = [
            'total_maintenances' => $query->count(),
            'scheduled_count' => (clone $query)->where('status', 'scheduled')->count(),
            'in_progress_count' => (clone $query)->where('status', 'in_progress')->count(),
            'completed_count' => (clone $query)->where('status', 'completed')->count(),
            'total_estimated_cost' => (clone $query)->sum('estimated_cost'),
            'total_actual_cost' => (clone $query)->sum('actual_cost'),
        ];
        
        // Status options
        $statusOptions = AssetMaintenance::getStatuses();
        
        // Priority options
        $priorityOptions = AssetMaintenance::getPriorities();
        
        // Maintenance type options
        $maintenanceTypeOptions = [
            'preventive' => 'Preventif',
            'corrective' => 'Korektif', 
            'emergency' => 'Darurat',
            'upgrade' => 'Upgrade'
        ];
        
        return view('reports.maintenance.index', compact(
            'maintenances',
            'branches', 
            'categories',
            'statusOptions',
            'priorityOptions',
            'maintenanceTypeOptions',
            'filters',
            'stats'
        ));
    }
    
    /**
     * Export maintenance report to Excel
     */
    public function export(Request $request)
    {
        // Build filename with current date and filters
        $filename = 'laporan-maintenance-' . date('Y-m-d-H-i-s');
        
        if ($request->filled('branch_id')) {
            $branch = Branch::find($request->branch_id);
            if ($branch) {
                $filename .= '-' . str_replace(' ', '-', strtolower($branch->name));
            }
        }
        
        if ($request->filled('category_id')) {
            $category = AssetCategory::find($request->category_id);
            if ($category) {
                $filename .= '-' . str_replace(' ', '-', strtolower($category->name));
            }
        }
        
        if ($request->filled('status')) {
            $filename .= '-' . $request->status;
        }
        
        $filename .= '.xlsx';
        
        return Excel::download(new MaintenanceReportExport($request->all()), $filename);
    }
}
