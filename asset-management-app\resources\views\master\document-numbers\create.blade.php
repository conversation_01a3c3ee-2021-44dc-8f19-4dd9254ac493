@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Konfigurasi Nomor Dokumen - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Nomor Dokumen /</span> Tambah Konfigurasi
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah Konfigurasi Nomor Dokumen</h5>
          <a href="{{ route('master.document-numbers.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.document-numbers.store') }}" method="POST">
            @csrf
            
            <div class="mb-3">
              <label class="form-label" for="document_type">Tipe Dokumen <span class="text-danger">*</span></label>
              <select class="form-select @error('document_type') is-invalid @enderror" id="document_type" name="document_type">
                <option value="">Pilih Tipe Dokumen</option>
                <option value="asset" {{ old('document_type') == 'asset' ? 'selected' : '' }}>Asset</option>
                <option value="purchase_order" {{ old('document_type') == 'purchase_order' ? 'selected' : '' }}>Purchase Order</option>
                <option value="invoice" {{ old('document_type') == 'invoice' ? 'selected' : '' }}>Invoice</option>
                <option value="maintenance" {{ old('document_type') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
              </select>
              @error('document_type')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Tipe dokumen yang akan menggunakan nomor ini</div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="format">Format Nomor <span class="text-danger">*</span></label>
              <input type="text" class="form-control @error('format') is-invalid @enderror"
                     id="format" name="format" value="{{ old('format', '{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}') }}"
                     placeholder="{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}">
              @error('format')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">
                Format nomor dokumen. Gunakan placeholder: {company_code}, {branch_code}, {year}, {category_code}, {asset_type_code}, {number}
                <br>Contoh: {company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number} → BITJKT25-511-01-0001
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="current_number">Nomor Awal <span class="text-danger">*</span></label>
                <input type="number" class="form-control @error('current_number') is-invalid @enderror" 
                       id="current_number" name="current_number" value="{{ old('current_number', 0) }}" 
                       min="0">
                @error('current_number')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Nomor awal untuk penomoran (biasanya 0)</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="year">Tahun <span class="text-danger">*</span></label>
                <select class="form-select @error('year') is-invalid @enderror" id="year" name="year">
                  @for($year = date('Y'); $year <= date('Y') + 5; $year++)
                    <option value="{{ $year }}" {{ old('year', date('Y')) == $year ? 'selected' : '' }}>
                      {{ $year }}
                    </option>
                  @endfor
                </select>
                @error('year')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-4">
                <label class="form-label" for="branch_id">Cabang <span class="text-danger">*</span></label>
                <select class="form-select @error('branch_id') is-invalid @enderror" id="branch_id" name="branch_id">
                  <option value="">Pilih Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }} ({{ $branch->code }})
                    </option>
                  @endforeach
                </select>
                @error('branch_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="asset_category_id">Kategori Asset</label>
                <select class="form-select @error('asset_category_id') is-invalid @enderror" id="asset_category_id" name="asset_category_id">
                  <option value="">Pilih Kategori (Opsional)</option>
                  @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ old('asset_category_id') == $category->id ? 'selected' : '' }}>
                      {{ $category->name }} ({{ $category->code }})
                    </option>
                  @endforeach
                </select>
                @error('asset_category_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="asset_type_id">Jenis Asset</label>
                <select class="form-select @error('asset_type_id') is-invalid @enderror" id="asset_type_id" name="asset_type_id">
                  <option value="">Pilih Jenis (Opsional)</option>
                </select>
                @error('asset_type_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi konfigurasi nomor dokumen">{{ old('description') }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.document-numbers.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Preview Nomor Dokumen</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Contoh Format:</h6>
            <p class="mb-2">Dengan format: <code>{prefix}-{branch}-{year}-{number}</code></p>
            <p class="mb-2">Prefix: <strong>AST</strong></p>
            <p class="mb-2">Branch: <strong>HQ</strong></p>
            <p class="mb-2">Year: <strong>2025</strong></p>
            <p class="mb-0">Hasil: <span class="badge bg-primary">AST-HQ-2025-0001</span></p>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Catatan:</h6>
            <ul class="mb-0">
              <li>Setiap kombinasi tipe dokumen, cabang, dan tahun harus unik</li>
              <li>Nomor akan otomatis increment setiap kali digunakan</li>
              <li>Format dapat disesuaikan dengan kebutuhan</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Load asset types when category changes
  const categorySelect = document.getElementById('asset_category_id');
  const assetTypeSelect = document.getElementById('asset_type_id');

  categorySelect.addEventListener('change', function() {
    const categoryId = this.value;

    // Clear asset type options
    assetTypeSelect.innerHTML = '<option value="">Pilih Jenis (Opsional)</option>';

    if (categoryId) {
      fetch(`{{ route('master.asset-types.by-category') }}?category_id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
          data.forEach(assetType => {
            const option = document.createElement('option');
            option.value = assetType.id;
            option.textContent = `${assetType.name} (${assetType.code})`;
            assetTypeSelect.appendChild(option);
          });
        })
        .catch(error => console.error('Error loading asset types:', error));
    }

    updatePreview();
  });

  // Preview nomor dokumen
  function updatePreview() {
    const format = document.getElementById('format').value;
    const year = document.getElementById('year').value || new Date().getFullYear();
    const branchSelect = document.getElementById('branch_id');
    const categorySelect = document.getElementById('asset_category_id');
    const assetTypeSelect = document.getElementById('asset_type_id');

    const companyCode = 'BIT'; // From company settings
    const branchCode = branchSelect.options[branchSelect.selectedIndex]?.text?.match(/\(([^)]+)\)/)?.[1] || 'XXX';
    const categoryCode = categorySelect.options[categorySelect.selectedIndex]?.text?.match(/\(([^)]+)\)/)?.[1] || '000';
    const assetTypeCode = assetTypeSelect.options[assetTypeSelect.selectedIndex]?.text?.match(/\(([^)]+)\)/)?.[1] || '00';

    const preview = format
      .replace('{company_code}', companyCode)
      .replace('{branch_code}', branchCode)
      .replace('{category_code}', categoryCode)
      .replace('{asset_type_code}', assetTypeCode)
      .replace('{year}', year.toString().slice(-2))
      .replace('{number}', '0001');

    document.getElementById('preview').textContent = preview;
  }

  // Update preview on input changes
  ['format', 'year', 'branch_id', 'asset_category_id', 'asset_type_id'].forEach(id => {
    const element = document.getElementById(id);
    if (element) {
      element.addEventListener('input', updatePreview);
      element.addEventListener('change', updatePreview);
    }
  });

  // Initial preview
  updatePreview();
});
</script>
@endsection
