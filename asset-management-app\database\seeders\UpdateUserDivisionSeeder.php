<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UpdateUserDivisionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get divisions
        $itDivision = \App\Models\Division::where('code', 'IT')->first();
        $finDivision = \App\Models\Division::where('code', 'FIN')->first();
        $hrDivision = \App\Models\Division::where('code', 'HR')->first();
        $mktDivision = \App\Models\Division::where('code', 'MKT')->first();
        $opsDivision = \App\Models\Division::where('code', 'OPS')->first();

        // Update existing users with divisions
        $users = [
            ['username' => 'superadmin', 'division_id' => $itDivision?->id],
            ['username' => 'admin', 'division_id' => $itDivision?->id],
            ['username' => 'manager', 'division_id' => $opsDivision?->id],
            ['username' => 'staff', 'division_id' => $mktDivision?->id],
        ];

        foreach ($users as $userData) {
            $user = \App\Models\User::where('username', $userData['username'])->first();
            if ($user && $userData['division_id']) {
                $user->update(['division_id' => $userData['division_id']]);
            }
        }
    }
}
