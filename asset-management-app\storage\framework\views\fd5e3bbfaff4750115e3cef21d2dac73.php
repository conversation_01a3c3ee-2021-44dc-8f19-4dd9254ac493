<?php $__env->startSection('title', 'Master Role - Asset Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Master Role
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="<?php echo e(route('master.roles.create')); ?>" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah Role
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="<?php echo e(route('master.roles.index')); ?>">
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="<?php echo e(request('search')); ?>" 
                   placeholder="Nama, slug, atau deskripsi role...">
          </div>
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <select class="form-select" name="is_active">
              <option value="">Semua Status</option>
              <option value="1" <?php echo e(request('is_active') === '1' ? 'selected' : ''); ?>>Aktif</option>
              <option value="0" <?php echo e(request('is_active') === '0' ? 'selected' : ''); ?>>Non-Aktif</option>
            </select>
          </div>
          <div class="col-md-5">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="<?php echo e(route('master.roles.index')); ?>" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Roles Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Role (<?php echo e($roles->total()); ?> role)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Nama Role</th>
            <th>Slug</th>
            <th>Deskripsi</th>
            <th>Jumlah User</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          <?php $__empty_1 = true; $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
          <tr>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar avatar-sm me-2">
                  <span class="avatar-initial rounded-circle bg-label-<?php echo e($role->slug === 'super-admin' ? 'danger' : ($role->slug === 'admin' ? 'warning' : 'primary')); ?>">
                    <i class="ri-shield-user-line"></i>
                  </span>
                </div>
                <div>
                  <strong><?php echo e($role->name); ?></strong>
                </div>
              </div>
            </td>
            <td>
              <span class="badge bg-info"><?php echo e($role->slug); ?></span>
            </td>
            <td>
              <div class="text-truncate" style="max-width: 250px;">
                <?php echo e($role->description ?: '-'); ?>

              </div>
            </td>
            <td>
              <span class="badge bg-secondary"><?php echo e($role->users_count); ?> user</span>
            </td>
            <td>
              <span class="badge bg-<?php echo e($role->is_active ? 'success' : 'secondary'); ?>">
                <?php echo e($role->is_active ? 'Aktif' : 'Non-Aktif'); ?>

              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="<?php echo e(route('master.roles.show', $role)); ?>">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="<?php echo e(route('master.roles.edit', $role)); ?>">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  <?php if($role->slug !== 'super-admin' && $role->users_count == 0): ?>
                  <div class="dropdown-divider"></div>
                  <form action="<?php echo e(route('master.roles.destroy', $role)); ?>" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus role ini?\n\nPastikan tidak ada user yang menggunakan role ini.')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                  <?php endif; ?>
                </div>
              </div>
            </td>
          </tr>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
          <tr>
            <td colspan="6" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-shield-user-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada data role</h6>
                <p class="text-muted mb-3">Belum ada role yang terdaftar atau sesuai dengan filter yang dipilih.</p>
                <a href="<?php echo e(route('master.roles.create')); ?>" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah Role Pertama
                </a>
              </div>
            </td>
          </tr>
          <?php endif; ?>
        </tbody>
      </table>
    </div>
    
    <?php if($roles->hasPages()): ?>
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan <?php echo e($roles->firstItem()); ?> - <?php echo e($roles->lastItem()); ?> dari <?php echo e($roles->total()); ?> data
        </div>
        <?php echo e($roles->links()); ?>

      </div>
    </div>
    <?php endif; ?>
  </div>
</div>

<?php if(session('success')): ?>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body"><?php echo e(session('success')); ?></div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
<?php endif; ?>

<?php if(session('error')): ?>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body"><?php echo e(session('error')); ?></div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/master/roles/index.blade.php ENDPATH**/ ?>