<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintenanceReceipt extends Model
{
    use HasFactory;

    protected $fillable = [
        'maintenance_id',
        'received_by',
        'received_date',
        'received_time',
        'receiver_name',
        'receiver_position',
        'asset_condition',
        'notes',
        'signature_data',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'received_date' => 'date',
    ];

    // Relationships
    public function maintenance()
    {
        return $this->belongsTo(AssetMaintenance::class, 'maintenance_id');
    }

    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    // Accessors
    public function getConditionTextAttribute()
    {
        return match($this->asset_condition) {
            'excellent' => 'Sangat Baik',
            'good' => 'Baik',
            'fair' => 'Cukup',
            'poor' => 'Buruk',
            'damaged' => 'Rusak',
            default => ucfirst($this->asset_condition)
        };
    }

    public function getConditionBadgeClassAttribute()
    {
        return match($this->asset_condition) {
            'excellent' => 'bg-success',
            'good' => 'bg-primary',
            'fair' => 'bg-warning',
            'poor' => 'bg-danger',
            'damaged' => 'bg-dark',
            default => 'bg-secondary'
        };
    }
}
