<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_opnames', function (Blueprint $table) {
            $table->id();
            $table->string('opname_number')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('branch_id')->constrained('branches');
            $table->foreignId('created_by')->constrained('users');
            $table->enum('status', ['draft', 'in_progress', 'completed', 'cancelled'])->default('draft');
            $table->datetime('start_date')->nullable();
            $table->datetime('end_date')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->json('settings')->nullable(); // Additional settings like categories, locations, etc.
            $table->integer('total_assets')->default(0);
            $table->integer('scanned_assets')->default(0);
            $table->integer('missing_assets')->default(0);
            $table->integer('found_assets')->default(0);
            $table->text('notes')->nullable();
            $table->boolean('is_locked')->default(false); // Lock asset operations during opname
            $table->timestamps();

            $table->index(['branch_id', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_opnames');
    }
};
