<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\AssetType;
use App\Models\Branch;

class AssetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing data
        $categories = AssetCategory::all();
        $types = AssetType::all();
        $branches = Branch::all();

        if ($categories->isEmpty() || $branches->isEmpty()) {
            $this->command->info('Please run AssetCategorySeeder and BranchSeeder first');
            return;
        }

        $assets = [
            [
                'asset_code' => 'COMP-JKT-2024-0001',
                'name' => 'Laptop Dell Latitude 5520',
                'asset_category_id' => $categories->where('name', 'Komputer')->first()?->id ?? $categories->first()->id,
                'asset_type_id' => $types->where('name', 'Laptop')->first()?->id,
                'branch_id' => $branches->first()->id,
                'description' => 'Laptop untuk karyawan IT',
                'status' => 'active',
                'dynamic_fields' => [
                    'brand_category' => 'laptop',
                    'specific_brand' => 'dell',
                    'serial_number' => 'DL123456789',
                    'processor' => 'Intel Core i5-11th Gen',
                    'memory' => '16GB DDR4',
                    'storage' => '512GB SSD',
                    'purchase_date' => '2024-01-15',
                    'purchase_price' => '15000000',
                    'warranty_end' => '2027-01-15'
                ]
            ],
            [
                'asset_code' => 'COMP-JKT-2024-0002',
                'name' => 'Desktop HP EliteDesk 800',
                'asset_category_id' => $categories->where('name', 'Komputer')->first()?->id ?? $categories->first()->id,
                'asset_type_id' => $types->where('name', 'Desktop')->first()?->id,
                'branch_id' => $branches->first()->id,
                'description' => 'Desktop untuk accounting',
                'status' => 'active',
                'dynamic_fields' => [
                    'brand_category' => 'laptop',
                    'specific_brand' => 'hp',
                    'serial_number' => 'HP987654321',
                    'processor' => 'Intel Core i7-12th Gen',
                    'memory' => '32GB DDR4',
                    'storage' => '1TB SSD',
                    'purchase_date' => '2024-02-01',
                    'purchase_price' => '********',
                    'warranty_end' => '2027-02-01'
                ]
            ],
            [
                'asset_code' => 'FURN-JKT-2024-0001',
                'name' => 'Meja Kerja Executive',
                'asset_category_id' => $categories->where('name', 'Furniture')->first()?->id ?? $categories->first()->id,
                'asset_type_id' => $types->where('name', 'Meja')->first()?->id,
                'branch_id' => $branches->first()->id,
                'description' => 'Meja kerja untuk manager',
                'status' => 'active',
                'dynamic_fields' => [
                    'material' => 'Kayu Jati',
                    'dimensions' => '160x80x75 cm',
                    'color' => 'Brown',
                    'purchase_date' => '2024-01-10',
                    'purchase_price' => '3500000',
                    'supplier' => 'PT Furniture Indonesia'
                ]
            ],
            [
                'asset_code' => 'COMP-JKT-2024-0003',
                'name' => 'Printer Canon ImageClass MF445dw',
                'asset_category_id' => $categories->where('name', 'Komputer')->first()?->id ?? $categories->first()->id,
                'asset_type_id' => $types->where('name', 'Printer')->first()?->id,
                'branch_id' => $branches->first()->id,
                'description' => 'Printer multifungsi untuk office',
                'status' => 'active',
                'dynamic_fields' => [
                    'brand_category' => 'network',
                    'specific_brand' => 'canon',
                    'serial_number' => 'CN445DW001',
                    'print_type' => 'Laser Monochrome',
                    'connectivity' => 'WiFi, Ethernet, USB',
                    'purchase_date' => '2024-03-01',
                    'purchase_price' => '4500000',
                    'warranty_end' => '2025-03-01'
                ]
            ],
            [
                'asset_code' => 'COMP-JKT-2024-0004',
                'name' => 'Laptop ASUS VivoBook 15',
                'asset_category_id' => $categories->where('name', 'Komputer')->first()?->id ?? $categories->first()->id,
                'asset_type_id' => $types->where('name', 'Laptop')->first()?->id,
                'branch_id' => $branches->count() > 1 ? $branches->skip(1)->first()->id : $branches->first()->id,
                'description' => 'Laptop untuk staff marketing',
                'status' => 'maintenance',
                'dynamic_fields' => [
                    'brand_category' => 'laptop',
                    'specific_brand' => 'asus',
                    'serial_number' => 'AS789123456',
                    'processor' => 'AMD Ryzen 5 5500U',
                    'memory' => '8GB DDR4',
                    'storage' => '256GB SSD',
                    'purchase_date' => '2023-12-15',
                    'purchase_price' => '8500000',
                    'warranty_end' => '2025-12-15'
                ]
            ]
        ];

        foreach ($assets as $assetData) {
            Asset::create($assetData);
        }

        $this->command->info('Sample assets created successfully!');
    }
}
