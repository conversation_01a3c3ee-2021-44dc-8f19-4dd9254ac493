<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\ApprovalWorkflow;
use App\Models\ApprovalLevel;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ApprovalWorkflowController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ApprovalWorkflow::with(['levels']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('module')) {
            $query->where('module', $request->module);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $workflows = $query->orderBy('priority', 'desc')->paginate(15);

        return view('master.approval-workflows.index', compact('workflows'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::active()->get();
        $users = User::active()->get();
        return view('master.approval-workflows.create', compact('roles', 'users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:approval_workflows,code',
            'description' => 'nullable|string',
            'module' => 'required|in:asset_requests,purchase_orders,budget_requests',
            'priority' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'conditions' => 'required|array',
            'levels' => 'required|array|min:1',
            'levels.*.level_name' => 'required|string|max:255',
            'levels.*.description' => 'nullable|string',
            'levels.*.approver_type' => 'required|in:role,user,position',
            'levels.*.approver_config' => 'required|array',
            'levels.*.is_required' => 'boolean',
            'levels.*.can_skip' => 'boolean',
            'levels.*.timeout_hours' => 'nullable|integer|min:1',
            'levels.*.timeout_action' => 'required|in:approve,reject,escalate',
        ]);

        $workflow = ApprovalWorkflow::create($validated);

        // Create levels
        foreach ($validated['levels'] as $index => $levelData) {
            $levelData['approval_workflow_id'] = $workflow->id;
            $levelData['level_order'] = $index + 1;
            ApprovalLevel::create($levelData);
        }

        return redirect()->route('master.approval-workflows.show', $workflow)
            ->with('success', 'Workflow approval berhasil dibuat.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ApprovalWorkflow $approvalWorkflow)
    {
        $approvalWorkflow->load(['levels.histories']);
        return view('master.approval-workflows.show', compact('approvalWorkflow'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApprovalWorkflow $approvalWorkflow)
    {
        $approvalWorkflow->load('levels');
        $roles = Role::active()->get();
        $users = User::active()->get();
        return view('master.approval-workflows.edit', compact('approvalWorkflow', 'roles', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApprovalWorkflow $approvalWorkflow)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:approval_workflows,code,' . $approvalWorkflow->id,
            'description' => 'nullable|string',
            'module' => 'required|in:asset_requests,purchase_orders,budget_requests',
            'priority' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'conditions' => 'required|array',
            'levels' => 'required|array|min:1',
            'levels.*.level_name' => 'required|string|max:255',
            'levels.*.description' => 'nullable|string',
            'levels.*.approver_type' => 'required|in:role,user,position',
            'levels.*.approver_config' => 'required|array',
            'levels.*.is_required' => 'boolean',
            'levels.*.can_skip' => 'boolean',
            'levels.*.timeout_hours' => 'nullable|integer|min:1',
            'levels.*.timeout_action' => 'required|in:approve,reject,escalate',
        ]);

        $approvalWorkflow->update($validated);

        // Delete existing levels and recreate
        $approvalWorkflow->levels()->delete();

        // Create new levels
        foreach ($validated['levels'] as $index => $levelData) {
            $levelData['approval_workflow_id'] = $approvalWorkflow->id;
            $levelData['level_order'] = $index + 1;
            ApprovalLevel::create($levelData);
        }

        return redirect()->route('master.approval-workflows.show', $approvalWorkflow)
            ->with('success', 'Workflow approval berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApprovalWorkflow $approvalWorkflow)
    {
        // Check if workflow is being used
        if ($approvalWorkflow->histories()->exists()) {
            return redirect()->route('master.approval-workflows.index')
                ->with('error', 'Workflow tidak dapat dihapus karena sedang digunakan.');
        }

        $approvalWorkflow->delete();

        return redirect()->route('master.approval-workflows.index')
            ->with('success', 'Workflow approval berhasil dihapus.');
    }
}
