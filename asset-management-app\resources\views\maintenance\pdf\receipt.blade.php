<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Tanda Terima Maintenance - {{ $maintenance->maintenance_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .document-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 10px;
        }
        .info-section {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        .info-label {
            width: 150px;
            font-weight: bold;
        }
        .info-value {
            flex: 1;
            border-bottom: 1px dotted #666;
            padding-bottom: 2px;
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            background-color: #f0f0f0;
            padding: 8px;
            margin: 20px 0 10px 0;
            border: 1px solid #ccc;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #666;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 200px;
            text-align: center;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            height: 60px;
            margin-bottom: 5px;
        }
        .qr-section {
            position: absolute;
            top: 20px;
            right: 20px;
            text-align: center;
            border: 2px solid #000;
            padding: 10px;
            background-color: #fff;
            width: 120px;
            height: 140px;
        }
        .qr-code {
            margin-bottom: 8px;
            display: block;
            width: 80px;
            height: 80px;
            margin: 0 auto 8px auto;
        }
        .qr-code svg {
            width: 80px !important;
            height: 80px !important;
            display: block;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        .status-scheduled { background-color: #007bff; }
        .status-in_progress { background-color: #ffc107; color: #000; }
        .status-completed { background-color: #28a745; }
        .status-cancelled { background-color: #dc3545; }
        .receipt-box {
            border: 2px solid #000;
            padding: 15px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <!-- QR Code Section -->
    <div class="qr-section">
        <div class="qr-code">
            {!! $qrCode !!}
        </div>
        <div style="font-size: 8px; font-weight: bold;">
            Scan untuk tracking
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $company->company_name ?? 'PT. NAMA PERUSAHAAN' }}</div>
        <div>{{ $company->address ?? 'Alamat Perusahaan' }}</div>
        <div>Telp: {{ $company->phone ?? '-' }} | Email: {{ $company->email ?? '-' }}</div>
        <div class="document-title">TANDA TERIMA MAINTENANCE ASSET</div>
    </div>

    <!-- Document Information -->
    <div class="info-section">
        <div class="info-row">
            <div class="info-label">Nomor Maintenance:</div>
            <div class="info-value">{{ $maintenance->maintenance_number }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Tanggal Maintenance:</div>
            <div class="info-value">{{ $maintenance->completed_date ? $maintenance->completed_date->format('d/m/Y') : $maintenance->scheduled_date->format('d/m/Y') }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Status:</div>
            <div class="info-value">
                <span class="status-badge status-{{ $maintenance->status }}">
                    {{ $maintenance->status_text }}
                </span>
            </div>
        </div>
    </div>

    <!-- Asset Information -->
    <div class="section-title">INFORMASI ASSET</div>
    <table class="table">
        <tr>
            <th width="25%">Kode Asset</th>
            <td>{{ $maintenance->asset->asset_code }}</td>
            <th width="25%">Nama Asset</th>
            <td>{{ $maintenance->asset->name }}</td>
        </tr>
        <tr>
            <th>Kategori</th>
            <td>{{ $maintenance->asset->assetCategory->name ?? '-' }}</td>
            <th>Cabang</th>
            <td>{{ $maintenance->asset->branch->name ?? '-' }}</td>
        </tr>
        <tr>
            <th>Brand/Model</th>
            <td>{{ $maintenance->asset->brand }} {{ $maintenance->asset->model }}</td>
            <th>Serial Number</th>
            <td>{{ $maintenance->asset->serial_number ?? '-' }}</td>
        </tr>
    </table>

    <!-- Maintenance Summary -->
    <div class="section-title">RINGKASAN MAINTENANCE</div>
    <table class="table">
        <tr>
            <th width="25%">Judul Maintenance</th>
            <td colspan="3">{{ $maintenance->title }}</td>
        </tr>
        <tr>
            <th>Tipe Maintenance</th>
            <td>{{ $maintenance->maintenance_type_text }}</td>
            <th width="25%">Prioritas</th>
            <td>{{ $maintenance->priority_text }}</td>
        </tr>
        @if($maintenance->estimated_cost)
        <tr>
            <th>Estimasi Biaya</th>
            <td>Rp {{ number_format($maintenance->estimated_cost, 0, ',', '.') }}</td>
            <th>Biaya Aktual</th>
            <td>Rp {{ number_format($maintenance->actual_cost ?? 0, 0, ',', '.') }}</td>
        </tr>
        @endif
    </table>

    <!-- Supplier Information -->
    @if($maintenance->supplier)
    <div class="section-title">INFORMASI SUPPLIER</div>
    <table class="table">
        <tr>
            <th width="25%">Nama Supplier</th>
            <td>{{ $maintenance->supplier->name }}</td>
            <th width="25%">Kode Supplier</th>
            <td>{{ $maintenance->supplier->supplier_code }}</td>
        </tr>
        @if($maintenance->supplier->phone || $maintenance->supplier->email)
        <tr>
            <th>Telepon</th>
            <td>{{ $maintenance->supplier->phone ?? '-' }}</td>
            <th>Email</th>
            <td>{{ $maintenance->supplier->email ?? '-' }}</td>
        </tr>
        @endif
    </table>
    @endif

    <!-- Receipt Confirmation -->
    <div class="receipt-box">
        <div class="section-title" style="margin: 0 0 15px 0; background: none; border: none; padding: 0;">
            KONFIRMASI PENERIMAAN
        </div>
        <p>Dengan ini saya menyatakan bahwa maintenance asset tersebut di atas telah:</p>
        <div style="margin: 15px 0;">
            <input type="checkbox" style="margin-right: 10px;"> Selesai dikerjakan sesuai dengan spesifikasi<br>
            <input type="checkbox" style="margin-right: 10px;"> Asset dalam kondisi baik dan berfungsi normal<br>
            <input type="checkbox" style="margin-right: 10px;"> Dokumentasi maintenance telah diterima<br>
            <input type="checkbox" style="margin-right: 10px;"> Tidak ada kerusakan tambahan yang ditemukan
        </div>
        
        <div style="margin-top: 20px;">
            <strong>Catatan Tambahan:</strong><br>
            <div style="border-bottom: 1px solid #666; height: 40px; margin-top: 5px;"></div>
        </div>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div>Yang Menyerahkan:</div>
            <div class="signature-line"></div>
            <div>{{ $maintenance->assignedTo->name ?? 'Teknisi' }}</div>
            <div>Tanggal: ___________</div>
        </div>
        
        <div class="signature-box">
            <div>Yang Menerima:</div>
            <div class="signature-line"></div>
            <div>{{ $maintenance->asset->assignedEmployee->full_name ?? 'User Asset' }}</div>
            <div>Tanggal: ___________</div>
        </div>
        
        <div class="signature-box">
            <div>Mengetahui:</div>
            <div class="signature-line"></div>
            <div>{{ $maintenance->approvedBy->name ?? 'Supervisor' }}</div>
            <div>Tanggal: ___________</div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        Dicetak pada: {{ $printDate }} oleh {{ $printedBy }}<br>
        Dokumen ini digenerate otomatis oleh sistem Asset Management<br>
        Scan QR Code untuk tracking status maintenance secara real-time
    </div>
</body>
</html>
