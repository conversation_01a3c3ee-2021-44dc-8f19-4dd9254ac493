<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AssetTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories
        $pcLaptopCategory = \App\Models\AssetCategory::where('code', 'PC-LAPTOP')->first();
        $toolsCategory = \App\Models\AssetCategory::where('code', 'TOOLS')->first();
        $furnitureCategory = \App\Models\AssetCategory::where('code', 'FURNITURE')->first();
        $electronicCategory = \App\Models\AssetCategory::where('code', 'ELECTRONIC')->first();
        $vehicleCategory = \App\Models\AssetCategory::where('code', 'VEHICLE')->first();
        $officeEquipCategory = \App\Models\AssetCategory::where('code', 'OFFICE-EQUIP')->first();

        $assetTypes = [
            // PC & Laptop Types
            ['name' => 'Desktop PC', 'code' => '01', 'category_id' => $pcLaptopCategory?->id, 'description' => 'Desktop Computer'],
            ['name' => 'Laptop', 'code' => '02', 'category_id' => $pcLaptopCategory?->id, 'description' => 'Laptop Computer'],
            ['name' => 'All-in-One PC', 'code' => '03', 'category_id' => $pcLaptopCategory?->id, 'description' => 'All-in-One Computer'],
            ['name' => 'Workstation', 'code' => '04', 'category_id' => $pcLaptopCategory?->id, 'description' => 'High-end Workstation'],
            ['name' => 'Mini PC', 'code' => '05', 'category_id' => $pcLaptopCategory?->id, 'description' => 'Mini PC/NUC'],

            // Tools Types
            ['name' => 'Power Tools', 'code' => '01', 'category_id' => $toolsCategory?->id, 'description' => 'Electric Power Tools'],
            ['name' => 'Hand Tools', 'code' => '02', 'category_id' => $toolsCategory?->id, 'description' => 'Manual Hand Tools'],
            ['name' => 'Measuring Tools', 'code' => '03', 'category_id' => $toolsCategory?->id, 'description' => 'Measurement Equipment'],

            // Furniture Types
            ['name' => 'Desk', 'code' => '01', 'category_id' => $furnitureCategory?->id, 'description' => 'Office Desk'],
            ['name' => 'Chair', 'code' => '02', 'category_id' => $furnitureCategory?->id, 'description' => 'Office Chair'],
            ['name' => 'Cabinet', 'code' => '03', 'category_id' => $furnitureCategory?->id, 'description' => 'Storage Cabinet'],
            ['name' => 'Meeting Table', 'code' => '04', 'category_id' => $furnitureCategory?->id, 'description' => 'Conference Table'],

            // Electronic Types
            ['name' => 'Printer', 'code' => '01', 'category_id' => $electronicCategory?->id, 'description' => 'Printer Device'],
            ['name' => 'Scanner', 'code' => '02', 'category_id' => $electronicCategory?->id, 'description' => 'Scanner Device'],
            ['name' => 'Projector', 'code' => '03', 'category_id' => $electronicCategory?->id, 'description' => 'Projector'],
            ['name' => 'Monitor', 'code' => '04', 'category_id' => $electronicCategory?->id, 'description' => 'Computer Monitor'],
            ['name' => 'UPS', 'code' => '05', 'category_id' => $electronicCategory?->id, 'description' => 'Uninterruptible Power Supply'],

            // Vehicle Types
            ['name' => 'Car', 'code' => '01', 'category_id' => $vehicleCategory?->id, 'description' => 'Company Car'],
            ['name' => 'Motorcycle', 'code' => '02', 'category_id' => $vehicleCategory?->id, 'description' => 'Company Motorcycle'],
            ['name' => 'Truck', 'code' => '03', 'category_id' => $vehicleCategory?->id, 'description' => 'Company Truck'],

            // Office Equipment Types
            ['name' => 'Phone', 'code' => '01', 'category_id' => $officeEquipCategory?->id, 'description' => 'Office Phone'],
            ['name' => 'Fax Machine', 'code' => '02', 'category_id' => $officeEquipCategory?->id, 'description' => 'Fax Machine'],
            ['name' => 'Shredder', 'code' => '03', 'category_id' => $officeEquipCategory?->id, 'description' => 'Paper Shredder'],
            ['name' => 'Laminator', 'code' => '04', 'category_id' => $officeEquipCategory?->id, 'description' => 'Document Laminator'],
        ];

        foreach ($assetTypes as $type) {
            if ($type['category_id']) {
                \App\Models\AssetType::create([
                    'name' => $type['name'],
                    'code' => $type['code'],
                    'asset_category_id' => $type['category_id'],
                    'description' => $type['description'],
                    'is_active' => true,
                ]);
            }
        }
    }
}
