<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApprovalLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'approval_workflow_id',
        'level_order',
        'level_name',
        'description',
        'approver_type',
        'approver_config',
        'is_required',
        'can_skip',
        'timeout_hours',
        'timeout_action',
        'is_active',
    ];

    protected $casts = [
        'approver_config' => 'array',
        'is_required' => 'boolean',
        'can_skip' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function workflow()
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'approval_workflow_id');
    }

    public function histories()
    {
        return $this->hasMany(ApprovalHistory::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    // Methods
    public function getApprovers()
    {
        $config = $this->approver_config;

        switch ($this->approver_type) {
            case 'role':
                return User::whereHas('role', function ($query) use ($config) {
                    $query->whereIn('id', $config['role_ids'] ?? []);
                })->get();

            case 'user':
                return User::whereIn('id', $config['user_ids'] ?? [])->get();

            case 'position':
                // For position-based approval (e.g., direct supervisor)
                return $this->getPositionBasedApprovers($config);

            default:
                return collect();
        }
    }

    private function getPositionBasedApprovers($config)
    {
        $position = $config['position'] ?? '';
        $users = collect();

        switch ($position) {
            case 'direct_supervisor':
                // Logic to find direct supervisor
                // This would depend on your organizational structure
                break;

            case 'branch_manager':
                // Find branch manager for the request's branch
                $users = User::whereHas('role', function ($query) {
                    $query->where('slug', 'manager');
                })->get();
                break;

            case 'division_head':
                // Find division head for the request's division
                break;

            default:
                break;
        }

        return $users;
    }

    public function canBeSkipped()
    {
        return $this->can_skip && !$this->is_required;
    }

    public function hasTimeout()
    {
        return $this->timeout_hours !== null && $this->timeout_hours > 0;
    }

    public function getTimeoutAt($startTime = null)
    {
        if (!$this->hasTimeout()) {
            return null;
        }

        $start = $startTime ?: now();
        return $start->addHours($this->timeout_hours);
    }
}
