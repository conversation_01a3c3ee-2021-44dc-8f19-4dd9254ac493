<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add asset maintenance permissions
        $maintenancePermissions = [
            ['slug' => 'asset_maintenance_view', 'name' => 'Lihat Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_create', 'name' => 'Buat Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_edit', 'name' => 'Edit Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_delete', 'name' => 'Hapus Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_start', 'name' => 'Mulai Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_complete', 'name' => 'Selesaikan Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_cancel', 'name' => 'Batalkan Maintenance Asset', 'module' => 'asset_maintenance'],
            ['slug' => 'asset_maintenance_approve', 'name' => 'Approve Maintenance Asset', 'module' => 'asset_maintenance'],
        ];

        // Create permissions if they don't exist
        foreach ($maintenancePermissions as $permissionData) {
            \App\Models\Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                [
                    'name' => $permissionData['name'],
                    'module' => $permissionData['module'],
                    'description' => $permissionData['name']
                ]
            );
        }

        // Assign permissions to roles
        $roles = \App\Models\Role::all();

        foreach ($roles as $role) {
            if ($role->name === 'Super Admin') {
                // Super Admin gets all permissions
                $permissionSlugs = collect($maintenancePermissions)->pluck('slug')->toArray();
                $this->assignPermissionsToRole($role, $permissionSlugs);
            } elseif ($role->name === 'Admin') {
                // Admin gets all maintenance permissions
                $permissionSlugs = collect($maintenancePermissions)->pluck('slug')->toArray();
                $this->assignPermissionsToRole($role, $permissionSlugs);
            } elseif ($role->name === 'Manager') {
                // Manager gets view, create, start, complete, approve permissions
                $managerPermissions = [
                    'asset_maintenance_view',
                    'asset_maintenance_create',
                    'asset_maintenance_edit',
                    'asset_maintenance_start',
                    'asset_maintenance_complete',
                    'asset_maintenance_approve'
                ];
                $this->assignPermissionsToRole($role, $managerPermissions);
            } elseif ($role->name === 'Staff') {
                // Staff gets view and create permissions
                $staffPermissions = [
                    'asset_maintenance_view',
                    'asset_maintenance_create'
                ];
                $this->assignPermissionsToRole($role, $staffPermissions);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove asset maintenance permissions
        $permissionSlugs = [
            'asset_maintenance_view',
            'asset_maintenance_create',
            'asset_maintenance_edit',
            'asset_maintenance_delete',
            'asset_maintenance_start',
            'asset_maintenance_complete',
            'asset_maintenance_cancel',
            'asset_maintenance_approve',
        ];

        \App\Models\Permission::whereIn('slug', $permissionSlugs)->delete();
    }

    /**
     * Helper method to assign permissions to role
     */
    private function assignPermissionsToRole($role, $permissionSlugs)
    {
        $permissions = \App\Models\Permission::whereIn('slug', $permissionSlugs)->get();
        $role->permissions()->syncWithoutDetaching($permissions->pluck('id'));
    }
};
