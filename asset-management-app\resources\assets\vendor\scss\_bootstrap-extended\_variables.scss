// Variables
//
// Variables should follow the `$component-state-property-size` formula for
// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.
//
// ? To change the layout colors you just need to change the $black and $body-bg colors and rest (border, card, font colors and etc...) will be adjust automatically.
// ? Use https://noeldelgado.github.io/shadowlord/ to select proper 💄 Color tints and shade for $black and $body-bg color.
// ? color-variables and theme-color-variables (branding colors) can be choose as per your preferences. We suggest you to use https://colors.eva.design/
// ! Light style use $black to generate the $gray shades.
// (C) Custom variables for extended components of bootstrap only

// * Color system
// *******************************************************************************

// scss-docs-start gray-color-variables
$white: #fff !default;
$black: #2e263d !default;
$gray-25: rgba($black, 0.025) !default; // (C)
$gray-50: rgba($black, 0.06) !default; // (C)
$gray-75: rgba($black, 0.08) !default; // (C)
$gray-100: rgba($black, 0.1) !default;
$gray-200: rgba($black, 0.2) !default;
$gray-300: rgba($black, 0.3) !default;
$gray-400: rgba($black, 0.4) !default;
$gray-500: rgba($black, 0.5) !default;
$gray-600: rgba($black, 0.6) !default;
$gray-700: rgba($black, 0.7) !default;
$gray-800: rgba($black, 0.8) !default;
$gray-900: rgba($black, 0.9) !default;
// scss-docs-end gray-color-variables

// scss-docs-start gray-colors-map
$grays: (
  '25': $gray-25,
  '50': $gray-50
) !default;
// scss-docs-end gray-colors-map

// scss-docs-start color-variables
$blue: #16b1ff !default;
$indigo: #666cff !default;
$purple: #8c57ff !default;
$pink: #e83e8c !default;
$red: #ff4c51 !default;
$orange: #ffb400 !default;
$yellow: #ffb400 !default;
$green: #56ca00 !default;
$teal: #20c997 !default;
$cyan: #28c3d7 !default;
// scss-docs-end color-variables

// scss-docs-start theme-color-variables
$primary: $purple !default;
$secondary: #8a8d93 !default;
$success: $green !default;
$info: $blue !default;
$warning: $orange !default;
$danger: $red !default;
$light: #dfdfe3 !default;
$dark: #4b4b4b !default;
$gray: $gray-500 !default; // (C)
// scss-docs-end theme-color-variables

// scss-docs-start theme-colors-map
$theme-colors: (
  'primary': $primary,
  'secondary': $secondary,
  'success': $success,
  'info': $info,
  'warning': $warning,
  'danger': $danger,
  'light': $light,
  'dark': $dark,
  'gray': $gray
) !default;
// scss-docs-end theme-colors-map

$color-scheme: 'light' !default; // (C)
// The contrast ratio to reach against white, to determine if color changes from "light" to "dark". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.
// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast
$min-contrast-ratio: 1.6 !default;

// Characters which are escaped by the escape-svg function
$escaped-characters: (('<', '%3c'), ('>', '%3e'), ('#', '%23'), ('(', '%28'), (')', '%29')) !default;

// * Options
// *******************************************************************************

$enable-negative-margins: true !default;
$enable-validation-icons: false !default;
$enable-dark-mode: false !default;

// Prefix for :root CSS variables
$variable-prefix: bs- !default;
$prefix: $variable-prefix !default;

// * Spacing
// *******************************************************************************

$spacer: 1rem !default;
$spacers: (
  0: 0,
  50: $spacer * 0.125,
  1: $spacer * 0.25,
  1_5: $spacer * 0.375,
  2: $spacer * 0.5,
  3: $spacer * 0.75,
  4: $spacer,
  5: $spacer * 1.25,
  6: $spacer * 1.5,
  7: $spacer * 1.75,
  8: $spacer * 2,
  9: $spacer * 2.25,
  10: $spacer * 2.5,
  11: $spacer * 2.75,
  12: $spacer * 3
) !default;

$sizes-px: (
  px-18: 18px,
  px-20: 20px,
  px-30: 30px,
  px-40: 40px,
  px-50: 50px,
  px-75: 75px,
  px-100: 100px,
  px-120: 120px,
  px-150: 150px,
  px-200: 200px,
  px-250: 250px,
  px-300: 300px,
  px-350: 350px,
  px-400: 400px,
  px-500: 500px,
  px-600: 600px,
  px-700: 700px,
  px-800: 800px,
  auto: auto
) !default; // (C)

// * Body
// *******************************************************************************

$body-bg: #f4f5fa !default;
$rgba-to-hex-bg: #fff !default; // (C)
$letter-spacing: 0.15px !default; // (C)
$body-color: rgba-to-hex($gray-700, $rgba-to-hex-bg) !default;
$rgba-to-hex-bg-inverted: #000 !default; // (C)

// * Links
// *******************************************************************************

$link-color: $primary !default;
$link-decoration: none !default;
$link-shade-percentage: 10% !default;
$link-hover-color: shift-color($link-color, $link-shade-percentage) !default;
$link-hover-decoration: null !default;

// * Grid
// *******************************************************************************

// Grid containers

// scss-docs-start container-max-widths
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1440px // Custom xxl size
) !default;
// scss-docs-end container-max-widths

$grid-gutter-width: 1.5rem !default;
$container-padding-x: 1.5rem !default; // (C)
$container-padding-x-sm: 1rem !default; // (C)
$container-padding-y: 1.5rem !default; // (C)

// * Components
// *******************************************************************************

// scss-docs-start border-variables
$border-width: 1px !default;
$border-color: rgba-to-hex(rgba($black, 0.12), $rgba-to-hex-bg) !default;
$border-inner-color: rgba($black, 0.075) !default; // (C)
// scss-docs-end border-variables

// scss-docs-start border-radius-variables
$border-radius: 0.375rem !default;
$border-radius-xl: 0.625rem !default; // (C)
$border-radius-lg: 0.5rem !default;
$border-radius-sm: 0.25rem !default;
$border-radius-xs: 0.125rem !default; // (C)
$border-radius-pill: 50rem !default;
// scss-docs-end border-radius-variables

// scss-docs-start box-shadow-variables
$box-shadow: 0 0.25rem 0.625rem rgba($black, 0.2) !default;
$box-shadow-xs: 0 0.125rem 0.25rem 0 rgba($black, 0.16) !default; //(c)
$box-shadow-sm: 0 0.1875rem 0.375rem rgba($black, 0.18) !default;
$box-shadow-lg: 0 0.375rem 1rem rgba($black, 0.22) !default;
$box-shadow-xl: 0 0.5rem 1.75rem 0 rgba($black, 0.24) !default; // (c)
// scss-docs-end box-shadow-variables

$component-active-color: $white !default;
$component-active-bg: $primary !default;

$component-line-height: 1.375 !default; // (C)
$component-focus-shadow-width: 0px !default; // (C)

$floating-component-border-color: rgba($black, 0.05) !default; // (C)
// $floating-component-shadow: 0 0.3125rem 1.25rem rgba($black, 0.3) !default; // (C)
$floating-component-shadow: 0px 3px 14px 0px rgba($black, 0.14) !default; // (C)

$hr-color: $border-color !default;
$hr-opacity: 1 !default;
$bordered-row-border-color: $hr-color !default; // (C)

$focus-ring-width: 0.15rem !default;
$focus-ring-opacity: 0.75 !default;
$focus-ring-color: rgba($gray-700, $focus-ring-opacity) !default;

// scss-docs-start caret-variables
$caret-width: 0.35em !default;
$caret-vertical-align: middle !default;
$caret-spacing: 0.8em !default;
// scss-docs-end caret-variables

// * Typography
// *******************************************************************************

// scss-docs-start font-variables
$font-family-sans-serif:
  'Inter',
  -apple-system,
  BlinkMacSystemFont,
  'Segoe UI',
  'Oxygen',
  'Ubuntu',
  'Cantarell',
  'Fira Sans',
  'Droid Sans',
  'Helvetica Neue',
  sans-serif !default;
$font-family-serif: Georgia, 'Times New Roman', serif !default; // (C)
$font-family-monospace: 'SFMono-Regular', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !default;
// stylelint-enable value-keyword-case
$font-family-base: var(--#{$variable-prefix}font-sans-serif) !default;
$font-family-code: var(--#{$variable-prefix}font-monospace) !default;

// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins
// $font-size-base effects the font size of the body text
$font-size-root: 16px !default;
$font-size-base: 0.9375rem !default; // Assumes the browser default, typically `16px`
$font-size-xl: 1.25rem !default; // (C)
$font-size-lg: 1.0625rem !default;
$font-size-sm: 0.8125rem !default;
$font-size-xs: 0.75rem !default; // (C)

$font-weight-lighter: lighter !default;
$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-medium: 500 !default;
$font-weight-semibold: 600 !default;
$font-weight-bold: 700 !default;
$font-weight-bolder: bolder !default;

$line-height-base: 1.375 !default;
$line-height-xl: 1.75 !default; // (C)
$line-height-lg: 1.625 !default;
$line-height-sm: 1.125 !default;
$line-height-xs: 1 !default; // (C)

$h1-font-size: 2.875rem !default;
$h2-font-size: 2.375rem !default;
$h3-font-size: 1.75rem !default;
$h4-font-size: 1.5rem !default;
$h5-font-size: 1.125rem !default;
$h6-font-size: $font-size-base !default;

$h1-line-height: 4.25rem !default;
$h2-line-height: 3.5rem !default;
$h3-line-height: 2.625rem !default;
$h4-line-height: 2.375rem !default;
$h5-line-height: 1.75rem !default;
$h6-line-height: 1.375rem !default;
// scss-docs-end font-variables

// scss-docs-start headings-variables
$headings-margin-bottom: $spacer !default;
$headings-font-weight: $font-weight-medium !default;
$headings-line-height: 1.375 !default;
$headings-color: rgba-to-hex($gray-900, $rgba-to-hex-bg) !default;
// scss-docs-end headings-variables

// scss-docs-start display-headings
$display-font-sizes: (
  1: 4rem,
  2: 3.5rem,
  3: 3rem,
  4: 2.5rem,
  5: 2rem,
  6: 1.5rem
) !default;

$display-font-weight: 500 !default;

// scss-docs-end display-headings

// scss-docs-start type-variables
$lead-font-size: 1.125rem !default;
$tiny-font-size: 70% !default; // (C)
$small-font-size: 0.8125rem !default;
$big-font-size: 112% !default; // (C)
$large-font-size: 150% !default; // (C)
$xlarge-font-size: 170% !default; // (C)

$text-muted: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;
$text-muted-hover: rgba-to-hex($gray-600, $rgba-to-hex-bg) !default; // (C)

$blockquote-font-size: $font-size-base !default;

$text-light: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default; // (C)
$text-lighter: rgba-to-hex($gray-300, $rgba-to-hex-bg) !default; // (C)
$text-lightest: rgba-to-hex($gray-200, $rgba-to-hex-bg) !default; // (C)

$dt-font-weight: $font-weight-medium !default;
// scss-docs-end type-variables

// * Z-index master list
// *******************************************************************************

$zindex-menu-fixed: 1080 !default;
$zindex-modal: 1090 !default;
$zindex-modal-backdrop: $zindex-modal - 1 !default;
// $zindex-modal-top: 1090 !default; // (C)
$zindex-offcanvas: 1090 !default;
$zindex-offcanvas-backdrop: $zindex-offcanvas - 1 !default;
$zindex-layout-mobile: 1100 !default; // (C)
$zindex-popover: 1091 !default;
$zindex-toast: 1095 !default; // (C)
$zindex-tooltip: 1099 !default;
$zindex-notification: 999999 !default; // (C)

// scss-docs-start zindex-levels-map
$zindex-levels: (
  n1: -1,
  0: 0,
  1: 1,
  2: 2,
  3: 3,
  4: 4,
  5: 5
) !default;
// scss-docs-end zindex-levels-map

// * Tables
// *******************************************************************************

// scss-docs-start table-variables
$table-head-padding-y: 1.176rem !default; // (C)
$table-head-padding-y-sm: 1.115rem !default; // (C)
$table-cell-padding-y: 0.784rem !default;
$table-cell-padding-x: 1.25rem !default;
$table-cell-padding-y-sm: 0.595rem !default;
$table-cell-padding-x-sm: $table-cell-padding-x !default;

$table-cell-vertical-align: middle !default;

$table-th-color: $headings-color !default; // (C)
$table-color: var(--#{$prefix}body-color) !default;
$table-bg: transparent !default;

$table-th-font-weight: $font-weight-medium !default;

$table-active-color: $body-color !default;
$table-active-bg-factor: 0.08 !default;
$table-active-bg: rgba($primary, $table-active-bg-factor) !default;

$table-active-bg-factor: 0.1 !default;
$table-active-bg: rgba($black, $table-active-bg-factor) !default;

$table-hover-bg-factor: 0.06 !default;
$table-hover-bg: rgba($black, $table-hover-bg-factor) !default;

$table-border-factor: 0.12 !default;
$table-border-color: rgba-to-hex(rgba($black, $table-border-factor), $rgba-to-hex-bg) !default;
$table-group-separator-color: $table-border-color !default;

$table-striped-bg-factor: 0.04 !default;
$table-striped-order: even !default;

$table-caption-color: $text-muted !default;

$table-bg-scale: -80% !default;

$table-header-bg-color: #f6f7fb !default; // (c)
// scss-docs-start table-loop
$table-variants: (
  'primary': shift-color($primary, $table-bg-scale),
  'secondary': shift-color($secondary, $table-bg-scale),
  'success': shift-color($success, $table-bg-scale),
  'info': shift-color($info, $table-bg-scale),
  'warning': shift-color($warning, $table-bg-scale),
  'danger': shift-color($danger, $table-bg-scale),
  'light': rgba-to-hex($gray-100, $rgba-to-hex-bg),
  'dark': $dark
) !default;
// scss-docs-end table-loop

// * Buttons + Forms
// *******************************************************************************

$input-btn-padding-y: 0.4806rem !default;
$input-btn-padding-x: 1.125rem !default;
$input-btn-font-size: $font-size-base !default;
$input-btn-line-height: $line-height-base !default;

$input-btn-focus-width: 0.05rem !default;
$input-btn-focus-color-opacity: 0.1 !default;
$input-btn-focus-color: rgba($component-active-bg, $input-btn-focus-color-opacity) !default;
$input-btn-focus-blur: 0.25rem !default;
$input-btn-focus-box-shadow: 0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;

$input-btn-padding-y-xs: 0.297rem !default; // (C)
$input-btn-padding-x-xs: 0.59rem !default; // (C)
$input-btn-font-size-xs: $font-size-xs !default; // (C)
$input-btn-line-height-xs: $line-height-base !default; // (C)

$input-btn-padding-y-sm: 0.4415rem !default;
$input-btn-padding-x-sm: 0.875rem !default;
$input-btn-font-size-sm: $font-size-sm !default;
$input-btn-line-height-sm: $line-height-base !default;

$input-btn-padding-y-lg: 0.52rem !default;
$input-btn-padding-x-lg: 1.375rem !default;
$input-btn-font-size-lg: $font-size-lg !default;
$input-btn-line-height-lg: $line-height-base !default;

$input-btn-border-width: $border-width !default;

$input-btn-padding-y-xl: 0.621rem !default; // (C)
$input-btn-padding-x-xl: 1.875rem !default; // (C)
$input-btn-font-size-xl: 1.188rem !default; // (C)
$input-btn-line-height-xl: $line-height-base !default; // (C)

// * Buttons
// *******************************************************************************

$btn-padding-y-xs: 0.297rem !default; // (C)
$btn-padding-x-xs: 0.625rem !default; // (C)
$btn-font-size-xs: $input-btn-font-size-xs !default; // (C)

$btn-padding-y-xl: 0.621rem !default; // (C)
$btn-padding-x-xl: 1.625rem !default; // (C)
$btn-font-size-xl: $input-btn-font-size-xl !default; // (C)

$btn-line-height-xs: $input-btn-line-height-xs !default; // (C)
$btn-line-height-sm: $input-btn-line-height-sm !default; // (C)
$btn-line-height-lg: $input-btn-line-height-lg !default; // (C)
$btn-line-height-xl: $input-btn-line-height-xl !default; // (C)

$btn-font-weight: $font-weight-medium !default;
$btn-box-shadow: $box-shadow-xs !default;
$btn-focus-box-shadow: $box-shadow-xs !default;
$btn-disabled-opacity: 0.45 !default;
$btn-active-box-shadow: $box-shadow-xs !default;

$btn-fab-box-shadow: 0px 1px 14px 0px rgba($black, 0.12) !default;
$btn-fab-active-box-shadow:
  0px 7px 8px -4px rgba($black, 0.2),
  0px 12px 17px 2px rgba($black, 0.14),
  0px 5px 22px 4px rgba($black, 0.12) !default;

$btn-border-radius: $border-radius !default;
$btn-border-radius-xs: $border-radius-xs !default; // (C)
$btn-border-radius-xl: $border-radius-xl !default; // (C)

$btn-transition: all 0.2s ease-in-out !default;
$btn-text-transform: capitalize !default; // (C)

$btn-label-bg-shade-amount: 84% !default; // (C)
$btn-label-bg-tint-amount: 85% !default; // (C)
$btn-label-disabled-bg-shade-amount: 84% !default; // (C)

$btn-hover-bg-shade-amount: 10% !default;
$btn-hover-bg-tint-amount: 90% !default;
$btn-hover-border-shade-amount: 10% !default;
$btn-hover-border-tint-amount: 90% !default;
$btn-active-bg-shade-amount: 10% !default;
$btn-active-bg-tint-amount: 90% !default;
$btn-active-border-shade-amount: 10% !default;
$btn-active-border-tint-amount: 90% !default;

// Outline buttons
$btn-outline-hover-bg-shade-amount: 8% !default; // (C)
$btn-outline-hover-bg-tint-amount: 92% !default; // (C)
$btn-outline-border-shade-amount: 50% !default; // (C)
$btn-outline-border-tint-amount: 50% !default; // (C)
$btn-outline-focus-bg-shade-amount: 75% !default; // (C)
$btn-outline-focus-bg-tint-amount: 92% !default; // (C)
$btn-outline-active-bg-shade-amount: 71% !default; // (C)
$btn-outline-active-bg-tint-amount: 92% !default; // (C)
// $btn-outline-border-shade-amount: 50% !default; // (C)
// $btn-outline-border-tint-amount: 50% !default; // (C)

// * Forms
// *******************************************************************************

// scss-docs-start form-text-variables
$form-text-margin-top: 0.25rem !default;
$form-text-color: $body-color !default;
// scss-docs-end form-text-variables

// scss-docs-start form-label-variables
$form-label-font-size: 0.8125rem !default;
$form-label-font-weight: $font-weight-medium !default;
$form-label-letter-spacing: inherit !default; //(C)
$form-label-text-transform: inherit !default; //(C)
// scss-docs-end form-label-variables

// scss-docs-start form-input-variables
$input-padding-y: 0.8555rem !default;
$input-padding-x: 1rem !default;

$input-padding-y-sm: 0.629rem !default;
$input-padding-x-sm: $input-padding-x !default;
$input-font-size-sm: $font-size-sm !default;

$input-padding-y-lg: 0.9572rem !default;
$input-padding-x-lg: $input-padding-x !default;
$input-font-size-lg: $font-size-lg !default;

$input-bg: transparent !default;
$input-disabled-color: $text-muted !default;
$input-disabled-bg: rgba-to-hex($gray-50, $rgba-to-hex-bg) !default;
$input-disabled-border-color: rgba-to-hex(rgba($black, 0.24), $rgba-to-hex-bg) !default;

$input-color: $headings-color !default;
$input-border-width: $input-btn-border-width !default;
$input-border-color: rgba-to-hex(rgba($black, 0.22), $rgba-to-hex-bg) !default;
$input-border-hover-color: rgba-to-hex($gray-300, $rgba-to-hex-bg) !default; // (C)

$input-border-radius: $border-radius !default;
$input-border-radius-sm: $border-radius-sm !default;
$input-border-radius-lg: $border-radius-lg !default;
$input-focus-border-width: 2px !default; //(C)
$input-focus-box-shadow: none !default;
$input-focus-border-color-scale: 50% !default; //(C)
$input-focus-border-color: tint-color($component-active-bg, $input-focus-border-color-scale) !default;

$input-placeholder-color: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;

$input-height-inner: add(($input-btn-font-size * $input-btn-line-height), ($input-btn-padding-y * 2)) !default;
$input-height-inner-sm: add(
  ($input-btn-font-size-sm * $input-btn-line-height-sm),
  ($input-btn-padding-y-sm * 2)
) !default; // (C)
$input-height-inner-lg: add(($font-size-lg * $line-height-lg), ($input-btn-padding-y-lg * 2)) !default; // (C)

$input-height: add($line-height-base * 1rem, add($input-padding-y * 2, 0.125rem, false)) !default;
// scss-docs-end form-input-variables

// scss-docs-start form-check-variables
$form-check-input-width: 1.2em !default;
$form-datatables-check-input-size: $form-check-input-width !default; // (C) For datatables with checkbox- update according to $form-check-input-width
$form-check-min-height: $font-size-base * $line-height-base * 1.067 !default;
$form-check-padding-start: $form-check-input-width + 0.6em !default;
$form-check-margin-bottom: 0.5rem !default;
$form-check-input-border: $input-focus-border-width solid $body-color !default;
$form-check-label-color: $headings-color !default;
$form-check-input-focus-box-shadow: none !default;

$form-check-label-cursor: pointer !default;

$form-check-input-border-radius: 0.267em !default;
$form-check-input-focus-border: $body-color !default;
$form-check-input-checked-color: $component-active-color !default;
$form-check-input-checked-bg-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 19 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-width='1.3' d='M5 10l3 3l6-6'/></svg>") !default;
$form-check-radio-checked-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='1.6' fill='#{$form-check-input-checked-color}'  /%3e%3c/svg%3e") !default;

$form-check-input-indeterminate-color: $component-active-color !default;
$form-check-input-indeterminate-bg-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-width='1.3' d='M6 10h8'/></svg>") !default;

$form-check-input-disabled-opacity: 0.45 !default;
$form-check-input-disabled-bg: rgba-to-hex($gray-300, $rgba-to-hex-bg) !default; // (C)
$form-check-label-disabled-color: $text-muted !default; // (C)

// scss-docs-end form-check-variables

// scss-docs-start form-switch-variables
$form-switch-color: $component-active-color !default;
$form-switch-width: 2em !default;
$form-switch-padding-start: $form-switch-width + 0.667em !default;
$form-switch-focus-color: $component-active-color;
$form-switch-bg-image: url("data:image/svg+xml,%3csvg width='22' height='22' viewBox='0 0 22 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cg filter='url(%23a)'%3e%3ccircle cx='12' cy='11' r='8.5' fill='#{$form-switch-color}'/%3e%3c/g%3e%3cdefs%3e%3cfilter id='a' x='0' y='0' width='22' height='22' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3e%3cfeFlood flood-opacity='0' result='BackgroundImageFix'/%3e%3cfeColorMatrix in='SourceAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3e%3cfeOffset dy='2'/%3e%3cfeGaussianBlur stdDeviation='2'/%3e%3cfeColorMatrix values='0 0 0 0 0.180392 0 0 0 0 0.14902 0 0 0 0 0.239216 0 0 0 0.16 0'/%3e%3cfeBlend in2='BackgroundImageFix' result='effect1_dropShadow_6488_3264'/%3e%3cfeBlend in='SourceGraphic' in2='effect1_dropShadow_6488_3264' result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e") !default;

$form-switch-focus-bg-image: url("data:image/svg+xml,%3csvg width='22' height='22' viewBox='0 0 22 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cg filter='url(%23a)'%3e%3ccircle cx='12' cy='11' r='8.5' fill='#{$form-switch-color}'/%3e%3c/g%3e%3cdefs%3e%3cfilter id='a' x='0' y='0' width='22' height='22' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3e%3cfeFlood flood-opacity='0' result='BackgroundImageFix'/%3e%3cfeColorMatrix in='SourceAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3e%3cfeOffset dy='2'/%3e%3cfeGaussianBlur stdDeviation='2'/%3e%3cfeColorMatrix values='0 0 0 0 0.180392 0 0 0 0 0.14902 0 0 0 0 0.239216 0 0 0 0.16 0'/%3e%3cfeBlend in2='BackgroundImageFix' result='effect1_dropShadow_6488_3264'/%3e%3cfeBlend in='SourceGraphic' in2='effect1_dropShadow_6488_3264' result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e") !default;
$form-switch-checked-bg-image: url("data:image/svg+xml,%3csvg width='22' height='22' viewBox='0 0 22 22' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cg filter='url(%23a)'%3e%3ccircle cx='12' cy='11' r='8.5' fill='#{$form-switch-color}'/%3e%3c/g%3e%3cdefs%3e%3cfilter id='a' x='0' y='0' width='22' height='22' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3e%3cfeFlood flood-opacity='0' result='BackgroundImageFix'/%3e%3cfeColorMatrix in='SourceAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3e%3cfeOffset dy='2'/%3e%3cfeGaussianBlur stdDeviation='2'/%3e%3cfeColorMatrix values='0 0 0 0 0.180392 0 0 0 0 0.14902 0 0 0 0 0.239216 0 0 0 0.16 0'/%3e%3cfeBlend in2='BackgroundImageFix' result='effect1_dropShadow_6488_3264'/%3e%3cfeBlend in='SourceGraphic' in2='effect1_dropShadow_6488_3264' result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e") !default;
$form-switch-checked-bg-position: 95% center !default;
$form-switch-bg: rgba-to-hex($gray-100, $rgba-to-hex-bg) !default; // (C)
$form-switch-box-shadow: 0 0 0.25rem 0 rgba(0, 0, 0, 0.16) inset !default; // (C)
// scss-docs-end form-switch-variables

//input-group-variables
$input-group-addon-color: $headings-color !default;
$input-group-addon-bg: $input-bg !default;
$input-group-addon-border-color: $input-border-color !default;
// scss-docs-end input-group-variables

// scss-docs-start form-select-variables
$form-select-padding-y: $input-padding-y !default;
$form-select-padding-x: $input-padding-x !default;
$form-select-indicator-padding: $form-select-padding-x + 1rem !default;
$form-select-disabled-color: $text-muted !default;
$form-select-disabled-bg: $input-disabled-bg !default;
$form-select-disabled-border-color: $border-color !default;
$form-select-bg-size: 22px 24px !default;
$form-select-indicator: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="#{$black}" fill-opacity="0.9"/></svg>') !default;
$form-select-disabled-indicator: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="#{$text-muted}" fill-opacity="0.9"/></svg>') !default; // (C)

$form-select-border-width: $input-border-width !default;

$form-select-focus-box-shadow: none !default;

$form-select-padding-y-sm: $input-padding-y-sm !default;
$form-select-padding-x-sm: $input-padding-x-sm !default;
$form-select-font-size-sm: $font-size-sm !default;
$form-select-border-radius-sm: $input-border-radius-sm !default;

$form-select-padding-y-lg: $input-padding-y-lg !default;
$form-select-padding-x-lg: $input-padding-x-lg !default;
$form-select-font-size-lg: $input-font-size-lg !default;
$form-select-border-radius-lg: $input-border-radius-lg !default;

// scss-docs-end form-select-variables

// scss-docs-start form-range-variables
$form-range-track-height: 0.25rem !default;
$form-range-track-box-shadow: none !default;
$form-range-track-disabled-bg: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default; // (C)
$form-range-thumb-width: 1.25rem !default;
$form-range-thumb-height: $form-range-thumb-width !default;
$form-range-thumb-box-shadow: $box-shadow-sm !default;
$form-range-thumb-disabled-bg: rgba-to-hex($gray-400, $rgba-to-hex-bg) !default;
// scss-docs-end form-range-variables

// scss-docs-start form-file-variables
$form-file-button-bg: $input-group-addon-bg !default;
$form-file-button-hover-bg: shade-color($form-file-button-bg, 5%) !default;
// scss-docs-end form-file-variables

// scss-docs-start form-floating-variables
$form-floating-padding-x: $input-padding-x !default;
$form-floating-padding-y: $input-padding-y !default;
$form-floating-line-height: $line-height-base !default;
$form-floating-input-padding-t: ($form-floating-padding-y * 1.5) !default;
$form-floating-input-padding-b: calc(($form-floating-padding-y * 0.5) + $border-width) !default;
$form-floating-input-bg: rgba-to-hex($gray-50, $rgba-to-hex-bg) !default; // (C)
$form-floating-label-padding-x: 0.375rem !default; // (C)
$form-floating-label-margin: 0.625rem !default; // (C)
$form-floating-hover-bg: rgba-to-hex(rgba($black, 0.079), $rgba-to-hex-bg) !default; // (C)
$form-floating-height: calc(($font-size-base * $form-floating-line-height) + ($form-floating-padding-y * 2)) !default;
$form-floating-disabled-opacity: 0.38 !default; // (c)
$form-floating-label-height: 0.75rem !default;
$form-floating-label-opacity: 1 !default;
$form-floating-transition:
  opacity 0.2s ease-in-out,
  transform 0.2s ease-in-out !default;
$form-floating-label-transform: scale(0.867) translateY(-0.6rem) translateX(-0.0625rem) !default;
$form-floating-label-disabled-color: $text-muted !default;

// Outline
$form-floating-outline-label-transform: translateY(-0.8rem) translateX(-$input-focus-border-width) !default; // (C)

// * Navs
// *******************************************************************************

$nav-spacer: 0.25rem !default; // (C)

$nav-link-padding-y: 0.5435rem !default;
$nav-link-padding-x: 1.375rem !default;
$nav-link-font-size: $font-size-base !default;
$nav-link-font-weight: $font-weight-medium !default;
$nav-link-color: $headings-color !default;
$nav-link-disabled-color: $text-lighter !default;
$nav-link-line-height: $line-height-base !default; // (C)

$nav-link-padding-y-lg: 0.5825rem !default; // (C)
$nav-link-padding-x-lg: 1.375rem !default; // (C)
$nav-link-line-height-lg: $line-height-base !default; // (C)

$nav-link-padding-y-sm: 0.3125rem !default; // (C)
$nav-link-padding-x-sm: 0.875rem !default; // (C)
$nav-link-line-height-sm: $line-height-base !default; // (C)

$nav-tabs-border-color: $border-color !default;
$nav-tabs-border-width: 0 !default;
$nav-tabs-link-hover-border-color: null !default;
$nav-tabs-link-active-color: $component-active-bg !default;
$nav-tabs-link-active-bg: $white !default;
$nav-tabs-link-active-border-color: $component-active-bg !default;

$nav-pills-padding-y: $nav-link-padding-y !default; // (C)
$nav-pills-padding-x: $nav-link-padding-x !default; // (C)

$nav-pills-link-hover-bg: rgba-to-hex(rgba($primary, 0.16), $rgba-to-hex-bg) !default; // (C)

$nav-pills-link-active-color: $white !default;
$nav-pills-link-active-bg: transparent !default;

// * Navbar
// *******************************************************************************

$navbar-nav-link-padding-x: 0.3rem !default;

$navbar-toggler-padding-y: 0.5rem !default;
$navbar-toggler-padding-x: 0.7rem !default;
$navbar-toggler-font-size: 0.625rem !default;

$navbar-dark-color: rgba($white, 0.8) !default;
$navbar-dark-hover-color: $white !default;
$navbar-dark-active-color: $white !default;
$navbar-dark-disabled-color: rgba($white, 0.4) !default;

$navbar-light-color: $headings-color !default;
$navbar-light-hover-color: $body-color !default;
$navbar-light-active-color: $body-color !default;
$navbar-light-disabled-color: $gray-300 !default;
$navbar-light-toggler-border-color: rgba($black, 0.06) !default;

// * Dropdowns
// *******************************************************************************

$dropdown-padding-y: 0.5rem !default;
$dropdown-font-size: $font-size-base !default;
$dropdown-bg: $white !default;
$dropdown-border-color: $floating-component-border-color !default;
$dropdown-border-width: 0 !default;
$dropdown-divider-bg: $dropdown-border-color !default;
$dropdown-box-shadow: $box-shadow-lg !default;

$dropdown-inner-border-radius: 0 !default;

$dropdown-link-color: $headings-color !default;
$dropdown-link-hover-color: $dropdown-link-color !default;
$dropdown-link-hover-bg: rgba-to-hex($gray-50, $rgba-to-hex-bg) !default;
$dropdown-link-line-height: $component-line-height !default; // (C)

$dropdown-link-active-color: $primary !default;
$dropdown-link-active-bg: rgba-to-hex(rgba($primary, 0.16), $rgba-to-hex-bg) !default;

$dropdown-item-padding-y: 0.543rem !default;
$dropdown-item-padding-x: 1rem !default;

$dropdown-link-disabled-color: $text-muted !default;
$dropdown-header-color: $text-muted !default;

// * Pagination
// *******************************************************************************

$pagination-padding-y: 0.481rem !default;
$pagination-padding-x: 0.5rem !default;
$pagination-padding-y-sm: 0.4416rem !default;
$pagination-padding-x-sm: 0.375rem !default;
$pagination-padding-y-lg: 0.52rem !default;
$pagination-padding-x-lg: 0.75rem !default;

$pagination-font-size: $font-size-base !default;

$pagination-line-height: $line-height-base !default; // (c)

$pagination-color: $headings-color !default;
$pagination-bg: rgba-to-hex(rgba($black, 0.08), $rgba-to-hex-bg) !default;
$pagination-border-radius: 50% !default;
$pagination-margin-start: $pagination-padding-x-sm !default;
$pagination-border-color: $text-lighter !default;

$pagination-focus-color: $pagination-color !default;
$pagination-focus-bg: rgba-to-hex($gray-50, $rgba-to-hex-bg) !default;
$pagination-focus-box-shadow: none !default;

$pagination-hover-color: $pagination-color !default;
$pagination-hover-bg: $pagination-focus-bg !default;
$pagination-hover-border-color: $pagination-border-color !default;
$pagination-hover-bg-scale: 84% !default; // (c)

$pagination-active-color: $component-active-color !default;
$pagination-active-bg: $primary !default;
$pagination-active-border-color: $pagination-active-bg !default;

$pagination-disabled-color: $pagination-color !default;
$pagination-disabled-bg: $pagination-bg !default;
$pagination-disabled-border-color: $pagination-border-color !default;
$pagination-disabled-opacity: $btn-disabled-opacity !default; // (c)

$pagination-border-radius-sm: $pagination-border-radius !default;
$pagination-border-radius-lg: $pagination-border-radius-sm !default;

// * Cards
// *******************************************************************************

$card-title-color: $headings-color !default;
$card-spacer-y: $spacer * 1.25 !default;
$card-spacer-x: $spacer * 1.25 !default;
$card-title-spacer-y: $spacer * 0.5 !default;
$card-subtitle-color: rgba-to-hex(rgba($black, 0.55), $rgba-to-hex-bg) !default;
$card-spacer-x-sm: 1rem !default; // (C)
$card-border-width: 0 !default;
$card-border-color: $border-color !default;
$card-border-radius: 0.375rem !default;
$card-inner-border-color: $border-inner-color !default; // (C)
$card-cap-padding-y: $card-spacer-y !default;
$card-bg: $white !default;
$card-cap-bg: transparent !default;
$card-cap-color: $headings-color !default;
$card-img-overlay-padding: 1.5rem !default;
$card-box-shadow: $box-shadow !default;
$card-group-margin: $grid-gutter-width !default;
$card-transition: all 0.2s ease-in-out !default; // (C)

// * Accordion
// *******************************************************************************

$accordion-padding-y: 0.793rem !default;
$accordion-padding-x: 1.25rem !default;
$accordion-body-padding-y: $accordion-padding-x !default;
$accordion-color: $body-color !default;
$accordion-bg: $card-bg !default;
$accordion-border-width: $border-width !default;
$accordion-border-color: $border-color !default;
$accordion-border-radius: $border-radius !default;
$accordion-inner-border-radius: $accordion-border-radius !default;

$accordion-body-padding-y: $accordion-padding-x !default;

$accordion-button-color: $headings-color !default;
$accordion-transition: 0.3s all cubic-bezier(0.4, 0, 0.2, 1) !default;
$accordion-button-active-bg: $accordion-bg !default;
$accordion-button-active-color: $accordion-button-color !default;

$accordion-button-focus-border-color: $accordion-border-color !default;
$accordion-button-focus-box-shadow: none !default;

$accordion-icon-width: 1.25rem !default;
$accordion-icon-color: $accordion-button-color !default;
$accordion-icon-active-color: $accordion-button-active-color !default;

$accordion-button-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'  fill='#{$accordion-icon-color}' viewBox='0 0 24 24'%3E%3Ctitle%3Echevron-down%3C/title%3E%3Cpath d='M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z' /%3E%3C/svg%3E") !default;
$accordion-button-active-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'  fill='#{$accordion-icon-active-color}' viewBox='0 0 24 24'%3E%3Ctitle%3Echevron-down%3C/title%3E%3Cpath d='M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z' /%3E%3C/svg%3E") !default;

$accordion-custom-button-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$accordion-icon-active-color}' viewBox='0 0 24 24'%3E%3Cpath d='M19 11h-6V5h-2v6H5v2h6v6h2v-6h6z'%3E%3C/path%3E%3C/svg%3E") !default;
$accordion-custom-button-active-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$accordion-icon-active-color}' viewBox='0 0 24 24'%3E%3Cpath d='M5 11h14v2H5z'%3E%3C/path%3E%3C/svg%3E") !default;

// * Tooltips
// *******************************************************************************

$tooltip-font-size: $font-size-sm !default;
$tooltip-bg: #1a0e33 !default;
$tooltip-color: $white !default;
$tooltip-border-radius: $border-radius-sm !default;
$tooltip-opacity: 1 !default;
$tooltip-padding-x: 0.75rem !default;
$tooltip-padding-y: 0.317rem !default;

$tooltip-arrow-width: 0.75rem !default;
$tooltip-arrow-height: 0.375rem !default;

// * Popovers
// *******************************************************************************

$popover-bg: $card-bg !default;
$popover-border-width: 0px !default;
$popover-border-color: $gray-100 !default;
$popover-box-shadow: $box-shadow !default;

$popover-header-bg: $card-bg !default;
$popover-header-color: $headings-color !default;
$popover-header-padding-y: $spacer !default;
$popover-header-padding-x: 1.125rem !default;

$popover-body-color: $body-color !default;
$popover-body-padding-y: 1.125rem !default;
$popover-body-padding-x: 1.125rem !default;

// * Toasts
// *******************************************************************************
$toast-padding-y: 0.406rem !default;
$toast-font-size: $font-size-base !default;
$toast-color: $body-color !default;
$toast-background-color: rgba($card-bg, 0.85);
$toast-border-width: 0rem !default;
$toast-border-color: $gray-100 !default;
$toast-border-radius: $border-radius !default;
$toast-header-color: $gray-600 !default;
$toast-header-background-color: rgba($card-bg, 0.85) !default;
$toast-header-border-color: $card-inner-border-color !default;
$toast-box-shadow: $box-shadow !default;
$toast-spacing: 1rem !default;

// * Badges
// *******************************************************************************

$badge-font-size: 0.86667em !default;
$badge-font-weight: $font-weight-medium !default;
$badge-padding-y: 0.236em !default;
$badge-padding-x: 0.9231em !default;
$badge-border-radius: 0.1875rem !default;

$badge-pill-padding-x: 0.583em !default;
$badge-pill-border-radius: 10rem !default;

$badge-height: 1.5rem !default; // (C)
$badge-width: 1.5rem !default; // (C)
$badge-center-font-size: $font-size-sm !default; // (C)

// * Modals
// *******************************************************************************

$modal-inner-padding: 1.25rem !default;

$modal-footer-margin-between: 1rem !default;

$modal-content-color: $body-color !default;
$modal-content-bg: $white !default;
$modal-content-border-color: $border-color !default;
$modal-content-border-width: 0px !default;
$modal-content-border-radius: $border-radius-lg !default;
$modal-content-box-shadow-xs: $box-shadow-xl !default;
$modal-content-box-shadow-sm-up: $box-shadow-xl !default;

$modal-backdrop-bg: #96929e !default;
$modal-backdrop-opacity: 0.5 !default;
$modal-header-border-width: 0 !default;
$modal-header-padding-y: 1.4531rem !default;
$modal-header-padding-x: $modal-inner-padding !default;
$modal-footer-padding: $modal-header-padding-y $modal-header-padding-x !default; // (C)

$modal-lg: 50rem !default;
$modal-md: 35rem !default;
$modal-sm: 22.5rem !default;

$modal-fade-transform: translateY(-100px) scale(0.8) !default;
$modal-show-transform: translateY(0) scale(1) !default;

$modal-transition-duration: 0.15s !default; // (C)
$modal-transition: transform $modal-transition-duration ease-out !default;

$modal-simple-padding: 4rem !default; // (C)
$modal-simple-close-position: 1.5rem !default; // (C)

// * Alerts
// *******************************************************************************

$alert-padding-y: 0.9375rem !default;
$alert-padding-x: 1rem !default;
$alert-bg-scale: 84% !default;
$alert-bg-tint-scale: 84% !default; // (c)
$alert-border-scale: -84% !default;
$alert-color-scale: 16% !default;
$alert-icon-size: 1.875rem !default; // (c)

// * Progress bars
// *******************************************************************************

$progress-height: 0.375rem !default;
$progress-font-size: 0.625rem !default;
$progress-bg-scale: 76% !default; // (C)
$progress-bg: shift-color($primary, $progress-bg-scale, $rgba-to-hex-bg) !default;
$progress-border-radius: $border-radius-pill !default;
$progress-bar-color: $white !default;

//   group
// *******************************************************************************

// scss-docs-start list-group-variables
$list-group-color: $headings-color !default;
$list-group-bg: transparent !default;
$list-group-border-color: $border-color !default;
$list-group-border-radius: $border-radius !default;

$list-group-item-padding-y: 0.5rem !default;
$list-group-item-padding-x: 1.25rem !default;

$list-group-item-bg-scale: -90% !default;
$list-group-item-color-scale: 10% !default;
$list-group-item-bg-hover-scale: 6% !default; //  (c)

$list-group-hover-bg: rgba-to-hex($gray-50, $rgba-to-hex-bg) !default;
$list-group-active-color: $headings-color !default;
$list-group-active-bg: rgba-to-hex(rgba($primary, 0.08), $rgba-to-hex-bg) !default;
$list-group-active-border-color: $list-group-border-color !default;

$list-group-disabled-color: $text-muted !default;
$list-group-disabled-bg: $list-group-bg !default;

$list-group-action-color: $list-group-active-color !default;
$list-group-action-hover-color: $list-group-action-color !default;

$list-group-action-active-color: $primary !default;
$list-group-action-active-bg: rgba-to-hex(rgba($primary, 0.08), $rgba-to-hex-bg) !default;

// scss-docs-end list-group-variables

// * Image thumbnails
// *******************************************************************************

$thumbnail-padding: 0 !default;
$thumbnail-bg: transparent !default;
$thumbnail-border-width: 0px !default;
$thumbnail-border-radius: 0px !default;

// * Figures
// *******************************************************************************

$figure-caption-color: $text-muted !default;

// * Breadcrumbs
// *******************************************************************************

$breadcrumb-font-size: $font-size-base !default;
$breadcrumb-padding-y: 0 !default;
$breadcrumb-padding-x: 0 !default;
$breadcrumb-item-padding-x: 0.5rem !default;
$breadcrumb-margin-bottom: 1rem !default;
$breadcrumb-bg: transparent !default;
$breadcrumb-divider-color: $body-color !default;
$breadcrumb-active-color: $headings-color !default;
$breadcrumb-divider: '/' !default;
$breadcrumb-divider-flipped: '\\' !default;
$breadcrumb-color: $breadcrumb-divider-color !default; // (C)

// Spinners
// *******************************************************************************

$spinner-width-lg: 3rem !default; // (C)
$spinner-height-lg: $spinner-width-lg !default; // (C)
$spinner-border-width-lg: 0.3em !default; // (C)

// * Close
// *******************************************************************************

$btn-close-width: 0.65rem !default;
$btn-close-color: $body-color !default;
$btn-close-focus-shadow: none !default;
$btn-close-focus-opacity: 0.75 !default;
$btn-close-opacity: 1 !default;
$close-font-weight: 300 !default; // (C)

// * Offcanvas
// *******************************************************************************

// scss-docs-start offcanvas-variables
$offcanvas-transition-duration: 0.25s !default;
$offcanvas-bg-color: $modal-content-bg !default;
$offcanvas-color: $modal-content-color !default;
// scss-docs-end offcanvas-variables

// Utilities
$overflows: auto, hidden, scroll, visible !default;

// Config
$rtl-support: false !default;
$dark-style: false !default;
