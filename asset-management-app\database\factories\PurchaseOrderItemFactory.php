<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\PurchaseOrderItem;
use App\Models\PurchaseOrder;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PurchaseOrderItem>
 */
class PurchaseOrderItemFactory extends Factory
{
    protected $model = PurchaseOrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $quantity = $this->faker->numberBetween(1, 10);
        $unitPrice = $this->faker->numberBetween(10000, 1000000);
        
        return [
            'purchase_order_id' => PurchaseOrder::factory(),
            'item_name' => $this->faker->words(3, true),
            'item_description' => $this->faker->optional()->sentence(),
            'item_code' => $this->faker->optional()->bothify('ITM-####'),
            'brand' => $this->faker->optional()->company(),
            'model' => $this->faker->optional()->bothify('MDL-####'),
            'specifications' => $this->faker->optional()->paragraph(),
            'quantity' => $quantity,
            'unit' => $this->faker->randomElement(['pcs', 'unit', 'set', 'box', 'kg', 'meter', 'liter']),
            'unit_price' => $unitPrice,
            'total_price' => $quantity * $unitPrice,
            'notes' => $this->faker->optional()->sentence(),
            'sort_order' => $this->faker->numberBetween(1, 10),
            'receive_status' => $this->faker->randomElement(['pending', 'partial', 'completed']),
            'received_quantity' => 0,
        ];
    }

    /**
     * Indicate that the item is pending receipt.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'receive_status' => 'pending',
            'received_quantity' => 0,
        ]);
    }

    /**
     * Indicate that the item is partially received.
     */
    public function partiallyReceived(): static
    {
        return $this->state(function (array $attributes) {
            $quantity = $attributes['quantity'] ?? $this->faker->numberBetween(1, 10);
            $receivedQuantity = $this->faker->numberBetween(1, $quantity - 1);
            
            return [
                'receive_status' => 'partial',
                'received_quantity' => $receivedQuantity,
            ];
        });
    }

    /**
     * Indicate that the item is fully received.
     */
    public function fullyReceived(): static
    {
        return $this->state(function (array $attributes) {
            $quantity = $attributes['quantity'] ?? $this->faker->numberBetween(1, 10);
            
            return [
                'receive_status' => 'completed',
                'received_quantity' => $quantity,
            ];
        });
    }

    /**
     * Set specific quantity and unit price.
     */
    public function withQuantityAndPrice(int $quantity, int $unitPrice): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total_price' => $quantity * $unitPrice,
        ]);
    }
}
