@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Divisi - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Divisi /</span> Edit Divisi
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Edit Divisi</h5>
          <a href="{{ route('master.divisions.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.divisions.update', $division) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name">Nama Divisi <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name', $division->name) }}" 
                       placeholder="Contoh: Information Technology">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Divisi <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                       id="code" name="code" value="{{ old('code', $division->code) }}" 
                       placeholder="Contoh: IT" maxlength="10">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode unik untuk divisi (maksimal 10 karakter)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi divisi dan tanggung jawabnya">{{ old('description', $division->description) }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <hr class="my-4">
            <h6 class="mb-3">Informasi Kepala Divisi</h6>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="head_name">Nama Kepala Divisi</label>
                <input type="text" class="form-control @error('head_name') is-invalid @enderror" 
                       id="head_name" name="head_name" value="{{ old('head_name', $division->head_name) }}" 
                       placeholder="Contoh: Budi Santoso">
                @error('head_name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="head_email">Email Kepala Divisi</label>
                <input type="email" class="form-control @error('head_email') is-invalid @enderror" 
                       id="head_email" name="head_email" value="{{ old('head_email', $division->head_email) }}" 
                       placeholder="Contoh: <EMAIL>">
                @error('head_email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="head_phone">Telepon Kepala Divisi</label>
                <input type="text" class="form-control @error('head_phone') is-invalid @enderror" 
                       id="head_phone" name="head_phone" value="{{ old('head_phone', $division->head_phone) }}" 
                       placeholder="Contoh: 08123456789">
                @error('head_phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">&nbsp;</label>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                           {{ old('is_active', $division->is_active) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                      Aktif
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.divisions.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Update
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Divisi</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Data Saat Ini:</h6>
            <p class="mb-2">Nama: <strong>{{ $division->name }}</strong></p>
            <p class="mb-2">Kode: <strong>{{ $division->code }}</strong></p>
            <p class="mb-2">Status: 
              <span class="badge bg-{{ $division->is_active ? 'success' : 'secondary' }}">
                {{ $division->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </p>
            <p class="mb-2">User: <strong>{{ $division->users->count() }} user</strong></p>
            <p class="mb-0">Dibuat: {{ $division->created_at->format('d/m/Y') }}</p>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Peringatan:</h6>
            <ul class="mb-0">
              <li>Hati-hati mengubah kode divisi jika sudah ada user terkait</li>
              <li>Pastikan kode tetap unik</li>
              <li>Non-aktifkan divisi jika tidak digunakan lagi</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase code
  const codeInput = document.getElementById('code');
  codeInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  });

  // Format phone number
  const phoneInput = document.getElementById('head_phone');
  phoneInput.addEventListener('input', function() {
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });
});
</script>
@endsection
