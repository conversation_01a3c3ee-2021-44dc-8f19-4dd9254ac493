@extends('layouts.contentNavbarLayout')

@section('title', 'Buat Approval Workflow - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Approval /</span> Buat Workflow
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Buat Approval Workflow</h5>
          <a href="{{ route('master.approval-workflows.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.approval-workflows.store') }}" method="POST" id="workflowForm">
            @csrf
            
            <h6 class="mb-3">Informasi Workflow</h6>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name">Nama Workflow <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" 
                       placeholder="Contoh: Permohonan Asset Standar">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Workflow <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                       id="code" name="code" value="{{ old('code') }}" 
                       placeholder="Contoh: ASSET_STANDARD" maxlength="50">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode unik untuk workflow (huruf besar, underscore)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi workflow dan kapan digunakan">{{ old('description') }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-4">
                <label class="form-label" for="module">Module <span class="text-danger">*</span></label>
                <select class="form-select @error('module') is-invalid @enderror" id="module" name="module">
                  <option value="">Pilih Module</option>
                  <option value="asset_requests" {{ old('module') === 'asset_requests' ? 'selected' : '' }}>Asset Requests</option>
                  <option value="purchase_orders" {{ old('module') === 'purchase_orders' ? 'selected' : '' }}>Purchase Orders</option>
                  <option value="budget_requests" {{ old('module') === 'budget_requests' ? 'selected' : '' }}>Budget Requests</option>
                </select>
                @error('module')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="priority">Prioritas <span class="text-danger">*</span></label>
                <input type="number" class="form-control @error('priority') is-invalid @enderror" 
                       id="priority" name="priority" value="{{ old('priority', 1) }}" min="0" max="100">
                @error('priority')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Semakin tinggi semakin prioritas</div>
              </div>
              <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                         {{ old('is_active', true) ? 'checked' : '' }}>
                  <label class="form-check-label" for="is_active">
                    Aktif
                  </label>
                </div>
              </div>
            </div>

            <hr class="my-4">
            <h6 class="mb-3">Kondisi Workflow</h6>
            <p class="text-muted mb-3">Tentukan kondisi kapan workflow ini akan digunakan</p>

            <div id="conditions-container">
              <div class="condition-item border rounded p-3 mb-3">
                <div class="row">
                  <div class="col-md-3">
                    <label class="form-label">Field</label>
                    <select class="form-select condition-field" name="conditions[0][field]">
                      <option value="">Pilih Field</option>
                      <option value="estimated_price">Total Harga</option>
                      <option value="quantity">Jumlah</option>
                      <option value="priority">Prioritas</option>
                      <option value="category_id">Kategori</option>
                      <option value="branch_id">Cabang</option>
                      <option value="division_id">Divisi</option>
                      <option value="user_role">Role User</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">Operator</label>
                    <select class="form-select condition-operator" name="conditions[0][operator]">
                      <option value="">Pilih Operator</option>
                      <option value="equals">Sama dengan (=)</option>
                      <option value="not_equals">Tidak sama dengan (≠)</option>
                      <option value="greater_than">Lebih besar (>)</option>
                      <option value="greater_than_or_equal">Lebih besar sama dengan (≥)</option>
                      <option value="less_than">Lebih kecil (<)</option>
                      <option value="less_than_or_equal">Lebih kecil sama dengan (≤)</option>
                      <option value="in">Dalam list</option>
                      <option value="not_in">Tidak dalam list</option>
                      <option value="contains">Mengandung</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Nilai</label>
                    <input type="text" class="form-control condition-value" name="conditions[0][value]" 
                           placeholder="Masukkan nilai">
                  </div>
                  <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger w-100 remove-condition" disabled>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <button type="button" class="btn btn-outline-primary mb-4" id="add-condition">
              <i class="ri-add-line me-1"></i>Tambah Kondisi
            </button>

            <hr class="my-4">
            <h6 class="mb-3">Level Approval</h6>
            <p class="text-muted mb-3">Tentukan level-level approval yang diperlukan</p>

            <div id="levels-container">
              <div class="level-item border rounded p-3 mb-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <h6 class="mb-0">Level 1</h6>
                  <button type="button" class="btn btn-outline-danger btn-sm remove-level" disabled>
                    <i class="ri-delete-bin-line"></i>
                  </button>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Nama Level <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="levels[0][level_name]" 
                           placeholder="Contoh: Review Supervisor">
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Tipe Approver <span class="text-danger">*</span></label>
                    <select class="form-select approver-type" name="levels[0][approver_type]">
                      <option value="">Pilih Tipe</option>
                      <option value="role">Berdasarkan Role</option>
                      <option value="user">User Spesifik</option>
                      <option value="position">Berdasarkan Posisi</option>
                    </select>
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">Deskripsi</label>
                  <textarea class="form-control" name="levels[0][description]" rows="2" 
                            placeholder="Deskripsi level approval ini"></textarea>
                </div>

                <div class="approver-config mb-3" style="display: none;">
                  <!-- Dynamic content based on approver type -->
                </div>

                <div class="row mb-3">
                  <div class="col-md-3">
                    <label class="form-label">Timeout (Jam)</label>
                    <input type="number" class="form-control" name="levels[0][timeout_hours]" 
                           placeholder="24" min="1">
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">Aksi Timeout</label>
                    <select class="form-select" name="levels[0][timeout_action]">
                      <option value="escalate">Escalate</option>
                      <option value="approve">Auto Approve</option>
                      <option value="reject">Auto Reject</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check mt-4">
                      <input class="form-check-input" type="checkbox" name="levels[0][is_required]" value="1" checked>
                      <label class="form-check-label">Wajib</label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check mt-4">
                      <input class="form-check-input" type="checkbox" name="levels[0][can_skip]" value="1">
                      <label class="form-check-label">Bisa Di-skip</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <button type="button" class="btn btn-outline-primary mb-4" id="add-level">
              <i class="ri-add-line me-1"></i>Tambah Level
            </button>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.approval-workflows.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan Workflow
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Pembuatan</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips Workflow:</h6>
            <ul class="mb-0">
              <li><strong>Prioritas:</strong> Workflow dengan prioritas tinggi akan dipilih terlebih dahulu</li>
              <li><strong>Kondisi:</strong> Semua kondisi harus terpenuhi agar workflow digunakan</li>
              <li><strong>Level:</strong> Minimal 1 level approval diperlukan</li>
              <li><strong>Timeout:</strong> Waktu maksimal untuk approval di level tersebut</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Contoh Kondisi:</h6>
            <ul class="mb-0">
              <li>Total Harga > 10000000</li>
              <li>Prioritas = urgent</li>
              <li>Kategori = PC-LAPTOP</li>
              <li>Role User = staff</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Tipe Approver:</h6>
            <ul class="mb-0">
              <li><strong>Role:</strong> Semua user dengan role tertentu</li>
              <li><strong>User:</strong> User spesifik yang ditentukan</li>
              <li><strong>Posisi:</strong> Berdasarkan struktur organisasi</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let conditionIndex = 1;
  let levelIndex = 1;

  // Auto generate code from name
  document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const code = name.toUpperCase()
      .replace(/[^A-Z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .substring(0, 50);
    document.getElementById('code').value = code;
  });

  // Add condition
  document.getElementById('add-condition').addEventListener('click', function() {
    const container = document.getElementById('conditions-container');
    const conditionHtml = `
      <div class="condition-item border rounded p-3 mb-3">
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Field</label>
            <select class="form-select condition-field" name="conditions[${conditionIndex}][field]">
              <option value="">Pilih Field</option>
              <option value="estimated_price">Total Harga</option>
              <option value="quantity">Jumlah</option>
              <option value="priority">Prioritas</option>
              <option value="category_id">Kategori</option>
              <option value="branch_id">Cabang</option>
              <option value="division_id">Divisi</option>
              <option value="user_role">Role User</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Operator</label>
            <select class="form-select condition-operator" name="conditions[${conditionIndex}][operator]">
              <option value="">Pilih Operator</option>
              <option value="equals">Sama dengan (=)</option>
              <option value="not_equals">Tidak sama dengan (≠)</option>
              <option value="greater_than">Lebih besar (>)</option>
              <option value="greater_than_or_equal">Lebih besar sama dengan (≥)</option>
              <option value="less_than">Lebih kecil (<)</option>
              <option value="less_than_or_equal">Lebih kecil sama dengan (≤)</option>
              <option value="in">Dalam list</option>
              <option value="not_in">Tidak dalam list</option>
              <option value="contains">Mengandung</option>
            </select>
          </div>
          <div class="col-md-4">
            <label class="form-label">Nilai</label>
            <input type="text" class="form-control condition-value" name="conditions[${conditionIndex}][value]" 
                   placeholder="Masukkan nilai">
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-outline-danger w-100 remove-condition">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
        </div>
      </div>
    `;
    container.insertAdjacentHTML('beforeend', conditionHtml);
    conditionIndex++;
    updateRemoveButtons();
  });

  // Remove condition
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-condition')) {
      e.target.closest('.condition-item').remove();
      updateRemoveButtons();
    }
  });

  // Add level
  document.getElementById('add-level').addEventListener('click', function() {
    const container = document.getElementById('levels-container');
    const levelHtml = `
      <div class="level-item border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Level ${levelIndex + 1}</h6>
          <button type="button" class="btn btn-outline-danger btn-sm remove-level">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Nama Level <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="levels[${levelIndex}][level_name]" 
                   placeholder="Contoh: Review Supervisor">
          </div>
          <div class="col-md-6">
            <label class="form-label">Tipe Approver <span class="text-danger">*</span></label>
            <select class="form-select approver-type" name="levels[${levelIndex}][approver_type]">
              <option value="">Pilih Tipe</option>
              <option value="role">Berdasarkan Role</option>
              <option value="user">User Spesifik</option>
              <option value="position">Berdasarkan Posisi</option>
            </select>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label">Deskripsi</label>
          <textarea class="form-control" name="levels[${levelIndex}][description]" rows="2" 
                    placeholder="Deskripsi level approval ini"></textarea>
        </div>

        <div class="approver-config mb-3" style="display: none;">
          <!-- Dynamic content based on approver type -->
        </div>

        <div class="row mb-3">
          <div class="col-md-3">
            <label class="form-label">Timeout (Jam)</label>
            <input type="number" class="form-control" name="levels[${levelIndex}][timeout_hours]" 
                   placeholder="24" min="1">
          </div>
          <div class="col-md-3">
            <label class="form-label">Aksi Timeout</label>
            <select class="form-select" name="levels[${levelIndex}][timeout_action]">
              <option value="escalate">Escalate</option>
              <option value="approve">Auto Approve</option>
              <option value="reject">Auto Reject</option>
            </select>
          </div>
          <div class="col-md-3">
            <div class="form-check mt-4">
              <input class="form-check-input" type="checkbox" name="levels[${levelIndex}][is_required]" value="1" checked>
              <label class="form-check-label">Wajib</label>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-check mt-4">
              <input class="form-check-input" type="checkbox" name="levels[${levelIndex}][can_skip]" value="1">
              <label class="form-check-label">Bisa Di-skip</label>
            </div>
          </div>
        </div>
      </div>
    `;
    container.insertAdjacentHTML('beforeend', levelHtml);
    levelIndex++;
    updateRemoveLevelButtons();
  });

  // Remove level
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-level')) {
      e.target.closest('.level-item').remove();
      updateRemoveLevelButtons();
      updateLevelNumbers();
    }
  });

  // Handle approver type change
  document.addEventListener('change', function(e) {
    if (e.target.classList.contains('approver-type')) {
      const configDiv = e.target.closest('.level-item').querySelector('.approver-config');
      const type = e.target.value;
      const levelIndex = Array.from(e.target.closest('.level-item').parentNode.children).indexOf(e.target.closest('.level-item'));
      
      if (type === 'role') {
        configDiv.innerHTML = `
          <label class="form-label">Pilih Role <span class="text-danger">*</span></label>
          <select class="form-select" name="levels[${levelIndex}][approver_config][role_ids][]" multiple>
            @foreach($roles as $role)
              <option value="{{ $role->id }}">{{ $role->name }}</option>
            @endforeach
          </select>
          <div class="form-text">Pilih satu atau lebih role yang bisa approve</div>
        `;
        configDiv.style.display = 'block';
      } else if (type === 'user') {
        configDiv.innerHTML = `
          <label class="form-label">Pilih User <span class="text-danger">*</span></label>
          <select class="form-select" name="levels[${levelIndex}][approver_config][user_ids][]" multiple>
            @foreach($users as $user)
              <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->username }})</option>
            @endforeach
          </select>
          <div class="form-text">Pilih user spesifik yang bisa approve</div>
        `;
        configDiv.style.display = 'block';
      } else if (type === 'position') {
        configDiv.innerHTML = `
          <label class="form-label">Pilih Posisi <span class="text-danger">*</span></label>
          <select class="form-select" name="levels[${levelIndex}][approver_config][position]">
            <option value="">Pilih Posisi</option>
            <option value="direct_supervisor">Supervisor Langsung</option>
            <option value="branch_manager">Manager Cabang</option>
            <option value="division_head">Kepala Divisi</option>
          </select>
          <div class="form-text">Berdasarkan struktur organisasi</div>
        `;
        configDiv.style.display = 'block';
      } else {
        configDiv.style.display = 'none';
      }
    }
  });

  function updateRemoveButtons() {
    const conditions = document.querySelectorAll('.condition-item');
    conditions.forEach((condition, index) => {
      const removeBtn = condition.querySelector('.remove-condition');
      removeBtn.disabled = conditions.length <= 1;
    });
  }

  function updateRemoveLevelButtons() {
    const levels = document.querySelectorAll('.level-item');
    levels.forEach((level, index) => {
      const removeBtn = level.querySelector('.remove-level');
      removeBtn.disabled = levels.length <= 1;
    });
  }

  function updateLevelNumbers() {
    const levels = document.querySelectorAll('.level-item');
    levels.forEach((level, index) => {
      level.querySelector('h6').textContent = `Level ${index + 1}`;
    });
  }

  // Initialize
  updateRemoveButtons();
  updateRemoveLevelButtons();
});
</script>
@endsection
