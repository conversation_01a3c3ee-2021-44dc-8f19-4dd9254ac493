<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->string('nik')->unique()->comment('NIK Karyawan');
            $table->string('full_name')->comment('<PERSON><PERSON>');
            $table->foreignId('branch_id')->constrained('branches')->comment('Cabang');
            $table->foreignId('division_id')->constrained('divisions')->comment('Divisi');
            $table->string('department')->nullable()->comment('Bagian/Departemen');
            $table->string('position')->comment('Jabatan');
            $table->string('email')->nullable()->unique()->comment('Email');
            $table->string('phone')->nullable()->comment('No. Telepon');
            $table->date('join_date')->nullable()->comment('Tanggal Bergabung');
            $table->date('birth_date')->nullable()->comment('Tanggal Lahir');
            $table->enum('gender', ['male', 'female'])->nullable()->comment('Jenis Kelamin');
            $table->text('address')->nullable()->comment('Alamat');
            $table->string('employee_status')->default('permanent')->comment('Status Karyawan (permanent, contract, intern)');
            $table->boolean('is_active')->default(true)->comment('Status Aktif');
            $table->text('notes')->nullable()->comment('Catatan');
            $table->timestamps();

            // Indexes
            $table->index(['branch_id', 'is_active']);
            $table->index(['division_id', 'is_active']);
            $table->index(['nik', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
