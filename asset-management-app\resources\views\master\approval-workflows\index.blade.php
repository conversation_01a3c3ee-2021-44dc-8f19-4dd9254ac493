@extends('layouts.contentNavbarLayout')

@section('title', 'Master Approval Workflow - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Master Approval Workflow
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('master.approval-workflows.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Buat Workflow
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('master.approval-workflows.index') }}">
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label"><PERSON><PERSON><PERSON></label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                   placeholder="Nama, kode, atau deskripsi...">
          </div>
          <div class="col-md-3">
            <label class="form-label">Module</label>
            <select class="form-select" name="module">
              <option value="">Semua Module</option>
              <option value="asset_requests" {{ request('module') === 'asset_requests' ? 'selected' : '' }}>Asset Requests</option>
              <option value="purchase_orders" {{ request('module') === 'purchase_orders' ? 'selected' : '' }}>Purchase Orders</option>
              <option value="budget_requests" {{ request('module') === 'budget_requests' ? 'selected' : '' }}>Budget Requests</option>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" name="is_active">
              <option value="">Semua Status</option>
              <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Aktif</option>
              <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Non-Aktif</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('master.approval-workflows.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Workflows Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Approval Workflow ({{ $workflows->total() }} workflow)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Workflow</th>
            <th>Module</th>
            <th>Levels</th>
            <th>Prioritas</th>
            <th>Kondisi</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($workflows as $workflow)
          <tr>
            <td>
              <div>
                <strong>{{ $workflow->name }}</strong>
                <br><small class="text-muted">{{ $workflow->code }}</small>
                @if($workflow->description)
                  <br><small class="text-muted">{{ Str::limit($workflow->description, 50) }}</small>
                @endif
              </div>
            </td>
            <td>
              <span class="badge bg-info">
                {{ ucfirst(str_replace('_', ' ', $workflow->module)) }}
              </span>
            </td>
            <td>
              <div>
                <span class="badge bg-primary">{{ $workflow->levels->count() }} Level</span>
                @if($workflow->levels->count() > 0)
                  <br><small class="text-muted">
                    @foreach($workflow->levels->take(2) as $level)
                      {{ $level->level_name }}@if(!$loop->last), @endif
                    @endforeach
                    @if($workflow->levels->count() > 2)
                      ...
                    @endif
                  </small>
                @endif
              </div>
            </td>
            <td>
              <span class="badge bg-{{ $workflow->priority > 5 ? 'danger' : ($workflow->priority > 2 ? 'warning' : 'success') }}">
                {{ $workflow->priority }}
              </span>
            </td>
            <td>
              <div>
                @if(is_array($workflow->conditions) && count($workflow->conditions) > 0)
                  <span class="badge bg-secondary">{{ count($workflow->conditions) }} Kondisi</span>
                  <br><small class="text-muted">
                    @foreach(array_slice($workflow->conditions, 0, 2) as $condition)
                      {{ $condition['field'] ?? '' }} {{ $condition['operator'] ?? '' }}
                      @if(!$loop->last), @endif
                    @endforeach
                    @if(count($workflow->conditions) > 2)
                      ...
                    @endif
                  </small>
                @else
                  <span class="text-muted">Tanpa kondisi</span>
                @endif
              </div>
            </td>
            <td>
              <span class="badge bg-{{ $workflow->is_active ? 'success' : 'secondary' }}">
                {{ $workflow->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('master.approval-workflows.show', $workflow) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('master.approval-workflows.edit', $workflow) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  <div class="dropdown-divider"></div>
                  @if($workflow->is_active)
                    <form action="{{ route('master.approval-workflows.update', $workflow) }}" method="POST" class="d-inline">
                      @csrf
                      @method('PUT')
                      <input type="hidden" name="is_active" value="0">
                      <button type="submit" class="dropdown-item text-warning">
                        <i class="ri-pause-circle-line me-1"></i> Non-aktifkan
                      </button>
                    </form>
                  @else
                    <form action="{{ route('master.approval-workflows.update', $workflow) }}" method="POST" class="d-inline">
                      @csrf
                      @method('PUT')
                      <input type="hidden" name="is_active" value="1">
                      <button type="submit" class="dropdown-item text-success">
                        <i class="ri-play-circle-line me-1"></i> Aktifkan
                      </button>
                    </form>
                  @endif
                  <form action="{{ route('master.approval-workflows.destroy', $workflow) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus workflow ini?')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="7" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-flow-chart display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada workflow approval</h6>
                <p class="text-muted mb-3">Belum ada workflow approval yang dibuat atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('master.approval-workflows.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Buat Workflow Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($workflows->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $workflows->firstItem() }} - {{ $workflows->lastItem() }} dari {{ $workflows->total() }} data
        </div>
        {{ $workflows->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
