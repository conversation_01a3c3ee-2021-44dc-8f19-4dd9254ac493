<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('maintenance_receipts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('maintenance_id')->constrained('asset_maintenances')->onDelete('cascade');
            $table->foreignId('received_by')->constrained('users');
            $table->date('received_date');
            $table->time('received_time');
            $table->string('receiver_name');
            $table->string('receiver_position');
            $table->enum('asset_condition', ['excellent', 'good', 'fair', 'poor', 'damaged']);
            $table->text('notes')->nullable();
            $table->longText('signature_data')->nullable(); // Base64 signature
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['maintenance_id']);
            $table->index(['received_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('maintenance_receipts');
    }
};
