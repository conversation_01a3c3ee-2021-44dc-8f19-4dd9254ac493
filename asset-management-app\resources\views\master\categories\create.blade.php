@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Kategori - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master <PERSON> /</span> Tambah Kategori
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah Kategori</h5>
          <a href="{{ route('master.categories.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.categories.store') }}" method="POST">
            @csrf
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name"><PERSON><PERSON> <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" 
                       placeholder="Contoh: Komputer & Laptop">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Kategori <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                       id="code" name="code" value="{{ old('code') }}" 
                       placeholder="Contoh: PC-LAPTOP" maxlength="20">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode unik untuk kategori (maksimal 20 karakter)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="4" 
                        placeholder="Deskripsi kategori asset">{{ old('description') }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.categories.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Pengisian</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips:</h6>
            <ul class="mb-0">
              <li><strong>Nama Kategori:</strong> Gunakan nama yang jelas dan mudah dipahami</li>
              <li><strong>Kode Kategori:</strong> Gunakan kode singkat dan unik (contoh: PC-LAPTOP, FURNITURE)</li>
              <li><strong>Deskripsi:</strong> Jelaskan jenis asset yang termasuk dalam kategori ini</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Kode kategori harus unik dan tidak boleh sama</li>
              <li>Kode akan digunakan untuk generate nomor asset</li>
              <li>Pastikan nama kategori mudah dipahami oleh semua user</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Contoh Kategori:</h6>
            <ul class="mb-0">
              <li><strong>PC-LAPTOP:</strong> Komputer & Laptop</li>
              <li><strong>FURNITURE:</strong> Furniture Kantor</li>
              <li><strong>VEHICLE:</strong> Kendaraan</li>
              <li><strong>TOOLS:</strong> Tools & Equipment</li>
              <li><strong>ELECTRONIC:</strong> Elektronik</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase code
  const codeInput = document.getElementById('code');
  codeInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9\-]/g, '');
  });

  // Auto generate code from name
  const nameInput = document.getElementById('name');
  nameInput.addEventListener('input', function() {
    if (!codeInput.value) {
      const words = this.value.split(' ');
      let code = '';
      
      if (words.length === 1) {
        code = words[0].substring(0, 10).toUpperCase();
      } else {
        code = words.map(word => word.substring(0, 3).toUpperCase()).join('-').substring(0, 20);
      }
      
      codeInput.value = code.replace(/[^A-Z0-9\-]/g, '');
    }
  });
});
</script>
@endsection
