<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('document_type'); // asset, purchase_order, etc
            $table->string('format'); // {company_code}-{branch_code}-{category_code}-{asset_type_code}-{number}
            $table->integer('current_number')->default(0);
            $table->integer('year')->default(date('Y'));
            $table->foreignId('branch_id')->constrained();
            $table->foreignId('asset_category_id')->nullable()->constrained();
            $table->foreignId('asset_type_id')->nullable()->constrained();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['document_type', 'branch_id', 'asset_category_id', 'asset_type_id', 'year'], 'doc_num_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_numbers');
    }
};
