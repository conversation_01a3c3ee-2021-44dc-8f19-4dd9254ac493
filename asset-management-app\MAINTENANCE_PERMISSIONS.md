# Asset Maintenance - Role-Based Permission System

## 📋 Overview
Sistem Asset Maintenance dilengkapi dengan role-based permission yang memungkinkan admin untuk mengatur dengan detail siapa yang dapat melakukan tindakan tertentu dalam proses maintenance asset.

## 🔐 Permission List

### Asset Maintenance Permissions
| Permission Slug | Nama Permission | Deskripsi |
|---|---|---|
| `asset_maintenance_view` | Lihat Maintenance Asset | Melihat daftar dan detail maintenance |
| `asset_maintenance_create` | Buat Maintenance Asset | Membuat maintenance baru |
| `asset_maintenance_edit` | Edit Maintenance Asset | Mengedit data maintenance |
| `asset_maintenance_delete` | Hapus Maintenance Asset | Menghapus data maintenance |
| `asset_maintenance_start` | **Mulai Maintenance Asset** | **Me<PERSON>lai proses maintenance** |
| `asset_maintenance_complete` | **Selesaikan Maintenance Asset** | **Menyelesaikan maintenance** |
| `asset_maintenance_cancel` | Batalkan Maintenance Asset | Membatalkan maintenance |
| `asset_maintenance_approve` | Approve Maintenance Asset | Menyetujui maintenance |

## 🎯 Key Features

### 1. **Tombol "Mulai Maintenance"**
- **Permission Required**: `asset_maintenance_start`
- **Fungsi**: Mengubah status maintenance dari "Scheduled" ke "In Progress"
- **Aksi**: Otomatis mengubah status asset menjadi "maintenance"
- **Lokasi**: Halaman detail maintenance dan dropdown aksi di daftar

### 2. **Tombol "Selesaikan Maintenance"**
- **Permission Required**: `asset_maintenance_complete`
- **Fungsi**: Mengubah status maintenance menjadi "Completed"
- **Aksi**: Otomatis mengembalikan status asset ke status sebelumnya
- **Lokasi**: Halaman detail maintenance dan dropdown aksi di daftar

## 👥 Contoh Role Configuration

### 1. **Maintenance Technician** (ID: 11)
**Permissions:**
- ✅ `asset_maintenance_view` - Dapat melihat maintenance
- ✅ `asset_maintenance_start` - **Dapat memulai maintenance**
- ❌ `asset_maintenance_complete` - Tidak dapat menyelesaikan
- ❌ `asset_maintenance_create` - Tidak dapat membuat maintenance baru

**Use Case:** Teknisi lapangan yang bertugas mengerjakan maintenance

### 2. **Maintenance Supervisor** (ID: 12)
**Permissions:**
- ✅ `asset_maintenance_view` - Dapat melihat maintenance
- ❌ `asset_maintenance_start` - Tidak dapat memulai maintenance
- ✅ `asset_maintenance_complete` - **Dapat menyelesaikan maintenance**
- ✅ `asset_maintenance_approve` - Dapat approve maintenance

**Use Case:** Supervisor yang mengawasi dan menyelesaikan maintenance

### 3. **Admin** (Default)
**Permissions:**
- ✅ Semua permission maintenance
- ✅ Dapat melakukan semua tindakan

## 🔧 Cara Mengatur Permission

### 1. **Melalui Master Role**
1. Login sebagai Super Admin
2. Buka **Master Data → Master Role**
3. Pilih role yang ingin diatur
4. Klik **Edit**
5. Scroll ke bagian **Asset Maintenance**
6. Centang permission yang diinginkan:
   - **Mulai Maintenance Asset** - untuk teknisi
   - **Selesaikan Maintenance Asset** - untuk supervisor
7. Klik **Update**

### 2. **Assign Role ke User**
1. Buka **Master Data → Master User**
2. Edit user yang ingin diatur
3. Pilih role yang sesuai di dropdown **Role**
4. Save perubahan

## 🎮 Workflow Example

### Scenario: Maintenance Laptop
1. **Admin** membuat maintenance request
2. **Teknisi** (role: Maintenance Technician):
   - Dapat melihat maintenance
   - Dapat klik **"Mulai Maintenance"**
   - Status asset berubah ke "maintenance"
   - Status maintenance berubah ke "In Progress"
3. **Supervisor** (role: Maintenance Supervisor):
   - Dapat melihat progress maintenance
   - Dapat klik **"Selesaikan Maintenance"**
   - Status maintenance berubah ke "Completed"
   - Status asset kembali ke "active"

## 🚨 Security Features

### 1. **Permission Validation**
- Setiap aksi divalidasi di controller
- User tanpa permission akan mendapat error 403
- Tombol tidak muncul jika user tidak memiliki permission

### 2. **Visual Indicators**
- Tombol yang tidak diizinkan ditampilkan dengan warna abu-abu
- Tooltip menjelaskan mengapa tombol tidak aktif
- Clear separation antara aksi yang diizinkan dan tidak

### 3. **Branch Access Control**
- User hanya dapat mengakses maintenance asset di branch mereka
- Super Admin dapat mengakses semua branch

## 📊 Benefits

### 1. **Separation of Duties**
- Teknisi fokus pada eksekusi
- Supervisor fokus pada approval dan completion
- Admin mengatur keseluruhan sistem

### 2. **Audit Trail**
- Setiap aksi tercatat dengan user yang melakukan
- Timeline maintenance jelas dan terstruktur
- Accountability untuk setiap tahap proses

### 3. **Flexibility**
- Permission dapat dikustomisasi per role
- Mudah menambah role baru sesuai kebutuhan
- Granular control untuk setiap fungsi

## 🔍 Testing

### Test Users Created:
1. **Username**: `teknisi` | **Password**: `********`
   - Role: Maintenance Technician
   - Can: View, Start maintenance
   - Cannot: Complete, Create, Delete

2. **Username**: `supervisor` | **Password**: `********`
   - Role: Maintenance Supervisor  
   - Can: View, Complete, Approve maintenance
   - Cannot: Start, Create, Delete

### Test Scenario:
1. Login sebagai `teknisi`
2. Buka maintenance yang berstatus "Scheduled"
3. Tombol "Mulai Maintenance" akan terlihat dan aktif
4. Tombol "Selesaikan" akan terlihat tapi disabled/tidak aktif
5. Logout dan login sebagai `supervisor`
6. Tombol "Selesaikan" akan aktif untuk maintenance yang "In Progress"

## 📝 Notes

- Super Admin selalu memiliki akses penuh
- Permission dapat diubah kapan saja melalui Master Role
- Perubahan permission berlaku langsung setelah user login ulang
- Sistem mendukung multiple role dengan permission yang berbeda
- Asset status otomatis dikelola berdasarkan status maintenance

---

**Sistem ini memberikan kontrol penuh kepada admin untuk mengatur workflow maintenance sesuai dengan struktur organisasi dan kebutuhan operasional.**
