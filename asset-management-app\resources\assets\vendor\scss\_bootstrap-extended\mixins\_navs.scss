// Navs
// *******************************************************************************

@mixin template-nav-size($padding-y, $padding-x, $font-size, $line-height) {
  > .nav .nav-link,
  &.nav .nav-link {
    padding: $padding-y $padding-x;
    font-size: $font-size;
    line-height: $line-height;
  }
}

@mixin template-nav-variant($parent, $background, $color: null) {
  // .nav-link class hover theme color for basic nav
  .nav .nav-link {
    &:hover,
    &:focus {
      color: shift-color($background, $link-shade-percentage);
    }
  }

  $pills-selector: if($parent== '', '.nav-pills', '#{$parent}.nav-pills, #{$parent} > .nav-pills');

  #{$pills-selector} .nav-link.active {
    &,
    &:hover,
    &:focus {
      background-color: $background;
      color: if($color, $color, color-contrast($background));
    }
  }

  #{$parent}.nav-tabs {
    .nav-link {
      &.active {
        &,
        &:hover,
        &:focus {
          color: $background;
        }
      }
      &.waves-effect {
        .waves-ripple {
          background: radial-gradient(
            rgba($background, 0.2) 0,
            rgba($background, 0.3) 40%,
            rgba($background, 0.4) 50%,
            rgba($background, 0.5) 60%,
            rgba($white, 0) 70%
          );
        }
      }
    }
    .tab-slider {
      background-color: $background;
    }
  }
}

@mixin template-nav-theme($background, $color: null) {
  @include template-nav-variant('', $background, $color);
}
