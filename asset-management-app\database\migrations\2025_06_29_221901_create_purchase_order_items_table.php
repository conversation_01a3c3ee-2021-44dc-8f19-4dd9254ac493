<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_id')->constrained('purchase_orders')->onDelete('cascade');
            
            // Item information
            $table->string('item_name'); // Nama barang
            $table->text('item_description')->nullable(); // Deskripsi barang
            $table->string('item_code')->nullable(); // Kode barang (jika ada)
            $table->string('brand')->nullable(); // Merk
            $table->string('model')->nullable(); // Model
            $table->string('specification')->nullable(); // Spesifikasi
            $table->string('unit')->default('pcs'); // Satuan (pcs, unit, set, dll)
            
            // Quantity and pricing
            $table->integer('quantity'); // Jumlah
            $table->decimal('unit_price', 15, 2); // Harga satuan
            $table->decimal('total_price', 15, 2); // Total harga (quantity * unit_price)
            
            // Additional info
            $table->text('notes')->nullable(); // Catatan item
            $table->integer('sort_order')->default(0); // Urutan item
            
            // Receiving tracking
            $table->integer('received_quantity')->default(0); // Jumlah yang sudah diterima
            $table->enum('receive_status', ['pending', 'partial', 'completed'])->default('pending');
            
            $table->timestamps();
            
            // Indexes
            $table->index(['purchase_order_id', 'sort_order']);
            $table->index(['purchase_order_id', 'receive_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_items');
    }
};
