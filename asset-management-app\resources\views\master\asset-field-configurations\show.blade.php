@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Konfigurasi Field Asset - Asset Management System')

@section('content')
<style>
.info-card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.info-label {
  font-weight: 600;
  color: #566a7f;
  margin-bottom: 0.5rem;
}

.info-value {
  color: #333;
  margin-bottom: 1rem;
}

.field-preview {
  border: 2px dashed #e7e7ff;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafbff;
}

.badge-custom {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-eye-line me-2"></i>
              Detail Konfigurasi Field Asset
            </h5>
            <small class="text-muted">{{ $assetFieldConfiguration->field_label }}</small>
          </div>
          <div class="d-flex gap-2">
            <a href="{{ route('master.asset-field-configurations.edit', $assetFieldConfiguration) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>
              Edit
            </a>
            <a href="{{ route('master.asset-field-configurations.index') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Field Information -->
    <div class="col-md-8">
      <div class="card info-card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Field
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="info-label">Field Name</div>
              <div class="info-value">
                <code class="text-primary">{{ $assetFieldConfiguration->field_name }}</code>
              </div>

              <div class="info-label">Field Label</div>
              <div class="info-value">
                <strong>{{ $assetFieldConfiguration->field_label }}</strong>
              </div>

              <div class="info-label">Field Type</div>
              <div class="info-value">
                <span class="badge bg-info badge-custom">
                  {{ $assetFieldConfiguration->field_type_display }}
                </span>
              </div>

              <div class="info-label">Field Group</div>
              <div class="info-value">
                <span class="badge bg-secondary badge-custom">
                  @php
                    $fieldGroups = \App\Models\AssetFieldConfiguration::getFieldGroups();
                  @endphp
                  {{ $fieldGroups[$assetFieldConfiguration->field_group] ?? $assetFieldConfiguration->field_group }}
                </span>
              </div>
            </div>
            <div class="col-md-6">
              <div class="info-label">Kategori Asset</div>
              <div class="info-value">
                @if($assetFieldConfiguration->assetCategory)
                  <span class="badge bg-primary badge-custom">{{ $assetFieldConfiguration->assetCategory->name }}</span>
                @else
                  <span class="badge bg-light text-dark badge-custom">Global (Semua Kategori)</span>
                @endif
              </div>

              <div class="info-label">Status</div>
              <div class="info-value">
                @if($assetFieldConfiguration->is_active)
                  <span class="badge bg-success badge-custom">Active</span>
                @else
                  <span class="badge bg-secondary badge-custom">Inactive</span>
                @endif
              </div>

              <div class="info-label">Required</div>
              <div class="info-value">
                @if($assetFieldConfiguration->is_required)
                  <span class="badge bg-danger badge-custom">Required</span>
                @else
                  <span class="badge bg-light text-muted badge-custom">Optional</span>
                @endif
              </div>

              <div class="info-label">Sort Order</div>
              <div class="info-value">
                <span class="badge bg-light text-dark badge-custom">{{ $assetFieldConfiguration->sort_order }}</span>
              </div>
            </div>
          </div>

          @if($assetFieldConfiguration->help_text)
            <div class="info-label">Help Text</div>
            <div class="info-value">
              <div class="alert alert-info">
                <i class="ri-information-line me-2"></i>
                {{ $assetFieldConfiguration->help_text }}
              </div>
            </div>
          @endif
        </div>
      </div>

      <!-- Field Options -->
      @if($assetFieldConfiguration->field_options)
        <div class="card info-card mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-list-check me-2"></i>
              Field Options
            </h6>
          </div>
          <div class="card-body">
            <div class="code-block">
              <pre>{{ json_encode($assetFieldConfiguration->field_options, JSON_PRETTY_PRINT) }}</pre>
            </div>
          </div>
        </div>
      @endif

      <!-- Validation Rules -->
      @if($assetFieldConfiguration->validation_rules)
        <div class="card info-card mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-shield-check-line me-2"></i>
              Validation Rules
            </h6>
          </div>
          <div class="card-body">
            <div class="code-block">
              <pre>{{ json_encode($assetFieldConfiguration->validation_rules, JSON_PRETTY_PRINT) }}</pre>
            </div>
            
            @if($assetFieldConfiguration->validation_rules_string)
              <div class="mt-3">
                <strong>Laravel Validation String:</strong>
                <div class="code-block mt-2">
                  <code>{{ $assetFieldConfiguration->validation_rules_string }}</code>
                </div>
              </div>
            @endif
          </div>
        </div>
      @endif

      <!-- Timestamps -->
      <div class="card info-card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-time-line me-2"></i>
            Informasi Waktu
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="info-label">Dibuat</div>
              <div class="info-value">
                {{ $assetFieldConfiguration->created_at->format('d M Y H:i') }}
                <br><small class="text-muted">{{ $assetFieldConfiguration->created_at->diffForHumans() }}</small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="info-label">Terakhir Diupdate</div>
              <div class="info-value">
                {{ $assetFieldConfiguration->updated_at->format('d M Y H:i') }}
                <br><small class="text-muted">{{ $assetFieldConfiguration->updated_at->diffForHumans() }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Field Preview -->
    <div class="col-md-4">
      <div class="card info-card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-eye-line me-2"></i>
            Preview Field
          </h6>
        </div>
        <div class="card-body">
          <div class="field-preview">
            @php
              $fieldType = $assetFieldConfiguration->field_type;
              $fieldLabel = $assetFieldConfiguration->field_label;
              $helpText = $assetFieldConfiguration->help_text;
              $isRequired = $assetFieldConfiguration->is_required;
              $fieldOptions = $assetFieldConfiguration->field_options;
            @endphp

            <div class="mb-3">
              <label class="form-label">
                {{ $fieldLabel }}
                @if($isRequired)
                  <span class="text-danger">*</span>
                @endif
              </label>

              @switch($fieldType)
                @case('text')
                @case('email')
                @case('url')
                @case('tel')
                  <input type="{{ $fieldType }}" class="form-control" placeholder="Masukkan {{ strtolower($fieldLabel) }}" disabled>
                  @break

                @case('number')
                  <input type="number" class="form-control" placeholder="Masukkan {{ strtolower($fieldLabel) }}" disabled>
                  @break

                @case('date')
                  <input type="date" class="form-control" disabled>
                  @break

                @case('datetime')
                  <input type="datetime-local" class="form-control" disabled>
                  @break

                @case('textarea')
                  <textarea class="form-control" rows="3" placeholder="Masukkan {{ strtolower($fieldLabel) }}" disabled></textarea>
                  @break

                @case('select')
                  <select class="form-select" disabled>
                    <option value="">Pilih {{ $fieldLabel }}</option>
                    @if($fieldOptions && isset($fieldOptions['options']))
                      @foreach($fieldOptions['options'] as $key => $value)
                        <option value="{{ $key }}">{{ $value }}</option>
                      @endforeach
                    @else
                      <option value="option1">Option 1</option>
                      <option value="option2">Option 2</option>
                    @endif
                  </select>
                  @break

                @case('radio')
                  @if($fieldOptions && isset($fieldOptions['options']))
                    @foreach($fieldOptions['options'] as $key => $value)
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="preview_radio" id="radio_{{ $key }}" value="{{ $key }}" disabled>
                        <label class="form-check-label" for="radio_{{ $key }}">
                          {{ $value }}
                        </label>
                      </div>
                    @endforeach
                  @else
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="preview_radio" id="radio1" value="option1" disabled>
                      <label class="form-check-label" for="radio1">Option 1</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="preview_radio" id="radio2" value="option2" disabled>
                      <label class="form-check-label" for="radio2">Option 2</label>
                    </div>
                  @endif
                  @break

                @case('checkbox')
                  @if($fieldOptions && isset($fieldOptions['options']))
                    @foreach($fieldOptions['options'] as $key => $value)
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check_{{ $key }}" value="{{ $key }}" disabled>
                        <label class="form-check-label" for="check_{{ $key }}">
                          {{ $value }}
                        </label>
                      </div>
                    @endforeach
                  @else
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="check1" value="option1" disabled>
                      <label class="form-check-label" for="check1">Option 1</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="check2" value="option2" disabled>
                      <label class="form-check-label" for="check2">Option 2</label>
                    </div>
                  @endif
                  @break

                @case('file')
                  <input type="file" class="form-control" disabled>
                  @break

                @default
                  <input type="text" class="form-control" placeholder="Masukkan {{ strtolower($fieldLabel) }}" disabled>
              @endswitch

              @if($helpText)
                <div class="form-text">{{ $helpText }}</div>
              @endif
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="card info-card mt-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-tools-line me-2"></i>
            Actions
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.asset-field-configurations.edit', $assetFieldConfiguration) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>
              Edit Field
            </a>
            
            @if($assetFieldConfiguration->is_active)
              <form action="{{ route('master.asset-field-configurations.update', $assetFieldConfiguration) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="is_active" value="0">
                <button type="submit" class="btn btn-outline-warning w-100">
                  <i class="ri-pause-line me-1"></i>
                  Nonaktifkan
                </button>
              </form>
            @else
              <form action="{{ route('master.asset-field-configurations.update', $assetFieldConfiguration) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="is_active" value="1">
                <button type="submit" class="btn btn-outline-success w-100">
                  <i class="ri-play-line me-1"></i>
                  Aktifkan
                </button>
              </form>
            @endif

            <form action="{{ route('master.asset-field-configurations.destroy', $assetFieldConfiguration) }}" method="POST">
              @csrf
              @method('DELETE')
              <button type="submit" class="btn btn-outline-danger w-100" 
                      onclick="return confirm('Yakin ingin menghapus field ini?\n\nTindakan ini tidak dapat dibatalkan!')">
                <i class="ri-delete-bin-line me-1"></i>
                Hapus Field
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

@endsection
