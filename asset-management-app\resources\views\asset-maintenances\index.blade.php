@extends('layouts/contentNavbarLayout')

@section('title', 'Maintenance Asset')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="ri-tools-line me-2"></i>
            Maintenance Asset
          </h5>
          <a href="{{ route('asset-maintenances.create') }}" class="btn btn-primary">
            <i class="ri-add-line me-1"></i>
            Tambah Maintenance
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-filter-line me-2"></i>
            Filter Data
          </h6>
        </div>
        <div class="card-body">
          <form method="GET" action="{{ route('asset-maintenances.index') }}" class="row g-3">
            <div class="col-md-2">
              <label for="status" class="form-label">Status</label>
              <select name="status" id="status" class="form-select">
                <option value="">Semua Status</option>
                @foreach(\App\Models\AssetMaintenance::getStatuses() as $key => $value)
                  <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                    {{ $value }}
                  </option>
                @endforeach
              </select>
            </div>
            
            <div class="col-md-2">
              <label for="priority" class="form-label">Prioritas</label>
              <select name="priority" id="priority" class="form-select">
                <option value="">Semua Prioritas</option>
                @foreach(\App\Models\AssetMaintenance::getPriorities() as $key => $value)
                  <option value="{{ $key }}" {{ request('priority') == $key ? 'selected' : '' }}>
                    {{ $value }}
                  </option>
                @endforeach
              </select>
            </div>
            
            <div class="col-md-2">
              <label for="maintenance_type" class="form-label">Tipe</label>
              <select name="maintenance_type" id="maintenance_type" class="form-select">
                <option value="">Semua Tipe</option>
                @php
                  $maintenanceTypesFilter = \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')
                                                             ->where('is_active', true)
                                                             ->orderBy('sort_order')
                                                             ->get();
                @endphp
                @foreach($maintenanceTypesFilter as $type)
                  <option value="{{ $type->lookup_name }}" {{ request('maintenance_type') == $type->lookup_name ? 'selected' : '' }}>
                    {{ $type->lookup_name }}
                  </option>
                @endforeach
              </select>
            </div>
            
            <div class="col-md-2">
              <label for="date_from" class="form-label">Dari Tanggal</label>
              <input type="date" name="date_from" id="date_from" class="form-control" 
                     value="{{ request('date_from') }}">
            </div>
            
            <div class="col-md-2">
              <label for="date_to" class="form-label">Sampai Tanggal</label>
              <input type="date" name="date_to" id="date_to" class="form-control" 
                     value="{{ request('date_to') }}">
            </div>
            
            <div class="col-md-2">
              <label for="search" class="form-label">Pencarian</label>
              <div class="input-group">
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Nomor, judul, asset..." value="{{ request('search') }}">
                <button type="submit" class="btn btn-primary">
                  <i class="ri-search-line"></i>
                </button>
              </div>
            </div>
            
            <div class="col-12">
              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="ri-search-line me-1"></i>
                  Filter
                </button>
                <a href="{{ route('asset-maintenances.index') }}" class="btn btn-outline-secondary">
                  <i class="ri-refresh-line me-1"></i>
                  Reset
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">
            <i class="ri-list-check-3 me-2"></i>
            Daftar Maintenance Asset
          </h6>
          <small class="text-muted">
            Total: {{ $maintenances->total() }} maintenance
          </small>
        </div>
        <div class="card-body">
          @if($maintenances->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Nomor Maintenance</th>
                    <th>Asset</th>
                    <th>Supplier</th>
                    <th>Judul</th>
                    <th>Tipe</th>
                    <th>Prioritas</th>
                    <th>Status</th>
                    <th>Tanggal Jadwal</th>
                    <th>Estimasi Biaya</th>
                    <th width="120">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($maintenances as $maintenance)
                    <tr>
                      <td>
                        <strong>{{ $maintenance->maintenance_number }}</strong>
                      </td>
                      <td>
                        <div>
                          <strong>{{ $maintenance->asset->asset_code }}</strong>
                          <br>
                          <small class="text-muted">{{ $maintenance->asset->name }}</small>
                        </div>
                      </td>
                      <td>
                        @if($maintenance->supplier)
                          <div>
                            <strong>{{ $maintenance->supplier->name }}</strong>
                            <br>
                            <small class="text-muted">{{ $maintenance->supplier->supplier_code }}</small>
                          </div>
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>{{ $maintenance->title }}</td>
                      <td>
                        <span class="badge bg-info">{{ $maintenance->maintenance_type_text }}</span>
                      </td>
                      <td>
                        <span class="badge {{ $maintenance->priority_badge_class }}">
                          {{ $maintenance->priority_text }}
                        </span>
                      </td>
                      <td>
                        <span class="badge {{ $maintenance->status_badge_class }}">
                          {{ $maintenance->status_text }}
                        </span>
                      </td>
                      <td>{{ $maintenance->scheduled_date->format('d/m/Y') }}</td>
                      <td>
                        @if($maintenance->estimated_cost)
                          Rp {{ number_format($maintenance->estimated_cost, 0, ',', '.') }}
                        @else
                          -
                        @endif
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                  data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-more-2-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="{{ route('asset-maintenances.show', $maintenance) }}">
                                <i class="ri-eye-line me-2"></i>
                                Detail
                              </a>
                            </li>
                            @if($maintenance->status !== 'completed')
                              <li>
                                <a class="dropdown-item" href="{{ route('asset-maintenances.edit', $maintenance) }}">
                                  <i class="ri-edit-line me-2"></i>
                                  Edit
                                </a>
                              </li>
                            @endif
                            @if($maintenance->canBeStarted())
                              @php
                                $userPermissions = session('user_permissions', []);
                                $canStart = auth()->user()->isSuperAdmin() || in_array('asset_maintenance_start', $userPermissions);
                              @endphp
                              @if($canStart)
                                <li>
                                  <form method="POST" action="{{ route('asset-maintenances.start', $maintenance) }}"
                                        onsubmit="return confirm('Mulai maintenance ini?')">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-success">
                                      <i class="ri-play-line me-2"></i>
                                      Mulai
                                    </button>
                                  </form>
                                </li>
                              @else
                                <li>
                                  <span class="dropdown-item text-muted" title="Anda tidak memiliki izin untuk memulai maintenance">
                                    <i class="ri-play-line me-2"></i>
                                    Mulai (Tidak Diizinkan)
                                  </span>
                                </li>
                              @endif
                            @endif
                            @if($maintenance->canBeCompleted())
                              @php
                                $userPermissions = session('user_permissions', []);
                                $canComplete = auth()->user()->isSuperAdmin() || in_array('asset_maintenance_complete', $userPermissions);
                              @endphp
                              @if($canComplete)
                                <li>
                                  <form method="POST" action="{{ route('asset-maintenances.complete', $maintenance) }}"
                                        onsubmit="return confirm('Selesaikan maintenance ini?')">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-primary">
                                      <i class="ri-check-line me-2"></i>
                                      Selesaikan
                                    </button>
                                  </form>
                                </li>
                              @else
                                <li>
                                  <span class="dropdown-item text-muted" title="Anda tidak memiliki izin untuk menyelesaikan maintenance">
                                    <i class="ri-check-line me-2"></i>
                                    Selesaikan (Tidak Diizinkan)
                                  </span>
                                </li>
                              @endif
                            @endif
                            @if($maintenance->canBeCancelled())
                              <li>
                                <form method="POST" action="{{ route('asset-maintenances.cancel', $maintenance) }}" 
                                      onsubmit="return confirm('Batalkan maintenance ini?')">
                                  @csrf
                                  <button type="submit" class="dropdown-item text-warning">
                                    <i class="ri-close-line me-2"></i>
                                    Batalkan
                                  </button>
                                </form>
                              </li>
                            @endif
                            @if($maintenance->status !== 'completed')
                              <li><hr class="dropdown-divider"></li>
                              <li>
                                <form method="POST" action="{{ route('asset-maintenances.destroy', $maintenance) }}" 
                                      onsubmit="return confirm('Hapus maintenance ini?')">
                                  @csrf
                                  @method('DELETE')
                                  <button type="submit" class="dropdown-item text-danger">
                                    <i class="ri-delete-bin-line me-2"></i>
                                    Hapus
                                  </button>
                                </form>
                              </li>
                            @endif
                          </ul>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
              <div>
                <small class="text-muted">
                  Menampilkan {{ $maintenances->firstItem() }} - {{ $maintenances->lastItem() }} 
                  dari {{ $maintenances->total() }} maintenance
                </small>
              </div>
              <div>
                {{ $maintenances->links() }}
              </div>
            </div>
          @else
            <!-- Empty State -->
            <div class="text-center py-5">
              <i class="ri-tools-line ri-48px text-muted mb-3"></i>
              <h5 class="text-muted">Belum Ada Data Maintenance</h5>
              <p class="text-muted mb-4">Buat maintenance asset pertama untuk memulai.</p>
              <a href="{{ route('asset-maintenances.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>
                Tambah Maintenance Pertama
              </a>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
