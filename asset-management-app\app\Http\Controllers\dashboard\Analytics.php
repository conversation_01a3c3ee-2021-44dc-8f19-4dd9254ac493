<?php

namespace App\Http\Controllers\dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Asset;
use App\Models\Contract;
use App\Models\User;
use App\Models\AssetAssignment;
use App\Models\AssetMaintenance;
use Carbon\Carbon;

class Analytics extends Controller
{
  public function index()
  {
    // Get latest assets (last 10)
    $latestAssets = Asset::with(['category', 'branch'])
                        ->orderBy('created_at', 'desc')
                        ->limit(10)
                        ->get();

    // Get contracts expiring soon (within 30 days)
    $expiringContracts = Contract::with(['supplier', 'branch'])
                               ->where('end_date', '<=', Carbon::now()->addDays(30))
                               ->where('end_date', '>=', Carbon::now())
                               ->where('status', 'active')
                               ->orderBy('end_date', 'asc')
                               ->limit(10)
                               ->get();

    // Get user information
    $user = auth()->user();
    $userInfo = [
        'name' => $user->name,
        'role' => $user->role->name ?? 'N/A',
        'branch' => $user->branch->name ?? 'N/A',
        'division' => $user->division->name ?? 'N/A',
    ];

    // Get statistics
    $stats = [
        'total_assets' => Asset::count(),
        'active_contracts' => Contract::where('status', 'active')->count(),
        'pending_assignments' => AssetAssignment::where('status', 'pending')->count(),
        'maintenance_in_progress' => AssetMaintenance::where('status', 'in_progress')->count(),
        'assets_this_month' => Asset::whereMonth('created_at', Carbon::now()->month)
                                   ->whereYear('created_at', Carbon::now()->year)
                                   ->count(),
        'expiring_contracts_count' => $expiringContracts->count(),
    ];

    return view('content.dashboard.dashboards-analytics', compact(
        'latestAssets',
        'expiringContracts',
        'userInfo',
        'stats'
    ));
  }
}
