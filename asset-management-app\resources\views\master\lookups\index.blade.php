@extends('layouts.contentNavbarLayout')

@section('title', 'Master Lookup - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Master Lookup
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('master.lookups.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah Lookup
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('master.lookups.index') }}">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                   placeholder="Kode, nama, atau deskripsi...">
          </div>
          <div class="col-md-2">
            <label class="form-label">Kode Lookup</label>
            <select class="form-select" name="lookup_code">
              <option value="">Semua Kode</option>
              @foreach($codes as $code)
                <option value="{{ $code }}" {{ request('lookup_code') === $code ? 'selected' : '' }}>
                  {{ $code }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Kategori</label>
            <select class="form-select" name="category">
              <option value="">Semua Kategori</option>
              @foreach($categories as $category)
                <option value="{{ $category }}" {{ request('category') === $category ? 'selected' : '' }}>
                  {{ $category }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" name="is_active">
              <option value="">Semua Status</option>
              <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Aktif</option>
              <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Non-Aktif</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('master.lookups.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Lookups Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Lookup ({{ $lookups->total() }} data)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Kode Lookup</th>
            <th>Nama Lookup</th>
            <th>Kategori</th>
            <th>Deskripsi</th>
            <th>Urutan</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($lookups as $lookup)
          <tr>
            <td>
              <span class="badge bg-primary">{{ $lookup->lookup_code }}</span>
            </td>
            <td>
              <div>
                <strong>{{ $lookup->lookup_name }}</strong>
                @if($lookup->metadata && count($lookup->metadata) > 0)
                  <br><small class="text-muted">
                    <i class="ri-database-line me-1"></i>{{ count($lookup->metadata) }} metadata
                  </small>
                @endif
              </div>
            </td>
            <td>
              @if($lookup->category)
                <span class="badge bg-info">{{ $lookup->category }}</span>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              @if($lookup->description)
                <span class="text-muted">{{ Str::limit($lookup->description, 50) }}</span>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              <span class="badge bg-secondary">{{ $lookup->sort_order }}</span>
            </td>
            <td>
              <span class="badge bg-{{ $lookup->is_active ? 'success' : 'secondary' }}">
                {{ $lookup->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('master.lookups.show', $lookup) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('master.lookups.edit', $lookup) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  <div class="dropdown-divider"></div>
                  @if($lookup->is_active)
                    <form action="{{ route('master.lookups.update', $lookup) }}" method="POST" class="d-inline">
                      @csrf
                      @method('PUT')
                      <input type="hidden" name="lookup_code" value="{{ $lookup->lookup_code }}">
                      <input type="hidden" name="lookup_name" value="{{ $lookup->lookup_name }}">
                      <input type="hidden" name="description" value="{{ $lookup->description }}">
                      <input type="hidden" name="category" value="{{ $lookup->category }}">
                      <input type="hidden" name="sort_order" value="{{ $lookup->sort_order }}">
                      <input type="hidden" name="is_active" value="0">
                      <button type="submit" class="dropdown-item text-warning">
                        <i class="ri-pause-circle-line me-1"></i> Non-aktifkan
                      </button>
                    </form>
                  @else
                    <form action="{{ route('master.lookups.update', $lookup) }}" method="POST" class="d-inline">
                      @csrf
                      @method('PUT')
                      <input type="hidden" name="lookup_code" value="{{ $lookup->lookup_code }}">
                      <input type="hidden" name="lookup_name" value="{{ $lookup->lookup_name }}">
                      <input type="hidden" name="description" value="{{ $lookup->description }}">
                      <input type="hidden" name="category" value="{{ $lookup->category }}">
                      <input type="hidden" name="sort_order" value="{{ $lookup->sort_order }}">
                      <input type="hidden" name="is_active" value="1">
                      <button type="submit" class="dropdown-item text-success">
                        <i class="ri-play-circle-line me-1"></i> Aktifkan
                      </button>
                    </form>
                  @endif
                  <form action="{{ route('master.lookups.destroy', $lookup) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus lookup ini?')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="7" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-database-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada data lookup</h6>
                <p class="text-muted mb-3">Belum ada data lookup yang dibuat atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('master.lookups.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah Lookup Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($lookups->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $lookups->firstItem() }} - {{ $lookups->lastItem() }} dari {{ $lookups->total() }} data
        </div>
        {{ $lookups->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
