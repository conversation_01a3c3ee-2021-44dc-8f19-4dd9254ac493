<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Supplier;
use Illuminate\Http\Request;

class SupplierLookupController extends Controller
{
    /**
     * Get active suppliers for lookup
     */
    public function index(Request $request)
    {
        $search = $request->get('search', '');
        $perPage = $request->get('per_page', 10);

        $suppliers = Supplier::where('is_active', true)
            ->when($search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('supplier_code', 'like', "%{$search}%")
                      ->orWhere('name', 'like', "%{$search}%")
                      ->orWhere('company_name', 'like', "%{$search}%");
                });
            })
            ->select([
                'id',
                'supplier_code',
                'name',
                'company_name',
                'city',
                'province',
                'phone',
                'email',
                'business_type',
                'payment_terms'
            ])
            ->orderBy('name')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $suppliers->items(),
            'pagination' => [
                'current_page' => $suppliers->currentPage(),
                'last_page' => $suppliers->lastPage(),
                'per_page' => $suppliers->perPage(),
                'total' => $suppliers->total(),
                'has_more' => $suppliers->hasMorePages()
            ]
        ]);
    }

    /**
     * Get supplier by ID
     */
    public function show($id)
    {
        $supplier = Supplier::where('is_active', true)
            ->select([
                'id',
                'supplier_code',
                'name',
                'company_name',
                'city',
                'province',
                'phone',
                'email',
                'business_type',
                'payment_terms'
            ])
            ->find($id);

        if (!$supplier) {
            return response()->json([
                'success' => false,
                'message' => 'Supplier tidak ditemukan atau tidak aktif'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $supplier
        ]);
    }
}
