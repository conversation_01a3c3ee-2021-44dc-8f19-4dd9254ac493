<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\Branch;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AssetReportExport;

class AssetReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:reports.assets.view')->only(['index']);
        $this->middleware('permission:reports.assets.export')->only(['export']);
    }

    /**
     * Display the asset report page
     */
    public function index(Request $request)
    {
        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $categories = AssetCategory::active()->orderBy('name')->get();
        
        // Initialize query
        $query = Asset::with(['category', 'branch', 'assignedToEmployee', 'supplier']);
        
        // Apply filters
        $filters = [];
        
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
            $filters['branch_id'] = $request->branch_id;
        }
        
        if ($request->filled('category_id')) {
            $query->where('asset_category_id', $request->category_id);
            $filters['category_id'] = $request->category_id;
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
            $filters['status'] = $request->status;
        }
        
        if ($request->filled('date_from')) {
            $query->whereDate('purchase_date', '>=', $request->date_from);
            $filters['date_from'] = $request->date_from;
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('purchase_date', '<=', $request->date_to);
            $filters['date_to'] = $request->date_to;
        }
        
        // Get assets with pagination
        $assets = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // Get statistics
        $stats = [
            'total_assets' => $query->count(),
            'total_value' => $query->sum('purchase_price'),
            'active_assets' => $query->where('status', 'active')->count(),
            'inactive_assets' => $query->where('status', 'inactive')->count(),
            'maintenance_assets' => $query->where('status', 'maintenance')->count(),
            'disposed_assets' => $query->where('status', 'disposed')->count(),
        ];
        
        // Status options
        $statusOptions = [
            'active' => 'Aktif',
            'inactive' => 'Non-Aktif', 
            'maintenance' => 'Maintenance',
            'disposed' => 'Disposed'
        ];
        
        return view('reports.assets.index', compact(
            'assets',
            'branches', 
            'categories',
            'statusOptions',
            'filters',
            'stats'
        ));
    }
    
    /**
     * Export asset report to Excel
     */
    public function export(Request $request)
    {
        // Build filename with current date and filters
        $filename = 'laporan-asset-' . date('Y-m-d-H-i-s');
        
        if ($request->filled('branch_id')) {
            $branch = Branch::find($request->branch_id);
            if ($branch) {
                $filename .= '-' . str_replace(' ', '-', strtolower($branch->name));
            }
        }
        
        if ($request->filled('category_id')) {
            $category = AssetCategory::find($request->category_id);
            if ($category) {
                $filename .= '-' . str_replace(' ', '-', strtolower($category->name));
            }
        }
        
        $filename .= '.xlsx';
        
        return Excel::download(new AssetReportExport($request->all()), $filename);
    }
}
