@extends('layouts/contentNavbarLayout')

@section('title', 'Dashboard - Analytics')

@section('vendor-style')
@vite('resources/assets/vendor/libs/apex-charts/apex-charts.scss')
@endsection

@section('vendor-script')
@vite('resources/assets/vendor/libs/apex-charts/apexcharts.js')
@endsection

@section('page-script')
@vite('resources/assets/js/dashboards-analytics.js')
@endsection

@section('content')
<div class="row gy-6">
  <!-- Welcome card -->
  <div class="col-md-12 col-lg-4">
    <div class="card">
      <div class="card-body text-nowrap">
        <h5 class="card-title mb-0 flex-wrap text-nowrap">👋 Hai {{ $userInfo['name'] }}</h5>
        <p class="mb-2">Asset Management System (AMS)</p>
        <div class="mb-3">
          <small class="text-muted">Role: <strong>{{ $userInfo['role'] }}</strong></small><br>
          <small class="text-muted">Cabang: <strong>{{ $userInfo['branch'] }}</strong></small><br>
          <small class="text-muted">Divisi: <strong>{{ $userInfo['division'] }}</strong></small>
        </div>
        <a href="{{ route('assets.view-all') }}" class="btn btn-sm btn-primary">Lihat Data Asset</a>
      </div>
      <img src="{{asset('assets/img/illustrations/trophy.png')}}" class="position-absolute bottom-0 end-0 me-5 mb-5" width="83" alt="view sales">
    </div>
  </div>
  <!--/ Welcome card -->

  <!-- Statistik Asset -->
  <div class="col-lg-8">
    <div class="card h-100">
      <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
          <h5 class="card-title m-0 me-2">Statistik Asset</h5>
          <div class="dropdown">
            <button class="btn text-muted p-0" type="button" id="statisticsDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="ri-more-2-line ri-24px"></i>
            </button>
            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="statisticsDropdown">
              <a class="dropdown-item" href="javascript:void(0);">Refresh</a>
              <a class="dropdown-item" href="{{ route('assets.view-all') }}">Lihat Semua Asset</a>
              <a class="dropdown-item" href="javascript:void(0);">Export Data</a>
            </div>
          </div>
        </div>
        <p class="small mb-0"><span class="h6 mb-0">{{ $stats['assets_this_month'] }} Asset Baru</span> 📈 bulan ini</p>
      </div>
      <div class="card-body pt-lg-10">
        <div class="row g-6">
          <div class="col-md-3 col-6">
            <div class="d-flex align-items-center">
              <div class="avatar">
                <div class="avatar-initial bg-primary rounded shadow-xs">
                  <i class="ri-computer-line ri-24px"></i>
                </div>
              </div>
              <div class="ms-3">
                <p class="mb-0">Total Asset</p>
                <h5 class="mb-0">{{ number_format($stats['total_assets']) }}</h5>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-6">
            <div class="d-flex align-items-center">
              <div class="avatar">
                <div class="avatar-initial bg-success rounded shadow-xs">
                  <i class="ri-file-text-line ri-24px"></i>
                </div>
              </div>
              <div class="ms-3">
                <p class="mb-0">Kontrak Aktif</p>
                <h5 class="mb-0">{{ number_format($stats['active_contracts']) }}</h5>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-6">
            <div class="d-flex align-items-center">
              <div class="avatar">
                <div class="avatar-initial bg-warning rounded shadow-xs">
                  <i class="ri-time-line ri-24px"></i>
                </div>
              </div>
              <div class="ms-3">
                <p class="mb-0">Pending Assignment</p>
                <h5 class="mb-0">{{ number_format($stats['pending_assignments']) }}</h5>
              </div>
            </div>
          </div>
          <div class="col-md-3 col-6">
            <div class="d-flex align-items-center">
              <div class="avatar">
                <div class="avatar-initial bg-info rounded shadow-xs">
                  <i class="ri-tools-line ri-24px"></i>
                </div>
              </div>
              <div class="ms-3">
                <p class="mb-0">Maintenance</p>
                <h5 class="mb-0">{{ number_format($stats['maintenance_in_progress']) }}</h5>
              </div>
            </div>
          </div>
          @if($canViewRequests)
          <div class="col-md-3 col-6">
            <div class="d-flex align-items-center">
              <div class="avatar">
                <div class="avatar-initial bg-warning rounded shadow-xs">
                  <i class="ri-file-list-3-line ri-24px"></i>
                </div>
              </div>
              <div class="ms-3">
                <p class="mb-0">Pending Request</p>
                <h5 class="mb-0">{{ number_format($stats['pending_requests']) }}</h5>
              </div>
            </div>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
  <!--/ Statistik Asset -->

  <!-- Kontrak Akan Berakhir -->
  <div class="col-xl-4 col-md-6">
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between">
          <h5 class="mb-1">Kontrak Akan Berakhir</h5>
          <div class="dropdown">
            <button class="btn text-muted p-0" type="button" id="contractsDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="ri-more-2-line ri-24px"></i>
            </button>
            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="contractsDropdown">
              <a class="dropdown-item" href="javascript:void(0);">Refresh</a>
              <a class="dropdown-item" href="{{ route('contracts.index') }}">Lihat Semua Kontrak</a>
              <a class="dropdown-item" href="javascript:void(0);">Export</a>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body pt-lg-2">
        @if($expiringContracts->count() > 0)
          <div class="mt-1 mt-md-3">
            <div class="d-flex align-items-center gap-4 mb-3">
              <h4 class="mb-0 text-warning">{{ $stats['expiring_contracts_count'] }}</h4>
              <p class="mb-0">Kontrak akan berakhir dalam 30 hari ⚠️</p>
            </div>
            <div class="list-group list-group-flush">
              @foreach($expiringContracts->take(3) as $contract)
                <div class="list-group-item px-0 py-2">
                  <div class="d-flex justify-content-between align-items-start">
                    <div>
                      <h6 class="mb-1">{{ $contract->supplier->name ?? 'N/A' }}</h6>
                      <small class="text-muted">{{ $contract->contract_number }}</small>
                    </div>
                    <small class="text-danger">{{ \Carbon\Carbon::parse($contract->end_date)->format('d/m/Y') }}</small>
                  </div>
                </div>
              @endforeach
            </div>
            <div class="d-grid mt-3 mt-md-4">
              <a href="{{ route('contracts.index') }}" class="btn btn-warning btn-sm">Lihat Semua</a>
            </div>
          </div>
        @else
          <div class="text-center py-4">
            <i class="ri-check-line ri-48px text-success mb-3"></i>
            <p class="mb-0">Tidak ada kontrak yang akan berakhir</p>
          </div>
        @endif
      </div>
    </div>
  </div>
  <!--/ Kontrak Akan Berakhir -->

  <!-- Asset Terbaru -->
  <div class="col-xl-4 col-md-6">
    <div class="card">
      <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title m-0 me-2">Asset Terbaru</h5>
        <div class="dropdown">
          <button class="btn text-muted p-0" type="button" id="latestAssetsDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="ri-more-2-line ri-24px"></i>
          </button>
          <div class="dropdown-menu dropdown-menu-end" aria-labelledby="latestAssetsDropdown">
            <a class="dropdown-item" href="javascript:void(0);">Refresh</a>
            <a class="dropdown-item" href="{{ route('assets.view-all') }}">Lihat Semua Asset</a>
            <a class="dropdown-item" href="{{ route('assets.create') }}">Tambah Asset Baru</a>
          </div>
        </div>
      </div>
      <div class="card-body pt-lg-8">
        @if($latestAssets->count() > 0)
          <div class="mb-5 mb-lg-12">
            <div class="d-flex align-items-center">
              <h3 class="mb-0">{{ $latestAssets->count() }}</h3>
              <span class="text-success ms-2">
                <i class="ri-arrow-up-s-line"></i>
                <span>Asset Baru</span>
              </span>
            </div>
            <p class="mb-0">Ditambahkan dalam 10 hari terakhir</p>
          </div>
          <ul class="p-0 m-0">
            @foreach($latestAssets->take(3) as $asset)
              <li class="d-flex mb-6">
                <div class="avatar flex-shrink-0 bg-lightest rounded me-3">
                  <i class="ri-computer-line ri-24px text-primary"></i>
                </div>
                <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2">
                  <div class="me-2">
                    <h6 class="mb-0">{{ $asset->name }}</h6>
                    <p class="mb-0">{{ $asset->category->name ?? 'N/A' }} - {{ $asset->branch->name ?? 'N/A' }}</p>
                  </div>
                  <div class="text-end">
                    <small class="text-muted">{{ $asset->asset_code }}</small>
                    <div class="mt-1">
                      <span class="badge bg-label-{{ $asset->status === 'active' ? 'success' : ($asset->status === 'maintenance' ? 'warning' : 'secondary') }}">
                        {{ ucfirst($asset->status) }}
                      </span>
                    </div>
                  </div>
                </div>
              </li>
            @endforeach
          </ul>
        @else
          <div class="text-center py-4">
            <i class="ri-computer-line ri-48px text-muted mb-3"></i>
            <p class="mb-0">Belum ada asset yang ditambahkan</p>
          </div>
        @endif
      </div>
    </div>
  </div>
  <!--/ Asset Terbaru -->

  @if($canViewRequests)
  <!-- Permintaan Asset Terbaru -->
  <div class="col-xl-4 col-md-6">
    <div class="card">
      <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title m-0 me-2">Permintaan Asset Terbaru</h5>
        <div class="dropdown">
          <button class="btn text-muted p-0" type="button" id="latestRequestsDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="ri-more-2-line ri-24px"></i>
          </button>
          <div class="dropdown-menu dropdown-menu-end" aria-labelledby="latestRequestsDropdown">
            <a class="dropdown-item" href="javascript:void(0);">Refresh</a>
            <a class="dropdown-item" href="{{ route('request-assets.index') }}">Lihat Semua Permintaan</a>
            <a class="dropdown-item" href="{{ route('request-assets.create') }}">Buat Permintaan Baru</a>
          </div>
        </div>
      </div>
      <div class="card-body pt-lg-8">
        @if($latestRequests->count() > 0)
          <div class="mb-5 mb-lg-12">
            <div class="d-flex align-items-center">
              <h3 class="mb-0">{{ $stats['total_requests_this_month'] }}</h3>
              <span class="text-info ms-2">
                <i class="ri-arrow-up-s-line"></i>
                <span>Permintaan</span>
              </span>
            </div>
            <p class="mb-0">Dibuat bulan ini</p>
          </div>
          <ul class="p-0 m-0">
            @foreach($latestRequests as $request)
              <li class="d-flex mb-6">
                <div class="avatar flex-shrink-0 bg-lightest rounded me-3">
                  <i class="ri-file-list-3-line ri-24px text-warning"></i>
                </div>
                <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2">
                  <div class="me-2">
                    <h6 class="mb-0">{{ $request->request_number }}</h6>
                    <p class="mb-0">{{ $request->requestedByUser->name ?? 'N/A' }} - {{ $request->division->name ?? 'N/A' }}</p>
                  </div>
                  <div class="text-end">
                    <small class="text-muted">{{ $request->created_at->format('d/m/Y') }}</small>
                    <div class="mt-1">
                      <span class="badge bg-label-{{
                        $request->status === 'approved' ? 'success' :
                        ($request->status === 'rejected' ? 'danger' :
                        ($request->status === 'submitted' ? 'warning' :
                        ($request->status === 'reviewed' ? 'info' : 'secondary')))
                      }}">
                        {{ ucfirst($request->status) }}
                      </span>
                    </div>
                  </div>
                </div>
              </li>
            @endforeach
          </ul>
        @else
          <div class="text-center py-4">
            <i class="ri-file-list-3-line ri-48px text-muted mb-3"></i>
            <p class="mb-0">Belum ada permintaan asset</p>
          </div>
        @endif
      </div>
    </div>
  </div>
  <!--/ Permintaan Asset Terbaru -->
  @endif

</div>
@endsection
