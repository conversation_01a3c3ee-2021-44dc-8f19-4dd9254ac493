<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Contract extends Model
{
    use HasFactory;

    protected $fillable = [
        'contract_number',
        'contract_name',
        'description',
        'supplier_id',
        'division_id',
        'branch_id',
        'contract_date',
        'start_date',
        'end_date',
        'contract_value',
        'contract_type',
        'status',
        'terms_conditions',
        'contract_file',
        'attachments',
        'notes',
        'auto_renewal',
        'renewal_period_months',
        'notification_days',
        'created_by',
        'updated_by',
        'is_active',
    ];

    protected $casts = [
        'contract_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'contract_value' => 'decimal:2',
        'attachments' => 'array',
        'auto_renewal' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function division()
    {
        return $this->belongsTo(Division::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Accessors
    public function getContractTypeTextAttribute()
    {
        $types = [
            'service' => 'Layanan',
            'supply' => 'Pengadaan',
            'maintenance' => 'Maintenance',
            'lease' => 'Sewa',
            'other' => 'Lainnya'
        ];

        return $types[$this->contract_type] ?? $this->contract_type;
    }

    public function getStatusTextAttribute()
    {
        $statuses = [
            'draft' => 'Draft',
            'active' => 'Aktif',
            'expired' => 'Kadaluarsa',
            'terminated' => 'Dihentikan',
            'renewed' => 'Diperpanjang'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => 'secondary',
            'active' => 'success',
            'expired' => 'danger',
            'terminated' => 'dark',
            'renewed' => 'info'
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getDaysUntilExpiryAttribute()
    {
        if (!$this->end_date) {
            return null;
        }

        return Carbon::now()->diffInDays($this->end_date, false);
    }

    public function getIsExpiringAttribute()
    {
        $daysUntilExpiry = $this->days_until_expiry;

        if ($daysUntilExpiry === null) {
            return false;
        }

        return $daysUntilExpiry <= $this->notification_days && $daysUntilExpiry >= 0;
    }

    public function getIsExpiredAttribute()
    {
        $daysUntilExpiry = $this->days_until_expiry;

        if ($daysUntilExpiry === null) {
            return false;
        }

        return $daysUntilExpiry < 0;
    }

    public function getExpiryStatusAttribute()
    {
        if ($this->is_expired) {
            return 'expired';
        } elseif ($this->is_expiring) {
            return 'expiring';
        } else {
            return 'normal';
        }
    }

    public function getExpiryStatusTextAttribute()
    {
        $daysUntilExpiry = $this->days_until_expiry;

        if ($daysUntilExpiry === null) {
            return 'Tanggal tidak tersedia';
        }

        if ($daysUntilExpiry < 0) {
            return 'Sudah kadaluarsa ' . abs($daysUntilExpiry) . ' hari';
        } elseif ($daysUntilExpiry == 0) {
            return 'Kadaluarsa hari ini';
        } elseif ($daysUntilExpiry <= $this->notification_days) {
            return 'Akan kadaluarsa dalam ' . $daysUntilExpiry . ' hari';
        } else {
            return 'Masih berlaku ' . $daysUntilExpiry . ' hari lagi';
        }
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeExpiring($query, $days = null)
    {
        $notificationDays = $days ?? 30;
        $expiryDate = Carbon::now()->addDays($notificationDays);

        return $query->where('end_date', '<=', $expiryDate)
                    ->where('end_date', '>=', Carbon::now())
                    ->where('status', 'active');
    }

    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', Carbon::now())
                    ->where('status', '!=', 'expired');
    }

    // Static methods
    public static function generateContractNumber()
    {
        $year = date('Y');
        $month = date('m');
        $prefix = 'CTR';

        $lastContract = self::where('contract_number', 'like', $prefix . '-' . $year . $month . '%')
                           ->orderBy('contract_number', 'desc')
                           ->first();

        if ($lastContract) {
            $lastNumber = (int) substr($lastContract->contract_number, -4);
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . '-' . $year . $month . '-' . $newNumber;
    }

    public static function getContractTypes()
    {
        return [
            'service' => 'Layanan',
            'supply' => 'Pengadaan',
            'maintenance' => 'Maintenance',
            'lease' => 'Sewa',
            'other' => 'Lainnya'
        ];
    }

    public static function getStatuses()
    {
        return [
            'draft' => 'Draft',
            'active' => 'Aktif',
            'expired' => 'Kadaluarsa',
            'terminated' => 'Dihentikan',
            'renewed' => 'Diperpanjang'
        ];
    }
}
