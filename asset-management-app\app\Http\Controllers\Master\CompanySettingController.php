<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\CompanySetting;
use Illuminate\Http\Request;

class CompanySettingController extends Controller
{
    /**
     * Display company settings.
     */
    public function index()
    {
        $setting = CompanySetting::first();
        return view('master.company-settings.index', compact('setting'));
    }

    /**
     * Show the form for editing company settings.
     */
    public function edit()
    {
        $setting = CompanySetting::first();
        if (!$setting) {
            $setting = new CompanySetting();
        }
        return view('master.company-settings.edit', compact('setting'));
    }

    /**
     * Update company settings.
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_code' => 'required|string|max:10',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'asset_usage_agreement' => 'nullable|string',
        ]);

        $setting = CompanySetting::first();
        if ($setting) {
            $setting->update($validated);
        } else {
            CompanySetting::create($validated);
        }

        return redirect()->route('master.company-settings.index')->with('success', 'Pengaturan perusahaan berhasil diperbarui.');
    }
}
