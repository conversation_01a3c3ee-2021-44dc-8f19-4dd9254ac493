<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add asset assignment permissions
        $assignmentPermissions = [
            ['slug' => 'asset_assignment_view', 'name' => 'Lihat Asset Assignment', 'module' => 'asset_assignment'],
            ['slug' => 'asset_assignment_create', 'name' => 'Buat Asset Assignment', 'module' => 'asset_assignment'],
            ['slug' => 'asset_assignment_edit', 'name' => 'Edit Asset Assignment', 'module' => 'asset_assignment'],
            ['slug' => 'asset_assignment_delete', 'name' => 'Hapus Asset Assignment', 'module' => 'asset_assignment'],
            ['slug' => 'asset_assignment_return', 'name' => 'Return Asset Assignment', 'module' => 'asset_assignment'],
            ['slug' => 'asset_assignment_transfer', 'name' => 'Transfer Asset Assignment', 'module' => 'asset_assignment'],
        ];

        // Create permissions if they don't exist
        foreach ($assignmentPermissions as $permissionData) {
            \App\Models\Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                [
                    'name' => $permissionData['name'],
                    'module' => $permissionData['module'],
                    'description' => $permissionData['name']
                ]
            );
        }

        // Assign permissions to roles
        $roles = \App\Models\Role::all();

        foreach ($roles as $role) {
            if ($role->name === 'Super Admin') {
                // Super Admin gets all permissions
                $permissionSlugs = collect($assignmentPermissions)->pluck('slug')->toArray();
                $this->assignPermissionsToRole($role, $permissionSlugs);
            } elseif ($role->name === 'Admin') {
                // Admin gets all assignment permissions
                $permissionSlugs = collect($assignmentPermissions)->pluck('slug')->toArray();
                $this->assignPermissionsToRole($role, $permissionSlugs);
            } elseif ($role->name === 'Manager') {
                // Manager gets view, create, return, transfer permissions
                $managerPermissions = [
                    'asset_assignment_view',
                    'asset_assignment_create',
                    'asset_assignment_return',
                    'asset_assignment_transfer'
                ];
                $this->assignPermissionsToRole($role, $managerPermissions);
            } elseif ($role->name === 'Staff') {
                // Staff gets only view permission
                $staffPermissions = ['asset_assignment_view'];
                $this->assignPermissionsToRole($role, $staffPermissions);
            }
        }
    }

    private function assignPermissionsToRole($role, $permissionSlugs)
    {
        $permissions = \App\Models\Permission::whereIn('slug', $permissionSlugs)->get();
        foreach ($permissions as $permission) {
            if (!$role->hasPermission($permission)) {
                $role->givePermissionTo($permission);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove asset assignment permissions
        $permissionSlugs = [
            'asset_assignment_view',
            'asset_assignment_create',
            'asset_assignment_edit',
            'asset_assignment_delete',
            'asset_assignment_return',
            'asset_assignment_transfer'
        ];

        // Remove permissions from all roles
        $roles = \App\Models\Role::all();
        $permissions = \App\Models\Permission::whereIn('slug', $permissionSlugs)->get();

        foreach ($roles as $role) {
            foreach ($permissions as $permission) {
                $role->revokePermissionTo($permission);
            }
        }

        // Delete the permissions
        \App\Models\Permission::whereIn('slug', $permissionSlugs)->delete();
    }
};
