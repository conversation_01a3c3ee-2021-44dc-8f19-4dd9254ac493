<?php $__env->startSection('title', 'Edit Role - Asset Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Role /</span> Edit Role
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Edit Role</h5>
          <a href="<?php echo e(route('master.roles.index')); ?>" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="<?php echo e(route('master.roles.update', $role)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name">Nama Role <span class="text-danger">*</span></label>
                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="name" name="name" value="<?php echo e(old('name', $role->name)); ?>" 
                       placeholder="Contoh: Manager">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="slug">Slug <span class="text-danger">*</span></label>
                <input type="text" class="form-control <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="slug" name="slug" value="<?php echo e(old('slug', $role->slug)); ?>" 
                       placeholder="Contoh: manager"
                       <?php echo e($role->slug === 'super-admin' ? 'readonly' : ''); ?>>
                <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <?php if($role->slug === 'super-admin'): ?>
                  <div class="form-text text-warning">Slug super-admin tidak dapat diubah</div>
                <?php else: ?>
                  <div class="form-text">Slug akan auto-generate dari nama jika dikosongkan</div>
                <?php endif; ?>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi role dan tanggung jawabnya"><?php echo e(old('description', $role->description)); ?></textarea>
              <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       <?php echo e(old('is_active', $role->is_active) ? 'checked' : ''); ?>

                       <?php echo e($role->slug === 'super-admin' ? 'disabled' : ''); ?>>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
                <?php if($role->slug === 'super-admin'): ?>
                  <div class="form-text text-warning">Super Admin selalu aktif</div>
                <?php endif; ?>
              </div>
            </div>

            <!-- Permissions Section -->
            <div class="mb-4">
              <label class="form-label">Permissions</label>
              <div class="card">
                <div class="card-body">
                  <?php if($permissions->count() > 0): ?>
                    <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module => $modulePermissions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="mb-4">
                      <div class="d-flex align-items-center mb-3">
                        <div class="form-check me-3">
                          <input class="form-check-input module-checkbox" type="checkbox" 
                                 id="module_<?php echo e($module); ?>" data-module="<?php echo e($module); ?>">
                          <label class="form-check-label fw-bold text-primary" for="module_<?php echo e($module); ?>">
                            <i class="ri-folder-line me-1"></i><?php echo e(ucfirst($module)); ?>

                          </label>
                        </div>
                        <small class="text-muted">(<?php echo e($modulePermissions->count()); ?> permissions)</small>
                      </div>
                      
                      <div class="row ms-3">
                        <?php $__currentLoopData = $modulePermissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 mb-2">
                          <div class="form-check">
                            <input class="form-check-input permission-checkbox" type="checkbox" 
                                   name="permissions[]" value="<?php echo e($permission->id); ?>" 
                                   id="permission_<?php echo e($permission->id); ?>" 
                                   data-module="<?php echo e($module); ?>"
                                   <?php echo e(in_array($permission->id, old('permissions', $rolePermissions)) ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="permission_<?php echo e($permission->id); ?>">
                              <?php echo e($permission->name); ?>

                            </label>
                          </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  <?php else: ?>
                    <div class="text-center py-4">
                      <i class="ri-shield-line display-4 text-muted mb-2"></i>
                      <p class="text-muted">Tidak ada permission yang tersedia.</p>
                    </div>
                  <?php endif; ?>
                </div>
              </div>
              <?php $__errorArgs = ['permissions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="text-danger mt-1"><?php echo e($message); ?></div>
              <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="<?php echo e(route('master.roles.index')); ?>" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Update
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Role</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Data Saat Ini:</h6>
            <p class="mb-2">Nama: <strong><?php echo e($role->name); ?></strong></p>
            <p class="mb-2">Slug: <strong><?php echo e($role->slug); ?></strong></p>
            <p class="mb-2">Status: 
              <span class="badge bg-<?php echo e($role->is_active ? 'success' : 'secondary'); ?>">
                <?php echo e($role->is_active ? 'Aktif' : 'Non-Aktif'); ?>

              </span>
            </p>
            <p class="mb-2">User: <strong><?php echo e($role->users->count()); ?> user</strong></p>
            <p class="mb-0">Permissions: <strong><?php echo e($role->permissions->count()); ?> permission</strong></p>
          </div>
          
          <?php if($role->slug === 'super-admin'): ?>
          <div class="alert alert-warning">
            <h6 class="alert-heading">Peringatan:</h6>
            <p class="mb-0">Role Super Admin memiliki batasan tertentu yang tidak dapat diubah untuk menjaga keamanan sistem.</p>
          </div>
          <?php else: ?>
          <div class="alert alert-warning">
            <h6 class="alert-heading">Peringatan:</h6>
            <ul class="mb-0">
              <li>Hati-hati mengubah permissions</li>
              <li>Perubahan akan mempengaruhi semua user dengan role ini</li>
              <li>Pastikan permissions sesuai dengan tanggung jawab</li>
            </ul>
          </div>
          <?php endif; ?>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Quick Actions</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllPermissions()">
              <i class="ri-check-double-line me-1"></i>Pilih Semua
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllPermissions()">
              <i class="ri-close-line me-1"></i>Hapus Semua
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto generate slug from name (only if not super-admin)
  const nameInput = document.getElementById('name');
  const slugInput = document.getElementById('slug');
  
  if (slugInput.getAttribute('readonly') !== '') {
    nameInput.addEventListener('input', function() {
      if (!slugInput.dataset.manual) {
        const slug = this.value.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
        slugInput.value = slug;
      }
    });

    // Mark slug as manually edited
    slugInput.addEventListener('input', function() {
      this.dataset.manual = 'true';
    });
  }

  // Module checkbox functionality
  const moduleCheckboxes = document.querySelectorAll('.module-checkbox');
  moduleCheckboxes.forEach(function(moduleCheckbox) {
    moduleCheckbox.addEventListener('change', function() {
      const module = this.dataset.module;
      const permissionCheckboxes = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);
      
      permissionCheckboxes.forEach(function(checkbox) {
        checkbox.checked = moduleCheckbox.checked;
      });
    });
  });

  // Permission checkbox functionality
  const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
  permissionCheckboxes.forEach(function(permissionCheckbox) {
    permissionCheckbox.addEventListener('change', function() {
      const module = this.dataset.module;
      const moduleCheckbox = document.querySelector(`input[data-module="${module}"].module-checkbox`);
      const modulePermissions = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);
      
      const checkedCount = Array.from(modulePermissions).filter(cb => cb.checked).length;
      const totalCount = modulePermissions.length;
      
      if (checkedCount === 0) {
        moduleCheckbox.checked = false;
        moduleCheckbox.indeterminate = false;
      } else if (checkedCount === totalCount) {
        moduleCheckbox.checked = true;
        moduleCheckbox.indeterminate = false;
      } else {
        moduleCheckbox.checked = false;
        moduleCheckbox.indeterminate = true;
      }
    });
  });

  // Initialize module checkboxes state
  moduleCheckboxes.forEach(function(moduleCheckbox) {
    const module = moduleCheckbox.dataset.module;
    const modulePermissions = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);
    const checkedCount = Array.from(modulePermissions).filter(cb => cb.checked).length;
    const totalCount = modulePermissions.length;
    
    if (checkedCount === 0) {
      moduleCheckbox.checked = false;
      moduleCheckbox.indeterminate = false;
    } else if (checkedCount === totalCount) {
      moduleCheckbox.checked = true;
      moduleCheckbox.indeterminate = false;
    } else {
      moduleCheckbox.checked = false;
      moduleCheckbox.indeterminate = true;
    }
  });
});

function selectAllPermissions() {
  const checkboxes = document.querySelectorAll('.permission-checkbox, .module-checkbox');
  checkboxes.forEach(function(checkbox) {
    checkbox.checked = true;
    checkbox.indeterminate = false;
  });
}

function clearAllPermissions() {
  const checkboxes = document.querySelectorAll('.permission-checkbox, .module-checkbox');
  checkboxes.forEach(function(checkbox) {
    checkbox.checked = false;
    checkbox.indeterminate = false;
  });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/master/roles/edit.blade.php ENDPATH**/ ?>