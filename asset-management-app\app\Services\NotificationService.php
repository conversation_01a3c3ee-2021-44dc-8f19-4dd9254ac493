<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\RequestAsset;
use App\Models\User;

class NotificationService
{
    /**
     * Create notification for new asset request
     */
    public function createRequestSubmittedNotification(RequestAsset $requestAsset)
    {
        // Get all users who can approve this request
        $approvers = $this->getApproversForRequest($requestAsset);
        
        foreach ($approvers as $approver) {
            Notification::createForUser(
                $approver->id,
                'approval_request',
                'Permintaan Asset Baru',
                "Permintaan asset baru dari {$requestAsset->requestedByUser->name} memerlukan persetujuan Anda.",
                [
                    'request_id' => $requestAsset->id,
                    'request_number' => $requestAsset->request_number,
                    'requester_name' => $requestAsset->requestedByUser->name,
                ]
            );
        }
    }

    /**
     * Create notification when request is approved
     */
    public function createRequestApprovedNotification(RequestAsset $requestAsset, User $approver)
    {
        Notification::createForUser(
            $requestAsset->requested_by,
            'approval_approved',
            'Permintaan Asset Disetujui',
            "Permintaan asset Anda ({$requestAsset->request_number}) telah disetujui oleh {$approver->name}.",
            [
                'request_id' => $requestAsset->id,
                'request_number' => $requestAsset->request_number,
                'approver_name' => $approver->name,
            ]
        );
    }

    /**
     * Create notification when request is rejected
     */
    public function createRequestRejectedNotification(RequestAsset $requestAsset, User $approver, $reason = null)
    {
        $message = "Permintaan asset Anda ({$requestAsset->request_number}) telah ditolak oleh {$approver->name}.";
        if ($reason) {
            $message .= " Alasan: {$reason}";
        }

        Notification::createForUser(
            $requestAsset->requested_by,
            'approval_rejected',
            'Permintaan Asset Ditolak',
            $message,
            [
                'request_id' => $requestAsset->id,
                'request_number' => $requestAsset->request_number,
                'approver_name' => $approver->name,
                'reason' => $reason,
            ]
        );
    }

    /**
     * Get approvers for a request
     */
    private function getApproversForRequest(RequestAsset $requestAsset)
    {
        // Get users with approval roles
        $approvalRoles = ['supervisor', 'manager', 'it_admin', 'hrd_admin', 'finance_admin', 'finance_manager'];
        
        return User::whereHas('role', function ($query) use ($approvalRoles) {
            $query->whereIn('slug', $approvalRoles);
        })->get();
    }

    /**
     * Get unread notifications for user
     */
    public function getUnreadNotifications($userId, $limit = 10)
    {
        return Notification::forUser($userId)
            ->unread()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get unread count for user
     */
    public function getUnreadCount($userId)
    {
        return Notification::getUnreadCountForUser($userId);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->markAsRead();
        }
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead($userId)
    {
        Notification::forUser($userId)->unread()->update(['read_at' => now()]);
    }

    /**
     * Get notification icon based on type
     */
    public function getNotificationIcon($type)
    {
        switch ($type) {
            case 'approval_request':
                return 'ri-file-list-3-line';
            case 'approval_approved':
                return 'ri-check-line';
            case 'approval_rejected':
                return 'ri-close-line';
            default:
                return 'ri-notification-line';
        }
    }

    /**
     * Get notification color based on type
     */
    public function getNotificationColor($type)
    {
        switch ($type) {
            case 'approval_request':
                return 'warning';
            case 'approval_approved':
                return 'success';
            case 'approval_rejected':
                return 'danger';
            default:
                return 'info';
        }
    }
}
