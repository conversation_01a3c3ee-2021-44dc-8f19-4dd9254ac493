@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Approval Workflow - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Approval /</span> Detail Workflow
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ $approvalWorkflow->name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.approval-workflows.edit', $approvalWorkflow) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.approval-workflows.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <!-- Status Badge -->
          <div class="mb-4">
            <span class="badge bg-{{ $approvalWorkflow->is_active ? 'success' : 'secondary' }} fs-6 me-2">
              {{ $approvalWorkflow->is_active ? 'Aktif' : 'Non-Aktif' }}
            </span>
            <span class="badge bg-info fs-6 me-2">{{ ucfirst(str_replace('_', ' ', $approvalWorkflow->module)) }}</span>
            <span class="badge bg-{{ $approvalWorkflow->priority > 5 ? 'danger' : ($approvalWorkflow->priority > 2 ? 'warning' : 'success') }} fs-6">
              Prioritas {{ $approvalWorkflow->priority }}
            </span>
          </div>

          <!-- Workflow Information -->
          <h6 class="mb-3">Informasi Workflow</h6>
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Workflow</label>
                <p class="fw-bold">{{ $approvalWorkflow->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Workflow</label>
                <p><span class="badge bg-primary fs-6">{{ $approvalWorkflow->code }}</span></p>
              </div>
            </div>
          </div>

          @if($approvalWorkflow->description)
          <div class="mb-4">
            <label class="form-label text-muted">Deskripsi</label>
            <p>{{ $approvalWorkflow->description }}</p>
          </div>
          @endif

          <!-- Conditions -->
          <hr class="my-4">
          <h6 class="mb-3">Kondisi Workflow</h6>
          @if(is_array($approvalWorkflow->conditions) && count($approvalWorkflow->conditions) > 0)
            <div class="table-responsive">
              <table class="table table-sm table-bordered">
                <thead>
                  <tr>
                    <th>Field</th>
                    <th>Operator</th>
                    <th>Nilai</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($approvalWorkflow->conditions as $condition)
                  <tr>
                    <td>
                      <span class="badge bg-info">
                        @switch($condition['field'])
                          @case('estimated_price') Total Harga @break
                          @case('quantity') Jumlah @break
                          @case('priority') Prioritas @break
                          @case('category_id') Kategori @break
                          @case('branch_id') Cabang @break
                          @case('division_id') Divisi @break
                          @case('user_role') Role User @break
                          @default {{ $condition['field'] }}
                        @endswitch
                      </span>
                    </td>
                    <td>
                      @switch($condition['operator'])
                        @case('equals') Sama dengan (=) @break
                        @case('not_equals') Tidak sama dengan (≠) @break
                        @case('greater_than') Lebih besar (>) @break
                        @case('greater_than_or_equal') Lebih besar sama dengan (≥) @break
                        @case('less_than') Lebih kecil (<) @break
                        @case('less_than_or_equal') Lebih kecil sama dengan (≤) @break
                        @case('in') Dalam list @break
                        @case('not_in') Tidak dalam list @break
                        @case('contains') Mengandung @break
                        @default {{ $condition['operator'] }}
                      @endswitch
                    </td>
                    <td>
                      <strong>
                        @if($condition['field'] === 'estimated_price')
                          Rp {{ number_format($condition['value'], 0, ',', '.') }}
                        @else
                          {{ is_array($condition['value']) ? implode(', ', $condition['value']) : $condition['value'] }}
                        @endif
                      </strong>
                    </td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          @else
            <div class="alert alert-info">
              <i class="ri-information-line me-2"></i>Workflow ini tidak memiliki kondisi khusus (berlaku untuk semua)
            </div>
          @endif

          <!-- Approval Levels -->
          <hr class="my-4">
          <h6 class="mb-3">Level Approval ({{ $approvalWorkflow->levels->count() }} Level)</h6>
          @if($approvalWorkflow->levels->count() > 0)
            @foreach($approvalWorkflow->levels as $level)
            <div class="card border mb-3">
              <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">
                    <span class="badge bg-primary me-2">{{ $level->level_order }}</span>
                    {{ $level->level_name }}
                  </h6>
                  <div>
                    @if($level->is_required)
                      <span class="badge bg-danger">Wajib</span>
                    @endif
                    @if($level->can_skip)
                      <span class="badge bg-warning">Bisa Di-skip</span>
                    @endif
                    @if(!$level->is_active)
                      <span class="badge bg-secondary">Non-Aktif</span>
                    @endif
                  </div>
                </div>
              </div>
              <div class="card-body">
                @if($level->description)
                  <p class="text-muted mb-3">{{ $level->description }}</p>
                @endif

                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label text-muted">Tipe Approver</label>
                      <p>
                        <span class="badge bg-info">
                          @switch($level->approver_type)
                            @case('role') Berdasarkan Role @break
                            @case('user') User Spesifik @break
                            @case('position') Berdasarkan Posisi @break
                            @default {{ $level->approver_type }}
                          @endswitch
                        </span>
                      </p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label text-muted">Approver</label>
                      <div>
                        @if($level->approver_type === 'role' && isset($level->approver_config['role_ids']))
                          @foreach($level->approver_config['role_ids'] as $roleId)
                            @php $role = \App\Models\Role::find($roleId) @endphp
                            @if($role)
                              <span class="badge bg-success me-1">{{ $role->name }}</span>
                            @endif
                          @endforeach
                        @elseif($level->approver_type === 'user' && isset($level->approver_config['user_ids']))
                          @foreach($level->approver_config['user_ids'] as $userId)
                            @php $user = \App\Models\User::find($userId) @endphp
                            @if($user)
                              <span class="badge bg-success me-1">{{ $user->name }}</span>
                            @endif
                          @endforeach
                        @elseif($level->approver_type === 'position' && isset($level->approver_config['position']))
                          <span class="badge bg-success">
                            @switch($level->approver_config['position'])
                              @case('direct_supervisor') Supervisor Langsung @break
                              @case('branch_manager') Manager Cabang @break
                              @case('division_head') Kepala Divisi @break
                              @default {{ $level->approver_config['position'] }}
                            @endswitch
                          </span>
                        @endif
                      </div>
                    </div>
                  </div>
                </div>

                @if($level->timeout_hours)
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label text-muted">Timeout</label>
                      <p>{{ $level->timeout_hours }} jam</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label text-muted">Aksi Timeout</label>
                      <p>
                        <span class="badge bg-{{ $level->timeout_action === 'approve' ? 'success' : ($level->timeout_action === 'reject' ? 'danger' : 'warning') }}">
                          @switch($level->timeout_action)
                            @case('approve') Auto Approve @break
                            @case('reject') Auto Reject @break
                            @case('escalate') Escalate @break
                            @default {{ $level->timeout_action }}
                          @endswitch
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                @endif
              </div>
            </div>
            @endforeach
          @else
            <div class="alert alert-warning">
              <i class="ri-alert-line me-2"></i>Workflow ini belum memiliki level approval
            </div>
          @endif
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Workflow</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ strtoupper(substr($approvalWorkflow->code, 0, 2)) }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $approvalWorkflow->name }}</h6>
              <small class="text-muted">{{ $approvalWorkflow->code }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>{{ $approvalWorkflow->levels->count() }} Level Approval</li>
              <li>{{ count($approvalWorkflow->conditions) }} Kondisi</li>
              <li>Prioritas {{ $approvalWorkflow->priority }}</li>
              <li>Module {{ ucfirst(str_replace('_', ' ', $approvalWorkflow->module)) }}</li>
              <li>Dibuat {{ $approvalWorkflow->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.approval-workflows.edit', $approvalWorkflow) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Workflow
            </a>
            
            @if($approvalWorkflow->is_active)
              <form action="{{ route('master.approval-workflows.update', $approvalWorkflow) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="is_active" value="0">
                <button type="submit" class="btn btn-warning w-100" onclick="return confirm('Yakin ingin menonaktifkan workflow ini?')">
                  <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
                </button>
              </form>
            @else
              <form action="{{ route('master.approval-workflows.update', $approvalWorkflow) }}" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="is_active" value="1">
                <button type="submit" class="btn btn-success w-100" onclick="return confirm('Yakin ingin mengaktifkan workflow ini?')">
                  <i class="ri-play-circle-line me-1"></i>Aktifkan
                </button>
              </form>
            @endif
            
            @if(!$approvalWorkflow->histories()->exists())
            <form action="{{ route('master.approval-workflows.destroy', $approvalWorkflow) }}" method="POST">
              @csrf
              @method('DELETE')
              <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Yakin ingin menghapus workflow ini?\n\nTindakan ini tidak dapat dibatalkan!')">
                <i class="ri-delete-bin-line me-1"></i>Hapus Workflow
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Flow Diagram</h6>
        </div>
        <div class="card-body">
          <div class="workflow-diagram">
            @foreach($approvalWorkflow->levels as $level)
              <div class="d-flex align-items-center mb-2">
                <div class="badge bg-primary me-2">{{ $level->level_order }}</div>
                <div class="flex-grow-1">
                  <div class="fw-bold">{{ $level->level_name }}</div>
                  <small class="text-muted">
                    @if($level->timeout_hours)
                      {{ $level->timeout_hours }}h timeout
                    @endif
                  </small>
                </div>
              </div>
              @if(!$loop->last)
                <div class="text-center mb-2">
                  <i class="ri-arrow-down-line text-muted"></i>
                </div>
              @endif
            @endforeach
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
