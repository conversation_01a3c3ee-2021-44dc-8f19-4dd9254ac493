// Alerts
// *******************************************************************************

// <PERSON>ert mixins
@each $state, $value in $theme-colors {
  @if $state != primary and $state != light {
    @include template-alert-variant('.alert-#{$state}', $value);
  }
}

// Adjust close link position
.alert-dismissible {
  .btn-close {
    padding: $alert-padding-y * 1.375 $alert-padding-x;
  }
}

// Alert and alert-icon styles
.alert {
  line-height: 1.375rem;
  .alert-icon {
    color: $white;
    height: $alert-icon-size;
    width: $alert-icon-size;
    padding: $spacer * 0.75;
    margin-right: $spacer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
