<?php

namespace App\Http\Controllers;

use App\Models\AssetMaintenance;
use App\Models\Asset;
use App\Models\User;
use App\Helpers\BranchHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AssetMaintenanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AssetMaintenance::with(['asset.branch', 'asset.assetCategory', 'supplier', 'requestedBy', 'assignedTo', 'approvedBy']);

        // Apply branch access control for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereHas('asset', function ($q) use ($accessibleBranchIds) {
                    $q->whereIn('branch_id', $accessibleBranchIds);
                });
            }
        }

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('maintenance_type')) {
            $query->where('maintenance_type', $request->maintenance_type);
        }

        if ($request->filled('asset_id')) {
            $query->where('asset_id', $request->asset_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_date', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('maintenance_number', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('asset', function ($assetQuery) use ($search) {
                      $assetQuery->where('asset_code', 'like', "%{$search}%")
                                 ->orWhere('name', 'like', "%{$search}%");
                  });
            });
        }

        $maintenances = $query->latest()->paginate(15)->withQueryString();

        // Get filter options
        $assets = Asset::with('branch')->get();
        $users = User::all();

        return view('asset-maintenances.index', compact('maintenances', 'assets', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get accessible assets based on user's branch access (only active assets)
        $assetsQuery = Asset::with(['branch', 'assetCategory'])
                           ->where('status', 'active'); // Only show active assets

        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $assetsQuery->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        $assets = $assetsQuery->get();
        $users = User::all();

        // Get maintenance types from lookup
        $maintenanceTypes = \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')
                                             ->where('is_active', true)
                                             ->orderBy('sort_order')
                                             ->get();

        return view('asset-maintenances.create', compact('assets', 'users', 'maintenanceTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Get valid maintenance types from lookup
        $validMaintenanceTypes = \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')
                                                   ->where('is_active', true)
                                                   ->pluck('lookup_name')
                                                   ->toArray();

        $validated = $request->validate([
            'asset_id' => 'required|exists:assets,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'maintenance_type' => 'required|in:' . implode(',', $validMaintenanceTypes),
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'problem_description' => 'nullable|string',
            'estimated_cost' => 'nullable|numeric|min:0',
            'scheduled_date' => 'required|date|after_or_equal:today',
            'priority' => 'required|in:low,medium,high,critical',
            'notes' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
        ]);

        // Check if user has access to the selected asset
        $asset = Asset::findOrFail($validated['asset_id']);
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke asset ini.');
            }
        }

        // Generate maintenance number using asset ID for proper branch/category detection
        $maintenanceNumber = AssetMaintenance::generateMaintenanceNumber($validated['asset_id']);

        $maintenance = AssetMaintenance::create([
            'maintenance_number' => $maintenanceNumber,
            'asset_id' => $validated['asset_id'],
            'supplier_id' => $validated['supplier_id'],
            'maintenance_type' => $validated['maintenance_type'],
            'title' => $validated['title'],
            'description' => $validated['description'],
            'problem_description' => $validated['problem_description'],
            'estimated_cost' => $validated['estimated_cost'],
            'scheduled_date' => $validated['scheduled_date'],
            'priority' => $validated['priority'],
            'notes' => $validated['notes'],
            'assigned_to' => $validated['assigned_to'],
            'requested_by' => auth()->id(),
            'status' => 'scheduled',
            'previous_asset_status' => $asset->status, // Store current asset status
        ]);

        // Update asset status to maintenance
        $this->updateAssetMaintenanceStatus($asset, 'maintenance');

        return redirect()->route('asset-maintenances.show', $maintenance)
                        ->with('success', 'Maintenance asset berhasil dijadwalkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetMaintenance $assetMaintenance)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        $assetMaintenance->load([
            'asset.branch',
            'asset.assetCategory',
            'asset.assetType',
            'requestedBy',
            'assignedTo',
            'approvedBy'
        ]);

        return view('asset-maintenances.show', compact('assetMaintenance'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetMaintenance $assetMaintenance)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        // Only allow editing if maintenance is not completed
        if ($assetMaintenance->status === 'completed') {
            return redirect()->route('asset-maintenances.show', $assetMaintenance)
                           ->with('error', 'Maintenance yang sudah selesai tidak dapat diedit.');
        }

        $assetMaintenance->load(['asset.branch', 'asset.assetCategory', 'supplier']);

        // Get accessible assets based on user's branch access (only active assets)
        $assetsQuery = Asset::with(['branch', 'assetCategory'])
                           ->where('status', 'active'); // Only show active assets

        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $assetsQuery->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        $assets = $assetsQuery->get();
        $users = User::all();

        // Get maintenance types from lookup
        $maintenanceTypes = \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')
                                             ->where('is_active', true)
                                             ->orderBy('sort_order')
                                             ->get();

        return view('asset-maintenances.edit', compact('assetMaintenance', 'assets', 'users', 'maintenanceTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetMaintenance $assetMaintenance)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        // Only allow updating if maintenance is not completed
        if ($assetMaintenance->status === 'completed') {
            return redirect()->route('asset-maintenances.show', $assetMaintenance)
                           ->with('error', 'Maintenance yang sudah selesai tidak dapat diedit.');
        }

        // Get valid maintenance types from lookup
        $validMaintenanceTypes = \App\Models\Lookup::where('lookup_code', 'MTC_TYPE')
                                                   ->where('is_active', true)
                                                   ->pluck('lookup_name')
                                                   ->toArray();

        $validated = $request->validate([
            'asset_id' => 'required|exists:assets,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'maintenance_type' => 'required|in:' . implode(',', $validMaintenanceTypes),
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'problem_description' => 'nullable|string',
            'solution_description' => 'nullable|string',
            'estimated_cost' => 'nullable|numeric|min:0',
            'actual_cost' => 'nullable|numeric|min:0',
            'scheduled_date' => 'required|date',
            'started_date' => 'nullable|date',
            'completed_date' => 'nullable|date|after_or_equal:started_date',
            'priority' => 'required|in:low,medium,high,critical',
            'status' => 'required|in:scheduled,in_progress,completed,cancelled',
            'notes' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
        ]);

        // Check if user has access to the selected asset
        $asset = Asset::findOrFail($validated['asset_id']);
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke asset ini.');
            }
        }

        // Auto-set dates based on status changes
        if ($validated['status'] === 'in_progress' && !$assetMaintenance->started_date) {
            $validated['started_date'] = now()->toDateString();
        }

        if ($validated['status'] === 'completed' && !$assetMaintenance->completed_date) {
            $validated['completed_date'] = now()->toDateString();
        }

        // Check if status is changing to completed or cancelled
        $oldStatus = $assetMaintenance->status;
        $newStatus = $validated['status'];

        $assetMaintenance->update($validated);

        // Handle asset status changes based on maintenance status
        if ($oldStatus !== $newStatus) {
            if ($newStatus === 'completed' || $newStatus === 'cancelled') {
                // Restore asset status when maintenance is completed or cancelled
                $this->restoreAssetStatus($assetMaintenance->asset);
            } elseif ($oldStatus === 'scheduled' && $newStatus === 'in_progress') {
                // Ensure asset is in maintenance status when work starts
                $this->updateAssetMaintenanceStatus($assetMaintenance->asset, 'maintenance');
            }
        }

        return redirect()->route('asset-maintenances.show', $assetMaintenance)
                        ->with('success', 'Data maintenance berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetMaintenance $assetMaintenance)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        // Only allow deletion if maintenance is not completed
        if ($assetMaintenance->status === 'completed') {
            return redirect()->route('asset-maintenances.index')
                           ->with('error', 'Maintenance yang sudah selesai tidak dapat dihapus.');
        }

        // Store asset reference before deletion
        $asset = $assetMaintenance->asset;

        $assetMaintenance->delete();

        // Restore asset status after maintenance deletion
        $this->restoreAssetStatus($asset);

        return redirect()->route('asset-maintenances.index')
                        ->with('success', 'Data maintenance berhasil dihapus.');
    }

    /**
     * Start maintenance (change status to in_progress)
     */
    public function start(AssetMaintenance $assetMaintenance)
    {
        // Check permission for starting maintenance
        if (!auth()->user()->isSuperAdmin()) {
            $userPermissions = session('user_permissions', []);
            if (!in_array('asset_maintenance_start', $userPermissions)) {
                abort(403, 'Anda tidak memiliki izin untuk memulai maintenance.');
            }
        }

        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        if (!$assetMaintenance->canBeStarted()) {
            return redirect()->back()->with('error', 'Maintenance tidak dapat dimulai.');
        }

        $assetMaintenance->update([
            'status' => 'in_progress',
            'started_date' => now()->toDateString(),
        ]);

        return redirect()->route('asset-maintenances.show', $assetMaintenance)
                        ->with('success', 'Maintenance telah dimulai.');
    }

    /**
     * Complete maintenance (change status to completed)
     */
    public function complete(AssetMaintenance $assetMaintenance)
    {
        // Check permission for completing maintenance
        if (!auth()->user()->isSuperAdmin()) {
            $userPermissions = session('user_permissions', []);
            if (!in_array('asset_maintenance_complete', $userPermissions)) {
                abort(403, 'Anda tidak memiliki izin untuk menyelesaikan maintenance.');
            }
        }

        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        if (!$assetMaintenance->canBeCompleted()) {
            return redirect()->back()->with('error', 'Maintenance tidak dapat diselesaikan.');
        }

        $assetMaintenance->update([
            'status' => 'completed',
            'completed_date' => now()->toDateString(),
        ]);

        // Restore asset status after maintenance completion
        $this->restoreAssetStatus($assetMaintenance->asset);

        return redirect()->route('asset-maintenances.show', $assetMaintenance)
                        ->with('success', 'Maintenance telah diselesaikan.');
    }

    /**
     * Cancel maintenance
     */
    public function cancel(AssetMaintenance $assetMaintenance)
    {
        // Check access for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($assetMaintenance->asset->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke data maintenance ini.');
            }
        }

        if (!$assetMaintenance->canBeCancelled()) {
            return redirect()->back()->with('error', 'Maintenance tidak dapat dibatalkan.');
        }

        $assetMaintenance->update(['status' => 'cancelled']);

        // Restore asset status after maintenance cancellation
        $this->restoreAssetStatus($assetMaintenance->asset);

        return redirect()->route('asset-maintenances.show', $assetMaintenance)
                        ->with('success', 'Maintenance telah dibatalkan.');
    }

    /**
     * Update asset maintenance status and store previous status
     */
    private function updateAssetMaintenanceStatus($asset, $newStatus)
    {
        // Update asset status to maintenance
        $asset->update(['status' => $newStatus]);
    }

    /**
     * Restore asset status after maintenance completion or cancellation
     */
    private function restoreAssetStatus($asset)
    {
        // Check if there are any other active maintenances for this asset
        $activeMaintenances = AssetMaintenance::where('asset_id', $asset->id)
            ->whereIn('status', ['scheduled', 'in_progress'])
            ->count();

        // Only restore status if no other active maintenances exist
        if ($activeMaintenances === 0) {
            // Get the most recent maintenance record to restore previous status
            $lastMaintenance = AssetMaintenance::where('asset_id', $asset->id)
                ->whereIn('status', ['completed', 'cancelled'])
                ->whereNotNull('previous_asset_status')
                ->latest()
                ->first();

            $restoreStatus = $lastMaintenance && $lastMaintenance->previous_asset_status
                ? $lastMaintenance->previous_asset_status
                : 'active';

            $asset->update(['status' => $restoreStatus]);
        }
    }
}
