# Database Backup & Restore System

## 📋 Overview
Sistem Backup & Restore Database telah diimplementasi untuk memberikan kemampuan backup dan restore database yang aman dan mudah digunakan. Fitur ini ditempatkan di menu **Reports & Settings → Settings → Backup DB**.

## 🎯 Key Features

### 1. **Database Backup**
- **Manual Backup**: Membuat backup database secara manual
- **Structure Only**: Backup hanya struktur tabel tanpa data
- **Full Backup**: Backup struktur tabel beserta semua data
- **Custom Naming**: Penamaan backup yang dapat dikustomisasi
- **Multiple Methods**: Mendukung mysqldump dan PHP-based backup

### 2. **File Management**
- **Download Backup**: Download file backup ke komputer lokal
- **Upload Backup**: Upload file backup dari komputer lokal
- **Delete Backup**: Hapus file backup yang tidak diperlukan
- **File Information**: Informasi detail file (ukuran, tanggal, dll)

### 3. **Database Restore**
- **Restore from Backup**: Restore database dari file backup
- **Safety Confirmation**: Konfirmasi keamanan sebelum restore
- **Restore Logging**: Log aktivitas restore untuk audit trail

### 4. **Security & Safety**
- **Access Control**: Hanya user yang memiliki akses yang dapat menggunakan
- **Confirmation Dialog**: Dialog konfirmasi untuk operasi berbahaya
- **Backup Logging**: Log semua aktivitas backup dan restore
- **File Validation**: Validasi file backup sebelum upload/restore

## 🔧 Technical Implementation

### **Controller Features**
```php
// Main backup operations
- index()           // Display backup management page
- createBackup()    // Create new database backup
- downloadBackup()  // Download backup file
- deleteBackup()    // Delete backup file
- restoreBackup()   // Restore database from backup
- uploadBackup()    // Upload backup file

// Helper methods
- getBackupList()   // Get list of available backups
- findMysqldump()   // Find mysqldump executable
- createPhpBackup() // PHP-based backup fallback
- formatBytes()     // Format file size display
```

### **Backup Methods**
1. **mysqldump (Primary)**
   - Uses native MySQL mysqldump utility
   - Faster and more reliable for large databases
   - Automatically detects mysqldump location
   - Supports XAMPP, WAMP, Linux, macOS

2. **PHP-based (Fallback)**
   - Pure PHP implementation using Laravel DB
   - Works when mysqldump is not available
   - Handles all table structures and data
   - Automatic fallback if mysqldump fails

### **File Storage**
- **Location**: `storage/app/backups/`
- **Format**: `.sql` files
- **Naming**: Customizable with timestamp
- **Security**: Files stored outside public directory

## 📱 User Interface

### **Dashboard Statistics**
- **Total Backup**: Jumlah total file backup
- **Total Size**: Total ukuran semua backup
- **Last Backup**: Waktu backup terakhir
- **Database Info**: Informasi database yang aktif
- **Most Active**: User yang paling aktif membuat backup
- **Backup Types**: Jumlah tipe backup yang tersedia

### **Backup List**
- **Card Layout**: Tampilan card untuk setiap backup
- **File Information**: Nama file, ukuran, tanggal, creator
- **Creator Information**: Menampilkan siapa yang membuat backup
- **Backup Type**: Manual, Uploaded, atau Automatic
- **Data Type**: Full Data atau Structure Only
- **Action Menu**: Download, Restore, Delete
- **Tooltips**: Informasi detail saat hover
- **Responsive Design**: Bekerja di semua device

### **Modal Dialogs**
1. **Create Backup Modal**
   - Custom backup name input
   - Include data checkbox
   - Progress information

2. **Upload Backup Modal**
   - Drag & drop file upload
   - File validation (.sql only)
   - Size limit (100MB)

3. **Restore Confirmation Modal**
   - Safety warnings
   - Confirmation checkbox
   - Risk acknowledgment

## 🚨 Safety Features

### **Backup Safety**
- **Automatic Validation**: Validasi file backup sebelum operasi
- **Error Handling**: Penanganan error yang comprehensive
- **Logging**: Log semua aktivitas untuk audit trail
- **File Size Check**: Validasi ukuran file untuk mencegah masalah

### **Restore Safety**
- **Multiple Warnings**: Peringatan berlapis sebelum restore
- **Confirmation Required**: Checkbox konfirmasi wajib dicentang
- **Backup Recommendation**: Saran backup sebelum restore
- **Rollback Information**: Informasi tentang irreversible operation

### **Access Control**
- **Authentication Required**: Hanya user yang login dapat akses
- **Permission Based**: Dapat dikontrol dengan permission system
- **Branch Isolation**: Dapat dibatasi per cabang jika diperlukan

## 🔄 Workflow Examples

### **Creating Backup**
1. **Access Menu**: Reports & Settings → Settings → Backup DB
2. **Click "Buat Backup"**: Buka modal create backup
3. **Set Parameters**: Nama backup dan include data option
4. **Execute**: Sistem membuat backup file
5. **Confirmation**: Notifikasi sukses dan file muncul di list

### **Restoring Database**
1. **Select Backup**: Pilih file backup dari list
2. **Click "Restore"**: Buka confirmation modal
3. **Read Warnings**: Baca semua peringatan dengan seksama
4. **Confirm**: Centang checkbox konfirmasi
5. **Execute**: Database di-restore dari backup
6. **Verification**: Verifikasi hasil restore

### **File Management**
1. **Download**: Klik "Download" untuk simpan ke komputer
2. **Upload**: Drag & drop file .sql ke upload area
3. **Delete**: Klik "Hapus" dengan konfirmasi
4. **View Info**: Lihat informasi detail file backup

## 📊 Database Tables

### **backup_logs**
```sql
- id (Primary Key)
- filename (VARCHAR)
- backup_name (VARCHAR)
- type (ENUM: manual, automatic, uploaded)
- include_data (BOOLEAN)
- file_size (BIGINT)
- created_by (Foreign Key to users)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

### **restore_logs**
```sql
- id (Primary Key)
- filename (VARCHAR)
- restored_by (Foreign Key to users)
- restored_at (TIMESTAMP)
```

## 🎮 Menu Integration

### **Menu Location**
```
Reports & Settings
└── Settings
    ├── Profile
    ├── System Settings
    └── Backup DB ← New menu item
```

### **Menu Configuration**
```json
{
  "url": "backup",
  "name": "Backup DB",
  "slug": "backup.index",
  "icon": "ri-database-2-line"
}
```

## 🔍 File Formats

### **Backup File Structure**
```sql
-- Database Backup
-- Generated on: 2025-06-28 18:45:00
-- Database: asset_management

SET FOREIGN_KEY_CHECKS=0;

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (...);

-- Dumping data for table `users`
INSERT INTO `users` (...) VALUES (...);

SET FOREIGN_KEY_CHECKS=1;
```

### **Supported Formats**
- **Input**: .sql files only
- **Output**: Standard MySQL dump format
- **Encoding**: UTF-8
- **Compression**: Not implemented (future enhancement)

## 📈 Performance Considerations

### **Large Database Handling**
- **Memory Management**: Efficient memory usage for large datasets
- **Timeout Handling**: Extended timeout for large operations
- **Progress Indication**: User feedback during long operations
- **Chunked Processing**: Process large tables in chunks

### **Storage Management**
- **File Size Monitoring**: Track total backup storage usage
- **Cleanup Recommendations**: Suggest old backup cleanup
- **Disk Space Check**: Verify available disk space
- **Compression**: Future enhancement for storage optimization

## 🚀 Future Enhancements

### **Planned Features**
- **Scheduled Backups**: Automatic backup scheduling
- **Backup Compression**: Gzip compression for smaller files
- **Cloud Storage**: Integration with cloud storage services
- **Incremental Backup**: Backup only changed data
- **Backup Verification**: Automatic backup integrity check

### **Advanced Features**
- **Multi-Database**: Support for multiple database backup
- **Selective Backup**: Backup specific tables only
- **Backup Encryption**: Encrypt backup files for security
- **Remote Backup**: Backup to remote servers
- **Backup Monitoring**: Real-time backup status monitoring

---

**Sistem Backup & Restore Database memberikan solusi lengkap untuk manajemen backup database dengan keamanan tinggi, kemudahan penggunaan, dan fleksibilitas yang dibutuhkan untuk operasional sistem asset management.**
