<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AssetReportPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Add permissions to relevant roles for asset reports
        $rolesToUpdate = ['staff', 'viewer', 'manager', 'admin'];

        $reportPermissions = [
            'reports.view',
            'reports.assets.view',
            'reports.assets.export',
        ];

        foreach ($rolesToUpdate as $roleSlug) {
            $role = DB::table('roles')->where('slug', $roleSlug)->first();

            if (!$role) {
                $this->command->error("Role not found: {$roleSlug}");
                continue;
            }

            foreach ($reportPermissions as $permissionSlug) {
                $permission = DB::table('permissions')->where('slug', $permissionSlug)->first();
                if ($permission) {
                    // Check if permission already assigned
                    $exists = DB::table('role_permissions')
                        ->where('role_id', $role->id)
                        ->where('permission_id', $permission->id)
                        ->exists();

                    if (!$exists) {
                        DB::table('role_permissions')->insert([
                            'role_id' => $role->id,
                            'permission_id' => $permission->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $this->command->info("Added permission: {$permissionSlug} to {$role->name} role");
                    } else {
                        $this->command->info("Permission already exists: {$permissionSlug} for {$role->name}");
                    }
                } else {
                    $this->command->error("Permission not found: {$permissionSlug}");
                }
            }
        }
    }
}
