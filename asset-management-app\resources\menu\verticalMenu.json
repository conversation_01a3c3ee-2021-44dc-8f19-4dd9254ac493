{"menu": [{"url": "dashboard", "name": "Dashboard", "icon": "menu-icon tf-icons ri-home-smile-line", "slug": "dashboard"}, {"menuHeader": "Asset Management"}, {"name": "Asset Management", "icon": "menu-icon tf-icons ri-computer-line", "slug": "assets", "permission": "assets.view", "submenu": [{"url": "assets/view-all", "name": "Lihat Data Asset", "slug": "assets.view-all", "permission": "assets.view"}, {"url": "assets/create", "name": "Tambah Asset", "slug": "assets.create", "permission": "assets.create"}, {"url": "asset-categories", "name": "<PERSON><PERSON><PERSON>", "slug": "asset-categories.index", "permission": "asset-categories.view"}, {"url": "qr-labels", "name": "Konfigurasi QR Label", "slug": "qr-labels.index", "permission": "assets.view"}]}, {"name": "<PERSON><PERSON> Assign", "icon": "menu-icon tf-icons ri-user-follow-line", "slug": "asset-assignments", "submenu": [{"url": "asset-assignments", "name": "<PERSON><PERSON><PERSON> Assign", "slug": "asset-assignments.index", "permission": "asset_assignment_view"}, {"url": "asset-assignments/create", "name": "Tambah Assign", "slug": "asset-assignments.create", "permission": "asset_assignment_create"}]}, {"name": "Stock Opname", "icon": "menu-icon tf-icons ri-search-eye-line", "slug": "stock-opname", "permission": "stock_opname_view", "submenu": [{"url": "stock-opnames", "name": "Daftar Stock Opname", "slug": "stock-opnames.index", "permission": "stock_opname_view"}, {"url": "stock-opnames/create", "name": "Buat Stock Opname", "slug": "stock-opnames.create", "permission": "stock_opname_create"}, {"url": "stock-opnames/scan", "name": "<PERSON><PERSON>", "slug": "stock-opnames.scan", "permission": "stock_opname_scan"}]}, {"menuHeader": "Master Data"}, {"name": "Master Data", "icon": "menu-icon tf-icons ri-database-2-line", "slug": "master", "submenu": [{"url": "master/company-settings", "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "master.company-settings"}, {"url": "master/document-numbers", "name": "<PERSON><PERSON>", "slug": "master.document-numbers"}, {"url": "master/branches", "name": "Master <PERSON><PERSON><PERSON>", "slug": "master.branches"}, {"url": "master/users", "name": "Master User", "slug": "master.users"}, {"url": "master/divisions", "name": "Master Divisi", "slug": "master.divisions"}, {"url": "master/employees", "name": "Master <PERSON><PERSON><PERSON>", "slug": "master.employees", "permission": "employee_view"}, {"url": "master/categories", "name": "Master <PERSON><PERSON><PERSON>", "slug": "master.categories"}, {"url": "master/asset-types", "name": "<PERSON><PERSON>", "slug": "master.asset-types"}, {"url": "master/roles", "name": "Master Role", "slug": "master.roles"}, {"url": "master/approval-workflows", "name": "Master A<PERSON><PERSON>al", "slug": "master.approval-workflows"}, {"url": "master/lookups", "name": "Master Lookup", "slug": "master.lookups"}, {"url": "master/asset-field-configurations", "name": "Konfigurasi Field Asset", "slug": "master.asset-field-configurations"}]}, {"name": "Master Supplier", "icon": "menu-icon tf-icons ri-building-line", "slug": "suppliers", "submenu": [{"url": "suppliers", "name": "Daftar Supplier", "slug": "suppliers.index", "permission": "supplier_view"}, {"url": "suppliers/create", "name": "Tambah Supplier", "slug": "suppliers.create", "permission": "supplier_create"}]}, {"name": "Kontrak", "icon": "menu-icon tf-icons ri-file-text-line", "slug": "contracts", "submenu": [{"url": "contracts", "name": "<PERSON><PERSON><PERSON>", "slug": "contracts.index", "permission": "contracts.view"}, {"url": "contracts/create", "name": "Tambah Kontrak", "slug": "contracts.create", "permission": "contracts.create"}]}, {"menuHeader": "Transaksi"}, {"url": "", "name": "<PERSON><PERSON><PERSON><PERSON>", "icon": "menu-icon ri-file-add-line", "slug": "request-assets", "submenu": [{"url": "request-assets", "name": "<PERSON><PERSON><PERSON>", "slug": "request-assets.index"}, {"url": "request-assets/create", "name": "<PERSON><PERSON><PERSON>", "slug": "request-assets.create"}]}, {"name": "Maintenance Asset", "icon": "menu-icon tf-icons ri-tools-line", "slug": "asset-maintenances", "permission": "asset_maintenance_view", "submenu": [{"url": "asset-maintenances", "name": "Daftar Maintenance", "slug": "asset-maintenances.index", "permission": "asset_maintenance_view"}, {"url": "asset-maintenances/create", "name": "Tambah Maintenance", "slug": "asset-maintenances.create", "permission": "asset_maintenance_create"}]}, {"menuHeader": "Reports & Settings"}, {"name": "Reports", "icon": "menu-icon tf-icons ri-file-chart-line", "slug": "reports", "permission": "report_view", "submenu": [{"url": "reports/assets", "name": "<PERSON><PERSON><PERSON>", "slug": "reports.assets", "permission": "report_asset"}, {"url": "reports/movements", "name": "<PERSON><PERSON><PERSON>", "slug": "reports.movements", "permission": "report_asset"}, {"url": "reports/stock-opnames", "name": "Laporan Stock Opname", "slug": "reports.stock-opnames", "permission": "report_stock_opname"}]}, {"name": "Settings", "icon": "menu-icon tf-icons ri-settings-3-line", "slug": "settings", "submenu": [{"url": "profile", "name": "Profile", "slug": "profile.index", "icon": "ri-id-card-line"}, {"url": "notifications", "name": "Notif<PERSON><PERSON>", "icon": "ri-notification-2-line", "slug": "notifications"}, {"url": "settings/system", "name": "System Settings", "slug": "settings.system"}, {"url": "backup", "name": "Backup DB", "slug": "backup.index", "icon": "ri-database-2-line"}]}]}