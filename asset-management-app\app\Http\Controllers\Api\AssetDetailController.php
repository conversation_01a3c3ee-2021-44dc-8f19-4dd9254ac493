<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetAssignment;

class AssetDetailController extends Controller
{
    /**
     * Get asset details with assignment information
     */
    public function show($id)
    {
        try {
            $asset = Asset::with([
                'assetCategory',
                'assetType',
                'branch',
                'supplier',
                'assignedEmployee.branch',
                'assignedEmployee.division'
            ])->find($id);

            if (!$asset) {
                return response()->json([
                    'success' => false,
                    'message' => 'Asset tidak ditemukan'
                ], 404);
            }

            // Get current assignment from AssetAssignment table
            $currentAssignment = AssetAssignment::with(['employee.branch', 'employee.division'])
                ->where('asset_id', $id)
                ->where('status', 'active')
                ->first();

            $assetData = [
                'id' => $asset->id,
                'asset_code' => $asset->asset_code,
                'name' => $asset->name,
                'description' => $asset->description,
                'brand' => $asset->brand,
                'model' => $asset->model,
                'serial_number' => $asset->serial_number,
                'status' => $asset->status,
                'purchase_date' => $asset->purchase_date,
                'purchase_price' => $asset->purchase_price,
                'asset_category' => $asset->assetCategory ? [
                    'id' => $asset->assetCategory->id,
                    'name' => $asset->assetCategory->name
                ] : null,
                'asset_type' => $asset->assetType ? [
                    'id' => $asset->assetType->id,
                    'name' => $asset->assetType->name
                ] : null,
                'branch' => $asset->branch ? [
                    'id' => $asset->branch->id,
                    'name' => $asset->branch->name,
                    'code' => $asset->branch->code
                ] : null,
                'supplier' => $asset->supplier ? [
                    'id' => $asset->supplier->id,
                    'name' => $asset->supplier->name,
                    'supplier_code' => $asset->supplier->supplier_code
                ] : null,
                'assigned_employee' => null,
                'assignment_date' => null
            ];

            // Add assignment information if exists
            $assignedEmployee = null;
            $assignmentDate = null;

            // First, try to get from AssetAssignment table
            if ($currentAssignment && $currentAssignment->employee) {
                $assignedEmployee = $currentAssignment->employee;
                $assignmentDate = $currentAssignment->assigned_at ?
                    \Carbon\Carbon::parse($currentAssignment->assigned_at)->format('d/m/Y') : null;
            }
            // Fallback to asset's assigned_to field
            elseif ($asset->assigned_to && $asset->assignedEmployee) {
                $assignedEmployee = $asset->assignedEmployee;
                $assignmentDate = $asset->assigned_at ?
                    \Carbon\Carbon::parse($asset->assigned_at)->format('d/m/Y') : null;
            }

            // Set assignment data if employee found
            if ($assignedEmployee) {
                $assetData['assigned_employee'] = [
                    'id' => $assignedEmployee->id,
                    'nik_karyawan' => $assignedEmployee->nik ?? '-',
                    'nama_lengkap' => $assignedEmployee->full_name ?? '-',
                    'cabang' => $assignedEmployee->branch ? $assignedEmployee->branch->name : '-',
                    'divisi' => $assignedEmployee->division ? $assignedEmployee->division->name : '-',
                    'bagian' => $assignedEmployee->department ?? '-',
                    'jabatan' => $assignedEmployee->position ?? '-'
                ];
                $assetData['assignment_date'] = $assignmentDate;
            }

            return response()->json([
                'success' => true,
                'data' => $assetData
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching asset details: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data asset'
            ], 500);
        }
    }
}
