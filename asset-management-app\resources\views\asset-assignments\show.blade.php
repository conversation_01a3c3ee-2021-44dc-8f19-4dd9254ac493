@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Asset Assignment - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('asset-assignments.index') }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-user-settings-line me-2"></i>
                  Detail Asset Assignment
                </h5>
                <small class="text-muted">{{ $assetAssignment->asset->asset_code }} → {{ $assetAssignment->employee->full_name }}</small>
              </div>
            </div>
            <div>
              <div class="btn-group">
                <a href="{{ route('asset-assignments.print-receipt', $assetAssignment) }}"
                   class="btn btn-success" target="_blank">
                  <i class="ri-printer-line me-2"></i>
                  Cetak Tanda Terima
                </a>
                <a href="{{ route('asset-assignments.download-pdf', $assetAssignment) }}"
                   class="btn btn-danger" target="_blank">
                  <i class="ri-file-pdf-line me-2"></i>
                  Download PDF
                </a>
                @if($assetAssignment->status === 'active')
                  <a href="{{ route('asset-assignments.return-form', $assetAssignment) }}" class="btn btn-warning">
                    <i class="ri-arrow-go-back-line me-2"></i>
                    Return Asset
                  </a>
                  <a href="{{ route('asset-assignments.transfer-form', $assetAssignment) }}" class="btn btn-info">
                    <i class="ri-exchange-line me-2"></i>
                    Transfer Asset
                  </a>
                @endif
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Assignment Information -->
    <div class="col-lg-8">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Assignment
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Asset</h6>
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Kode Asset:</td>
                  <td><span class="badge bg-primary">{{ $assetAssignment->asset->asset_code }}</span></td>
                </tr>
                <tr>
                  <td class="text-muted">Nama Asset:</td>
                  <td><strong>{{ $assetAssignment->asset->name }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Kategori:</td>
                  <td>{{ $assetAssignment->asset->assetCategory->name ?? '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Cabang Asset:</td>
                  <td>{{ $assetAssignment->asset->branch->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Kondisi Saat Assignment:</td>
                  <td>
                    <span class="badge bg-{{ $assetAssignment->condition_when_assigned === 'excellent' ? 'success' : ($assetAssignment->condition_when_assigned === 'good' ? 'info' : ($assetAssignment->condition_when_assigned === 'fair' ? 'warning' : 'danger')) }}">
                      {{ $assetAssignment->condition_when_assigned_text }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6 class="text-muted mb-3">Informasi Karyawan</h6>
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">NIK:</td>
                  <td><span class="badge bg-info">{{ $assetAssignment->employee->nik }}</span></td>
                </tr>
                <tr>
                  <td class="text-muted">Nama Lengkap:</td>
                  <td><strong>{{ $assetAssignment->employee->full_name }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Jabatan:</td>
                  <td>{{ $assetAssignment->employee->position }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Bagian:</td>
                  <td>{{ $assetAssignment->employee->department ?: '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Cabang:</td>
                  <td>{{ $assetAssignment->employee->branch->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Divisi:</td>
                  <td>{{ $assetAssignment->employee->division->name }}</td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Assignment Details -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-calendar-line me-2"></i>
            Detail Assignment
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Tanggal Assignment:</td>
                  <td><strong>{{ $assetAssignment->assigned_at->format('d/m/Y H:i') }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Di-assign oleh:</td>
                  <td>{{ $assetAssignment->assignedBy->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Status:</td>
                  <td>
                    <span class="badge bg-{{ $assetAssignment->status === 'active' ? 'success' : ($assetAssignment->status === 'returned' ? 'info' : 'warning') }}">
                      {{ $assetAssignment->status_text }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Durasi:</td>
                  <td>{{ $assetAssignment->duration }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              @if($assetAssignment->status !== 'active')
                <table class="table table-borderless">
                  <tr>
                    <td class="text-muted" width="150">Tanggal Return:</td>
                    <td>{{ $assetAssignment->returned_at ? $assetAssignment->returned_at->format('d/m/Y H:i') : '-' }}</td>
                  </tr>
                  <tr>
                    <td class="text-muted">Di-return oleh:</td>
                    <td>{{ $assetAssignment->returnedBy->name ?? '-' }}</td>
                  </tr>
                  @if($assetAssignment->condition_when_returned)
                    <tr>
                      <td class="text-muted">Kondisi Saat Return:</td>
                      <td>
                        <span class="badge bg-{{ $assetAssignment->condition_when_returned === 'excellent' ? 'success' : ($assetAssignment->condition_when_returned === 'good' ? 'info' : ($assetAssignment->condition_when_returned === 'fair' ? 'warning' : 'danger')) }}">
                          {{ $assetAssignment->condition_when_returned_text }}
                        </span>
                      </td>
                    </tr>
                  @endif
                </table>
              @endif
            </div>
          </div>

          @if($assetAssignment->assignment_notes)
            <div class="row mt-3">
              <div class="col-12">
                <h6 class="text-muted mb-2">Catatan Assignment</h6>
                <div class="alert alert-info">
                  {{ $assetAssignment->assignment_notes }}
                </div>
              </div>
            </div>
          @endif

          @if($assetAssignment->return_notes)
            <div class="row mt-3">
              <div class="col-12">
                <h6 class="text-muted mb-2">Catatan Return</h6>
                <div class="alert alert-warning">
                  {{ $assetAssignment->return_notes }}
                </div>
              </div>
            </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Statistics & Actions -->
    <div class="col-lg-4">
      <!-- Status Card -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-bar-chart-line me-2"></i>
            Status Assignment
          </h6>
        </div>
        <div class="card-body text-center">
          <div class="avatar avatar-xl mx-auto mb-3">
            <span class="avatar-initial rounded-circle bg-label-{{ $assetAssignment->status === 'active' ? 'success' : ($assetAssignment->status === 'returned' ? 'info' : 'warning') }}">
              <i class="ri-{{ $assetAssignment->status === 'active' ? 'check' : ($assetAssignment->status === 'returned' ? 'arrow-go-back' : 'exchange') }}-line ri-24px"></i>
            </span>
          </div>
          <h5 class="mb-1">{{ $assetAssignment->status_text }}</h5>
          <p class="text-muted mb-0">{{ $assetAssignment->duration }}</p>
        </div>
      </div>

      <!-- Quick Actions -->
      @if($assetAssignment->status === 'active')
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-settings-line me-2"></i>
              Aksi Cepat
            </h6>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <a href="{{ route('asset-assignments.return-form', $assetAssignment) }}" class="btn btn-outline-warning">
                <i class="ri-arrow-go-back-line me-2"></i>
                Return Asset
              </a>
              <a href="{{ route('asset-assignments.transfer-form', $assetAssignment) }}" class="btn btn-outline-info">
                <i class="ri-exchange-line me-2"></i>
                Transfer ke Karyawan Lain
              </a>
            </div>
          </div>
        </div>
      @endif

      <!-- Assignment Timeline -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-time-line me-2"></i>
            Timeline
          </h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-point timeline-point-primary"></div>
              <div class="timeline-event">
                <div class="timeline-header">
                  <h6 class="timeline-title">Asset Di-assign</h6>
                  <small class="text-muted">{{ $assetAssignment->assigned_at->format('d/m/Y H:i') }}</small>
                </div>
                <p class="timeline-text">Asset di-assign ke {{ $assetAssignment->employee->full_name }} oleh {{ $assetAssignment->assignedBy->name }}</p>
              </div>
            </div>
            
            @if($assetAssignment->returned_at)
              <div class="timeline-item">
                <div class="timeline-point timeline-point-{{ $assetAssignment->status === 'returned' ? 'info' : 'warning' }}"></div>
                <div class="timeline-event">
                  <div class="timeline-header">
                    <h6 class="timeline-title">Asset {{ $assetAssignment->status === 'returned' ? 'Dikembalikan' : 'Dipindahkan' }}</h6>
                    <small class="text-muted">{{ $assetAssignment->returned_at->format('d/m/Y H:i') }}</small>
                  </div>
                  <p class="timeline-text">Asset {{ $assetAssignment->status === 'returned' ? 'dikembalikan' : 'dipindahkan' }} oleh {{ $assetAssignment->returnedBy->name ?? '-' }}</p>
                </div>
              </div>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

@endsection
