// List groups
// *******************************************************************************

// List Group Mixin
@each $color, $value in $theme-colors {
  @if $color != primary and $color != light {
    @include template-list-group-item-variant('.list-group-item-#{$color}', $value);
  }
}
.list-group {
  .list-group-item-action {
    &:not(.active) {
      &:active {
        color: $list-group-color;
        background-color: $list-group-hover-bg !important;
      }
    }
  }
  .list-group-item {
    line-height: 1.375rem;
    padding-bottom: calc($list-group-item-padding-y - 1px);
  }
  &:not([class*='list-group-flush']) .list-group-item:first-of-type {
    padding-top: calc($list-group-item-padding-y - 1px);
  }
  &[class*='list-group-flush'] .list-group-item:last-of-type {
    padding-bottom: $list-group-item-padding-y;
  }
  &[class*='list-group-horizontal-md'] .list-group-item {
    @include media-breakpoint-up(md) {
      padding-top: calc($list-group-item-padding-y - 1px);
    }
  }
}

.list-group {
  // Timeline CSS
  &.list-group-timeline {
    position: relative;
    &:before {
      background-color: $border-color;
      position: absolute;
      content: '';
      width: 1px;
      height: 100%;
      top: 0;
      bottom: 0;
      left: 0.2rem;
    }
    .list-group-item {
      border: none;
      padding-left: 1.25rem;
      &:before {
        position: absolute;
        display: block;
        content: '';
        width: 7px;
        height: 7px;
        left: 0;
        top: 50%;
        margin-top: -3.5px;
        border-radius: 100%;
      }
    }
  }

  .list-group-item.active {
    h1,
    .h1,
    h2,
    .h2,
    h3,
    .h3,
    h4,
    .h4,
    h5,
    .h5,
    h6,
    .h6 {
      color: $list-group-action-active-color;
    }
  }
}
