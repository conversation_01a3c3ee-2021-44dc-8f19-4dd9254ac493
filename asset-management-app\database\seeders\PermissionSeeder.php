<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Dashboard
            ['name' => 'View Dashboard', 'slug' => 'dashboard.view', 'module' => 'Dashboard'],

            // User Management
            ['name' => 'View Users', 'slug' => 'users.view', 'module' => 'User Management'],
            ['name' => 'Create Users', 'slug' => 'users.create', 'module' => 'User Management'],
            ['name' => 'Edit Users', 'slug' => 'users.edit', 'module' => 'User Management'],
            ['name' => 'Delete Users', 'slug' => 'users.delete', 'module' => 'User Management'],

            // Role Management
            ['name' => 'View Roles', 'slug' => 'roles.view', 'module' => 'Role Management'],
            ['name' => 'Create Roles', 'slug' => 'roles.create', 'module' => 'Role Management'],
            ['name' => 'Edit Roles', 'slug' => 'roles.edit', 'module' => 'Role Management'],
            ['name' => 'Delete Roles', 'slug' => 'roles.delete', 'module' => 'Role Management'],

            // Branch Management
            ['name' => 'View Branches', 'slug' => 'branches.view', 'module' => 'Branch Management'],
            ['name' => 'Create Branches', 'slug' => 'branches.create', 'module' => 'Branch Management'],
            ['name' => 'Edit Branches', 'slug' => 'branches.edit', 'module' => 'Branch Management'],
            ['name' => 'Delete Branches', 'slug' => 'branches.delete', 'module' => 'Branch Management'],

            // Asset Management
            ['name' => 'View Assets', 'slug' => 'assets.view', 'module' => 'Asset Management'],
            ['name' => 'Create Assets', 'slug' => 'assets.create', 'module' => 'Asset Management'],
            ['name' => 'Edit Assets', 'slug' => 'assets.edit', 'module' => 'Asset Management'],
            ['name' => 'Delete Assets', 'slug' => 'assets.delete', 'module' => 'Asset Management'],

            // Asset Categories
            ['name' => 'View Asset Categories', 'slug' => 'asset-categories.view', 'module' => 'Asset Categories'],
            ['name' => 'Create Asset Categories', 'slug' => 'asset-categories.create', 'module' => 'Asset Categories'],
            ['name' => 'Edit Asset Categories', 'slug' => 'asset-categories.edit', 'module' => 'Asset Categories'],
            ['name' => 'Delete Asset Categories', 'slug' => 'asset-categories.delete', 'module' => 'Asset Categories'],

            // Asset Requests
            ['name' => 'View Asset Requests', 'slug' => 'asset-requests.view', 'module' => 'Asset Requests'],
            ['name' => 'Create Asset Requests', 'slug' => 'asset-requests.create', 'module' => 'Asset Requests'],
            ['name' => 'Edit Asset Requests', 'slug' => 'asset-requests.edit', 'module' => 'Asset Requests'],
            ['name' => 'Delete Asset Requests', 'slug' => 'asset-requests.delete', 'module' => 'Asset Requests'],
            ['name' => 'View All Asset Requests', 'slug' => 'asset-requests.view-all', 'module' => 'Asset Requests'],
            ['name' => 'Edit All Asset Requests', 'slug' => 'asset-requests.edit-all', 'module' => 'Asset Requests'],
            ['name' => 'Delete All Asset Requests', 'slug' => 'asset-requests.delete-all', 'module' => 'Asset Requests'],
            ['name' => 'Submit Asset Requests', 'slug' => 'asset-requests.submit', 'module' => 'Asset Requests'],
            ['name' => 'Review Asset Requests', 'slug' => 'asset-requests.review', 'module' => 'Asset Requests'],
            ['name' => 'Approve Asset Requests', 'slug' => 'asset-requests.approve', 'module' => 'Asset Requests'],
            ['name' => 'Reject Asset Requests', 'slug' => 'asset-requests.reject', 'module' => 'Asset Requests'],
            ['name' => 'Cancel All Asset Requests', 'slug' => 'asset-requests.cancel-all', 'module' => 'Asset Requests'],

            // Division Management
            ['name' => 'View Divisions', 'slug' => 'divisions.view', 'module' => 'Division Management'],
            ['name' => 'Create Divisions', 'slug' => 'divisions.create', 'module' => 'Division Management'],
            ['name' => 'Edit Divisions', 'slug' => 'divisions.edit', 'module' => 'Division Management'],
            ['name' => 'Delete Divisions', 'slug' => 'divisions.delete', 'module' => 'Division Management'],

            // Reports
            ['name' => 'View Reports', 'slug' => 'reports.view', 'module' => 'Reports'],
            ['name' => 'Export Reports', 'slug' => 'reports.export', 'module' => 'Reports'],

            // Import/Export
            ['name' => 'Import Data', 'slug' => 'data.import', 'module' => 'Import/Export'],
            ['name' => 'Export Data', 'slug' => 'data.export', 'module' => 'Import/Export'],
        ];

        foreach ($permissions as $permission) {
            \DB::table('permissions')->insert([
                'name' => $permission['name'],
                'slug' => $permission['slug'],
                'module' => $permission['module'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
