<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('asset_field_configurations', function (Blueprint $table) {
            $table->foreignId('parent_field_id')->nullable()->after('asset_category_id')->constrained('asset_field_configurations')->onDelete('cascade');
            $table->json('parent_field_conditions')->nullable()->after('parent_field_id'); // Conditions when this field should show
            $table->boolean('is_conditional')->default(false)->after('parent_field_conditions'); // Is this field conditional
            $table->json('conditional_logic')->nullable()->after('is_conditional'); // Show/hide logic
            $table->integer('column_width')->default(12)->after('sort_order'); // Bootstrap column width (1-12)
            $table->string('css_class')->nullable()->after('column_width'); // Custom CSS classes
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asset_field_configurations', function (Blueprint $table) {
            $table->dropForeign(['parent_field_id']);
            $table->dropColumn([
                'parent_field_id',
                'parent_field_conditions',
                'is_conditional',
                'conditional_logic',
                'column_width',
                'css_class'
            ]);
        });
    }
};
