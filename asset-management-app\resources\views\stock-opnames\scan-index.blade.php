@extends('layouts.contentNavbarLayout')

@section('title', 'Scan Asset - Stock Opname')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('stock-opnames.index') }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-qr-scan-line me-2"></i>
                  Scan Asset Stock Opname
                </h5>
                <small class="text-muted">Pilih stock opname yang sedang berjalan untuk mulai scan asset</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  @if($activeOpnames->count() > 0)
    <!-- Active Stock Opnames -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-play-line me-2"></i>
              Stock Opname Aktif
            </h6>
          </div>
          <div class="card-body">
            <p class="text-muted mb-4">
              Pilih stock opname yang sedang berjalan untuk mulai scan asset. 
              Hanya stock opname yang sedang dalam status "In Progress" yang dapat di-scan.
            </p>
            
            <div class="row">
              @foreach($activeOpnames as $opname)
                <div class="col-lg-6 col-md-12 mb-4">
                  <div class="card border">
                    <div class="card-body">
                      <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                          <h6 class="card-title mb-1">{{ $opname->title }}</h6>
                          <span class="badge bg-primary">{{ $opname->opname_number }}</span>
                        </div>
                        <span class="badge bg-warning">
                          <i class="ri-play-line me-1"></i>
                          Aktif
                        </span>
                      </div>
                      
                      <div class="mb-3">
                        <div class="row">
                          <div class="col-6">
                            <small class="text-muted">Cabang:</small>
                            <div><strong>{{ $opname->branch->name }}</strong></div>
                          </div>
                          <div class="col-6">
                            <small class="text-muted">Dibuat oleh:</small>
                            <div>{{ $opname->creator->name }}</div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="mb-3">
                        <div class="row">
                          <div class="col-6">
                            <small class="text-muted">Dimulai:</small>
                            <div>{{ $opname->start_date->format('d/m/Y H:i') }}</div>
                          </div>
                          <div class="col-6">
                            <small class="text-muted">Total Asset:</small>
                            <div><strong>{{ $opname->total_assets }}</strong></div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Progress -->
                      <div class="mb-3">
                        @if($opname->total_assets > 0)
                          @php
                            $progress = round(($opname->scanned_assets / $opname->total_assets) * 100, 1);
                          @endphp
                          <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">Progress Scanning:</small>
                            <small class="text-muted">{{ $progress }}%</small>
                          </div>
                          <div class="progress mb-2" style="height: 6px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ $progress }}%" 
                                 aria-valuenow="{{ $progress }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100"></div>
                          </div>
                          <small class="text-muted">{{ $opname->scanned_assets }}/{{ $opname->total_assets }} asset telah di-scan</small>
                        @else
                          <small class="text-muted">Belum ada asset untuk di-scan</small>
                        @endif
                      </div>
                      
                      <!-- Statistics -->
                      <div class="row mb-3">
                        <div class="col-4 text-center">
                          <div class="badge bg-success fs-6">{{ $opname->found_assets }}</div>
                          <small class="d-block text-muted">Ditemukan</small>
                        </div>
                        <div class="col-4 text-center">
                          <div class="badge bg-danger fs-6">{{ $opname->missing_assets }}</div>
                          <small class="d-block text-muted">Hilang</small>
                        </div>
                        <div class="col-4 text-center">
                          @php
                            $discrepancy = $opname->details()->where('has_discrepancy', true)->count();
                          @endphp
                          <div class="badge bg-warning fs-6">{{ $discrepancy }}</div>
                          <small class="d-block text-muted">Ketidaksesuaian</small>
                        </div>
                      </div>
                      
                      @if($opname->description)
                        <div class="mb-3">
                          <small class="text-muted">Deskripsi:</small>
                          <p class="mb-0 small">{{ Str::limit($opname->description, 100) }}</p>
                        </div>
                      @endif
                      
                      <!-- Actions -->
                      <div class="d-flex gap-2">
                        <a href="{{ route('stock-opnames.scan-detail', $opname) }}" class="btn btn-primary flex-fill">
                          <i class="ri-qr-scan-line me-2"></i>
                          Mulai Scan
                        </a>
                        <a href="{{ route('stock-opnames.show', $opname) }}" class="btn btn-outline-info">
                          <i class="ri-eye-line"></i>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              @endforeach
            </div>
          </div>
        </div>
      </div>
    </div>
  @else
    <!-- No Active Stock Opnames -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body text-center py-5">
            <i class="ri-search-eye-line ri-48px text-muted mb-3"></i>
            <h5 class="text-muted mb-3">Tidak Ada Stock Opname Aktif</h5>
            <p class="text-muted mb-4">
              Saat ini tidak ada stock opname yang sedang berjalan. 
              Untuk dapat melakukan scan asset, stock opname harus dalam status "In Progress".
            </p>
            
            <div class="alert alert-info d-inline-block text-start" role="alert">
              <h6 class="alert-heading mb-2">
                <i class="ri-information-line me-2"></i>
                Cara Memulai Stock Opname:
              </h6>
              <ol class="mb-0 ps-3">
                <li>Buat stock opname baru atau pilih yang sudah ada</li>
                <li>Klik "Mulai Stock Opname" untuk mengubah status menjadi "In Progress"</li>
                <li>Setelah dimulai, Anda dapat melakukan scan asset</li>
              </ol>
            </div>
            
            <div class="mt-4">
              <a href="{{ route('stock-opnames.index') }}" class="btn btn-primary me-2">
                <i class="ri-list-check-3 me-2"></i>
                Lihat Daftar Stock Opname
              </a>
              <a href="{{ route('stock-opnames.create') }}" class="btn btn-outline-primary">
                <i class="ri-add-line me-2"></i>
                Buat Stock Opname Baru
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  @endif

  <!-- Quick Access by Branch -->
  @if($activeOpnames->count() > 1)
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-building-line me-2"></i>
              Akses Cepat per Cabang
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              @foreach($branches as $branch)
                @php
                  $branchOpname = $activeOpnames->where('branch_id', $branch->id)->first();
                @endphp
                <div class="col-lg-4 col-md-6 mb-3">
                  <div class="card border {{ $branchOpname ? 'border-primary' : 'border-light' }}">
                    <div class="card-body text-center">
                      <h6 class="card-title">{{ $branch->name }}</h6>
                      @if($branchOpname)
                        <span class="badge bg-success mb-2">Stock Opname Aktif</span>
                        <p class="small text-muted mb-2">{{ $branchOpname->title }}</p>
                        <a href="{{ route('stock-opnames.scan-detail', $branchOpname) }}" class="btn btn-sm btn-primary">
                          <i class="ri-qr-scan-line me-1"></i>
                          Scan
                        </a>
                      @else
                        <span class="badge bg-secondary mb-2">Tidak Ada Stock Opname</span>
                        <p class="small text-muted mb-2">Belum ada stock opname aktif</p>
                        <button class="btn btn-sm btn-outline-secondary" disabled>
                          <i class="ri-qr-scan-line me-1"></i>
                          Scan
                        </button>
                      @endif
                    </div>
                  </div>
                </div>
              @endforeach
            </div>
          </div>
        </div>
      </div>
    </div>
  @endif
</div>

@if(session('success'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-check-line me-2"></i>
      <div class="me-auto fw-semibold">Success</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('success') }}
    </div>
  </div>
@endif

@if(session('error'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-danger show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-error-warning-line me-2"></i>
      <div class="me-auto fw-semibold">Error</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('error') }}
    </div>
  </div>
@endif

@endsection
