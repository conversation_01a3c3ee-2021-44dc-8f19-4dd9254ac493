var L="top",R="bottom",x="right",I="left",ge="auto",Mt=[L,R,x,I],pt="start",St="end",is="clippingParents",qe="viewport",wt="popper",rs="reference",Be=Mt.reduce(function(n,t){return n.concat([t+"-"+pt,t+"-"+St])},[]),Xe=[].concat(Mt,[ge]).reduce(function(n,t){return n.concat([t,t+"-"+pt,t+"-"+St])},[]),os="beforeRead",as="read",cs="afterRead",ls="beforeMain",us="main",hs="afterMain",ds="beforeWrite",fs="write",ps="afterWrite",_s=[os,as,cs,ls,us,hs,ds,fs,ps];function z(n){return n?(n.nodeName||"").toLowerCase():null}function k(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var t=n.ownerDocument;return t&&t.defaultView||window}return n}function _t(n){var t=k(n).Element;return n instanceof t||n instanceof Element}function V(n){var t=k(n).HTMLElement;return n instanceof t||n instanceof HTMLElement}function Qe(n){if(typeof ShadowRoot>"u")return!1;var t=k(n).ShadowRoot;return n instanceof t||n instanceof ShadowRoot}function ui(n){var t=n.state;Object.keys(t.elements).forEach(function(e){var s=t.styles[e]||{},i=t.attributes[e]||{},r=t.elements[e];!V(r)||!z(r)||(Object.assign(r.style,s),Object.keys(i).forEach(function(o){var a=i[o];a===!1?r.removeAttribute(o):r.setAttribute(o,a===!0?"":a)}))})}function hi(n){var t=n.state,e={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,e.popper),t.styles=e,t.elements.arrow&&Object.assign(t.elements.arrow.style,e.arrow),function(){Object.keys(t.elements).forEach(function(s){var i=t.elements[s],r=t.attributes[s]||{},o=Object.keys(t.styles.hasOwnProperty(s)?t.styles[s]:e[s]),a=o.reduce(function(l,h){return l[h]="",l},{});!V(i)||!z(i)||(Object.assign(i.style,a),Object.keys(r).forEach(function(l){i.removeAttribute(l)}))})}}const Ze={name:"applyStyles",enabled:!0,phase:"write",fn:ui,effect:hi,requires:["computeStyles"]};function U(n){return n.split("-")[0]}var ft=Math.max,fe=Math.min,Dt=Math.round;function Fe(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function ms(){return!/^((?!chrome|android).)*safari/i.test(Fe())}function $t(n,t,e){t===void 0&&(t=!1),e===void 0&&(e=!1);var s=n.getBoundingClientRect(),i=1,r=1;t&&V(n)&&(i=n.offsetWidth>0&&Dt(s.width)/n.offsetWidth||1,r=n.offsetHeight>0&&Dt(s.height)/n.offsetHeight||1);var o=_t(n)?k(n):window,a=o.visualViewport,l=!ms()&&e,h=(s.left+(l&&a?a.offsetLeft:0))/i,u=(s.top+(l&&a?a.offsetTop:0))/r,p=s.width/i,_=s.height/r;return{width:p,height:_,top:u,right:h+p,bottom:u+_,left:h,x:h,y:u}}function Je(n){var t=$t(n),e=n.offsetWidth,s=n.offsetHeight;return Math.abs(t.width-e)<=1&&(e=t.width),Math.abs(t.height-s)<=1&&(s=t.height),{x:n.offsetLeft,y:n.offsetTop,width:e,height:s}}function gs(n,t){var e=t.getRootNode&&t.getRootNode();if(n.contains(t))return!0;if(e&&Qe(e)){var s=t;do{if(s&&n.isSameNode(s))return!0;s=s.parentNode||s.host}while(s)}return!1}function X(n){return k(n).getComputedStyle(n)}function di(n){return["table","td","th"].indexOf(z(n))>=0}function st(n){return((_t(n)?n.ownerDocument:n.document)||window.document).documentElement}function Ee(n){return z(n)==="html"?n:n.assignedSlot||n.parentNode||(Qe(n)?n.host:null)||st(n)}function Tn(n){return!V(n)||X(n).position==="fixed"?null:n.offsetParent}function fi(n){var t=/firefox/i.test(Fe()),e=/Trident/i.test(Fe());if(e&&V(n)){var s=X(n);if(s.position==="fixed")return null}var i=Ee(n);for(Qe(i)&&(i=i.host);V(i)&&["html","body"].indexOf(z(i))<0;){var r=X(i);if(r.transform!=="none"||r.perspective!=="none"||r.contain==="paint"||["transform","perspective"].indexOf(r.willChange)!==-1||t&&r.willChange==="filter"||t&&r.filter&&r.filter!=="none")return i;i=i.parentNode}return null}function Kt(n){for(var t=k(n),e=Tn(n);e&&di(e)&&X(e).position==="static";)e=Tn(e);return e&&(z(e)==="html"||z(e)==="body"&&X(e).position==="static")?t:e||fi(n)||t}function tn(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function Bt(n,t,e){return ft(n,fe(t,e))}function pi(n,t,e){var s=Bt(n,t,e);return s>e?e:s}function Es(){return{top:0,right:0,bottom:0,left:0}}function vs(n){return Object.assign({},Es(),n)}function bs(n,t){return t.reduce(function(e,s){return e[s]=n,e},{})}var _i=function(t,e){return t=typeof t=="function"?t(Object.assign({},e.rects,{placement:e.placement})):t,vs(typeof t!="number"?t:bs(t,Mt))};function mi(n){var t,e=n.state,s=n.name,i=n.options,r=e.elements.arrow,o=e.modifiersData.popperOffsets,a=U(e.placement),l=tn(a),h=[I,x].indexOf(a)>=0,u=h?"height":"width";if(!(!r||!o)){var p=_i(i.padding,e),_=Je(r),f=l==="y"?L:I,A=l==="y"?R:x,m=e.rects.reference[u]+e.rects.reference[l]-o[l]-e.rects.popper[u],E=o[l]-e.rects.reference[l],T=Kt(r),w=T?l==="y"?T.clientHeight||0:T.clientWidth||0:0,O=m/2-E/2,g=p[f],v=w-_[u]-p[A],b=w/2-_[u]/2+O,y=Bt(g,b,v),S=l;e.modifiersData[s]=(t={},t[S]=y,t.centerOffset=y-b,t)}}function gi(n){var t=n.state,e=n.options,s=e.element,i=s===void 0?"[data-popper-arrow]":s;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||gs(t.elements.popper,i)&&(t.elements.arrow=i))}const As={name:"arrow",enabled:!0,phase:"main",fn:mi,effect:gi,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Lt(n){return n.split("-")[1]}var Ei={top:"auto",right:"auto",bottom:"auto",left:"auto"};function vi(n,t){var e=n.x,s=n.y,i=t.devicePixelRatio||1;return{x:Dt(e*i)/i||0,y:Dt(s*i)/i||0}}function yn(n){var t,e=n.popper,s=n.popperRect,i=n.placement,r=n.variation,o=n.offsets,a=n.position,l=n.gpuAcceleration,h=n.adaptive,u=n.roundOffsets,p=n.isFixed,_=o.x,f=_===void 0?0:_,A=o.y,m=A===void 0?0:A,E=typeof u=="function"?u({x:f,y:m}):{x:f,y:m};f=E.x,m=E.y;var T=o.hasOwnProperty("x"),w=o.hasOwnProperty("y"),O=I,g=L,v=window;if(h){var b=Kt(e),y="clientHeight",S="clientWidth";if(b===k(e)&&(b=st(e),X(b).position!=="static"&&a==="absolute"&&(y="scrollHeight",S="scrollWidth")),b=b,i===L||(i===I||i===x)&&r===St){g=R;var N=p&&b===v&&v.visualViewport?v.visualViewport.height:b[y];m-=N-s.height,m*=l?1:-1}if(i===I||(i===L||i===R)&&r===St){O=x;var C=p&&b===v&&v.visualViewport?v.visualViewport.width:b[S];f-=C-s.width,f*=l?1:-1}}var D=Object.assign({position:a},h&&Ei),B=u===!0?vi({x:f,y:m},k(e)):{x:f,y:m};if(f=B.x,m=B.y,l){var $;return Object.assign({},D,($={},$[g]=w?"0":"",$[O]=T?"0":"",$.transform=(v.devicePixelRatio||1)<=1?"translate("+f+"px, "+m+"px)":"translate3d("+f+"px, "+m+"px, 0)",$))}return Object.assign({},D,(t={},t[g]=w?m+"px":"",t[O]=T?f+"px":"",t.transform="",t))}function bi(n){var t=n.state,e=n.options,s=e.gpuAcceleration,i=s===void 0?!0:s,r=e.adaptive,o=r===void 0?!0:r,a=e.roundOffsets,l=a===void 0?!0:a,h={placement:U(t.placement),variation:Lt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,yn(Object.assign({},h,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,yn(Object.assign({},h,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const en={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:bi,data:{}};var se={passive:!0};function Ai(n){var t=n.state,e=n.instance,s=n.options,i=s.scroll,r=i===void 0?!0:i,o=s.resize,a=o===void 0?!0:o,l=k(t.elements.popper),h=[].concat(t.scrollParents.reference,t.scrollParents.popper);return r&&h.forEach(function(u){u.addEventListener("scroll",e.update,se)}),a&&l.addEventListener("resize",e.update,se),function(){r&&h.forEach(function(u){u.removeEventListener("scroll",e.update,se)}),a&&l.removeEventListener("resize",e.update,se)}}const nn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ai,data:{}};var Ti={left:"right",right:"left",bottom:"top",top:"bottom"};function ue(n){return n.replace(/left|right|bottom|top/g,function(t){return Ti[t]})}var yi={start:"end",end:"start"};function wn(n){return n.replace(/start|end/g,function(t){return yi[t]})}function sn(n){var t=k(n),e=t.pageXOffset,s=t.pageYOffset;return{scrollLeft:e,scrollTop:s}}function rn(n){return $t(st(n)).left+sn(n).scrollLeft}function wi(n,t){var e=k(n),s=st(n),i=e.visualViewport,r=s.clientWidth,o=s.clientHeight,a=0,l=0;if(i){r=i.width,o=i.height;var h=ms();(h||!h&&t==="fixed")&&(a=i.offsetLeft,l=i.offsetTop)}return{width:r,height:o,x:a+rn(n),y:l}}function Oi(n){var t,e=st(n),s=sn(n),i=(t=n.ownerDocument)==null?void 0:t.body,r=ft(e.scrollWidth,e.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=ft(e.scrollHeight,e.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-s.scrollLeft+rn(n),l=-s.scrollTop;return X(i||e).direction==="rtl"&&(a+=ft(e.clientWidth,i?i.clientWidth:0)-r),{width:r,height:o,x:a,y:l}}function on(n){var t=X(n),e=t.overflow,s=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(e+i+s)}function Ts(n){return["html","body","#document"].indexOf(z(n))>=0?n.ownerDocument.body:V(n)&&on(n)?n:Ts(Ee(n))}function Ft(n,t){var e;t===void 0&&(t=[]);var s=Ts(n),i=s===((e=n.ownerDocument)==null?void 0:e.body),r=k(s),o=i?[r].concat(r.visualViewport||[],on(s)?s:[]):s,a=t.concat(o);return i?a:a.concat(Ft(Ee(o)))}function Ke(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function Ci(n,t){var e=$t(n,!1,t==="fixed");return e.top=e.top+n.clientTop,e.left=e.left+n.clientLeft,e.bottom=e.top+n.clientHeight,e.right=e.left+n.clientWidth,e.width=n.clientWidth,e.height=n.clientHeight,e.x=e.left,e.y=e.top,e}function On(n,t,e){return t===qe?Ke(wi(n,e)):_t(t)?Ci(t,e):Ke(Oi(st(n)))}function Ni(n){var t=Ft(Ee(n)),e=["absolute","fixed"].indexOf(X(n).position)>=0,s=e&&V(n)?Kt(n):n;return _t(s)?t.filter(function(i){return _t(i)&&gs(i,s)&&z(i)!=="body"}):[]}function Si(n,t,e,s){var i=t==="clippingParents"?Ni(n):[].concat(t),r=[].concat(i,[e]),o=r[0],a=r.reduce(function(l,h){var u=On(n,h,s);return l.top=ft(u.top,l.top),l.right=fe(u.right,l.right),l.bottom=fe(u.bottom,l.bottom),l.left=ft(u.left,l.left),l},On(n,o,s));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function ys(n){var t=n.reference,e=n.element,s=n.placement,i=s?U(s):null,r=s?Lt(s):null,o=t.x+t.width/2-e.width/2,a=t.y+t.height/2-e.height/2,l;switch(i){case L:l={x:o,y:t.y-e.height};break;case R:l={x:o,y:t.y+t.height};break;case x:l={x:t.x+t.width,y:a};break;case I:l={x:t.x-e.width,y:a};break;default:l={x:t.x,y:t.y}}var h=i?tn(i):null;if(h!=null){var u=h==="y"?"height":"width";switch(r){case pt:l[h]=l[h]-(t[u]/2-e[u]/2);break;case St:l[h]=l[h]+(t[u]/2-e[u]/2);break}}return l}function It(n,t){t===void 0&&(t={});var e=t,s=e.placement,i=s===void 0?n.placement:s,r=e.strategy,o=r===void 0?n.strategy:r,a=e.boundary,l=a===void 0?is:a,h=e.rootBoundary,u=h===void 0?qe:h,p=e.elementContext,_=p===void 0?wt:p,f=e.altBoundary,A=f===void 0?!1:f,m=e.padding,E=m===void 0?0:m,T=vs(typeof E!="number"?E:bs(E,Mt)),w=_===wt?rs:wt,O=n.rects.popper,g=n.elements[A?w:_],v=Si(_t(g)?g:g.contextElement||st(n.elements.popper),l,u,o),b=$t(n.elements.reference),y=ys({reference:b,element:O,placement:i}),S=Ke(Object.assign({},O,y)),N=_===wt?S:b,C={top:v.top-N.top+T.top,bottom:N.bottom-v.bottom+T.bottom,left:v.left-N.left+T.left,right:N.right-v.right+T.right},D=n.modifiersData.offset;if(_===wt&&D){var B=D[i];Object.keys(C).forEach(function($){var ot=[x,R].indexOf($)>=0?1:-1,at=[L,R].indexOf($)>=0?"y":"x";C[$]+=B[at]*ot})}return C}function Di(n,t){t===void 0&&(t={});var e=t,s=e.placement,i=e.boundary,r=e.rootBoundary,o=e.padding,a=e.flipVariations,l=e.allowedAutoPlacements,h=l===void 0?Xe:l,u=Lt(s),p=u?a?Be:Be.filter(function(A){return Lt(A)===u}):Mt,_=p.filter(function(A){return h.indexOf(A)>=0});_.length===0&&(_=p);var f=_.reduce(function(A,m){return A[m]=It(n,{placement:m,boundary:i,rootBoundary:r,padding:o})[U(m)],A},{});return Object.keys(f).sort(function(A,m){return f[A]-f[m]})}function $i(n){if(U(n)===ge)return[];var t=ue(n);return[wn(n),t,wn(t)]}function Li(n){var t=n.state,e=n.options,s=n.name;if(!t.modifiersData[s]._skip){for(var i=e.mainAxis,r=i===void 0?!0:i,o=e.altAxis,a=o===void 0?!0:o,l=e.fallbackPlacements,h=e.padding,u=e.boundary,p=e.rootBoundary,_=e.altBoundary,f=e.flipVariations,A=f===void 0?!0:f,m=e.allowedAutoPlacements,E=t.options.placement,T=U(E),w=T===E,O=l||(w||!A?[ue(E)]:$i(E)),g=[E].concat(O).reduce(function(At,J){return At.concat(U(J)===ge?Di(t,{placement:J,boundary:u,rootBoundary:p,padding:h,flipVariations:A,allowedAutoPlacements:m}):J)},[]),v=t.rects.reference,b=t.rects.popper,y=new Map,S=!0,N=g[0],C=0;C<g.length;C++){var D=g[C],B=U(D),$=Lt(D)===pt,ot=[L,R].indexOf(B)>=0,at=ot?"width":"height",M=It(t,{placement:D,boundary:u,rootBoundary:p,altBoundary:_,padding:h}),F=ot?$?x:I:$?R:L;v[at]>b[at]&&(F=ue(F));var Zt=ue(F),ct=[];if(r&&ct.push(M[B]<=0),a&&ct.push(M[F]<=0,M[Zt]<=0),ct.every(function(At){return At})){N=D,S=!1;break}y.set(D,ct)}if(S)for(var Jt=A?3:1,Te=function(J){var Ht=g.find(function(ee){var lt=y.get(ee);if(lt)return lt.slice(0,J).every(function(ye){return ye})});if(Ht)return N=Ht,"break"},Vt=Jt;Vt>0;Vt--){var te=Te(Vt);if(te==="break")break}t.placement!==N&&(t.modifiersData[s]._skip=!0,t.placement=N,t.reset=!0)}}const ws={name:"flip",enabled:!0,phase:"main",fn:Li,requiresIfExists:["offset"],data:{_skip:!1}};function Cn(n,t,e){return e===void 0&&(e={x:0,y:0}),{top:n.top-t.height-e.y,right:n.right-t.width+e.x,bottom:n.bottom-t.height+e.y,left:n.left-t.width-e.x}}function Nn(n){return[L,x,R,I].some(function(t){return n[t]>=0})}function Ii(n){var t=n.state,e=n.name,s=t.rects.reference,i=t.rects.popper,r=t.modifiersData.preventOverflow,o=It(t,{elementContext:"reference"}),a=It(t,{altBoundary:!0}),l=Cn(o,s),h=Cn(a,i,r),u=Nn(l),p=Nn(h);t.modifiersData[e]={referenceClippingOffsets:l,popperEscapeOffsets:h,isReferenceHidden:u,hasPopperEscaped:p},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":p})}const Os={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ii};function Pi(n,t,e){var s=U(n),i=[I,L].indexOf(s)>=0?-1:1,r=typeof e=="function"?e(Object.assign({},t,{placement:n})):e,o=r[0],a=r[1];return o=o||0,a=(a||0)*i,[I,x].indexOf(s)>=0?{x:a,y:o}:{x:o,y:a}}function Mi(n){var t=n.state,e=n.options,s=n.name,i=e.offset,r=i===void 0?[0,0]:i,o=Xe.reduce(function(u,p){return u[p]=Pi(p,t.rects,r),u},{}),a=o[t.placement],l=a.x,h=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=h),t.modifiersData[s]=o}const Cs={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Mi};function Ri(n){var t=n.state,e=n.name;t.modifiersData[e]=ys({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const an={name:"popperOffsets",enabled:!0,phase:"read",fn:Ri,data:{}};function xi(n){return n==="x"?"y":"x"}function ki(n){var t=n.state,e=n.options,s=n.name,i=e.mainAxis,r=i===void 0?!0:i,o=e.altAxis,a=o===void 0?!1:o,l=e.boundary,h=e.rootBoundary,u=e.altBoundary,p=e.padding,_=e.tether,f=_===void 0?!0:_,A=e.tetherOffset,m=A===void 0?0:A,E=It(t,{boundary:l,rootBoundary:h,padding:p,altBoundary:u}),T=U(t.placement),w=Lt(t.placement),O=!w,g=tn(T),v=xi(g),b=t.modifiersData.popperOffsets,y=t.rects.reference,S=t.rects.popper,N=typeof m=="function"?m(Object.assign({},t.rects,{placement:t.placement})):m,C=typeof N=="number"?{mainAxis:N,altAxis:N}:Object.assign({mainAxis:0,altAxis:0},N),D=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,B={x:0,y:0};if(b){if(r){var $,ot=g==="y"?L:I,at=g==="y"?R:x,M=g==="y"?"height":"width",F=b[g],Zt=F+E[ot],ct=F-E[at],Jt=f?-S[M]/2:0,Te=w===pt?y[M]:S[M],Vt=w===pt?-S[M]:-y[M],te=t.elements.arrow,At=f&&te?Je(te):{width:0,height:0},J=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Es(),Ht=J[ot],ee=J[at],lt=Bt(0,y[M],At[M]),ye=O?y[M]/2-Jt-lt-Ht-C.mainAxis:Te-lt-Ht-C.mainAxis,ii=O?-y[M]/2+Jt+lt+ee+C.mainAxis:Vt+lt+ee+C.mainAxis,we=t.elements.arrow&&Kt(t.elements.arrow),ri=we?g==="y"?we.clientTop||0:we.clientLeft||0:0,fn=($=D==null?void 0:D[g])!=null?$:0,oi=F+ye-fn-ri,ai=F+ii-fn,pn=Bt(f?fe(Zt,oi):Zt,F,f?ft(ct,ai):ct);b[g]=pn,B[g]=pn-F}if(a){var _n,ci=g==="x"?L:I,li=g==="x"?R:x,ut=b[v],ne=v==="y"?"height":"width",mn=ut+E[ci],gn=ut-E[li],Oe=[L,I].indexOf(T)!==-1,En=(_n=D==null?void 0:D[v])!=null?_n:0,vn=Oe?mn:ut-y[ne]-S[ne]-En+C.altAxis,bn=Oe?ut+y[ne]+S[ne]-En-C.altAxis:gn,An=f&&Oe?pi(vn,ut,bn):Bt(f?vn:mn,ut,f?bn:gn);b[v]=An,B[v]=An-ut}t.modifiersData[s]=B}}const Ns={name:"preventOverflow",enabled:!0,phase:"main",fn:ki,requiresIfExists:["offset"]};function Vi(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function Hi(n){return n===k(n)||!V(n)?sn(n):Vi(n)}function Wi(n){var t=n.getBoundingClientRect(),e=Dt(t.width)/n.offsetWidth||1,s=Dt(t.height)/n.offsetHeight||1;return e!==1||s!==1}function ji(n,t,e){e===void 0&&(e=!1);var s=V(t),i=V(t)&&Wi(t),r=st(t),o=$t(n,i,e),a={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(s||!s&&!e)&&((z(t)!=="body"||on(r))&&(a=Hi(t)),V(t)?(l=$t(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):r&&(l.x=rn(r))),{x:o.left+a.scrollLeft-l.x,y:o.top+a.scrollTop-l.y,width:o.width,height:o.height}}function Bi(n){var t=new Map,e=new Set,s=[];n.forEach(function(r){t.set(r.name,r)});function i(r){e.add(r.name);var o=[].concat(r.requires||[],r.requiresIfExists||[]);o.forEach(function(a){if(!e.has(a)){var l=t.get(a);l&&i(l)}}),s.push(r)}return n.forEach(function(r){e.has(r.name)||i(r)}),s}function Fi(n){var t=Bi(n);return _s.reduce(function(e,s){return e.concat(t.filter(function(i){return i.phase===s}))},[])}function Ki(n){var t;return function(){return t||(t=new Promise(function(e){Promise.resolve().then(function(){t=void 0,e(n())})})),t}}function Yi(n){var t=n.reduce(function(e,s){var i=e[s.name];return e[s.name]=i?Object.assign({},i,s,{options:Object.assign({},i.options,s.options),data:Object.assign({},i.data,s.data)}):s,e},{});return Object.keys(t).map(function(e){return t[e]})}var Sn={placement:"bottom",modifiers:[],strategy:"absolute"};function Dn(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];return!t.some(function(s){return!(s&&typeof s.getBoundingClientRect=="function")})}function ve(n){n===void 0&&(n={});var t=n,e=t.defaultModifiers,s=e===void 0?[]:e,i=t.defaultOptions,r=i===void 0?Sn:i;return function(a,l,h){h===void 0&&(h=r);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Sn,r),modifiersData:{},elements:{reference:a,popper:l},attributes:{},styles:{}},p=[],_=!1,f={state:u,setOptions:function(T){var w=typeof T=="function"?T(u.options):T;m(),u.options=Object.assign({},r,u.options,w),u.scrollParents={reference:_t(a)?Ft(a):a.contextElement?Ft(a.contextElement):[],popper:Ft(l)};var O=Fi(Yi([].concat(s,u.options.modifiers)));return u.orderedModifiers=O.filter(function(g){return g.enabled}),A(),f.update()},forceUpdate:function(){if(!_){var T=u.elements,w=T.reference,O=T.popper;if(Dn(w,O)){u.rects={reference:ji(w,Kt(O),u.options.strategy==="fixed"),popper:Je(O)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(C){return u.modifiersData[C.name]=Object.assign({},C.data)});for(var g=0;g<u.orderedModifiers.length;g++){if(u.reset===!0){u.reset=!1,g=-1;continue}var v=u.orderedModifiers[g],b=v.fn,y=v.options,S=y===void 0?{}:y,N=v.name;typeof b=="function"&&(u=b({state:u,options:S,name:N,instance:f})||u)}}}},update:Ki(function(){return new Promise(function(E){f.forceUpdate(),E(u)})}),destroy:function(){m(),_=!0}};if(!Dn(a,l))return f;f.setOptions(h).then(function(E){!_&&h.onFirstUpdate&&h.onFirstUpdate(E)});function A(){u.orderedModifiers.forEach(function(E){var T=E.name,w=E.options,O=w===void 0?{}:w,g=E.effect;if(typeof g=="function"){var v=g({state:u,name:T,instance:f,options:O}),b=function(){};p.push(v||b)}})}function m(){p.forEach(function(E){return E()}),p=[]}return f}}var Ui=ve(),zi=[nn,an,en,Ze],Gi=ve({defaultModifiers:zi}),qi=[nn,an,en,Ze,Cs,ws,Ns,As,Os],cn=ve({defaultModifiers:qi});const Ss=Object.freeze(Object.defineProperty({__proto__:null,afterMain:hs,afterRead:cs,afterWrite:ps,applyStyles:Ze,arrow:As,auto:ge,basePlacements:Mt,beforeMain:ls,beforeRead:os,beforeWrite:ds,bottom:R,clippingParents:is,computeStyles:en,createPopper:cn,createPopperBase:Ui,createPopperLite:Gi,detectOverflow:It,end:St,eventListeners:nn,flip:ws,hide:Os,left:I,main:us,modifierPhases:_s,offset:Cs,placements:Xe,popper:wt,popperGenerator:ve,popperOffsets:an,preventOverflow:Ns,read:as,reference:rs,right:x,start:pt,top:L,variationPlacements:Be,viewport:qe,write:fs},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.3.7 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const tt=new Map,Ce={set(n,t,e){tt.has(n)||tt.set(n,new Map);const s=tt.get(n);if(!s.has(t)&&s.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`);return}s.set(t,e)},get(n,t){return tt.has(n)&&tt.get(n).get(t)||null},remove(n,t){if(!tt.has(n))return;const e=tt.get(n);e.delete(t),e.size===0&&tt.delete(n)}},Xi=1e6,Qi=1e3,Ye="transitionend",Ds=n=>(n&&window.CSS&&window.CSS.escape&&(n=n.replace(/#([^\s"#']+)/g,(t,e)=>`#${CSS.escape(e)}`)),n),Zi=n=>n==null?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase(),Ji=n=>{do n+=Math.floor(Math.random()*Xi);while(document.getElementById(n));return n},tr=n=>{if(!n)return 0;let{transitionDuration:t,transitionDelay:e}=window.getComputedStyle(n);const s=Number.parseFloat(t),i=Number.parseFloat(e);return!s&&!i?0:(t=t.split(",")[0],e=e.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(e))*Qi)},$s=n=>{n.dispatchEvent(new Event(Ye))},G=n=>!n||typeof n!="object"?!1:(typeof n.jquery<"u"&&(n=n[0]),typeof n.nodeType<"u"),et=n=>G(n)?n.jquery?n[0]:n:typeof n=="string"&&n.length>0?document.querySelector(Ds(n)):null,Rt=n=>{if(!G(n)||n.getClientRects().length===0)return!1;const t=getComputedStyle(n).getPropertyValue("visibility")==="visible",e=n.closest("details:not([open])");if(!e)return t;if(e!==n){const s=n.closest("summary");if(s&&s.parentNode!==e||s===null)return!1}return t},nt=n=>!n||n.nodeType!==Node.ELEMENT_NODE||n.classList.contains("disabled")?!0:typeof n.disabled<"u"?n.disabled:n.hasAttribute("disabled")&&n.getAttribute("disabled")!=="false",Ls=n=>{if(!document.documentElement.attachShadow)return null;if(typeof n.getRootNode=="function"){const t=n.getRootNode();return t instanceof ShadowRoot?t:null}return n instanceof ShadowRoot?n:n.parentNode?Ls(n.parentNode):null},pe=()=>{},Yt=n=>{n.offsetHeight},Is=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Ne=[],er=n=>{document.readyState==="loading"?(Ne.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of Ne)t()}),Ne.push(n)):n()},H=()=>document.documentElement.dir==="rtl",j=n=>{er(()=>{const t=Is();if(t){const e=n.NAME,s=t.fn[e];t.fn[e]=n.jQueryInterface,t.fn[e].Constructor=n,t.fn[e].noConflict=()=>(t.fn[e]=s,n.jQueryInterface)}})},P=(n,t=[],e=n)=>typeof n=="function"?n.call(...t):e,Ps=(n,t,e=!0)=>{if(!e){P(n);return}const i=tr(t)+5;let r=!1;const o=({target:a})=>{a===t&&(r=!0,t.removeEventListener(Ye,o),P(n))};t.addEventListener(Ye,o),setTimeout(()=>{r||$s(t)},i)},ln=(n,t,e,s)=>{const i=n.length;let r=n.indexOf(t);return r===-1?!e&&s?n[i-1]:n[0]:(r+=e?1:-1,s&&(r=(r+i)%i),n[Math.max(0,Math.min(r,i-1))])},nr=/[^.]*(?=\..*)\.|.*/,sr=/\..*/,ir=/::\d+$/,Se={};let $n=1;const Ms={mouseenter:"mouseover",mouseleave:"mouseout"},rr=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Rs(n,t){return t&&`${t}::${$n++}`||n.uidEvent||$n++}function xs(n){const t=Rs(n);return n.uidEvent=t,Se[t]=Se[t]||{},Se[t]}function or(n,t){return function e(s){return un(s,{delegateTarget:n}),e.oneOff&&c.off(n,s.type,t),t.apply(n,[s])}}function ar(n,t,e){return function s(i){const r=n.querySelectorAll(t);for(let{target:o}=i;o&&o!==this;o=o.parentNode)for(const a of r)if(a===o)return un(i,{delegateTarget:o}),s.oneOff&&c.off(n,i.type,t,e),e.apply(o,[i])}}function ks(n,t,e=null){return Object.values(n).find(s=>s.callable===t&&s.delegationSelector===e)}function Vs(n,t,e){const s=typeof t=="string",i=s?e:t||e;let r=Hs(n);return rr.has(r)||(r=n),[s,i,r]}function Ln(n,t,e,s,i){if(typeof t!="string"||!n)return;let[r,o,a]=Vs(t,e,s);t in Ms&&(o=(A=>function(m){if(!m.relatedTarget||m.relatedTarget!==m.delegateTarget&&!m.delegateTarget.contains(m.relatedTarget))return A.call(this,m)})(o));const l=xs(n),h=l[a]||(l[a]={}),u=ks(h,o,r?e:null);if(u){u.oneOff=u.oneOff&&i;return}const p=Rs(o,t.replace(nr,"")),_=r?ar(n,e,o):or(n,o);_.delegationSelector=r?e:null,_.callable=o,_.oneOff=i,_.uidEvent=p,h[p]=_,n.addEventListener(a,_,r)}function Ue(n,t,e,s,i){const r=ks(t[e],s,i);r&&(n.removeEventListener(e,r,!!i),delete t[e][r.uidEvent])}function cr(n,t,e,s){const i=t[e]||{};for(const[r,o]of Object.entries(i))r.includes(s)&&Ue(n,t,e,o.callable,o.delegationSelector)}function Hs(n){return n=n.replace(sr,""),Ms[n]||n}const c={on(n,t,e,s){Ln(n,t,e,s,!1)},one(n,t,e,s){Ln(n,t,e,s,!0)},off(n,t,e,s){if(typeof t!="string"||!n)return;const[i,r,o]=Vs(t,e,s),a=o!==t,l=xs(n),h=l[o]||{},u=t.startsWith(".");if(typeof r<"u"){if(!Object.keys(h).length)return;Ue(n,l,o,r,i?e:null);return}if(u)for(const p of Object.keys(l))cr(n,l,p,t.slice(1));for(const[p,_]of Object.entries(h)){const f=p.replace(ir,"");(!a||t.includes(f))&&Ue(n,l,o,_.callable,_.delegationSelector)}},trigger(n,t,e){if(typeof t!="string"||!n)return null;const s=Is(),i=Hs(t),r=t!==i;let o=null,a=!0,l=!0,h=!1;r&&s&&(o=s.Event(t,e),s(n).trigger(o),a=!o.isPropagationStopped(),l=!o.isImmediatePropagationStopped(),h=o.isDefaultPrevented());const u=un(new Event(t,{bubbles:a,cancelable:!0}),e);return h&&u.preventDefault(),l&&n.dispatchEvent(u),u.defaultPrevented&&o&&o.preventDefault(),u}};function un(n,t={}){for(const[e,s]of Object.entries(t))try{n[e]=s}catch{Object.defineProperty(n,e,{configurable:!0,get(){return s}})}return n}function In(n){if(n==="true")return!0;if(n==="false")return!1;if(n===Number(n).toString())return Number(n);if(n===""||n==="null")return null;if(typeof n!="string")return n;try{return JSON.parse(decodeURIComponent(n))}catch{return n}}function De(n){return n.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const q={setDataAttribute(n,t,e){n.setAttribute(`data-bs-${De(t)}`,e)},removeDataAttribute(n,t){n.removeAttribute(`data-bs-${De(t)}`)},getDataAttributes(n){if(!n)return{};const t={},e=Object.keys(n.dataset).filter(s=>s.startsWith("bs")&&!s.startsWith("bsConfig"));for(const s of e){let i=s.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1),t[i]=In(n.dataset[s])}return t},getDataAttribute(n,t){return In(n.getAttribute(`data-bs-${De(t)}`))}};class Ut{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const s=G(e)?q.getDataAttribute(e,"config"):{};return{...this.constructor.Default,...typeof s=="object"?s:{},...G(e)?q.getDataAttributes(e):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[s,i]of Object.entries(e)){const r=t[s],o=G(r)?"element":Zi(r);if(!new RegExp(i).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${o}" but expected type "${i}".`)}}}const lr="5.3.7";class Y extends Ut{constructor(t,e){super(),t=et(t),t&&(this._element=t,this._config=this._getConfig(e),Ce.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Ce.remove(this._element,this.constructor.DATA_KEY),c.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,s=!0){Ps(t,e,s)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return Ce.get(et(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,typeof e=="object"?e:null)}static get VERSION(){return lr}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const $e=n=>{let t=n.getAttribute("data-bs-target");if(!t||t==="#"){let e=n.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),t=e&&e!=="#"?e.trim():null}return t?t.split(",").map(e=>Ds(e)).join(","):null},d={find(n,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,n))},findOne(n,t=document.documentElement){return Element.prototype.querySelector.call(t,n)},children(n,t){return[].concat(...n.children).filter(e=>e.matches(t))},parents(n,t){const e=[];let s=n.parentNode.closest(t);for(;s;)e.push(s),s=s.parentNode.closest(t);return e},prev(n,t){let e=n.previousElementSibling;for(;e;){if(e.matches(t))return[e];e=e.previousElementSibling}return[]},next(n,t){let e=n.nextElementSibling;for(;e;){if(e.matches(t))return[e];e=e.nextElementSibling}return[]},focusableChildren(n){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,n).filter(e=>!nt(e)&&Rt(e))},getSelectorFromElement(n){const t=$e(n);return t&&d.findOne(t)?t:null},getElementFromSelector(n){const t=$e(n);return t?d.findOne(t):null},getMultipleElementsFromSelector(n){const t=$e(n);return t?d.find(t):[]}},be=(n,t="hide")=>{const e=`click.dismiss${n.EVENT_KEY}`,s=n.NAME;c.on(document,e,`[data-bs-dismiss="${s}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),nt(this))return;const r=d.getElementFromSelector(this)||this.closest(`.${s}`);n.getOrCreateInstance(r)[t]()})},ur="alert",hr="bs.alert",Ws=`.${hr}`,dr=`close${Ws}`,fr=`closed${Ws}`,pr="fade",_r="show";class zt extends Y{static get NAME(){return ur}close(){if(c.trigger(this._element,dr).defaultPrevented)return;this._element.classList.remove(_r);const e=this._element.classList.contains(pr);this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),c.trigger(this._element,fr),this.dispose()}static jQueryInterface(t){return this.each(function(){const e=zt.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}be(zt,"close");j(zt);const mr="button",gr="bs.button",Er=`.${gr}`,vr=".data-api",br="active",Pn='[data-bs-toggle="button"]',Ar=`click${Er}${vr}`;class Gt extends Y{static get NAME(){return mr}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(br))}static jQueryInterface(t){return this.each(function(){const e=Gt.getOrCreateInstance(this);t==="toggle"&&e[t]()})}}c.on(document,Ar,Pn,n=>{n.preventDefault();const t=n.target.closest(Pn);Gt.getOrCreateInstance(t).toggle()});j(Gt);const Tr="swipe",xt=".bs.swipe",yr=`touchstart${xt}`,wr=`touchmove${xt}`,Or=`touchend${xt}`,Cr=`pointerdown${xt}`,Nr=`pointerup${xt}`,Sr="touch",Dr="pen",$r="pointer-event",Lr=40,Ir={endCallback:null,leftCallback:null,rightCallback:null},Pr={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class _e extends Ut{constructor(t,e){super(),this._element=t,!(!t||!_e.isSupported())&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return Ir}static get DefaultType(){return Pr}static get NAME(){return Tr}dispose(){c.off(this._element,xt)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),P(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=Lr)return;const e=t/this._deltaX;this._deltaX=0,e&&P(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(c.on(this._element,Cr,t=>this._start(t)),c.on(this._element,Nr,t=>this._end(t)),this._element.classList.add($r)):(c.on(this._element,yr,t=>this._start(t)),c.on(this._element,wr,t=>this._move(t)),c.on(this._element,Or,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===Dr||t.pointerType===Sr)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Mr="carousel",Rr="bs.carousel",it=`.${Rr}`,js=".data-api",xr="ArrowLeft",kr="ArrowRight",Vr=500,Wt="next",Tt="prev",Ot="left",he="right",Hr=`slide${it}`,Le=`slid${it}`,Wr=`keydown${it}`,jr=`mouseenter${it}`,Br=`mouseleave${it}`,Fr=`dragstart${it}`,Kr=`load${it}${js}`,Yr=`click${it}${js}`,Bs="carousel",ie="active",Ur="slide",zr="carousel-item-end",Gr="carousel-item-start",qr="carousel-item-next",Xr="carousel-item-prev",Fs=".active",Ks=".carousel-item",Qr=Fs+Ks,Zr=".carousel-item img",Jr=".carousel-indicators",to="[data-bs-slide], [data-bs-slide-to]",eo='[data-bs-ride="carousel"]',no={[xr]:he,[kr]:Ot},so={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},io={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class kt extends Y{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=d.findOne(Jr,this._element),this._addEventListeners(),this._config.ride===Bs&&this.cycle()}static get Default(){return so}static get DefaultType(){return io}static get NAME(){return Mr}next(){this._slide(Wt)}nextWhenVisible(){!document.hidden&&Rt(this._element)&&this.next()}prev(){this._slide(Tt)}pause(){this._isSliding&&$s(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){c.one(this._element,Le,()=>this.cycle());return}this.cycle()}}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding){c.one(this._element,Le,()=>this.to(t));return}const s=this._getItemIndex(this._getActive());if(s===t)return;const i=t>s?Wt:Tt;this._slide(i,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&c.on(this._element,Wr,t=>this._keydown(t)),this._config.pause==="hover"&&(c.on(this._element,jr,()=>this.pause()),c.on(this._element,Br,()=>this._maybeEnableCycle())),this._config.touch&&_e.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of d.find(Zr,this._element))c.on(s,Fr,i=>i.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(Ot)),rightCallback:()=>this._slide(this._directionToOrder(he)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),Vr+this._config.interval))}};this._swipeHelper=new _e(this._element,e)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=no[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=d.findOne(Fs,this._indicatorsElement);e.classList.remove(ie),e.removeAttribute("aria-current");const s=d.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);s&&(s.classList.add(ie),s.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const s=this._getActive(),i=t===Wt,r=e||ln(this._getItems(),s,i,this._config.wrap);if(r===s)return;const o=this._getItemIndex(r),a=f=>c.trigger(this._element,f,{relatedTarget:r,direction:this._orderToDirection(t),from:this._getItemIndex(s),to:o});if(a(Hr).defaultPrevented||!s||!r)return;const h=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=r;const u=i?Gr:zr,p=i?qr:Xr;r.classList.add(p),Yt(r),s.classList.add(u),r.classList.add(u);const _=()=>{r.classList.remove(u,p),r.classList.add(ie),s.classList.remove(ie,p,u),this._isSliding=!1,a(Le)};this._queueCallback(_,s,this._isAnimated()),h&&this.cycle()}_isAnimated(){return this._element.classList.contains(Ur)}_getActive(){return d.findOne(Qr,this._element)}_getItems(){return d.find(Ks,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return H()?t===Ot?Tt:Wt:t===Ot?Wt:Tt}_orderToDirection(t){return H()?t===Tt?Ot:he:t===Tt?he:Ot}static jQueryInterface(t){return this.each(function(){const e=kt.getOrCreateInstance(this,t);if(typeof t=="number"){e.to(t);return}if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}c.on(document,Yr,to,function(n){const t=d.getElementFromSelector(this);if(!t||!t.classList.contains(Bs))return;n.preventDefault();const e=kt.getOrCreateInstance(t),s=this.getAttribute("data-bs-slide-to");if(s){e.to(s),e._maybeEnableCycle();return}if(q.getDataAttribute(this,"slide")==="next"){e.next(),e._maybeEnableCycle();return}e.prev(),e._maybeEnableCycle()});c.on(window,Kr,()=>{const n=d.find(eo);for(const t of n)kt.getOrCreateInstance(t)});j(kt);const ro="collapse",oo="bs.collapse",qt=`.${oo}`,ao=".data-api",co=`show${qt}`,lo=`shown${qt}`,uo=`hide${qt}`,ho=`hidden${qt}`,fo=`click${qt}${ao}`,Ie="show",Nt="collapse",re="collapsing",po="collapsed",_o=`:scope .${Nt} .${Nt}`,mo="collapse-horizontal",go="width",Eo="height",vo=".collapse.show, .collapse.collapsing",ze='[data-bs-toggle="collapse"]',bo={parent:null,toggle:!0},Ao={parent:"(null|element)",toggle:"boolean"};class Pt extends Y{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const s=d.find(ze);for(const i of s){const r=d.getSelectorFromElement(i),o=d.find(r).filter(a=>a===this._element);r!==null&&o.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return bo}static get DefaultType(){return Ao}static get NAME(){return ro}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(vo).filter(a=>a!==this._element).map(a=>Pt.getOrCreateInstance(a,{toggle:!1}))),t.length&&t[0]._isTransitioning||c.trigger(this._element,co).defaultPrevented)return;for(const a of t)a.hide();const s=this._getDimension();this._element.classList.remove(Nt),this._element.classList.add(re),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(re),this._element.classList.add(Nt,Ie),this._element.style[s]="",c.trigger(this._element,lo)},o=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(i,this._element,!0),this._element.style[s]=`${this._element[o]}px`}hide(){if(this._isTransitioning||!this._isShown()||c.trigger(this._element,uo).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,Yt(this._element),this._element.classList.add(re),this._element.classList.remove(Nt,Ie);for(const i of this._triggerArray){const r=d.getElementFromSelector(i);r&&!this._isShown(r)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;const s=()=>{this._isTransitioning=!1,this._element.classList.remove(re),this._element.classList.add(Nt),c.trigger(this._element,ho)};this._element.style[e]="",this._queueCallback(s,this._element,!0)}_isShown(t=this._element){return t.classList.contains(Ie)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=et(t.parent),t}_getDimension(){return this._element.classList.contains(mo)?go:Eo}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(ze);for(const e of t){const s=d.getElementFromSelector(e);s&&this._addAriaAndCollapsedClass([e],this._isShown(s))}}_getFirstLevelChildren(t){const e=d.find(_o,this._config.parent);return d.find(t,this._config.parent).filter(s=>!e.includes(s))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const s of t)s.classList.toggle(po,!e),s.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return typeof t=="string"&&/show|hide/.test(t)&&(e.toggle=!1),this.each(function(){const s=Pt.getOrCreateInstance(this,e);if(typeof t=="string"){if(typeof s[t]>"u")throw new TypeError(`No method named "${t}"`);s[t]()}})}}c.on(document,fo,ze,function(n){(n.target.tagName==="A"||n.delegateTarget&&n.delegateTarget.tagName==="A")&&n.preventDefault();for(const t of d.getMultipleElementsFromSelector(this))Pt.getOrCreateInstance(t,{toggle:!1}).toggle()});j(Pt);const Mn="dropdown",To="bs.dropdown",Et=`.${To}`,hn=".data-api",yo="Escape",Rn="Tab",wo="ArrowUp",xn="ArrowDown",Oo=2,Co=`hide${Et}`,No=`hidden${Et}`,So=`show${Et}`,Do=`shown${Et}`,Ys=`click${Et}${hn}`,Us=`keydown${Et}${hn}`,$o=`keyup${Et}${hn}`,Ct="show",Lo="dropup",Io="dropend",Po="dropstart",Mo="dropup-center",Ro="dropdown-center",ht='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',xo=`${ht}.${Ct}`,de=".dropdown-menu",ko=".navbar",Vo=".navbar-nav",Ho=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Wo=H()?"top-end":"top-start",jo=H()?"top-start":"top-end",Bo=H()?"bottom-end":"bottom-start",Fo=H()?"bottom-start":"bottom-end",Ko=H()?"left-start":"right-start",Yo=H()?"right-start":"left-start",Uo="top",zo="bottom",Go={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},qo={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class K extends Y{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=d.next(this._element,de)[0]||d.prev(this._element,de)[0]||d.findOne(de,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Go}static get DefaultType(){return qo}static get NAME(){return Mn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(nt(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!c.trigger(this._element,So,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Vo))for(const s of[].concat(...document.body.children))c.on(s,"mouseover",pe);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Ct),this._element.classList.add(Ct),c.trigger(this._element,Do,t)}}hide(){if(nt(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!c.trigger(this._element,Co,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))c.off(s,"mouseover",pe);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ct),this._element.classList.remove(Ct),this._element.setAttribute("aria-expanded","false"),q.removeDataAttribute(this._menu,"popper"),c.trigger(this._element,No,t),this._element.focus()}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!G(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${Mn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof Ss>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;this._config.reference==="parent"?t=this._parent:G(this._config.reference)?t=et(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=cn(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Ct)}_getPlacement(){const t=this._parent;if(t.classList.contains(Io))return Ko;if(t.classList.contains(Po))return Yo;if(t.classList.contains(Mo))return Uo;if(t.classList.contains(Ro))return zo;const e=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(Lo)?e?jo:Wo:e?Fo:Bo}_detectNavbar(){return this._element.closest(ko)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(q.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...P(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const s=d.find(Ho,this._menu).filter(i=>Rt(i));s.length&&ln(s,e,t===xn,!s.includes(e)).focus()}static jQueryInterface(t){return this.each(function(){const e=K.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}static clearMenus(t){if(t.button===Oo||t.type==="keyup"&&t.key!==Rn)return;const e=d.find(xo);for(const s of e){const i=K.getInstance(s);if(!i||i._config.autoClose===!1)continue;const r=t.composedPath(),o=r.includes(i._menu);if(r.includes(i._element)||i._config.autoClose==="inside"&&!o||i._config.autoClose==="outside"&&o||i._menu.contains(t.target)&&(t.type==="keyup"&&t.key===Rn||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const a={relatedTarget:i._element};t.type==="click"&&(a.clickEvent=t),i._completeHide(a)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),s=t.key===yo,i=[wo,xn].includes(t.key);if(!i&&!s||e&&!s)return;t.preventDefault();const r=this.matches(ht)?this:d.prev(this,ht)[0]||d.next(this,ht)[0]||d.findOne(ht,t.delegateTarget.parentNode),o=K.getOrCreateInstance(r);if(i){t.stopPropagation(),o.show(),o._selectMenuItem(t);return}o._isShown()&&(t.stopPropagation(),o.hide(),r.focus())}}c.on(document,Us,ht,K.dataApiKeydownHandler);c.on(document,Us,de,K.dataApiKeydownHandler);c.on(document,Ys,K.clearMenus);c.on(document,$o,K.clearMenus);c.on(document,Ys,ht,function(n){n.preventDefault(),K.getOrCreateInstance(this).toggle()});j(K);const zs="backdrop",Xo="fade",kn="show",Vn=`mousedown.bs.${zs}`,Qo={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Zo={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Gs extends Ut{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Qo}static get DefaultType(){return Zo}static get NAME(){return zs}show(t){if(!this._config.isVisible){P(t);return}this._append();const e=this._getElement();this._config.isAnimated&&Yt(e),e.classList.add(kn),this._emulateAnimation(()=>{P(t)})}hide(t){if(!this._config.isVisible){P(t);return}this._getElement().classList.remove(kn),this._emulateAnimation(()=>{this.dispose(),P(t)})}dispose(){this._isAppended&&(c.off(this._element,Vn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(Xo),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=et(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),c.on(t,Vn,()=>{P(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){Ps(t,this._getElement(),this._config.isAnimated)}}const Jo="focustrap",ta="bs.focustrap",me=`.${ta}`,ea=`focusin${me}`,na=`keydown.tab${me}`,sa="Tab",ia="forward",Hn="backward",ra={autofocus:!0,trapElement:null},oa={autofocus:"boolean",trapElement:"element"};class qs extends Ut{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ra}static get DefaultType(){return oa}static get NAME(){return Jo}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),c.off(document,me),c.on(document,ea,t=>this._handleFocusin(t)),c.on(document,na,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,c.off(document,me))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const s=d.focusableChildren(e);s.length===0?e.focus():this._lastTabNavDirection===Hn?s[s.length-1].focus():s[0].focus()}_handleKeydown(t){t.key===sa&&(this._lastTabNavDirection=t.shiftKey?Hn:ia)}}const Wn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",jn=".sticky-top",oe="padding-right",Bn="margin-right";class Ge{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,oe,e=>e+t),this._setElementAttributes(Wn,oe,e=>e+t),this._setElementAttributes(jn,Bn,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,oe),this._resetElementAttributes(Wn,oe),this._resetElementAttributes(jn,Bn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,s){const i=this.getWidth(),r=o=>{if(o!==this._element&&window.innerWidth>o.clientWidth+i)return;this._saveInitialAttribute(o,e);const a=window.getComputedStyle(o).getPropertyValue(e);o.style.setProperty(e,`${s(Number.parseFloat(a))}px`)};this._applyManipulationCallback(t,r)}_saveInitialAttribute(t,e){const s=t.style.getPropertyValue(e);s&&q.setDataAttribute(t,e,s)}_resetElementAttributes(t,e){const s=i=>{const r=q.getDataAttribute(i,e);if(r===null){i.style.removeProperty(e);return}q.removeDataAttribute(i,e),i.style.setProperty(e,r)};this._applyManipulationCallback(t,s)}_applyManipulationCallback(t,e){if(G(t)){e(t);return}for(const s of d.find(t,this._element))e(s)}}const aa="modal",ca="bs.modal",W=`.${ca}`,la=".data-api",ua="Escape",ha=`hide${W}`,da=`hidePrevented${W}`,Xs=`hidden${W}`,Qs=`show${W}`,fa=`shown${W}`,pa=`resize${W}`,_a=`click.dismiss${W}`,ma=`mousedown.dismiss${W}`,ga=`keydown.dismiss${W}`,Ea=`click${W}${la}`,Fn="modal-open",va="fade",Kn="show",Pe="modal-static",ba=".modal.show",Aa=".modal-dialog",Ta=".modal-body",ya='[data-bs-toggle="modal"]',wa={backdrop:!0,focus:!0,keyboard:!0},Oa={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class mt extends Y{constructor(t,e){super(t,e),this._dialog=d.findOne(Aa,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Ge,this._addEventListeners()}static get Default(){return wa}static get DefaultType(){return Oa}static get NAME(){return aa}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||c.trigger(this._element,Qs,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Fn),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||c.trigger(this._element,ha).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Kn),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){c.off(window,W),c.off(this._dialog,W),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Gs({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new qs({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=d.findOne(Ta,this._dialog);e&&(e.scrollTop=0),Yt(this._element),this._element.classList.add(Kn);const s=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,c.trigger(this._element,fa,{relatedTarget:t})};this._queueCallback(s,this._dialog,this._isAnimated())}_addEventListeners(){c.on(this._element,ga,t=>{if(t.key===ua){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),c.on(window,pa,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),c.on(this._element,ma,t=>{c.one(this._element,_a,e=>{if(!(this._element!==t.target||this._element!==e.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Fn),this._resetAdjustments(),this._scrollBar.reset(),c.trigger(this._element,Xs)})}_isAnimated(){return this._element.classList.contains(va)}_triggerBackdropTransition(){if(c.trigger(this._element,da).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;s==="hidden"||this._element.classList.contains(Pe)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Pe),this._queueCallback(()=>{this._element.classList.remove(Pe),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),s=e>0;if(s&&!t){const i=H()?"paddingLeft":"paddingRight";this._element.style[i]=`${e}px`}if(!s&&t){const i=H()?"paddingRight":"paddingLeft";this._element.style[i]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const s=mt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof s[t]>"u")throw new TypeError(`No method named "${t}"`);s[t](e)}})}}c.on(document,Ea,ya,function(n){const t=d.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&n.preventDefault(),c.one(t,Qs,i=>{i.defaultPrevented||c.one(t,Xs,()=>{Rt(this)&&this.focus()})});const e=d.findOne(ba);e&&mt.getInstance(e).hide(),mt.getOrCreateInstance(t).toggle(this)});be(mt);j(mt);const Ca="offcanvas",Na="bs.offcanvas",Z=`.${Na}`,Zs=".data-api",Sa=`load${Z}${Zs}`,Da="Escape",Yn="show",Un="showing",zn="hiding",$a="offcanvas-backdrop",Js=".offcanvas.show",La=`show${Z}`,Ia=`shown${Z}`,Pa=`hide${Z}`,Gn=`hidePrevented${Z}`,ti=`hidden${Z}`,Ma=`resize${Z}`,Ra=`click${Z}${Zs}`,xa=`keydown.dismiss${Z}`,ka='[data-bs-toggle="offcanvas"]',Va={backdrop:!0,keyboard:!0,scroll:!1},Ha={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Q extends Y{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Va}static get DefaultType(){return Ha}static get NAME(){return Ca}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||c.trigger(this._element,La,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Ge().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Un);const s=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(Yn),this._element.classList.remove(Un),c.trigger(this._element,Ia,{relatedTarget:t})};this._queueCallback(s,this._element,!0)}hide(){if(!this._isShown||c.trigger(this._element,Pa).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(zn),this._backdrop.hide();const e=()=>{this._element.classList.remove(Yn,zn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Ge().reset(),c.trigger(this._element,ti)};this._queueCallback(e,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){c.trigger(this._element,Gn);return}this.hide()},e=!!this._config.backdrop;return new Gs({className:$a,isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?t:null})}_initializeFocusTrap(){return new qs({trapElement:this._element})}_addEventListeners(){c.on(this._element,xa,t=>{if(t.key===Da){if(this._config.keyboard){this.hide();return}c.trigger(this._element,Gn)}})}static jQueryInterface(t){return this.each(function(){const e=Q.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}c.on(document,Ra,ka,function(n){const t=d.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),nt(this))return;c.one(t,ti,()=>{Rt(this)&&this.focus()});const e=d.findOne(Js);e&&e!==t&&Q.getInstance(e).hide(),Q.getOrCreateInstance(t).toggle(this)});c.on(window,Sa,()=>{for(const n of d.find(Js))Q.getOrCreateInstance(n).show()});c.on(window,Ma,()=>{for(const n of d.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(n).position!=="fixed"&&Q.getOrCreateInstance(n).hide()});be(Q);j(Q);const Wa=/^aria-[\w-]*$/i,ei={"*":["class","dir","id","lang","role",Wa],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},ja=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Ba=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Fa=(n,t)=>{const e=n.nodeName.toLowerCase();return t.includes(e)?ja.has(e)?!!Ba.test(n.nodeValue):!0:t.filter(s=>s instanceof RegExp).some(s=>s.test(e))};function Ka(n,t,e){if(!n.length)return n;if(e&&typeof e=="function")return e(n);const i=new window.DOMParser().parseFromString(n,"text/html"),r=[].concat(...i.body.querySelectorAll("*"));for(const o of r){const a=o.nodeName.toLowerCase();if(!Object.keys(t).includes(a)){o.remove();continue}const l=[].concat(...o.attributes),h=[].concat(t["*"]||[],t[a]||[]);for(const u of l)Fa(u,h)||o.removeAttribute(u.nodeName)}return i.body.innerHTML}const Ya="TemplateFactory",Ua={allowList:ei,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},za={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Ga={entry:"(string|element|function|null)",selector:"(string|element)"};class qa extends Ut{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Ua}static get DefaultType(){return za}static get NAME(){return Ya}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[i,r]of Object.entries(this._config.content))this._setContent(t,r,i);const e=t.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&e.classList.add(...s.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,s]of Object.entries(t))super._typeCheckConfig({selector:e,entry:s},Ga)}_setContent(t,e,s){const i=d.findOne(s,t);if(i){if(e=this._resolvePossibleFunction(e),!e){i.remove();return}if(G(e)){this._putElementInTemplate(et(e),i);return}if(this._config.html){i.innerHTML=this._maybeSanitize(e);return}i.textContent=e}}_maybeSanitize(t){return this._config.sanitize?Ka(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return P(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html){e.innerHTML="",e.append(t);return}e.textContent=t.textContent}}const Xa="tooltip",Qa=new Set(["sanitize","allowList","sanitizeFn"]),Me="fade",Za="modal",ae="show",Ja=".tooltip-inner",qn=`.${Za}`,Xn="hide.bs.modal",jt="hover",Re="focus",xe="click",tc="manual",ec="hide",nc="hidden",sc="show",ic="shown",rc="inserted",oc="click",ac="focusin",cc="focusout",lc="mouseenter",uc="mouseleave",hc={AUTO:"auto",TOP:"top",RIGHT:H()?"left":"right",BOTTOM:"bottom",LEFT:H()?"right":"left"},dc={allowList:ei,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},fc={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class vt extends Y{constructor(t,e){if(typeof Ss>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return dc}static get DefaultType(){return fc}static get NAME(){return Xa}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),c.off(this._element.closest(qn),Xn,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=c.trigger(this._element,this.constructor.eventName(sc)),s=(Ls(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!s)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(i),c.trigger(this._element,this.constructor.eventName(rc))),this._popper=this._createPopper(i),i.classList.add(ae),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))c.on(a,"mouseover",pe);const o=()=>{c.trigger(this._element,this.constructor.eventName(ic)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(o,this.tip,this._isAnimated())}hide(){if(!this._isShown()||c.trigger(this._element,this.constructor.eventName(ec)).defaultPrevented)return;if(this._getTipElement().classList.remove(ae),"ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))c.off(i,"mouseover",pe);this._activeTrigger[xe]=!1,this._activeTrigger[Re]=!1,this._activeTrigger[jt]=!1,this._isHovered=null;const s=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),c.trigger(this._element,this.constructor.eventName(nc)))};this._queueCallback(s,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Me,ae),e.classList.add(`bs-${this.constructor.NAME}-auto`);const s=Ji(this.constructor.NAME).toString();return e.setAttribute("id",s),this._isAnimated()&&e.classList.add(Me),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new qa({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Ja]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Me)}_isShown(){return this.tip&&this.tip.classList.contains(ae)}_createPopper(t){const e=P(this._config.placement,[this,t,this._element]),s=hc[e.toUpperCase()];return cn(this._element,t,this._getPopperConfig(s))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_resolvePossibleFunction(t){return P(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:s=>{this._getTipElement().setAttribute("data-popper-placement",s.state.placement)}}]};return{...e,...P(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if(e==="click")c.on(this._element,this.constructor.eventName(oc),this._config.selector,s=>{const i=this._initializeOnDelegatedTarget(s);i._activeTrigger[xe]=!(i._isShown()&&i._activeTrigger[xe]),i.toggle()});else if(e!==tc){const s=e===jt?this.constructor.eventName(lc):this.constructor.eventName(ac),i=e===jt?this.constructor.eventName(uc):this.constructor.eventName(cc);c.on(this._element,s,this._config.selector,r=>{const o=this._initializeOnDelegatedTarget(r);o._activeTrigger[r.type==="focusin"?Re:jt]=!0,o._enter()}),c.on(this._element,i,this._config.selector,r=>{const o=this._initializeOnDelegatedTarget(r);o._activeTrigger[r.type==="focusout"?Re:jt]=o._element.contains(r.relatedTarget),o._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},c.on(this._element.closest(qn),Xn,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=q.getDataAttributes(this._element);for(const s of Object.keys(e))Qa.has(s)&&delete e[s];return t={...e,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:et(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,s]of Object.entries(this._config))this.constructor.Default[e]!==s&&(t[e]=s);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=vt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}}j(vt);const pc="popover",_c=".popover-header",mc=".popover-body",gc={...vt.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Ec={...vt.DefaultType,content:"(null|string|element|function)"};class Ae extends vt{static get Default(){return gc}static get DefaultType(){return Ec}static get NAME(){return pc}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[_c]:this._getTitle(),[mc]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=Ae.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}}j(Ae);const vc="scrollspy",bc="bs.scrollspy",dn=`.${bc}`,Ac=".data-api",Tc=`activate${dn}`,Qn=`click${dn}`,yc=`load${dn}${Ac}`,wc="dropdown-item",yt="active",Oc='[data-bs-spy="scroll"]',ke="[href]",Cc=".nav, .list-group",Zn=".nav-link",Nc=".nav-item",Sc=".list-group-item",Dc=`${Zn}, ${Nc} > ${Zn}, ${Sc}`,$c=".dropdown",Lc=".dropdown-toggle",Ic={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Pc={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Xt extends Y{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ic}static get DefaultType(){return Pc}static get NAME(){return vc}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=et(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(e=>Number.parseFloat(e))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(c.off(this._config.target,Qn),c.on(this._config.target,Qn,ke,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const s=this._rootElement||window,i=e.offsetTop-this._element.offsetTop;if(s.scrollTo){s.scrollTo({top:i,behavior:"smooth"});return}s.scrollTop=i}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),t)}_observerCallback(t){const e=o=>this._targetLinks.get(`#${o.target.id}`),s=o=>{this._previousScrollData.visibleEntryTop=o.target.offsetTop,this._process(e(o))},i=(this._rootElement||document.documentElement).scrollTop,r=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const a=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(r&&a){if(s(o),!i)return;continue}!r&&!a&&s(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=d.find(ke,this._config.target);for(const e of t){if(!e.hash||nt(e))continue;const s=d.findOne(decodeURI(e.hash),this._element);Rt(s)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,s))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(yt),this._activateParents(t),c.trigger(this._element,Tc,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(wc)){d.findOne(Lc,t.closest($c)).classList.add(yt);return}for(const e of d.parents(t,Cc))for(const s of d.prev(e,Dc))s.classList.add(yt)}_clearActiveClass(t){t.classList.remove(yt);const e=d.find(`${ke}.${yt}`,t);for(const s of e)s.classList.remove(yt)}static jQueryInterface(t){return this.each(function(){const e=Xt.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}c.on(window,yc,()=>{for(const n of d.find(Oc))Xt.getOrCreateInstance(n)});j(Xt);const Mc="tab",Rc="bs.tab",bt=`.${Rc}`,xc=`hide${bt}`,kc=`hidden${bt}`,Vc=`show${bt}`,Hc=`shown${bt}`,Wc=`click${bt}`,jc=`keydown${bt}`,Bc=`load${bt}`,Fc="ArrowLeft",Jn="ArrowRight",Kc="ArrowUp",ts="ArrowDown",Ve="Home",es="End",dt="active",ns="fade",He="show",Yc="dropdown",ni=".dropdown-toggle",Uc=".dropdown-menu",We=`:not(${ni})`,zc='.list-group, .nav, [role="tablist"]',Gc=".nav-item, .list-group-item",qc=`.nav-link${We}, .list-group-item${We}, [role="tab"]${We}`,si='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',je=`${qc}, ${si}`,Xc=`.${dt}[data-bs-toggle="tab"], .${dt}[data-bs-toggle="pill"], .${dt}[data-bs-toggle="list"]`;class gt extends Y{constructor(t){super(t),this._parent=this._element.closest(zc),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),c.on(this._element,jc,e=>this._keydown(e)))}static get NAME(){return Mc}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),s=e?c.trigger(e,xc,{relatedTarget:t}):null;c.trigger(t,Vc,{relatedTarget:e}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(dt),this._activate(d.getElementFromSelector(t));const s=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(He);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),c.trigger(t,Hc,{relatedTarget:e})};this._queueCallback(s,t,t.classList.contains(ns))}_deactivate(t,e){if(!t)return;t.classList.remove(dt),t.blur(),this._deactivate(d.getElementFromSelector(t));const s=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(He);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),c.trigger(t,kc,{relatedTarget:e})};this._queueCallback(s,t,t.classList.contains(ns))}_keydown(t){if(![Fc,Jn,Kc,ts,Ve,es].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter(i=>!nt(i));let s;if([Ve,es].includes(t.key))s=e[t.key===Ve?0:e.length-1];else{const i=[Jn,ts].includes(t.key);s=ln(e,t.target,i,!0)}s&&(s.focus({preventScroll:!0}),gt.getOrCreateInstance(s).show())}_getChildren(){return d.find(je,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const s of e)this._setInitialAttributesOnChild(s)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),s=this._getOuterElement(t);t.setAttribute("aria-selected",e),s!==t&&this._setAttributeIfNotExists(s,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=d.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const s=this._getOuterElement(t);if(!s.classList.contains(Yc))return;const i=(r,o)=>{const a=d.findOne(r,s);a&&a.classList.toggle(o,e)};i(ni,dt),i(Uc,He),s.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,s){t.hasAttribute(e)||t.setAttribute(e,s)}_elemIsActive(t){return t.classList.contains(dt)}_getInnerElement(t){return t.matches(je)?t:d.findOne(je,t)}_getOuterElement(t){return t.closest(Gc)||t}static jQueryInterface(t){return this.each(function(){const e=gt.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}c.on(document,Wc,si,function(n){["A","AREA"].includes(this.tagName)&&n.preventDefault(),!nt(this)&&gt.getOrCreateInstance(this).show()});c.on(window,Bc,()=>{for(const n of d.find(Xc))gt.getOrCreateInstance(n)});j(gt);const Qc="toast",Zc="bs.toast",rt=`.${Zc}`,Jc=`mouseover${rt}`,tl=`mouseout${rt}`,el=`focusin${rt}`,nl=`focusout${rt}`,sl=`hide${rt}`,il=`hidden${rt}`,rl=`show${rt}`,ol=`shown${rt}`,al="fade",ss="hide",ce="show",le="showing",cl={animation:"boolean",autohide:"boolean",delay:"number"},ll={animation:!0,autohide:!0,delay:5e3};class Qt extends Y{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return ll}static get DefaultType(){return cl}static get NAME(){return Qc}show(){if(c.trigger(this._element,rl).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(al);const e=()=>{this._element.classList.remove(le),c.trigger(this._element,ol),this._maybeScheduleHide()};this._element.classList.remove(ss),Yt(this._element),this._element.classList.add(ce,le),this._queueCallback(e,this._element,this._config.animation)}hide(){if(!this.isShown()||c.trigger(this._element,sl).defaultPrevented)return;const e=()=>{this._element.classList.add(ss),this._element.classList.remove(le,ce),c.trigger(this._element,il)};this._element.classList.add(le),this._queueCallback(e,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(ce),super.dispose()}isShown(){return this._element.classList.contains(ce)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=e;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=e;break}}if(e){this._clearTimeout();return}const s=t.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){c.on(this._element,Jc,t=>this._onInteraction(t,!0)),c.on(this._element,tl,t=>this._onInteraction(t,!1)),c.on(this._element,el,t=>this._onInteraction(t,!0)),c.on(this._element,nl,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const e=Qt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}be(Qt);j(Qt);const ul=Object.freeze(Object.defineProperty({__proto__:null,Alert:zt,Button:Gt,Carousel:kt,Collapse:Pt,Dropdown:K,Modal:mt,Offcanvas:Q,Popover:Ae,ScrollSpy:Xt,Tab:gt,Toast:Qt,Tooltip:vt},Symbol.toStringTag,{value:"Module"}));try{window.bootstrap=ul}catch{}
