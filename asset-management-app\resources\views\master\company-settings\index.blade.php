@extends('layouts.contentNavbarLayout')

@section('title', 'Pengaturan Perusahaan - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Pengaturan Perusahaan
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Informasi Perusahaan</h5>
          <a href="{{ route('master.company-settings.edit') }}" class="btn btn-primary">
            <i class="ri-pencil-line me-1"></i>Edit
          </a>
        </div>
        <div class="card-body">
          @if($setting)
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted"><PERSON><PERSON></label>
                  <p class="fw-bold">{{ $setting->company_name }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Kode Perusahaan</label>
                  <p><span class="badge bg-primary fs-6">{{ $setting->company_code }}</span></p>
                </div>
              </div>
            </div>

            @if($setting->address)
            <div class="mb-3">
              <label class="form-label text-muted">Alamat</label>
              <p>{{ $setting->address }}</p>
            </div>
            @endif

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Nomor Telepon</label>
                  <p>{{ $setting->phone ?: '-' }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Email</label>
                  <p>{{ $setting->email ?: '-' }}</p>
                </div>
              </div>
            </div>

            @if($setting->website)
            <div class="mb-3">
              <label class="form-label text-muted">Website</label>
              <p><a href="{{ $setting->website }}" target="_blank">{{ $setting->website }}</a></p>
            </div>
            @endif

            @if($setting->asset_usage_agreement)
            <div class="mb-3">
              <label class="form-label text-muted">Perjanjian Penggunaan Asset</label>
              <div class="p-3 bg-light border rounded">
                <p class="mb-0" style="white-space: pre-line;">{{ $setting->asset_usage_agreement }}</p>
              </div>
            </div>
            @endif

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Dibuat</label>
                  <p>{{ $setting->created_at->format('d/m/Y H:i') }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Terakhir Diupdate</label>
                  <p>{{ $setting->updated_at->format('d/m/Y H:i') }}</p>
                </div>
              </div>
            </div>
          @else
            <div class="text-center py-5">
              <i class="ri-building-line display-4 text-muted mb-3"></i>
              <h6 class="text-muted">Belum ada pengaturan perusahaan</h6>
              <p class="text-muted mb-3">Silakan tambahkan informasi perusahaan untuk melengkapi sistem.</p>
              <a href="{{ route('master.company-settings.edit') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>Tambah Pengaturan
              </a>
            </div>
          @endif
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Sistem</h6>
        </div>
        <div class="card-body">
          @if($setting)
            <div class="d-flex align-items-center mb-3">
              <div class="avatar avatar-lg me-3">
                <span class="avatar-initial rounded-circle bg-label-primary">
                  {{ strtoupper(substr($setting->company_code, 0, 2)) }}
                </span>
              </div>
              <div>
                <h6 class="mb-0">{{ $setting->company_name }}</h6>
                <small class="text-muted">{{ $setting->company_code }}</small>
              </div>
            </div>
          @endif
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Penggunaan Kode:</h6>
            <ul class="mb-0">
              <li>Kode perusahaan digunakan dalam format nomor dokumen</li>
              <li>Format: <code>{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}</code></li>
              <li>Contoh: <code>BITJKT25-511-01-0001</code></li>
            </ul>
          </div>

          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Kode perusahaan sebaiknya tidak diubah setelah ada data</li>
              <li>Perubahan akan mempengaruhi format nomor dokumen baru</li>
              <li>Gunakan kode yang singkat dan mudah diingat</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
