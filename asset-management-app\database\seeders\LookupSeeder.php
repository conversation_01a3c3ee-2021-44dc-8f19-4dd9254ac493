<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LookupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $lookups = [
            // Status Lookups
            ['lookup_code' => 'STATUS', 'lookup_name' => 'Aktif', 'description' => 'Status aktif untuk data', 'category' => 'System', 'sort_order' => 1, 'metadata' => ['color' => 'success', 'icon' => 'ri-check-line']],
            ['lookup_code' => 'STATUS', 'lookup_name' => 'Non-Aktif', 'description' => 'Status non-aktif untuk data', 'category' => 'System', 'sort_order' => 2, 'metadata' => ['color' => 'secondary', 'icon' => 'ri-close-line']],
            ['lookup_code' => 'STATUS', 'lookup_name' => 'Pending', 'description' => 'Status menunggu untuk data', 'category' => 'System', 'sort_order' => 3, 'metadata' => ['color' => 'warning', 'icon' => 'ri-time-line']],
            ['lookup_code' => 'STATUS', 'lookup_name' => 'Suspended', 'description' => 'Status ditangguhkan', 'category' => 'System', 'sort_order' => 4, 'metadata' => ['color' => 'danger', 'icon' => 'ri-pause-line']],

            // Priority Lookups
            ['lookup_code' => 'PRIORITY', 'lookup_name' => 'Low', 'description' => 'Prioritas rendah', 'category' => 'System', 'sort_order' => 1, 'metadata' => ['color' => 'success', 'level' => 1]],
            ['lookup_code' => 'PRIORITY', 'lookup_name' => 'Medium', 'description' => 'Prioritas sedang', 'category' => 'System', 'sort_order' => 2, 'metadata' => ['color' => 'info', 'level' => 2]],
            ['lookup_code' => 'PRIORITY', 'lookup_name' => 'High', 'description' => 'Prioritas tinggi', 'category' => 'System', 'sort_order' => 3, 'metadata' => ['color' => 'warning', 'level' => 3]],
            ['lookup_code' => 'PRIORITY', 'lookup_name' => 'Urgent', 'description' => 'Prioritas mendesak', 'category' => 'System', 'sort_order' => 4, 'metadata' => ['color' => 'danger', 'level' => 4]],

            // Asset Condition Lookups
            ['lookup_code' => 'ASSET_CONDITION', 'lookup_name' => 'Baru', 'description' => 'Kondisi asset baru', 'category' => 'Asset', 'sort_order' => 1, 'metadata' => ['color' => 'success', 'percentage' => 100]],
            ['lookup_code' => 'ASSET_CONDITION', 'lookup_name' => 'Baik', 'description' => 'Kondisi asset baik', 'category' => 'Asset', 'sort_order' => 2, 'metadata' => ['color' => 'info', 'percentage' => 80]],
            ['lookup_code' => 'ASSET_CONDITION', 'lookup_name' => 'Cukup', 'description' => 'Kondisi asset cukup', 'category' => 'Asset', 'sort_order' => 3, 'metadata' => ['color' => 'warning', 'percentage' => 60]],
            ['lookup_code' => 'ASSET_CONDITION', 'lookup_name' => 'Rusak Ringan', 'description' => 'Kondisi asset rusak ringan', 'category' => 'Asset', 'sort_order' => 4, 'metadata' => ['color' => 'warning', 'percentage' => 40]],
            ['lookup_code' => 'ASSET_CONDITION', 'lookup_name' => 'Rusak Berat', 'description' => 'Kondisi asset rusak berat', 'category' => 'Asset', 'sort_order' => 5, 'metadata' => ['color' => 'danger', 'percentage' => 20]],

            // User Type Lookups
            ['lookup_code' => 'USER_TYPE', 'lookup_name' => 'Internal', 'description' => 'User internal perusahaan', 'category' => 'User', 'sort_order' => 1, 'metadata' => ['access_level' => 'full']],
            ['lookup_code' => 'USER_TYPE', 'lookup_name' => 'External', 'description' => 'User eksternal/vendor', 'category' => 'User', 'sort_order' => 2, 'metadata' => ['access_level' => 'limited']],
            ['lookup_code' => 'USER_TYPE', 'lookup_name' => 'Contractor', 'description' => 'User kontraktor', 'category' => 'User', 'sort_order' => 3, 'metadata' => ['access_level' => 'temporary']],

            // Document Type Lookups
            ['lookup_code' => 'DOCUMENT_TYPE', 'lookup_name' => 'Invoice', 'description' => 'Dokumen invoice', 'category' => 'Document', 'sort_order' => 1, 'metadata' => ['format' => 'pdf', 'required_approval' => true]],
            ['lookup_code' => 'DOCUMENT_TYPE', 'lookup_name' => 'Receipt', 'description' => 'Dokumen kwitansi', 'category' => 'Document', 'sort_order' => 2, 'metadata' => ['format' => 'pdf', 'required_approval' => false]],
            ['lookup_code' => 'DOCUMENT_TYPE', 'lookup_name' => 'Contract', 'description' => 'Dokumen kontrak', 'category' => 'Document', 'sort_order' => 3, 'metadata' => ['format' => 'pdf', 'required_approval' => true]],
            ['lookup_code' => 'DOCUMENT_TYPE', 'lookup_name' => 'Warranty', 'description' => 'Dokumen garansi', 'category' => 'Document', 'sort_order' => 4, 'metadata' => ['format' => 'pdf', 'required_approval' => false]],

            // Location Type Lookups
            ['lookup_code' => 'LOCATION_TYPE', 'lookup_name' => 'Office', 'description' => 'Lokasi kantor', 'category' => 'Location', 'sort_order' => 1, 'metadata' => ['security_level' => 'high']],
            ['lookup_code' => 'LOCATION_TYPE', 'lookup_name' => 'Warehouse', 'description' => 'Lokasi gudang', 'category' => 'Location', 'sort_order' => 2, 'metadata' => ['security_level' => 'medium']],
            ['lookup_code' => 'LOCATION_TYPE', 'lookup_name' => 'Field', 'description' => 'Lokasi lapangan', 'category' => 'Location', 'sort_order' => 3, 'metadata' => ['security_level' => 'low']],
            ['lookup_code' => 'LOCATION_TYPE', 'lookup_name' => 'Remote', 'description' => 'Lokasi remote/WFH', 'category' => 'Location', 'sort_order' => 4, 'metadata' => ['security_level' => 'variable']],

            // Asset Field Group Lookups
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Basic Information', 'description' => 'Informasi dasar asset', 'category' => 'Asset Field', 'sort_order' => 1, 'metadata' => ['value' => 'basic_info', 'icon' => 'ri-information-line', 'color' => 'primary']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Technical Specifications', 'description' => 'Spesifikasi teknis asset', 'category' => 'Asset Field', 'sort_order' => 2, 'metadata' => ['value' => 'technical_specs', 'icon' => 'ri-settings-4-line', 'color' => 'info']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Purchase Information', 'description' => 'Informasi pembelian asset', 'category' => 'Asset Field', 'sort_order' => 3, 'metadata' => ['value' => 'purchase_info', 'icon' => 'ri-shopping-cart-line', 'color' => 'success']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Maintenance', 'description' => 'Informasi maintenance asset', 'category' => 'Asset Field', 'sort_order' => 4, 'metadata' => ['value' => 'maintenance', 'icon' => 'ri-tools-line', 'color' => 'warning']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Location & Assignment', 'description' => 'Lokasi dan penugasan asset', 'category' => 'Asset Field', 'sort_order' => 5, 'metadata' => ['value' => 'location', 'icon' => 'ri-map-pin-line', 'color' => 'secondary']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Custom Fields', 'description' => 'Field kustom tambahan', 'category' => 'Asset Field', 'sort_order' => 6, 'metadata' => ['value' => 'custom', 'icon' => 'ri-add-box-line', 'color' => 'dark']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Financial Information', 'description' => 'Informasi keuangan asset', 'category' => 'Asset Field', 'sort_order' => 7, 'metadata' => ['value' => 'financial', 'icon' => 'ri-money-dollar-circle-line', 'color' => 'success']],
            ['lookup_code' => 'FIELD_GROUP', 'lookup_name' => 'Compliance & Legal', 'description' => 'Informasi compliance dan legal', 'category' => 'Asset Field', 'sort_order' => 8, 'metadata' => ['value' => 'compliance', 'icon' => 'ri-shield-check-line', 'color' => 'danger']],

            // Approval Status Lookups
            ['lookup_code' => 'APPROVAL_STATUS', 'lookup_name' => 'Draft', 'description' => 'Status draft', 'category' => 'Workflow', 'sort_order' => 1, 'metadata' => ['color' => 'secondary', 'editable' => true]],
            ['lookup_code' => 'APPROVAL_STATUS', 'lookup_name' => 'Submitted', 'description' => 'Status diajukan', 'category' => 'Workflow', 'sort_order' => 2, 'metadata' => ['color' => 'info', 'editable' => false]],
            ['lookup_code' => 'APPROVAL_STATUS', 'lookup_name' => 'In Review', 'description' => 'Status dalam review', 'category' => 'Workflow', 'sort_order' => 3, 'metadata' => ['color' => 'warning', 'editable' => false]],
            ['lookup_code' => 'APPROVAL_STATUS', 'lookup_name' => 'Approved', 'description' => 'Status disetujui', 'category' => 'Workflow', 'sort_order' => 4, 'metadata' => ['color' => 'success', 'editable' => false]],
            ['lookup_code' => 'APPROVAL_STATUS', 'lookup_name' => 'Rejected', 'description' => 'Status ditolak', 'category' => 'Workflow', 'sort_order' => 5, 'metadata' => ['color' => 'danger', 'editable' => true]],

            // Currency Lookups
            ['lookup_code' => 'CURRENCY', 'lookup_name' => 'IDR', 'description' => 'Rupiah Indonesia', 'category' => 'Finance', 'sort_order' => 1, 'metadata' => ['symbol' => 'Rp', 'code' => 'IDR', 'decimal_places' => 0]],
            ['lookup_code' => 'CURRENCY', 'lookup_name' => 'USD', 'description' => 'US Dollar', 'category' => 'Finance', 'sort_order' => 2, 'metadata' => ['symbol' => '$', 'code' => 'USD', 'decimal_places' => 2]],
            ['lookup_code' => 'CURRENCY', 'lookup_name' => 'EUR', 'description' => 'Euro', 'category' => 'Finance', 'sort_order' => 3, 'metadata' => ['symbol' => '€', 'code' => 'EUR', 'decimal_places' => 2]],
        ];

        foreach ($lookups as $lookup) {
            \App\Models\Lookup::create($lookup);
        }
    }
}
