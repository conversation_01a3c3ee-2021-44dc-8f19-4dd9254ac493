<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class AssetDigital extends Model
{
    use HasFactory;

    protected $fillable = [
        'asset_code',
        'name',
        'license_type',
        'license_key',
        'username',
        'password',
        'login_url',
        'description',
        'supplier_id',
        'purchase_order_id',
        'vendor', // Keep for backward compatibility
        'version',
        'purchase_date',
        'expiry_date',
        'purchase_price',
        'max_users',
        'current_users',
        'status',
        'branch_id',
        'assigned_to',
        'created_by',
        'notes',
    ];

    protected $casts = [
        'purchase_date' => 'datetime',
        'expiry_date' => 'datetime',
        'purchase_price' => 'decimal:2',
        'max_users' => 'integer',
        'current_users' => 'integer',
    ];

    protected $hidden = [
        'password',
    ];

    // Boot method for auto-generating asset code
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($assetDigital) {
            if (empty($assetDigital->asset_code)) {
                $assetDigital->asset_code = self::generateAssetCode($assetDigital->branch_id);
            }
        });
    }

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function assignedTo()
    {
        return $this->belongsTo(Employee::class, 'assigned_to');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    // Accessors & Mutators
    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = Crypt::encryptString($value);
        }
    }

    public function getPasswordAttribute($value)
    {
        if ($value) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                return null;
            }
        }
        return null;
    }

    public function getDecryptedPasswordAttribute()
    {
        return $this->password;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>=', now());
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    // Helper methods
    public function isExpired()
    {
        return $this->expiry_date && $this->expiry_date < now();
    }

    public function isExpiringSoon($days = 30)
    {
        return $this->expiry_date &&
               $this->expiry_date > now() &&
               $this->expiry_date <= now()->addDays($days);
    }

    public function getDaysUntilExpiry()
    {
        if (!$this->expiry_date) {
            return null;
        }

        return now()->diffInDays($this->expiry_date, false);
    }

    public function getUsagePercentage()
    {
        if (!$this->max_users || $this->max_users == 0) {
            return 0;
        }

        return round(($this->current_users / $this->max_users) * 100, 2);
    }

    // Static methods
    public static function generateAssetCode($branchId)
    {
        $branch = Branch::find($branchId);
        $branchCode = $branch ? $branch->code : 'UNK';

        $year = date('Y');
        $month = date('m');

        // Get last number for this branch, year, month
        $lastAsset = self::where('asset_code', 'like', "DIG{$branchCode}{$year}{$month}%")
                        ->orderBy('asset_code', 'desc')
                        ->first();

        if ($lastAsset) {
            $lastNumber = (int) substr($lastAsset->asset_code, -5);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf("DIG%s%s%s%05d", $branchCode, $year, $month, $newNumber);
    }
}
