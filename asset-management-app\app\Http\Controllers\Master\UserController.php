<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Models\Division;
use App\Helpers\BranchHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::with(['role', 'branch', 'division']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role_id')) {
            $query->where('role_id', $request->role_id);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('division_id')) {
            $query->where('division_id', $request->division_id);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $users = $query->latest()->paginate(15);
        $roles = Role::active()->get();
        $branches = BranchHelper::getAccessibleBranches();
        $divisions = Division::active()->get();

        return view('master.users.index', compact('users', 'roles', 'branches', 'divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::active()->get();
        $branches = BranchHelper::getAccessibleBranches();
        $divisions = Division::active()->get();
        return view('master.users.create', compact('roles', 'branches', 'divisions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username',
            'email' => 'required|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'branch_id' => 'required|exists:branches,id',
            'division_id' => 'nullable|exists:divisions,id',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        // Check branch access
        if (!BranchHelper::canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        $validated['password'] = Hash::make($validated['password']);

        User::create($validated);

        return redirect()->route('master.users.index')->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($user->branch_id)) {
            abort(403, 'You do not have access to this user.');
        }

        $user->load(['role', 'branch', 'division']);
        return view('master.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($user->branch_id)) {
            abort(403, 'You do not have access to this user.');
        }

        $roles = Role::active()->get();
        $branches = BranchHelper::getAccessibleBranches();
        $divisions = Division::active()->get();
        return view('master.users.edit', compact('user', 'roles', 'branches', 'divisions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($user->branch_id)) {
            abort(403, 'You do not have access to this user.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $user->id,
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'branch_id' => 'required|exists:branches,id',
            'division_id' => 'nullable|exists:divisions,id',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        // Check new branch access
        if (!BranchHelper::canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        return redirect()->route('master.users.index')->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($user->branch_id)) {
            abort(403, 'You do not have access to this user.');
        }

        // Prevent deleting current user
        if ($user->id === auth()->id()) {
            return redirect()->route('master.users.index')->with('error', 'Cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('master.users.index')->with('success', 'User deleted successfully.');
    }
}
