<?php $__env->startSection('title', 'Lihat Data Asset - Asset Management System'); ?>

<?php $__env->startSection('content'); ?>
<style>
.filter-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: linear-gradient(135deg,rgb(60, 216, 154) 0%, rgb(35, 178, 121) 100%);
  color: black;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.table-responsive {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dynamic-fields-preview {
  max-width: 200px;
  font-size: 0.875rem;
}

.dynamic-field-badge {
  background: #f8f9fa;
  color: #566a7f;
  border: 1px solid #e7e7ff;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin: 0.125rem;
  display: inline-block;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-database-2-line me-2"></i>
              Data Asset Seluruh Cabang
            </h5>
            <small class="text-muted">Tampilan komprehensif semua data asset dengan detail lengkap</small>
          </div>
          <div class="d-flex gap-2">
            <a href="<?php echo e(route('assets.create')); ?>" class="btn btn-primary">
              <i class="ri-add-line me-1"></i>
              Tambah Asset
            </a>
           
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics -->
  <div class="row">
    <div class="col-12">
      <div class="stats-card">
        <div class="row">
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><?php echo e($assets->total()); ?></h3>
            <small>Total Asset</small>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><?php echo e($assets->where('status', 'active')->count()); ?></h3>
            <small>Asset Aktif</small>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><?php echo e($assets->where('status', 'maintenance')->count()); ?></h3>
            <small>Maintenance</small>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><?php echo e($categories->count()); ?></h3>
            <small>Kategori</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Advanced Filters -->
  <div class="row">
    <div class="col-12">
      <div class="filter-section">
        <h6 class="mb-3">
          <i class="ri-filter-3-line me-2"></i>
          Filter & Pencarian Advanced
        </h6>
        
        <form method="GET" action="<?php echo e(route('assets.view-all')); ?>">
          <div class="row">
            <div class="col-md-3">
              <div class="mb-3">
                <label for="search" class="form-label">Pencarian</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?php echo e(request('search')); ?>" placeholder="Nama, kode, atau deskripsi...">
              </div>
            </div>
            <div class="col-md-2">
              <div class="mb-3">
                <label for="category" class="form-label">Kategori</label>
                <select class="form-select" id="category" name="category">
                  <option value="">Semua Kategori</option>
                  <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                      <?php echo e($category->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="mb-3">
                <label for="type" class="form-label">Tipe</label>
                <select class="form-select" id="type" name="type">
                  <option value="">Semua Tipe</option>
                  <?php $__currentLoopData = $assetTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($type->id); ?>" <?php echo e(request('type') == $type->id ? 'selected' : ''); ?>>
                      <?php echo e($type->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                  <option value="">Semua Status</option>
                  <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                  <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                  <option value="maintenance" <?php echo e(request('status') == 'maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                  <option value="disposed" <?php echo e(request('status') == 'disposed' ? 'selected' : ''); ?>>Disposed</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="mb-3">
                <label for="branch" class="form-label">Cabang</label>
                <select class="form-select" id="branch" name="branch">
                  <option value="">Semua Cabang</option>
                  <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch') == $branch->id ? 'selected' : ''); ?>>
                      <?php echo e($branch->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>
            <div class="col-md-1">
              <div class="mb-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-1">
                  <button type="submit" class="btn btn-primary btn-sm">
                    <i class="ri-search-line"></i>
                  </button>
                  <a href="<?php echo e(route('assets.view-all')); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="ri-refresh-line"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Assets Data Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">
            <i class="ri-table-line me-2"></i>
            Data Asset Lengkap
          </h6>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary btn-sm" id="qrMultipleBtn" onclick="handleQrMultipleClick()">
              <i class="ri-qr-code-line me-1"></i>
              Cetak QR Multiple
            </button>
            <button type="button" class="btn btn-success btn-sm" id="printSelectedBtn" onclick="showQrModal()" style="display: none;">
              <i class="ri-printer-line me-1"></i>
              Cetak QR (<span id="selectedCountBtn">0</span>)
            </button>
            <button type="button" class="btn btn-outline-success btn-sm">
              <i class="ri-file-excel-line me-1"></i>
              Export Excel
            </button>
            <button type="button" class="btn btn-outline-danger btn-sm">
              <i class="ri-file-pdf-line me-1"></i>
              Export PDF
            </button>
          </div>
        </div>
        <div class="card-body">
          <?php if($assets->count() > 0): ?>
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th width="40">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                      </div>
                    </th>
                    <th>Kode Asset</th>
                    <th>Nama Asset</th>
                    <th>Kategori</th>
                    <th>Tipe</th>
                    <th>Status</th>
                    <th>Cabang</th>
                    <th>Detail Tambahan</th>
                    <th>Tanggal Dibuat</th>
                    <th width="120">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  <?php $__currentLoopData = $assets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $asset): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                      <td>
                        <div class="form-check">
                          <input class="form-check-input asset-checkbox" type="checkbox" value="<?php echo e($asset->id); ?>">
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-primary"><?php echo e($asset->asset_code); ?></span>
                      </td>
                      <td>
                        <div>
                          <strong><?php echo e($asset->name); ?></strong>
                          <?php if($asset->description): ?>
                            <br><small class="text-muted"><?php echo e(Str::limit($asset->description, 60)); ?></small>
                          <?php endif; ?>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-info"><?php echo e($asset->assetCategory->name ?? '-'); ?></span>
                      </td>
                      <td>
                        <?php echo e($asset->assetType->name ?? '-'); ?>

                      </td>
                      <td>
                        <?php switch($asset->status):
                          case ('active'): ?>
                            <span class="badge bg-success">Active</span>
                            <?php break; ?>
                          <?php case ('inactive'): ?>
                            <span class="badge bg-secondary">Inactive</span>
                            <?php break; ?>
                          <?php case ('maintenance'): ?>
                            <span class="badge bg-warning">Maintenance</span>
                            <?php break; ?>
                          <?php case ('disposed'): ?>
                            <span class="badge bg-danger">Disposed</span>
                            <?php break; ?>
                          <?php default: ?>
                            <span class="badge bg-secondary"><?php echo e(ucfirst($asset->status)); ?></span>
                        <?php endswitch; ?>
                      </td>
                      <td>
                        <?php echo e($asset->branch->name ?? '-'); ?>

                      </td>
                      <td>
                        <div class="dynamic-fields-preview">
                          <?php if($asset->dynamic_fields && count($asset->dynamic_fields) > 0): ?>
                            <?php
                              $displayCount = 0;
                              $maxDisplay = 3;
                            ?>
                            <?php $__currentLoopData = $asset->dynamic_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldName => $fieldValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <?php if($displayCount < $maxDisplay && !empty($fieldValue)): ?>
                                <span class="dynamic-field-badge">
                                  <strong><?php echo e(ucwords(str_replace('_', ' ', $fieldName))); ?>:</strong>
                                  <?php if(is_array($fieldValue)): ?>
                                    <?php echo e(Str::limit(implode(', ', $fieldValue), 30)); ?>

                                  <?php else: ?>
                                    <?php echo e(Str::limit($fieldValue, 30)); ?>

                                  <?php endif; ?>
                                </span>
                                <?php $displayCount++; ?>
                              <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php if(count(array_filter($asset->dynamic_fields)) > $maxDisplay): ?>
                              <span class="dynamic-field-badge">
                                <i class="ri-more-line"></i>
                                +<?php echo e(count(array_filter($asset->dynamic_fields)) - $maxDisplay); ?> lainnya
                              </span>
                            <?php endif; ?>
                          <?php else: ?>
                            <span class="text-muted">-</span>
                          <?php endif; ?>
                        </div>
                      </td>
                      <td>
                        <small><?php echo e($asset->created_at->format('d/m/Y')); ?></small>
                        <br><small class="text-muted"><?php echo e($asset->created_at->format('H:i')); ?></small>
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle"
                                  data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-more-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="<?php echo e(route('assets.show', $asset)); ?>">
                                <i class="ri-eye-line me-2"></i>
                                Lihat Detail
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="<?php echo e(route('assets.edit', $asset)); ?>">
                                <i class="ri-pencil-line me-2"></i>
                                Edit
                              </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                              <form method="POST" action="<?php echo e(route('assets.destroy', $asset)); ?>"
                                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus asset ini?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="dropdown-item text-danger">
                                  <i class="ri-delete-bin-line me-2"></i>
                                  Hapus
                                </button>
                              </form>
                            </li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <!-- Empty State -->
            <div class="text-center py-5">
              <i class="ri-database-2-line ri-48px text-muted mb-3"></i>
              <h5 class="text-muted">Tidak Ada Asset Ditemukan</h5>
              <p class="text-muted mb-4">
                <?php if(request()->hasAny(['search', 'category', 'type', 'status', 'branch'])): ?>
                  Tidak ada asset yang sesuai dengan filter yang dipilih.
                <?php else: ?>
                  Belum ada asset yang terdaftar dalam sistem.
                <?php endif; ?>
              </p>
              <?php if(!request()->hasAny(['search', 'category', 'type', 'status', 'branch'])): ?>
                <a href="<?php echo e(route('assets.create')); ?>" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>
                  Tambah Asset Pertama
                </a>
              <?php else: ?>
                <a href="<?php echo e(route('assets.view-all')); ?>" class="btn btn-outline-secondary">
                  <i class="ri-refresh-line me-1"></i>
                  Reset Filter
                </a>
              <?php endif; ?>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <?php if($assets->hasPages()): ?>
    <div class="row">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <small class="text-muted">
              Menampilkan <?php echo e($assets->firstItem()); ?> - <?php echo e($assets->lastItem()); ?> 
              dari <?php echo e($assets->total()); ?> asset
            </small>
          </div>
          <div>
            <?php echo e($assets->links()); ?>

          </div>
        </div>
      </div>
    </div>
  <?php endif; ?>
</div>

<!-- QR Multiple Selection Modal -->
<div class="modal fade" id="qrMultipleModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="ri-qr-code-line me-2"></i>
          Cetak QR Label Multiple
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="POST" action="<?php echo e(route('assets.qr-labels.multiple')); ?>" target="_blank">
        <?php echo csrf_field(); ?>
        <div class="modal-body">
          <div class="mb-3">
            <label for="config_id" class="form-label">Pilih Konfigurasi Label</label>
            <select class="form-select" id="config_id" name="config_id" required>
              <?php
                $qrConfigs = \App\Models\QrLabelConfiguration::active()->get();
              ?>
              <?php $__currentLoopData = $qrConfigs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($config->id); ?>" <?php echo e($config->is_default ? 'selected' : ''); ?>>
                  <?php echo e($config->name); ?> (<?php echo e($config->width); ?>x<?php echo e($config->height); ?>mm)
                  <?php if($config->is_default): ?> - Default <?php endif; ?>
                </option>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Asset yang Dipilih</label>
            <div id="selectedAssetsInfo" class="alert alert-info">
              <span id="selectedCount">0</span> asset dipilih
            </div>
          </div>
          <input type="hidden" id="selectedAssetIds" name="asset_ids">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary" id="printQrBtn" disabled>
            <i class="ri-printer-line me-1"></i>
            Cetak QR Labels
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
let selectedAssets = [];
let qrSelectionMode = false;

function handleQrMultipleClick() {
  if (!qrSelectionMode) {
    // Start selection mode
    toggleQrSelection();
  } else {
    // Cancel selection mode
    toggleQrSelection();
  }
}

function toggleQrSelection() {
  qrSelectionMode = !qrSelectionMode;
  const qrBtn = document.getElementById('qrMultipleBtn');
  const printBtn = document.getElementById('printSelectedBtn');

  if (qrSelectionMode) {
    // Show checkboxes and enable selection mode
    document.querySelectorAll('.asset-checkbox, #selectAll').forEach(cb => {
      cb.style.display = 'block';
    });

    // Update button text
    qrBtn.innerHTML = '<i class="ri-close-line me-1"></i>Batal Pilih';
    qrBtn.classList.remove('btn-outline-primary');
    qrBtn.classList.add('btn-outline-danger');
  } else {
    // Hide checkboxes and disable selection mode
    document.querySelectorAll('.asset-checkbox, #selectAll').forEach(cb => {
      cb.style.display = 'none';
      cb.checked = false;
    });

    // Reset selection
    selectedAssets = [];
    updateSelectedCount();

    // Update button text
    qrBtn.innerHTML = '<i class="ri-qr-code-line me-1"></i>Cetak QR Multiple';
    qrBtn.classList.remove('btn-outline-danger');
    qrBtn.classList.add('btn-outline-primary');

    // Hide print button
    printBtn.style.display = 'none';
  }
}

function updateSelectedCount() {
  const count = selectedAssets.length;
  const printBtn = document.getElementById('printSelectedBtn');
  const selectedCountBtn = document.getElementById('selectedCountBtn');

  // Update modal count
  document.getElementById('selectedCount').textContent = count;
  document.getElementById('printQrBtn').disabled = count === 0;

  // Update button count
  selectedCountBtn.textContent = count;

  // Show/hide print button based on selection
  if (count > 0 && qrSelectionMode) {
    printBtn.style.display = 'inline-block';
  } else {
    printBtn.style.display = 'none';
  }

  if (count > 0) {
    document.getElementById('selectedAssetIds').value = JSON.stringify(selectedAssets);
  }
}

// Initialize checkboxes as hidden
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.asset-checkbox, #selectAll').forEach(cb => {
    cb.style.display = 'none';
  });

  // Select all functionality
  document.getElementById('selectAll').addEventListener('change', function() {
    const isChecked = this.checked;
    selectedAssets = [];

    document.querySelectorAll('.asset-checkbox').forEach(cb => {
      cb.checked = isChecked;
      if (isChecked) {
        selectedAssets.push(parseInt(cb.value));
      }
    });

    updateSelectedCount();
  });

  // Individual checkbox functionality
  document.querySelectorAll('.asset-checkbox').forEach(cb => {
    cb.addEventListener('change', function() {
      const assetId = parseInt(this.value);

      if (this.checked) {
        if (!selectedAssets.includes(assetId)) {
          selectedAssets.push(assetId);
        }
      } else {
        selectedAssets = selectedAssets.filter(id => id !== assetId);
        document.getElementById('selectAll').checked = false;
      }

      updateSelectedCount();
    });
  });

  // Show modal when print button clicked
  document.getElementById('printQrBtn').addEventListener('click', function(e) {
    if (selectedAssets.length === 0) {
      e.preventDefault();
      alert('Pilih minimal satu asset untuk dicetak QR labelnya.');
      return;
    }
  });
});

// Show QR modal when assets are selected
function showQrModal() {
  if (selectedAssets.length === 0) {
    alert('Pilih minimal satu asset untuk dicetak QR labelnya.');
    return;
  }

  // Update the hidden input with selected asset IDs
  document.getElementById('selectedAssetIds').value = JSON.stringify(selectedAssets);

  const modal = new bootstrap.Modal(document.getElementById('qrMultipleModal'));
  modal.show();
}
</script>

<?php if(session('success')): ?>
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-check-line me-2"></i>
      <div class="me-auto fw-semibold">Success</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      <?php echo e(session('success')); ?>

    </div>
  </div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/assets/view-all.blade.php ENDPATH**/ ?>