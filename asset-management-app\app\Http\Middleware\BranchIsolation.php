<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BranchIsolation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $userBranchId = session('user_branch');

        // Super Admin can access all branches
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Set global scope for branch isolation
        if ($userBranchId) {
            // Apply branch filter to models that have branch_id
            \App\Models\Asset::addGlobalScope('branch', function ($builder) use ($userBranchId) {
                $builder->where('branch_id', $userBranchId);
            });

            // For users, non-super-admin can only see users from their branch
            if (!$user->isSuperAdmin()) {
                \App\Models\User::addGlobalScope('branch', function ($builder) use ($userBranchId) {
                    $builder->where('branch_id', $userBranchId);
                });
            }
        }

        return $next($request);
    }
}
