<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_order_id',
        'item_name',
        'item_description',
        'item_code',
        'brand',
        'model',
        'specification',
        'unit',
        'quantity',
        'unit_price',
        'total_price',
        'notes',
        'sort_order',
        'received_quantity',
        'receive_status',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'received_quantity' => 'integer',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    // Accessors & Mutators
    public function getReceiveStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'secondary',
            'partial' => 'warning',
            'completed' => 'success',
        ];

        return $badges[$this->receive_status] ?? 'secondary';
    }

    public function getReceiveStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Belum Diterima',
            'partial' => 'Diterima Sebagian',
            'completed' => 'Diterima Lengkap',
        ];

        return $labels[$this->receive_status] ?? 'Unknown';
    }

    public function getRemainingQuantityAttribute()
    {
        return $this->quantity - $this->received_quantity;
    }

    public function getReceivePercentageAttribute()
    {
        if ($this->quantity == 0) return 0;
        return round(($this->received_quantity / $this->quantity) * 100, 2);
    }

    // Helper methods
    public function isFullyReceived()
    {
        return $this->received_quantity >= $this->quantity;
    }

    public function isPartiallyReceived()
    {
        return $this->received_quantity > 0 && $this->received_quantity < $this->quantity;
    }

    public function isPending()
    {
        return $this->received_quantity == 0;
    }

    // Calculate total price
    public function calculateTotalPrice()
    {
        $this->total_price = $this->quantity * $this->unit_price;
        return $this;
    }

    // Update receive status based on quantities
    public function updateReceiveStatus()
    {
        if ($this->received_quantity == 0) {
            $this->receive_status = 'pending';
        } elseif ($this->received_quantity >= $this->quantity) {
            $this->receive_status = 'completed';
        } else {
            $this->receive_status = 'partial';
        }

        return $this;
    }

    // Boot method for auto-calculation
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            // Auto-calculate total price
            $item->calculateTotalPrice();
            
            // Auto-update receive status
            $item->updateReceiveStatus();
        });

        static::saved(function ($item) {
            // Recalculate PO totals when item is saved
            $item->purchaseOrder->calculateTotals();
        });

        static::deleted(function ($item) {
            // Recalculate PO totals when item is deleted
            if ($item->purchaseOrder) {
                $item->purchaseOrder->calculateTotals();
            }
        });
    }
}
