<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('asset_field_configurations', function (Blueprint $table) {
            $table->string('data_source_type')->default('manual')->after('field_options'); // manual, database, lookup
            $table->string('data_source_table')->nullable()->after('data_source_type'); // table name for database source
            $table->string('data_source_value_column')->nullable()->after('data_source_table'); // column for value
            $table->string('data_source_label_column')->nullable()->after('data_source_value_column'); // column for label
            $table->string('data_source_filter')->nullable()->after('data_source_label_column'); // additional filter conditions
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asset_field_configurations', function (Blueprint $table) {
            $table->dropColumn([
                'data_source_type',
                'data_source_table',
                'data_source_value_column',
                'data_source_label_column',
                'data_source_filter'
            ]);
        });
    }
};
