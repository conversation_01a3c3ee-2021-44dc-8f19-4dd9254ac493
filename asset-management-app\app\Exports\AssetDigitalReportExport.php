<?php

namespace App\Exports;

use App\Models\AssetDigital;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class AssetDigitalReportExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, WithTitle
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = AssetDigital::with(['branch', 'assignedTo', 'createdBy', 'supplier']);
        
        // Apply same filters as in controller
        if (!empty($this->filters['branch_id'])) {
            $query->where('branch_id', $this->filters['branch_id']);
        }
        
        if (!empty($this->filters['license_type'])) {
            $query->where('license_type', $this->filters['license_type']);
        }
        
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }
        
        if (!empty($this->filters['date_from'])) {
            $query->whereDate('purchase_date', '>=', $this->filters['date_from']);
        }
        
        if (!empty($this->filters['date_to'])) {
            $query->whereDate('purchase_date', '<=', $this->filters['date_to']);
        }
        
        if (!empty($this->filters['expiry_from'])) {
            $query->whereDate('expiry_date', '>=', $this->filters['expiry_from']);
        }
        
        if (!empty($this->filters['expiry_to'])) {
            $query->whereDate('expiry_date', '<=', $this->filters['expiry_to']);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'No',
            'Kode Asset',
            'Nama Asset',
            'Tipe Lisensi',
            'Cabang',
            'Supplier',
            'License Key',
            'Username',
            'Login URL',
            'Versi',
            'Tanggal Pembelian',
            'Tanggal Expired',
            'Harga Pembelian',
            'Max Users',
            'Current Users',
            'Status',
            'Assigned To',
            'Created By',
            'Deskripsi',
            'Notes'
        ];
    }

    /**
     * @param mixed $assetDigital
     * @return array
     */
    public function map($assetDigital): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $assetDigital->asset_code,
            $assetDigital->name,
            $assetDigital->license_type ?? '-',
            $assetDigital->branch->name ?? '-',
            $assetDigital->supplier->name ?? '-',
            $assetDigital->license_key ?? '-',
            $assetDigital->username ?? '-',
            $assetDigital->login_url ?? '-',
            $assetDigital->version ?? '-',
            $assetDigital->purchase_date ? $assetDigital->purchase_date->format('d/m/Y') : '-',
            $assetDigital->expiry_date ? $assetDigital->expiry_date->format('d/m/Y') : '-',
            $assetDigital->purchase_price ? 'Rp ' . number_format($assetDigital->purchase_price, 0, ',', '.') : '-',
            $assetDigital->max_users ?? '-',
            $assetDigital->current_users ?? '0',
            $this->getStatusText($assetDigital->status),
            $assetDigital->assignedTo->full_name ?? '-',
            $assetDigital->createdBy->name ?? '-',
            $assetDigital->description ?? '-',
            $assetDigital->notes ?? '-'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $lastRow = $sheet->getHighestRow();
        $lastColumn = $sheet->getHighestColumn();
        
        // Header styling
        $headerStyle = [
            'font' => ['bold' => true, 'size' => 11, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '4472C4']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]]
        ];
        
        // Data styling
        $dataStyle = [
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => 'CCCCCC']]],
            'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'wrapText' => true]
        ];
        
        $styles = [
            1 => $headerStyle,
        ];
        
        // Apply data styling to all data rows
        for ($row = 2; $row <= $lastRow; $row++) {
            $styles[$row] = $dataStyle;
        }
        
        return $styles;
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 5,  // No
            'B' => 18, // Kode Asset
            'C' => 25, // Nama Asset
            'D' => 15, // Tipe Lisensi
            'E' => 15, // Cabang
            'F' => 20, // Supplier
            'G' => 25, // License Key
            'H' => 15, // Username
            'I' => 30, // Login URL
            'J' => 12, // Versi
            'K' => 15, // Tanggal Pembelian
            'L' => 15, // Tanggal Expired
            'M' => 18, // Harga Pembelian
            'N' => 12, // Max Users
            'O' => 12, // Current Users
            'P' => 12, // Status
            'Q' => 20, // Assigned To
            'R' => 15, // Created By
            'S' => 30, // Deskripsi
            'T' => 25, // Notes
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Laporan Asset Digital';
    }

    /**
     * Get status text in Indonesian
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'active' => 'Aktif',
            'inactive' => 'Non-Aktif',
            'expired' => 'Expired',
            'suspended' => 'Suspended'
        ];

        return $statusMap[$status] ?? ucfirst($status);
    }
}
