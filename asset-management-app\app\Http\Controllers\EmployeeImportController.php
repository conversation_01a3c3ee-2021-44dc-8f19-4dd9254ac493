<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use App\Models\Employee;
use App\Models\Branch;
use App\Models\Division;

class EmployeeImportController extends Controller
{
    /**
     * Download template Excel untuk import employee
     */
    public function downloadTemplate()
    {
        $spreadsheet = new Spreadsheet();
        
        // Sheet 1: Template Import
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Template Import Karyawan');
        
        // Header template
        $headers = [
            'A1' => 'nik*',
            'B1' => 'full_name*',
            'C1' => 'branch_code*',
            'D1' => 'division_code*',
            'E1' => 'department',
            'F1' => 'position*',
            'G1' => 'email',
            'H1' => 'phone',
            'I1' => 'join_date',
            'J1' => 'birth_date',
            'K1' => 'gender',
            'L1' => 'address',
            'M1' => 'employee_status*',
            'N1' => 'is_active',
            'O1' => 'notes'
        ];
        
        // Set headers
        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }
        
        // Style headers
        $sheet->getStyle('A1:O1')->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ]);
        
        // Example data
        $exampleData = [
            [
                '1234567890123456',
                'John Doe',
                'JKT',
                'IT',
                'Software Development',
                'Software Engineer',
                '<EMAIL>',
                '081234567890',
                '01/01/2024',
                '15/06/1990',
                'male',
                'Jl. Sudirman No. 123, Jakarta',
                'permanent',
                'true',
                'Karyawan baru'
            ]
        ];
        
        // Add example data
        $row = 2;
        foreach ($exampleData as $data) {
            $col = 'A';
            foreach ($data as $value) {
                $sheet->setCellValue($col . $row, $value);
                $col++;
            }
            $row++;
        }
        
        // Auto width for all columns
        foreach (range('A', 'O') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Create instructions sheet
        $this->createInstructionsSheet($spreadsheet);
        
        // Create reference data sheet
        $this->createReferenceDataSheet($spreadsheet);
        
        $writer = new Xlsx($spreadsheet);
        
        $filename = 'Template_Import_Karyawan_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $writer->save('php://output');
        exit;
    }

    /**
     * Create instructions sheet
     */
    private function createInstructionsSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Petunjuk Pengisian');
        
        $instructions = [
            ['KOLOM', 'KETERANGAN'],
            ['', ''],
            ['KOLOM WAJIB DIISI (*):', ''],
            ['nik*', 'NIK karyawan (16 digit, unik)'],
            ['full_name*', 'Nama lengkap karyawan'],
            ['branch_code*', 'Kode cabang (lihat sheet "Data Referensi")'],
            ['division_code*', 'Kode divisi (lihat sheet "Data Referensi")'],
            ['position*', 'Jabatan karyawan'],
            ['employee_status*', 'Status karyawan: permanent, contract, intern'],
            ['', ''],
            ['KOLOM OPSIONAL:', ''],
            ['department', 'Bagian/departemen'],
            ['email', 'Email karyawan (harus unik jika diisi)'],
            ['phone', 'Nomor telepon'],
            ['join_date', 'Tanggal bergabung (format: dd/mm/yyyy)'],
            ['birth_date', 'Tanggal lahir (format: dd/mm/yyyy)'],
            ['gender', 'Jenis kelamin: male atau female'],
            ['address', 'Alamat lengkap'],
            ['is_active', 'Status aktif: true atau false (default: true)'],
            ['notes', 'Catatan tambahan'],
            ['', ''],
            ['CATATAN PENTING:', ''],
            ['1. NIK harus 16 digit dan unik', ''],
            ['2. Email harus unik jika diisi', ''],
            ['3. Format tanggal: dd/mm/yyyy', ''],
            ['4. Gender: male atau female', ''],
            ['5. Employee status: permanent, contract, intern', ''],
            ['6. Is active: true atau false', ''],
            ['7. Maksimal 1000 baris per import', ''],
        ];
        
        $row = 1;
        foreach ($instructions as $instruction) {
            $sheet->setCellValue('A' . $row, $instruction[0]);
            $sheet->setCellValue('B' . $row, $instruction[1]);
            $row++;
        }
        
        // Style headers
        $headerRows = [1, 3, 11, 22];
        foreach ($headerRows as $headerRow) {
            $sheet->getStyle('A' . $headerRow . ':B' . $headerRow)->applyFromArray([
                'font' => ['bold' => true, 'color' => ['rgb' => '2F5597']],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E7F3FF']]
            ]);
        }
        
        // Auto width
        $sheet->getColumnDimension('A')->setWidth(25);
        $sheet->getColumnDimension('B')->setWidth(50);
    }

    /**
     * Create reference data sheet
     */
    private function createReferenceDataSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Data Referensi');
        
        $startRow = 1;
        
        // Branches
        $branches = Branch::active()->get();
        $sheet->setCellValue('A' . $startRow, 'KODE CABANG');
        $sheet->setCellValue('B' . $startRow, 'NAMA CABANG');
        $sheet->getStyle('A' . $startRow . ':B' . $startRow)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']]
        ]);
        
        $row = $startRow + 1;
        foreach ($branches as $branch) {
            $sheet->setCellValue('A' . $row, $branch->branch_code);
            $sheet->setCellValue('B' . $row, $branch->name);
            $row++;
        }
        
        // Divisions
        $currentCol = 'D';
        $divisions = Division::active()->get();
        $sheet->setCellValue($currentCol . $startRow, 'KODE DIVISI');
        $sheet->setCellValue(chr(ord($currentCol) + 1) . $startRow, 'NAMA DIVISI');
        $sheet->getStyle($currentCol . $startRow . ':' . chr(ord($currentCol) + 1) . $startRow)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']]
        ]);
        
        $row = $startRow + 1;
        foreach ($divisions as $division) {
            $sheet->setCellValue($currentCol . $row, $division->division_code);
            $sheet->setCellValue(chr(ord($currentCol) + 1) . $row, $division->name);
            $row++;
        }
        
        // Auto width for all columns
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * Import employees from Excel
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls|max:10240'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $file = $request->file('file');
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Remove header row
            $header = array_shift($rows);

            // Validate header
            $expectedHeaders = [
                'nik', 'full_name', 'branch_code', 'division_code', 'department',
                'position', 'email', 'phone', 'join_date', 'birth_date',
                'gender', 'address', 'employee_status', 'is_active', 'notes'
            ];

            $cleanHeader = array_map(function($h) {
                return str_replace('*', '', trim($h));
            }, $header);

            if ($cleanHeader !== $expectedHeaders) {
                return redirect()->back()->withErrors([
                    'file' => 'Format header tidak sesuai template. Silakan download template terbaru.'
                ]);
            }

            // Validate max rows
            if (count($rows) > 1000) {
                return redirect()->back()->withErrors([
                    'file' => 'Maksimal 1000 baris data per import.'
                ]);
            }

            // Process import
            $results = $this->processImport($rows);

            return redirect()->back()->with('success',
                "Import berhasil! {$results['success']} data berhasil diimport, {$results['failed']} data gagal."
            )->with('import_errors', $results['errors']);

        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'file' => 'Terjadi kesalahan saat memproses file: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process import data
     */
    private function processImport($rows)
    {
        $success = 0;
        $failed = 0;
        $errors = [];

        // Cache reference data for performance
        $branches = Branch::active()->pluck('id', 'branch_code')->toArray();
        $divisions = Division::active()->pluck('id', 'division_code')->toArray();

        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 because we removed header and Excel starts from 1

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    $data = $this->validateAndPrepareRowData($row, $rowNumber, [
                        'branches' => $branches,
                        'divisions' => $divisions
                    ]);

                    Employee::create($data);
                    $success++;

                } catch (\Exception $e) {
                    $failed++;
                    $errors[] = "Baris {$rowNumber}: " . $e->getMessage();
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return [
            'success' => $success,
            'failed' => $failed,
            'errors' => $errors
        ];
    }

    /**
     * Validate and prepare row data
     */
    private function validateAndPrepareRowData($row, $rowNumber, $references)
    {
        $data = [];

        // NIK (required)
        $nik = trim($row[0]);
        if (empty($nik)) {
            throw new \Exception("NIK tidak boleh kosong");
        }
        if (strlen($nik) !== 16) {
            throw new \Exception("NIK harus 16 digit");
        }
        if (!is_numeric($nik)) {
            throw new \Exception("NIK harus berupa angka");
        }
        if (Employee::where('nik', $nik)->exists()) {
            throw new \Exception("NIK '{$nik}' sudah ada dalam database");
        }
        $data['nik'] = $nik;

        // Full Name (required)
        $fullName = trim($row[1]);
        if (empty($fullName)) {
            throw new \Exception("Nama lengkap tidak boleh kosong");
        }
        $data['full_name'] = $fullName;

        // Branch (required)
        $branchCode = trim($row[2]);
        if (empty($branchCode)) {
            throw new \Exception("Kode cabang tidak boleh kosong");
        }
        if (!isset($references['branches'][$branchCode])) {
            throw new \Exception("Kode cabang '{$branchCode}' tidak ditemukan");
        }
        $data['branch_id'] = $references['branches'][$branchCode];

        // Division (required)
        $divisionCode = trim($row[3]);
        if (empty($divisionCode)) {
            throw new \Exception("Kode divisi tidak boleh kosong");
        }
        if (!isset($references['divisions'][$divisionCode])) {
            throw new \Exception("Kode divisi '{$divisionCode}' tidak ditemukan");
        }
        $data['division_id'] = $references['divisions'][$divisionCode];

        // Department (optional)
        $data['department'] = !empty(trim($row[4])) ? trim($row[4]) : null;

        // Position (required)
        $position = trim($row[5]);
        if (empty($position)) {
            throw new \Exception("Jabatan tidak boleh kosong");
        }
        $data['position'] = $position;

        // Email (optional, must be unique if provided)
        $email = trim($row[6]);
        if (!empty($email)) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception("Format email tidak valid");
            }
            if (Employee::where('email', $email)->exists()) {
                throw new \Exception("Email '{$email}' sudah ada dalam database");
            }
            $data['email'] = $email;
        }

        // Phone (optional)
        $data['phone'] = !empty(trim($row[7])) ? trim($row[7]) : null;

        // Join Date (optional)
        if (!empty(trim($row[8]))) {
            try {
                $joinDate = \DateTime::createFromFormat('d/m/Y', trim($row[8]));
                if (!$joinDate) {
                    throw new \Exception("Format tanggal bergabung tidak valid (gunakan dd/mm/yyyy)");
                }
                $data['join_date'] = $joinDate->format('Y-m-d');
            } catch (\Exception $e) {
                throw new \Exception("Format tanggal bergabung tidak valid (gunakan dd/mm/yyyy)");
            }
        }

        // Birth Date (optional)
        if (!empty(trim($row[9]))) {
            try {
                $birthDate = \DateTime::createFromFormat('d/m/Y', trim($row[9]));
                if (!$birthDate) {
                    throw new \Exception("Format tanggal lahir tidak valid (gunakan dd/mm/yyyy)");
                }
                if ($birthDate >= new \DateTime()) {
                    throw new \Exception("Tanggal lahir harus sebelum hari ini");
                }
                $data['birth_date'] = $birthDate->format('Y-m-d');
            } catch (\Exception $e) {
                throw new \Exception("Format tanggal lahir tidak valid (gunakan dd/mm/yyyy)");
            }
        }

        // Gender (optional)
        $gender = trim($row[10]);
        if (!empty($gender)) {
            if (!in_array($gender, ['male', 'female'])) {
                throw new \Exception("Jenis kelamin harus 'male' atau 'female'");
            }
            $data['gender'] = $gender;
        }

        // Address (optional)
        $data['address'] = !empty(trim($row[11])) ? trim($row[11]) : null;

        // Employee Status (required)
        $employeeStatus = trim($row[12]);
        if (empty($employeeStatus)) {
            throw new \Exception("Status karyawan tidak boleh kosong");
        }
        if (!in_array($employeeStatus, ['permanent', 'contract', 'intern'])) {
            throw new \Exception("Status karyawan harus 'permanent', 'contract', atau 'intern'");
        }
        $data['employee_status'] = $employeeStatus;

        // Is Active (optional, default true)
        $isActive = trim($row[13]);
        if (!empty($isActive)) {
            if (!in_array(strtolower($isActive), ['true', 'false', '1', '0'])) {
                throw new \Exception("Status aktif harus 'true' atau 'false'");
            }
            $data['is_active'] = in_array(strtolower($isActive), ['true', '1']);
        } else {
            $data['is_active'] = true;
        }

        // Notes (optional)
        $data['notes'] = !empty(trim($row[14])) ? trim($row[14]) : null;

        return $data;
    }
}
