@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Permintaan Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi / Permintaan Asset /</span> Detail Permintaan
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ $requestAsset->request_number }}</h5>
          <div class="d-flex gap-2">
            @if($requestAsset->canBeEdited())
              <a href="{{ route('request-assets.edit', $requestAsset) }}" class="btn btn-primary btn-sm">
                <i class="ri-pencil-line me-1"></i>Edit
              </a>
            @endif
            @if(in_array($requestAsset->status, ['draft', 'submitted', 'reviewed', 'approved', 'completed']))
              <a href="{{ route('request-assets.print', $requestAsset) }}" class="btn btn-success btn-sm" target="_blank">
                <i class="ri-printer-line me-1"></i>Cetak PDF
              </a>
            @endif
            <a href="{{ route('request-assets.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <!-- Status Badge -->
          <div class="mb-4">
            <span class="badge bg-{{ $requestAsset->status_badge }} fs-6 me-2">
              {{ $requestAsset->status_label }}
            </span>
            <span class="badge bg-info fs-6 me-2">{{ $requestAsset->request_category }}</span>
            <span class="badge bg-secondary fs-6">{{ $requestAsset->item_type }}</span>
          </div>

          <!-- Request Information -->
          <h6 class="mb-3">Informasi Permintaan</h6>
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nomor Permintaan</label>
                <p class="fw-bold">{{ $requestAsset->request_number }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $requestAsset->status_badge }}">
                    {{ $requestAsset->status_label }}
                  </span>
                </p>
              </div>
            </div>
          </div>

          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tanggal Permohonan</label>
                <p class="fw-bold">{{ $requestAsset->request_date->format('d/m/Y') }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tanggal Dibutuhkan</label>
                <p class="fw-bold">{{ $requestAsset->needed_date->format('d/m/Y') }}</p>
              </div>
            </div>
          </div>

          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kategori Permintaan</label>
                <p><span class="badge bg-info">{{ $requestAsset->request_category }}</span></p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Item Permintaan</label>
                <p><span class="badge bg-secondary">{{ $requestAsset->item_type }}</span></p>
              </div>
            </div>
          </div>

          <!-- Requester Information -->
          <hr class="my-4">
          <h6 class="mb-3">Informasi Pemohon</h6>
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Diminta Oleh</label>
                <p class="fw-bold">{{ $requestAsset->requestedByUser->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Jabatan</label>
                <p>{{ $requestAsset->position }}</p>
              </div>
            </div>
          </div>

          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Divisi</label>
                <p>{{ $requestAsset->division->name }}</p>
              </div>
            </div>
            @if($requestAsset->estimated_total)
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Estimasi Total Biaya</label>
                <p class="fw-bold text-primary">Rp {{ number_format($requestAsset->estimated_total, 0, ',', '.') }}</p>
              </div>
            </div>
            @endif
          </div>

          <!-- Purpose -->
          <div class="mb-4">
            <label class="form-label text-muted">Tujuan Permintaan</label>
            <p>{{ $requestAsset->purpose }}</p>
          </div>

          <!-- Items -->
          <hr class="my-4">
          <h6 class="mb-3">Detail Item Permintaan ({{ $requestAsset->total_items }} item)</h6>
          @if($requestAsset->items && count($requestAsset->items) > 0)
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th>No</th>
                    <th>Nama Barang/Jasa</th>
                    <th>Jumlah</th>
                    <th>Keterangan</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($requestAsset->items as $index => $item)
                  <tr>
                    <td>{{ $index + 1 }}</td>
                    <td><strong>{{ $item['name'] }}</strong></td>
                    <td><span class="badge bg-primary">{{ $item['quantity'] }}</span></td>
                    <td>{{ $item['description'] ?? '-' }}</td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          @else
            <div class="alert alert-warning">
              <i class="ri-alert-line me-2"></i>Tidak ada item yang tercatat
            </div>
          @endif

          <!-- Notes -->
          @if($requestAsset->notes)
          <hr class="my-4">
          <div class="mb-4">
            <label class="form-label text-muted">Catatan Tambahan</label>
            <p>{{ $requestAsset->notes }}</p>
          </div>
          @endif

          <!-- Approval Information -->
          @if(in_array($requestAsset->status, ['approved', 'rejected']) && $requestAsset->approvedByUser)
          <hr class="my-4">
          <h6 class="mb-3">Informasi Persetujuan</h6>
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Disetujui/Ditolak Oleh</label>
                <p class="fw-bold">{{ $requestAsset->approvedByUser->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tanggal Persetujuan</label>
                <p>{{ $requestAsset->approved_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>

          @if($requestAsset->approval_notes)
          <div class="mb-4">
            <label class="form-label text-muted">Catatan Persetujuan</label>
            <div class="alert alert-{{ $requestAsset->status === 'approved' ? 'success' : 'danger' }}">
              {{ $requestAsset->approval_notes }}
            </div>
          </div>
          @endif
          @endif
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Detail</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ strtoupper(substr($requestAsset->request_category, 0, 2)) }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $requestAsset->request_number }}</h6>
              <small class="text-muted">{{ $requestAsset->request_category }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>Status: <strong>{{ $requestAsset->status_label }}</strong></li>
              <li>Total Item: <strong>{{ $requestAsset->total_items }}</strong></li>
              <li>Pemohon: <strong>{{ $requestAsset->requestedByUser->name }}</strong></li>
              <li>Divisi: <strong>{{ $requestAsset->division->name }}</strong></li>
              @if($requestAsset->estimated_total)
                <li>Estimasi: <strong>Rp {{ number_format($requestAsset->estimated_total, 0, ',', '.') }}</strong></li>
              @endif
              <li>Dibuat: {{ $requestAsset->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            @if($requestAsset->canBeEdited())
              <a href="{{ route('request-assets.edit', $requestAsset) }}" class="btn btn-primary">
                <i class="ri-pencil-line me-1"></i>Edit Permintaan
              </a>
            @endif

            @if(in_array($requestAsset->status, ['draft', 'submitted', 'reviewed', 'approved', 'completed']))
              <a href="{{ route('request-assets.print', $requestAsset) }}" class="btn btn-success" target="_blank">
                <i class="ri-printer-line me-1"></i>Cetak PDF
              </a>
            @endif
            
            @if($requestAsset->canBeSubmitted())
              <form action="{{ route('request-assets.submit', $requestAsset) }}" method="POST">
                @csrf
                <button type="submit" class="btn btn-success w-100" onclick="return confirm('Yakin ingin mengajukan permintaan ini?')">
                  <i class="ri-send-plane-line me-1"></i>Ajukan Permintaan
                </button>
              </form>
            @endif
            
            @if($canApprove)
              <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                <i class="ri-check-line me-1"></i>Setujui
              </button>

              <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                <i class="ri-close-line me-1"></i>Tolak
              </button>
            @else
              <!-- Debug Info -->
              @if(config('app.debug'))
                <div class="alert alert-info mt-2">
                  <small>
                    <strong>Debug Info:</strong><br>
                    Status: {{ $requestAsset->status }}<br>
                    Current User: {{ auth()->user()->name }}<br>
                    User Role: {{ auth()->user()->role_name }}<br>
                    Approval Histories: {{ $requestAsset->approvalHistories->count() }}<br>
                    Current Approval: {{ $requestAsset->currentApprovalHistory ? 'Yes' : 'No' }}<br>
                    @if($requestAsset->currentApprovalHistory)
                      Current Level: {{ $requestAsset->currentApprovalHistory->metadata['level_name'] ?? 'Unknown' }}<br>
                      Required Roles: {{ implode(', ', $requestAsset->currentApprovalHistory->metadata['approver_config']['roles'] ?? []) }}<br>
                    @endif
                  </small>
                </div>
              @endif
            @endif
            
            @if($requestAsset->status === 'draft')
              <form action="{{ route('request-assets.destroy', $requestAsset) }}" method="POST">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Yakin ingin menghapus permintaan ini?\n\nTindakan ini tidak dapat dibatalkan!')">
                  <i class="ri-delete-bin-line me-1"></i>Hapus Permintaan
                </button>
              </form>
            @endif
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Timeline Approval</h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <!-- Draft Status -->
            <div class="timeline-item">
              <div class="timeline-point bg-primary"></div>
              <div class="timeline-content">
                <h6 class="mb-1">Draft Dibuat</h6>
                <small class="text-muted">{{ $requestAsset->created_at->format('d/m/Y H:i') }}</small>
                <br><small class="text-muted">oleh {{ $requestAsset->requestedByUser->name }}</small>
              </div>
            </div>

            @if(count($approvalTimeline) > 0)
              <!-- Submitted Status -->
              <div class="timeline-item">
                <div class="timeline-point bg-info"></div>
                <div class="timeline-content">
                  <h6 class="mb-1">Diajukan untuk Approval</h6>
                  <small class="text-muted">{{ $requestAsset->updated_at->format('d/m/Y H:i') }}</small>
                  <br><small class="text-muted">oleh {{ $requestAsset->requestedByUser->name }}</small>
                </div>
              </div>

              <!-- Approval Timeline -->
              @foreach($approvalTimeline as $timeline)
              <div class="timeline-item">
                <div class="timeline-point bg-{{
                  $timeline['status'] === 'approved' ? 'success' :
                  ($timeline['status'] === 'rejected' ? 'danger' :
                  ($timeline['status'] === 'pending' ? 'warning' : 'secondary'))
                }}"></div>
                <div class="timeline-content">
                  <h6 class="mb-1">
                    {{ $timeline['level_name'] }}
                    @if($timeline['status'] === 'approved')
                      <span class="badge bg-success ms-2">Disetujui</span>
                    @elseif($timeline['status'] === 'rejected')
                      <span class="badge bg-danger ms-2">Ditolak</span>
                    @elseif($timeline['status'] === 'pending')
                      <span class="badge bg-warning ms-2">Menunggu</span>
                    @else
                      <span class="badge bg-secondary ms-2">Belum Diproses</span>
                    @endif
                  </h6>

                  @if($timeline['approved_at'])
                    <small class="text-muted">{{ $timeline['approved_at']->format('d/m/Y H:i') }}</small>
                  @elseif($timeline['status'] === 'pending')
                    <small class="text-warning">Menunggu approval</small>
                    @if($timeline['timeout_at'])
                      <br><small class="text-muted">Timeout: {{ $timeline['timeout_at']->format('d/m/Y H:i') }}</small>
                    @endif
                  @else
                    <small class="text-muted">Belum diproses</small>
                  @endif

                  @if($timeline['approver'])
                    <br><small class="text-muted">oleh {{ $timeline['approver']->name }}</small>
                  @endif

                  @if($timeline['notes'])
                    <br><small class="text-info">{{ $timeline['notes'] }}</small>
                  @endif
                </div>
              </div>
              @endforeach
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Setujui Permintaan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('request-assets.approve', $requestAsset) }}" method="POST">
        @csrf
        <div class="modal-body">
          <p>Yakin ingin menyetujui permintaan <strong>{{ $requestAsset->request_number }}</strong>?</p>
          
          <div class="mb-3">
            <label class="form-label">Catatan Persetujuan (Opsional)</label>
            <textarea class="form-control" name="approval_notes" rows="3" 
                      placeholder="Catatan atau instruksi tambahan..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-success">
            <i class="ri-check-line me-1"></i>Setujui
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Tolak Permintaan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form action="{{ route('request-assets.reject', $requestAsset) }}" method="POST">
        @csrf
        <div class="modal-body">
          <p>Yakin ingin menolak permintaan <strong>{{ $requestAsset->request_number }}</strong>?</p>
          
          <div class="mb-3">
            <label class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
            <textarea class="form-control" name="approval_notes" rows="3" required
                      placeholder="Jelaskan alasan penolakan..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-danger">
            <i class="ri-close-line me-1"></i>Tolak
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

<style>
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -22px;
  top: 20px;
  width: 2px;
  height: calc(100% + 10px);
  background-color: #e9ecef;
}

.timeline-point {
  position: absolute;
  left: -26px;
  top: 4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content h6 {
  margin-bottom: 2px;
}
</style>
@endsection
