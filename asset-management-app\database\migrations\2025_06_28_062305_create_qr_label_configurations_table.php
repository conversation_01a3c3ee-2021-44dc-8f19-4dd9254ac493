<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qr_label_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->decimal('width', 8, 2); // in mm
            $table->decimal('height', 8, 2); // in mm
            $table->decimal('qr_size', 8, 2); // in mm
            $table->decimal('margin_top', 8, 2)->default(5);
            $table->decimal('margin_bottom', 8, 2)->default(5);
            $table->decimal('margin_left', 8, 2)->default(5);
            $table->decimal('margin_right', 8, 2)->default(5);
            $table->string('font_family')->default('Arial');
            $table->integer('font_size_title')->default(12);
            $table->integer('font_size_content')->default(10);
            $table->boolean('show_asset_name')->default(true);
            $table->boolean('show_asset_code')->default(true);
            $table->boolean('show_category')->default(true);
            $table->boolean('show_branch')->default(true);
            $table->boolean('show_location')->default(false);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qr_label_configurations');
    }
};
