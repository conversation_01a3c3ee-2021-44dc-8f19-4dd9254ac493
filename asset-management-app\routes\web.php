<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\dashboard\Analytics;

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Redirect root to login if not authenticated, otherwise to dashboard
Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return redirect()->route('login');
});

// Test Route (No Auth Required)
Route::get('test-server', function() {
    return 'Server is working! Laravel version: ' . app()->version();
})->name('test.server');



// Public QR Code View (No Auth Required)
Route::get('assets/{asset}/qr', [\App\Http\Controllers\AssetController::class, 'qrView'])->name('assets.qr-view');

// Test QR Code Generation
Route::get('test-qr', function() {
    try {
        $qr = \SimpleSoftwareIO\QrCode\Facades\QrCode::size(200)
                ->format('svg')
                ->generate('https://www.google.com');
        return response($qr)->header('Content-Type', 'image/svg+xml');
    } catch (Exception $e) {
        return 'QR Error: ' . $e->getMessage();
    }
});

// Test QR Label Size
Route::get('test-label-size', function() {
    $config = \App\Models\QrLabelConfiguration::getDefault();
    $asset = \App\Models\Asset::first();

    if (!$asset) {
        return 'No asset found for testing';
    }

    $qrUrl = route('assets.qr-view', $asset->id);
    $qrSizePixels = $config->qr_size * 2.834645669;
    $qrCodeSvg = \SimpleSoftwareIO\QrCode\Facades\QrCode::size($qrSizePixels)
                   ->format('svg')
                   ->errorCorrection('M')
                   ->margin(0)
                   ->generate($qrUrl);

    $qrCodeBase64 = 'data:image/svg+xml;base64,' . base64_encode($qrCodeSvg);

    return view('qr-labels.single-exact', compact('asset', 'config', 'qrCodeBase64'));
});

// Test Custom Label Size
Route::get('test-custom-label', function() {
    // Find LABEL KOSTUM configuration
    $config = \App\Models\QrLabelConfiguration::where('name', 'LIKE', '%KOSTUM%')->first();

    if (!$config) {
        $config = \App\Models\QrLabelConfiguration::getDefault();
    }

    $asset = \App\Models\Asset::first();

    if (!$asset) {
        return 'No asset found for testing';
    }

    $qrUrl = route('assets.qr-view', $asset->id);
    $qrSizePixels = $config->qr_size * 2.834645669;
    $qrCodeSvg = \SimpleSoftwareIO\QrCode\Facades\QrCode::size($qrSizePixels)
                   ->format('svg')
                   ->errorCorrection('M')
                   ->margin(0)
                   ->generate($qrUrl);

    $qrCodeBase64 = 'data:image/svg+xml;base64,' . base64_encode($qrCodeSvg);

    return view('qr-labels.single-exact', compact('asset', 'config', 'qrCodeBase64'));
});

// API Routes for AJAX calls
Route::get('api/branches/{branch}/asset-count', function($branchId) {
    $query = \App\Models\Asset::where('branch_id', $branchId);

    // Filter by category if provided
    if (request('category_id')) {
        $query->where('asset_category_id', request('category_id'));
    }

    $count = $query->count();
    return response()->json(['count' => $count]);
});

Route::get('api/assets/{asset}/code', function($assetId) {
    $asset = \App\Models\Asset::find($assetId);
    if ($asset) {
        return response()->json([
            'asset_code' => $asset->asset_code,
            'asset_name' => $asset->name,
            'asset_id' => $asset->id
        ]);
    }
    return response()->json(['error' => 'Asset not found'], 404);
});

// Protected Routes
Route::middleware(['auth', 'branch.isolation'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [Analytics::class, 'index'])->name('dashboard')->middleware('permission:dashboard.view');

    // Asset Management Test Route
    Route::get('assets-test', function() {
        return 'Assets route is working! User: ' . auth()->user()->name . ', Role: ' . auth()->user()->role_slug;
    })->name('assets.test');

    // Simple Assets Test Route
    Route::get('assets-simple', function() {
        $assetCount = \App\Models\Asset::count();
        return "Simple Assets Test: Found {$assetCount} assets in database. User: " . auth()->user()->name;
    })->name('assets.simple');

    // Asset Management Bypass Route (for testing)
    Route::get('assets-bypass', function() {
        $assets = \App\Models\Asset::with(['assetCategory', 'assetType', 'branch'])->paginate(15);
        return view('assets.index', compact('assets'));
    })->name('assets.bypass');









    // Asset Management Bypass Route (for testing)
    Route::get('assets-bypass', function() {
        $assets = \App\Models\Asset::with(['assetCategory', 'assetType', 'branch'])->paginate(15);
        return view('assets.index', compact('assets'));
    })->name('assets.bypass');

    // Asset Management (with stock opname lock protection)
    Route::get('assets/view-all', [\App\Http\Controllers\AssetController::class, 'viewAll'])->name('assets.view-all');
    Route::get('assets/field-configurations', [\App\Http\Controllers\AssetController::class, 'getFieldConfigurations'])->name('assets.field-configurations');
    Route::resource('assets', \App\Http\Controllers\AssetController::class)->middleware('check.stock.opname.lock');
    Route::resource('asset-categories', \App\Http\Controllers\AssetCategoryController::class)->middleware('check.stock.opname.lock');

    // QR Label Management
    Route::get('qr-labels', [\App\Http\Controllers\QrLabelController::class, 'index'])->name('qr-labels.index');
    Route::post('qr-labels', [\App\Http\Controllers\QrLabelController::class, 'store'])->name('qr-labels.store');
    Route::delete('qr-labels/{qrLabel}', [\App\Http\Controllers\QrLabelController::class, 'destroy'])->name('qr-labels.destroy');
    Route::get('qr-labels/preview', [\App\Http\Controllers\QrLabelController::class, 'preview'])->name('qr-labels.preview');
    Route::get('assets/{asset}/qr-label', [\App\Http\Controllers\QrLabelController::class, 'generateSingle'])->name('assets.qr-label');
    Route::post('assets/qr-labels/multiple', [\App\Http\Controllers\QrLabelController::class, 'generateMultiple'])->name('assets.qr-labels.multiple');

    // Asset Assignment Management
    Route::resource('asset-assignments', \App\Http\Controllers\AssetAssignmentController::class);
    Route::post('asset-assignments/check-existing', [\App\Http\Controllers\AssetAssignmentController::class, 'checkExistingAssignment'])->name('asset-assignments.check-existing');
    Route::get('asset-assignments/{assetAssignment}/return', [\App\Http\Controllers\AssetAssignmentController::class, 'returnForm'])->name('asset-assignments.return-form');
    Route::post('asset-assignments/{assetAssignment}/return', [\App\Http\Controllers\AssetAssignmentController::class, 'returnAsset'])->name('asset-assignments.return');

    // Asset Digital Management
    Route::resource('asset-digitals', \App\Http\Controllers\AssetDigitalController::class);
    Route::get('asset-assignments/{assetAssignment}/transfer', [\App\Http\Controllers\AssetAssignmentController::class, 'transferForm'])->name('asset-assignments.transfer-form');
    Route::post('asset-assignments/{assetAssignment}/transfer', [\App\Http\Controllers\AssetAssignmentController::class, 'transferAsset'])->name('asset-assignments.transfer');
    Route::get('asset-assignments/{assetAssignment}/print-receipt', [\App\Http\Controllers\AssetAssignmentController::class, 'printReceipt'])->name('asset-assignments.print-receipt');
    Route::get('asset-assignments/{assetAssignment}/download-pdf', [\App\Http\Controllers\AssetAssignmentController::class, 'downloadReceiptPdf'])->name('asset-assignments.download-pdf');

    // Asset Maintenance Management
    Route::resource('asset-maintenances', \App\Http\Controllers\AssetMaintenanceController::class);
    Route::post('asset-maintenances/{assetMaintenance}/start', [\App\Http\Controllers\AssetMaintenanceController::class, 'start'])->name('asset-maintenances.start');
    Route::post('asset-maintenances/{assetMaintenance}/complete', [\App\Http\Controllers\AssetMaintenanceController::class, 'complete'])->name('asset-maintenances.complete');
    Route::post('asset-maintenances/{assetMaintenance}/cancel', [\App\Http\Controllers\AssetMaintenanceController::class, 'cancel'])->name('asset-maintenances.cancel');

    // Contract Management (Main Menu)
    Route::get('contracts/{contract}/download', [\App\Http\Controllers\Master\ContractController::class, 'downloadFile'])->name('contracts.download');
    Route::resource('contracts', \App\Http\Controllers\Master\ContractController::class);

    // Stock Opname Management
    // Specific routes must come before resource routes
    Route::get('stock-opnames/scan', [\App\Http\Controllers\StockOpnameController::class, 'scanIndex'])->name('stock-opnames.scan');
    Route::get('stock-opnames/{stockOpname}/scan', [\App\Http\Controllers\StockOpnameController::class, 'scan'])->name('stock-opnames.scan-detail');
    Route::post('stock-opnames/{stockOpname}/process-scan', [\App\Http\Controllers\StockOpnameController::class, 'processScan'])->name('stock-opnames.process-scan');
    Route::get('stock-opnames/{stockOpname}/asset-info', [\App\Http\Controllers\StockOpnameController::class, 'getAssetInfo'])->name('stock-opnames.asset-info');
    Route::get('stock-opnames/{stockOpname}/report', [\App\Http\Controllers\StockOpnameController::class, 'report'])->name('stock-opnames.report');
    Route::get('stock-opnames/{stockOpname}/hardcopy-form', [\App\Http\Controllers\StockOpnameController::class, 'generateHardcopyForm'])->name('stock-opnames.hardcopy-form');
    Route::post('stock-opnames/{stockOpname}/start', [\App\Http\Controllers\StockOpnameController::class, 'start'])->name('stock-opnames.start');
    Route::post('stock-opnames/{stockOpname}/complete', [\App\Http\Controllers\StockOpnameController::class, 'complete'])->name('stock-opnames.complete');
    Route::post('stock-opnames/{stockOpname}/cancel', [\App\Http\Controllers\StockOpnameController::class, 'cancel'])->name('stock-opnames.cancel');

    // Resource routes come last
    Route::resource('stock-opnames', \App\Http\Controllers\StockOpnameController::class);

    // Stock Opname Reports
    Route::get('reports/stock-opnames', [\App\Http\Controllers\StockOpnameReportController::class, 'index'])->name('reports.stock-opnames');
    Route::get('reports/stock-opnames/{stockOpname}', [\App\Http\Controllers\StockOpnameReportController::class, 'show'])->name('reports.stock-opnames.show');
    Route::get('reports/stock-opnames/export/excel', [\App\Http\Controllers\StockOpnameReportController::class, 'export'])->name('reports.stock-opnames.export');
    Route::get('reports/stock-opnames/{stockOpname}/export', [\App\Http\Controllers\StockOpnameReportController::class, 'exportDetail'])->name('reports.stock-opnames.export-detail');
    Route::get('api/stock-opnames/summary', [\App\Http\Controllers\StockOpnameReportController::class, 'summary'])->name('api.stock-opnames.summary');

    // Asset Reports
    Route::get('reports/assets', [\App\Http\Controllers\Reports\AssetReportController::class, 'index'])->name('reports.assets.index');
    Route::get('reports/assets/export', [\App\Http\Controllers\Reports\AssetReportController::class, 'export'])->name('reports.assets.export');

    // Asset Digital Reports
    Route::get('reports/asset-digitals', [\App\Http\Controllers\Reports\AssetDigitalReportController::class, 'index'])->name('reports.asset-digitals.index');
    Route::get('reports/asset-digitals/export', [\App\Http\Controllers\Reports\AssetDigitalReportController::class, 'export'])->name('reports.asset-digitals.export');

// Maintenance Reports
Route::get('reports/maintenance', [\App\Http\Controllers\Reports\MaintenanceReportController::class, 'index'])->name('reports.maintenance.index');
Route::get('reports/maintenance/export', [\App\Http\Controllers\Reports\MaintenanceReportController::class, 'export'])->name('reports.maintenance.export');



// Test Auto Login Route




    // Test Stock Opname List
    Route::get('test-stock-opnames', function() {
        $stockOpnames = \App\Models\StockOpname::with('branch')->get();
        $html = '<h2>Stock Opnames Available:</h2>';
        foreach ($stockOpnames as $so) {
            $html .= '<p><a href="/test-export/' . $so->id . '">' . $so->id . ' - ' . $so->title . ' (' . $so->status . ') - ' . $so->branch->name . '</a></p>';
        }
        if ($stockOpnames->count() == 0) {
            $html .= '<p>No stock opnames found. <a href="/stock-opnames/create">Create one</a></p>';
        }
        return $html;
    })->name('test.stock-opnames');

    // Test Export Route
    Route::get('test-export/{stockOpname?}', function($stockOpnameId = null) {
        try {
            if ($stockOpnameId) {
                $stockOpname = \App\Models\StockOpname::find($stockOpnameId);
                if (!$stockOpname) {
                    return response()->json(['error' => 'Stock opname not found'], 404);
                }

                $filename = 'test-detail-stock-opname-' . $stockOpname->opname_number . '-' . date('Y-m-d-H-i-s') . '.xlsx';
                return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\StockOpnameReportExport([], $stockOpnameId), $filename);
            } else {
                $filename = 'test-laporan-stock-opname-' . date('Y-m-d-H-i-s') . '.xlsx';
                return \Maatwebsite\Excel\Facades\Excel::download(new \App\Exports\StockOpnameReportExport([]), $filename);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()], 500);
        }
    })->name('test.export');

    // Master Data
    Route::prefix('master')->name('master.')->group(function () {
        Route::resource('document-numbers', \App\Http\Controllers\Master\DocumentNumberController::class);
        Route::resource('branches', \App\Http\Controllers\Master\BranchController::class);
        Route::resource('users', \App\Http\Controllers\Master\UserController::class);
        Route::resource('categories', \App\Http\Controllers\Master\CategoryController::class);
        Route::resource('asset-types', \App\Http\Controllers\Master\AssetTypeController::class);
        Route::resource('divisions', \App\Http\Controllers\Master\DivisionController::class);
        Route::resource('roles', \App\Http\Controllers\Master\RoleController::class);
        Route::resource('approval-workflows', \App\Http\Controllers\Master\ApprovalWorkflowController::class);
        Route::resource('lookups', \App\Http\Controllers\Master\LookupController::class);
        Route::resource('employees', \App\Http\Controllers\Master\EmployeeController::class);

        // Employee additional routes
        Route::post('employees/{employee}/toggle-status', [\App\Http\Controllers\Master\EmployeeController::class, 'toggleStatus'])->name('employees.toggle-status');

        // AJAX routes for asset field configurations (must be before resource route)
        Route::get('asset-field-configurations/by-category', [\App\Http\Controllers\Master\AssetFieldConfigurationController::class, 'getByCategory'])->name('asset-field-configurations.by-category');
        Route::get('asset-field-configurations/preview', [\App\Http\Controllers\Master\AssetFieldConfigurationController::class, 'preview'])->name('asset-field-configurations.preview');
        Route::get('asset-field-configurations/table-columns', [\App\Http\Controllers\Master\AssetFieldConfigurationController::class, 'getTableColumns'])->name('asset-field-configurations.table-columns');
        Route::get('asset-field-configurations/dependent-options', [\App\Http\Controllers\Master\AssetFieldConfigurationController::class, 'getDependentOptions'])->name('asset-field-configurations.dependent-options');
        Route::get('asset-field-configurations/test-data-source', [\App\Http\Controllers\Master\AssetFieldConfigurationController::class, 'testDataSource'])->name('asset-field-configurations.test-data-source');

        Route::resource('asset-field-configurations', \App\Http\Controllers\Master\AssetFieldConfigurationController::class);

        // Contract Management (Master)
        Route::get('contracts/{contract}/download', [\App\Http\Controllers\Master\ContractController::class, 'downloadFile'])->name('master.contracts.download');
        Route::resource('contracts', \App\Http\Controllers\Master\ContractController::class);

        // Company Settings
        Route::get('company-settings', [\App\Http\Controllers\Master\CompanySettingController::class, 'index'])->name('company-settings.index');
        Route::get('company-settings/edit', [\App\Http\Controllers\Master\CompanySettingController::class, 'edit'])->name('company-settings.edit');
        Route::put('company-settings', [\App\Http\Controllers\Master\CompanySettingController::class, 'update'])->name('company-settings.update');

        // Other AJAX routes
        Route::get('asset-types/by-category', [\App\Http\Controllers\Master\AssetTypeController::class, 'getByCategory'])->name('asset-types.by-category');
        Route::get('lookups/by-code', [\App\Http\Controllers\Master\LookupController::class, 'getByCode'])->name('lookups.by-code');
        Route::get('lookups/by-category', [\App\Http\Controllers\Master\LookupController::class, 'getByCategory'])->name('lookups.by-category');
    });



    // Request Assets routes
    Route::resource('request-assets', \App\Http\Controllers\RequestAssetController::class);
    Route::post('request-assets/{requestAsset}/submit', [\App\Http\Controllers\RequestAssetController::class, 'submit'])->name('request-assets.submit');
    Route::post('request-assets/{requestAsset}/approve', [\App\Http\Controllers\RequestAssetController::class, 'approve'])->name('request-assets.approve');
    Route::post('request-assets/{requestAsset}/reject', [\App\Http\Controllers\RequestAssetController::class, 'reject'])->name('request-assets.reject');
    Route::get('request-assets/preview/document-number', [\App\Http\Controllers\RequestAssetController::class, 'previewDocumentNumber'])->name('request-assets.preview-document-number');
    Route::get('request-assets/{requestAsset}/print', [\App\Http\Controllers\RequestAssetController::class, 'printPdf'])->name('request-assets.print');

    // Notification routes
    Route::get('notifications', [\App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::get('notifications/{id}/read', [\App\Http\Controllers\NotificationController::class, 'read'])->name('notifications.read');
    Route::post('notifications/mark-all-read', [\App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::get('api/notifications/unread-count', [\App\Http\Controllers\NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
    Route::get('api/notifications/recent', [\App\Http\Controllers\NotificationController::class, 'getRecent'])->name('notifications.recent');

    // Profile routes
    Route::get('profile', [\App\Http\Controllers\ProfileController::class, 'index'])->name('profile.index');
    Route::put('profile', [\App\Http\Controllers\ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::put('profile/password', [\App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.update-password');

    // Database Backup routes
    Route::get('backup', [\App\Http\Controllers\DatabaseBackupController::class, 'index'])->name('backup.index');
    Route::post('backup/create', [\App\Http\Controllers\DatabaseBackupController::class, 'createBackup'])->name('backup.create');
    Route::post('backup/upload', [\App\Http\Controllers\DatabaseBackupController::class, 'uploadBackup'])->name('backup.upload');
    Route::post('backup/restore', [\App\Http\Controllers\DatabaseBackupController::class, 'restoreBackup'])->name('backup.restore');
    Route::get('backup/download/{filename}', [\App\Http\Controllers\DatabaseBackupController::class, 'downloadBackup'])->name('backup.download');
    Route::get('backup/delete/{filename}', [\App\Http\Controllers\DatabaseBackupController::class, 'deleteBackup'])->name('backup.delete');

    // Supplier routes
    Route::resource('suppliers', \App\Http\Controllers\SupplierController::class);
    Route::patch('suppliers/{supplier}/toggle-status', [\App\Http\Controllers\SupplierController::class, 'toggleStatus'])->name('suppliers.toggle-status');
    Route::get('suppliers/{supplier}/download-npwp', [\App\Http\Controllers\SupplierController::class, 'downloadNpwpFile'])->name('suppliers.download-npwp');
    Route::delete('suppliers/{supplier}/delete-npwp', [\App\Http\Controllers\SupplierController::class, 'deleteNpwpFile'])->name('suppliers.delete-npwp');

    // Supplier Lookup API routes
    Route::get('api/suppliers/lookup', [\App\Http\Controllers\Api\SupplierLookupController::class, 'index'])->name('api.suppliers.lookup');
    Route::get('api/suppliers/lookup/{id}', [\App\Http\Controllers\Api\SupplierLookupController::class, 'show'])->name('api.suppliers.lookup.show');

    // Purchase Order Lookup API routes
    Route::get('api/purchase-orders/lookup', [\App\Http\Controllers\PurchaseOrderController::class, 'lookup'])->name('api.purchase-orders.lookup');

    // Asset Detail API routes
    Route::get('api/assets/{id}/details', [\App\Http\Controllers\Api\AssetDetailController::class, 'show'])->name('api.assets.details');

    // Maintenance Number Preview API routes
    Route::get('api/maintenance/preview-number/{assetId}', [\App\Http\Controllers\Api\MaintenanceNumberController::class, 'previewNumber'])->name('api.maintenance.preview-number');

    // Maintenance PDF routes
    Route::get('maintenance/{assetMaintenance}/pdf/form', [\App\Http\Controllers\MaintenancePdfController::class, 'generatePdf'])->name('maintenance.pdf.form');
    Route::get('maintenance/{assetMaintenance}/pdf/receipt', [\App\Http\Controllers\MaintenancePdfController::class, 'generateReceipt'])->name('maintenance.pdf.receipt');

    // Purchase Orders
    Route::resource('purchase-orders', \App\Http\Controllers\PurchaseOrderController::class);
    Route::post('purchase-orders/{purchaseOrder}/submit', [\App\Http\Controllers\PurchaseOrderController::class, 'submit'])->name('purchase-orders.submit');
    Route::post('purchase-orders/{purchaseOrder}/approve', [\App\Http\Controllers\PurchaseOrderController::class, 'approve'])->name('purchase-orders.approve');
    Route::post('purchase-orders/{purchaseOrder}/reject', [\App\Http\Controllers\PurchaseOrderController::class, 'reject'])->name('purchase-orders.reject');
    Route::get('purchase-orders/{purchaseOrder}/print', [\App\Http\Controllers\PurchaseOrderController::class, 'print'])->name('purchase-orders.print');
    Route::post('purchase-orders/preview-document-number', [\App\Http\Controllers\PurchaseOrderController::class, 'previewDocumentNumber'])->name('purchase-orders.preview-document-number');

    // Helper Routes
    Route::post('get-terbilang', function(\Illuminate\Http\Request $request) {
        $amount = $request->input('amount', 0);
        return response()->json([
            'terbilang' => terbilang_rupiah($amount)
        ]);
    })->name('get-terbilang');
});

// Public Maintenance Tracking routes (accessible via QR code without login)
Route::get('maintenance/track/{id}', [\App\Http\Controllers\MaintenanceTrackingController::class, 'track'])->name('maintenance.track');

// Protected Maintenance Receipt routes (require login)
Route::middleware(['auth'])->group(function () {
    Route::get('maintenance/{id}/receipt/form', [\App\Http\Controllers\MaintenanceTrackingController::class, 'showReceiptForm'])->name('maintenance.receipt.form');
    Route::post('maintenance/{id}/receipt', [\App\Http\Controllers\MaintenanceTrackingController::class, 'storeReceipt'])->name('maintenance.receipt.store');
});


