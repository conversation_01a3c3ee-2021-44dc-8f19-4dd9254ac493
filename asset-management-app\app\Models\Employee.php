<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Employee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'nik',
        'full_name',
        'branch_id',
        'division_id',
        'department',
        'position',
        'email',
        'phone',
        'join_date',
        'birth_date',
        'gender',
        'address',
        'employee_status',
        'is_active',
        'notes'
    ];

    protected $casts = [
        'join_date' => 'datetime',
        'birth_date' => 'datetime',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function division()
    {
        return $this->belongsTo(Division::class);
    }

    public function assets()
    {
        return $this->hasMany(Asset::class, 'assigned_to');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByDivision($query, $divisionId)
    {
        return $query->where('division_id', $divisionId);
    }

    // Accessors
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'Aktif' : 'Tidak Aktif';
    }

    public function getGenderTextAttribute()
    {
        return match($this->gender) {
            'male' => 'Laki-laki',
            'female' => 'Perempuan',
            default => '-'
        };
    }

    public function getEmployeeStatusTextAttribute()
    {
        return match($this->employee_status) {
            'permanent' => 'Tetap',
            'contract' => 'Kontrak',
            'intern' => 'Magang',
            default => ucfirst($this->employee_status)
        };
    }

    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->diffInYears(now()) : null;
    }

    public function getWorkDurationAttribute()
    {
        return $this->join_date ? $this->join_date->diffForHumans(now(), true) : null;
    }

    // Methods
    public function getAssignedAssetsCount()
    {
        return $this->assets()->count();
    }

    public function getActiveAssetsCount()
    {
        return $this->assets()->where('status', 'active')->count();
    }
}
