@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Konfigurasi Field Asset - Asset Management System')

@section('content')
<style>
.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-section h6 {
  color: #566a7f;
  margin-bottom: 1rem;
  font-weight: 600;
}

.field-preview {
  border: 2px dashed #e7e7ff;
  border-radius: 8px;
  padding: 1rem;
  background: #fafbff;
}

.validation-rules-container {
  display: none;
}

.field-options-container {
  display: {{ in_array($assetFieldConfiguration->field_type, ['select', 'radio', 'checkbox']) ? 'block' : 'none' }};
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-pencil-line me-2"></i>
              Edit Konfigurasi Field Asset
            </h5>
            <small class="text-muted">{{ $assetFieldConfiguration->field_label }}</small>
          </div>
          <div>
            <a href="{{ route('master.asset-field-configurations.show', $assetFieldConfiguration) }}" class="btn btn-outline-info me-2">
              <i class="ri-eye-line me-1"></i>
              Lihat Detail
            </a>
            <a href="{{ route('master.asset-field-configurations.index') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <form method="POST" action="{{ route('master.asset-field-configurations.update', $assetFieldConfiguration) }}">
    @csrf
    @method('PUT')
    
    <div class="row">
      <!-- Form Configuration -->
      <div class="col-md-8">
        <div class="card">
          <div class="card-body">
            <!-- Basic Information -->
            <div class="form-section">
              <h6><i class="ri-information-line me-2"></i>Informasi Dasar</h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_name" class="form-label">Field Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('field_name') is-invalid @enderror" 
                           id="field_name" name="field_name" value="{{ old('field_name', $assetFieldConfiguration->field_name) }}" 
                           placeholder="contoh: processor_type" required>
                    <div class="form-text">Gunakan format snake_case (huruf kecil dan underscore)</div>
                    @error('field_name')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_label" class="form-label">Field Label <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('field_label') is-invalid @enderror" 
                           id="field_label" name="field_label" value="{{ old('field_label', $assetFieldConfiguration->field_label) }}" 
                           placeholder="contoh: Tipe Processor" required>
                    <div class="form-text">Label yang akan ditampilkan di form</div>
                    @error('field_label')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_type" class="form-label">Field Type <span class="text-danger">*</span></label>
                    <select class="form-select @error('field_type') is-invalid @enderror" 
                            id="field_type" name="field_type" required>
                      <option value="">Pilih Tipe Field</option>
                      @foreach($fieldTypes as $key => $type)
                        <option value="{{ $key }}" {{ old('field_type', $assetFieldConfiguration->field_type) == $key ? 'selected' : '' }}>
                          {{ $type }}
                        </option>
                      @endforeach
                    </select>
                    @error('field_type')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="field_group" class="form-label">Field Group <span class="text-danger">*</span></label>
                    <select class="form-select @error('field_group') is-invalid @enderror" 
                            id="field_group" name="field_group" required>
                      <option value="">Pilih Grup Field</option>
                      @foreach($fieldGroups as $key => $group)
                        <option value="{{ $key }}" {{ old('field_group', $assetFieldConfiguration->field_group) == $key ? 'selected' : '' }}>
                          {{ $group }}
                        </option>
                      @endforeach
                    </select>
                    @error('field_group')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>
            </div>

            <!-- Configuration -->
            <div class="form-section">
              <h6><i class="ri-settings-4-line me-2"></i>Konfigurasi</h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="asset_category_id" class="form-label">Kategori Asset</label>
                    <select class="form-select @error('asset_category_id') is-invalid @enderror" 
                            id="asset_category_id" name="asset_category_id">
                      <option value="">Global (Semua Kategori)</option>
                      @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ old('asset_category_id', $assetFieldConfiguration->asset_category_id) == $category->id ? 'selected' : '' }}>
                          {{ $category->name }}
                        </option>
                      @endforeach
                    </select>
                    <div class="form-text">Kosongkan untuk field global</div>
                    @error('asset_category_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="sort_order" class="form-label">Urutan</label>
                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                           id="sort_order" name="sort_order" value="{{ old('sort_order', $assetFieldConfiguration->sort_order) }}" min="0">
                    <div class="form-text">Urutan tampil field (0 = paling atas)</div>
                    @error('sort_order')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="help_text" class="form-label">Help Text</label>
                <textarea class="form-control @error('help_text') is-invalid @enderror" 
                          id="help_text" name="help_text" rows="2" 
                          placeholder="Teks bantuan untuk user...">{{ old('help_text', $assetFieldConfiguration->help_text) }}</textarea>
                @error('help_text')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_required" name="is_required" value="1" 
                           {{ old('is_required', $assetFieldConfiguration->is_required) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_required">
                      Field Wajib Diisi
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                           {{ old('is_active', $assetFieldConfiguration->is_active) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                      Field Aktif
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Dependent Dropdown Configuration (only for select fields) -->
            <div class="form-section dependent-dropdown-section" style="display: {{ $assetFieldConfiguration->field_type === 'select' ? 'block' : 'none' }};">
              <h6><i class="ri-git-branch-line me-2"></i>Dependent Dropdown Configuration</h6>

              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="enable_dependent" name="enable_dependent" value="1"
                         {{ old('enable_dependent', $assetFieldConfiguration->enable_dependent) ? 'checked' : '' }}>
                  <label class="form-check-label" for="enable_dependent">
                    <strong>Enable Dependent Dropdown</strong>
                  </label>
                </div>
                <div class="form-text">Centang jika dropdown ini bergantung pada dropdown lain</div>
              </div>

              <div id="dependent-config" style="display: {{ old('enable_dependent', $assetFieldConfiguration->enable_dependent) ? 'block' : 'none' }};">
                <div class="mb-3">
                  <label for="parent_field_id" class="form-label">Parent Field</label>
                  <select class="form-select" id="parent_field_id" name="parent_field_id">
                    <option value="">Pilih Parent Field</option>
                    @foreach(\App\Models\AssetFieldConfiguration::where('field_type', 'select')->where('id', '!=', $assetFieldConfiguration->id)->active()->get() as $parentField)
                      <option value="{{ $parentField->id }}" {{ old('parent_field_id', $assetFieldConfiguration->parent_field_id) == $parentField->id ? 'selected' : '' }}>
                        {{ $parentField->field_label }} ({{ $parentField->field_name }})
                      </option>
                    @endforeach
                  </select>
                  <div class="form-text">Pilih field dropdown yang akan menjadi parent</div>
                </div>

                <div id="parent-conditions">
                  <div class="mb-3">
                    <label class="form-label">Parent Field Conditions</label>
                    <div class="alert alert-info">
                      <small>
                        <strong>Untuk Database:</strong> Gunakan format JSON seperti: <code>[{"column": "parent_id", "operator": "=", "value": "{parent_value}"}]</code><br>
                        <strong>Untuk Lookup:</strong> Akan menggunakan lookup code yang disesuaikan dengan parent value
                      </small>
                    </div>
                    <textarea class="form-control" id="parent_field_conditions" name="parent_field_conditions" rows="3"
                              placeholder='[{"column": "category_id", "operator": "=", "value": "{parent_value}"}]'>{{ old('parent_field_conditions', is_array($assetFieldConfiguration->parent_field_conditions) ? json_encode($assetFieldConfiguration->parent_field_conditions) : $assetFieldConfiguration->parent_field_conditions) }}</textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- Field Options (for select, radio, checkbox) -->
            <div class="form-section field-options-container">
              <h6><i class="ri-list-check me-2"></i>Opsi Field</h6>
              <div id="field-options">
                <div class="mb-3">
                  <label class="form-label">Options (untuk Select/Radio/Checkbox)</label>
                  <div id="options-container">
                    @if($assetFieldConfiguration->field_options && isset($assetFieldConfiguration->field_options['options']))
                      @foreach($assetFieldConfiguration->field_options['options'] as $key => $value)
                        <div class="option-item mb-2">
                          <div class="input-group">
                            <input type="text" class="form-control" name="field_options[options][{{ $key }}]" 
                                   placeholder="Key" value="{{ $key }}">
                            <input type="text" class="form-control" name="field_options[options][{{ $key }}]" 
                                   placeholder="Value" value="{{ $value }}">
                            <button type="button" class="btn btn-outline-danger remove-option">
                              <i class="ri-delete-bin-line"></i>
                            </button>
                          </div>
                        </div>
                      @endforeach
                    @else
                      <div class="option-item mb-2">
                        <div class="input-group">
                          <input type="text" class="form-control" name="field_options[options][key][]" placeholder="Key">
                          <input type="text" class="form-control" name="field_options[options][value][]" placeholder="Value">
                          <button type="button" class="btn btn-outline-danger remove-option">
                            <i class="ri-delete-bin-line"></i>
                          </button>
                        </div>
                      </div>
                    @endif
                  </div>
                  <button type="button" class="btn btn-sm btn-outline-primary" id="add-option">
                    <i class="ri-add-line me-1"></i>Tambah Option
                  </button>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.asset-field-configurations.show', $assetFieldConfiguration) }}" class="btn btn-outline-secondary">
                Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                Update Field
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview -->
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-eye-line me-2"></i>
              Preview Field
            </h6>
          </div>
          <div class="card-body">
            <div class="field-preview" id="field-preview">
              <!-- Preview will be generated by JavaScript -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const fieldTypeSelect = document.getElementById('field_type');
  const fieldLabelInput = document.getElementById('field_label');
  const fieldNameInput = document.getElementById('field_name');
  const helpTextInput = document.getElementById('help_text');
  const isRequiredCheck = document.getElementById('is_required');
  const previewContainer = document.getElementById('field-preview');
  const optionsContainer = document.querySelector('.field-options-container');
  const dependentDropdownSection = document.querySelector('.dependent-dropdown-section');
  const enableDependentCheck = document.getElementById('enable_dependent');

  // Show/hide containers based on field type
  fieldTypeSelect.addEventListener('change', function() {
    const fieldType = this.value;
    const needsOptions = ['select', 'radio', 'checkbox'].includes(fieldType);
    const isSelect = fieldType === 'select';

    // Show/hide options container
    optionsContainer.style.display = needsOptions ? 'block' : 'none';

    // Show/hide dependent dropdown section (only for select fields)
    dependentDropdownSection.style.display = isSelect ? 'block' : 'none';

    // Reset dependent dropdown if not select field
    if (!isSelect) {
      enableDependentCheck.checked = false;
      document.getElementById('dependent-config').style.display = 'none';
    }

    updatePreview();
  });

  // Show/hide dependent config when checkbox is toggled
  if (enableDependentCheck) {
    enableDependentCheck.addEventListener('change', function() {
      const dependentConfig = document.getElementById('dependent-config');
      dependentConfig.style.display = this.checked ? 'block' : 'none';

      // Reset parent field selection when disabled
      if (!this.checked) {
        document.getElementById('parent_field_id').value = '';
      }
    });
  }

  // Update preview on any change
  [fieldNameInput, fieldLabelInput, fieldTypeSelect, helpTextInput, isRequiredCheck].forEach(element => {
    element.addEventListener('input', updatePreview);
    element.addEventListener('change', updatePreview);
  });

  function updatePreview() {
    const fieldType = fieldTypeSelect.value;
    const fieldLabel = fieldLabelInput.value;
    const helpText = helpTextInput.value;
    const isRequired = isRequiredCheck.checked;

    if (!fieldType || !fieldLabel) {
      previewContainer.innerHTML = `
        <p class="text-muted text-center">
          <i class="ri-eye-off-line ri-24px d-block mb-2"></i>
          Isi form untuk melihat preview
        </p>
      `;
      return;
    }

    let fieldHtml = '';
    const requiredMark = isRequired ? '<span class="text-danger">*</span>' : '';

    switch (fieldType) {
      case 'text':
      case 'email':
      case 'url':
      case 'tel':
        fieldHtml = `<input type="${fieldType}" class="form-control" placeholder="Masukkan ${fieldLabel.toLowerCase()}">`;
        break;
      case 'number':
        fieldHtml = `<input type="number" class="form-control" placeholder="Masukkan ${fieldLabel.toLowerCase()}">`;
        break;
      case 'date':
        fieldHtml = `<input type="date" class="form-control">`;
        break;
      case 'textarea':
        fieldHtml = `<textarea class="form-control" rows="3" placeholder="Masukkan ${fieldLabel.toLowerCase()}"></textarea>`;
        break;
      case 'select':
        fieldHtml = `
          <select class="form-select">
            <option value="">Pilih ${fieldLabel}</option>
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </select>
        `;
        break;
      case 'file':
        fieldHtml = `<input type="file" class="form-control">`;
        break;
      default:
        fieldHtml = `<input type="text" class="form-control" placeholder="Masukkan ${fieldLabel.toLowerCase()}">`;
    }

    previewContainer.innerHTML = `
      <div class="mb-3">
        <label class="form-label">${fieldLabel} ${requiredMark}</label>
        ${fieldHtml}
        ${helpText ? `<div class="form-text">${helpText}</div>` : ''}
      </div>
    `;
  }

  // Add option functionality
  document.getElementById('add-option').addEventListener('click', function() {
    const container = document.getElementById('options-container');
    const newOption = document.createElement('div');
    newOption.className = 'option-item mb-2';
    newOption.innerHTML = `
      <div class="input-group">
        <input type="text" class="form-control" name="field_options[options][key][]" placeholder="Key">
        <input type="text" class="form-control" name="field_options[options][value][]" placeholder="Value">
        <button type="button" class="btn btn-outline-danger remove-option">
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>
    `;
    container.appendChild(newOption);
  });

  // Remove option functionality
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-option')) {
      e.target.closest('.option-item').remove();
    }
  });

  // Initial preview update
  updatePreview();
});
</script>

@endsection
