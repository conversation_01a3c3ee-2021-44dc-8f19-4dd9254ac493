@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Laporan Stock Opname - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('reports.stock-opnames') }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-file-text-line me-2"></i>
                  Detail Laporan Stock Opname
                </h5>
                <small class="text-muted">{{ $stockOpname->title }} - {{ $stockOpname->opname_number }}</small>
              </div>
            </div>
            <div>
              <a href="{{ route('reports.stock-opnames.export-detail', $stockOpname) }}" class="btn btn-success">
                <i class="ri-file-excel-2-line me-2"></i>
                Export Excel
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Priority Alert for Not Scanned Assets -->
  @if($groupedDetails['not_scanned']->count() > 0)
    <div class="row mb-4">
      <div class="col-12">
        <div class="alert alert-danger d-flex align-items-center" role="alert">
          <i class="ri-alert-line ri-24px me-3"></i>
          <div class="flex-grow-1">
            <h6 class="alert-heading mb-1">URGENT: Asset Belum Di-scan</h6>
            <p class="mb-0">
              Terdapat <strong>{{ $groupedDetails['not_scanned']->count() }}</strong> asset yang belum di-scan. 
              Segera lakukan scanning untuk melengkapi stock opname.
            </p>
          </div>
          <div class="ms-3">
            @if($stockOpname->status === 'in_progress')
              <a href="{{ route('stock-opnames.scan-detail', $stockOpname) }}" class="btn btn-danger btn-sm">
                <i class="ri-qr-scan-line me-1"></i>
                Scan Sekarang
              </a>
            @endif
          </div>
        </div>
      </div>
    </div>
  @endif

  <!-- Summary Cards with Priority -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-danger">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-danger">
              <i class="ri-alert-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1 text-danger">{{ $groupedDetails['not_scanned']->count() }}</h4>
          <p class="mb-0"><strong>URGENT - Belum Di-scan</strong></p>
          <small class="text-muted">Prioritas Tertinggi</small>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-warning">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-warning">
              <i class="ri-close-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1 text-warning">{{ $groupedDetails['missing']->count() }}</h4>
          <p class="mb-0"><strong>HIGH - Asset Hilang</strong></p>
          <small class="text-muted">Perlu Investigasi</small>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-info">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-info">
              <i class="ri-error-warning-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1 text-info">{{ $groupedDetails['discrepancy']->count() }}</h4>
          <p class="mb-0"><strong>MEDIUM - Ketidaksesuaian</strong></p>
          <small class="text-muted">Perlu Review</small>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card border-success">
        <div class="card-body text-center">
          <div class="avatar mx-auto mb-2">
            <span class="avatar-initial rounded bg-label-success">
              <i class="ri-check-line ri-24px"></i>
            </span>
          </div>
          <h4 class="mb-1 text-success">{{ $groupedDetails['found']->count() }}</h4>
          <p class="mb-0"><strong>NORMAL - Ditemukan</strong></p>
          <small class="text-muted">Selesai</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Progress Card -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="card-title mb-0">Progress Stock Opname</h6>
            <span class="badge bg-primary">{{ $statistics['progress'] }}%</span>
          </div>
          <div class="progress mb-2" style="height: 10px;">
            <div class="progress-bar" role="progressbar" 
                 style="width: {{ $statistics['progress'] }}%" 
                 aria-valuenow="{{ $statistics['progress'] }}" 
                 aria-valuemin="0" 
                 aria-valuemax="100"></div>
          </div>
          <div class="row text-center">
            <div class="col-3">
              <small class="text-muted">Total: {{ $statistics['total'] }}</small>
            </div>
            <div class="col-3">
              <small class="text-success">Selesai: {{ $statistics['scanned'] }}</small>
            </div>
            <div class="col-3">
              <small class="text-danger">Belum: {{ $statistics['total'] - $statistics['scanned'] }}</small>
            </div>
            <div class="col-3">
              <small class="text-warning">Hilang: {{ $statistics['missing'] }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Stock Opname Info -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Stock Opname
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Nomor Opname:</td>
                  <td><strong>{{ $stockOpname->opname_number }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Judul:</td>
                  <td>{{ $stockOpname->title }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Cabang:</td>
                  <td>{{ $stockOpname->branch->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Dibuat oleh:</td>
                  <td>{{ $stockOpname->creator->name }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="150">Tanggal Mulai:</td>
                  <td>{{ $stockOpname->start_date ? $stockOpname->start_date->format('d/m/Y H:i') : '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Tanggal Selesai:</td>
                  <td>{{ $stockOpname->end_date ? $stockOpname->end_date->format('d/m/Y H:i') : '-' }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Status:</td>
                  <td>
                    @switch($stockOpname->status)
                      @case('draft')
                        <span class="badge bg-secondary">Draft</span>
                        @break
                      @case('in_progress')
                        <span class="badge bg-warning">Sedang Berjalan</span>
                        @break
                      @case('completed')
                        <span class="badge bg-success">Selesai</span>
                        @break
                      @case('cancelled')
                        <span class="badge bg-danger">Dibatalkan</span>
                        @break
                    @endswitch
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Total Asset:</td>
                  <td><strong>{{ $stockOpname->total_assets }}</strong></td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Reports with Priority Tabs -->
  <div class="row">
    <div class="col-12">
      <!-- Nav tabs with priority colors -->
      <ul class="nav nav-tabs" id="reportTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active text-danger" id="not-scanned-tab" data-bs-toggle="tab" data-bs-target="#not-scanned" type="button" role="tab">
            <i class="ri-alert-line me-2"></i>
            URGENT - Belum Di-scan ({{ $groupedDetails['not_scanned']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link text-warning" id="missing-tab" data-bs-toggle="tab" data-bs-target="#missing" type="button" role="tab">
            <i class="ri-close-line me-2"></i>
            HIGH - Asset Hilang ({{ $groupedDetails['missing']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link text-info" id="discrepancy-tab" data-bs-toggle="tab" data-bs-target="#discrepancy" type="button" role="tab">
            <i class="ri-error-warning-line me-2"></i>
            MEDIUM - Ketidaksesuaian ({{ $groupedDetails['discrepancy']->count() }})
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link text-success" id="found-tab" data-bs-toggle="tab" data-bs-target="#found" type="button" role="tab">
            <i class="ri-check-line me-2"></i>
            NORMAL - Ditemukan ({{ $groupedDetails['found']->count() }})
          </button>
        </li>
      </ul>

      <!-- Tab content -->
      <div class="tab-content" id="reportTabsContent">
        <!-- Not Scanned Assets (URGENT) -->
        <div class="tab-pane fade show active" id="not-scanned" role="tabpanel">
          <div class="card">
            <div class="card-header bg-danger text-white">
              <h6 class="mb-0">
                <i class="ri-alert-line me-2"></i>
                URGENT: Asset yang Belum Di-scan
              </h6>
            </div>
            <div class="card-body">
              @if($groupedDetails['not_scanned']->count() > 0)
                <div class="alert alert-warning mb-3">
                  <strong>Perhatian:</strong> Asset berikut harus segera di-scan untuk melengkapi stock opname.
                </div>
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Asset</th>
                        <th>Kategori</th>
                        <th>Status Terakhir</th>
                        <th>Lokasi Terakhir</th>
                        <th>Prioritas</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($groupedDetails['not_scanned'] as $detail)
                        <tr class="table-danger">
                          <td><span class="badge bg-danger">{{ $detail->asset_code }}</span></td>
                          <td><strong>{{ $detail->asset_name }}</strong></td>
                          <td>{{ $detail->asset->assetCategory->name ?? '-' }}</td>
                          <td>
                            <span class="badge bg-info">{{ ucfirst($detail->expected_status) }}</span>
                          </td>
                          <td>{{ $detail->asset->location ?? '-' }}</td>
                          <td>
                            <span class="badge bg-danger">
                              <i class="ri-alert-line me-1"></i>
                              URGENT
                            </span>
                          </td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-check-line ri-48px text-success mb-3"></i>
                  <h6 class="text-success">Semua Asset Sudah Di-scan</h6>
                  <p class="text-muted">Tidak ada asset yang belum di-scan.</p>
                </div>
              @endif
            </div>
          </div>
        </div>

        <!-- Other tabs content would continue here... -->
        <!-- For brevity, I'll add the other tabs in the next edit -->
      </div>
    </div>
  </div>
</div>

@endsection
