<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use App\Models\AssetDigital;
use App\Models\Branch;
use App\Models\Lookup;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AssetDigitalReportExport;

class AssetDigitalReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:reports.asset-digitals.view')->only(['index']);
        $this->middleware('permission:reports.asset-digitals.export')->only(['export']);
    }

    /**
     * Display the asset digital report page
     */
    public function index(Request $request)
    {
        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $licenseTypes = Lookup::where('lookup_code', 'LIC_TYPE')->orderBy('lookup_name')->get();
        
        // Initialize query
        $query = AssetDigital::with(['branch', 'assignedTo', 'createdBy', 'supplier']);
        
        // Apply filters
        $filters = [];
        
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
            $filters['branch_id'] = $request->branch_id;
        }
        
        if ($request->filled('license_type')) {
            $query->where('license_type', $request->license_type);
            $filters['license_type'] = $request->license_type;
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
            $filters['status'] = $request->status;
        }
        
        if ($request->filled('date_from')) {
            $query->whereDate('purchase_date', '>=', $request->date_from);
            $filters['date_from'] = $request->date_from;
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('purchase_date', '<=', $request->date_to);
            $filters['date_to'] = $request->date_to;
        }
        
        if ($request->filled('expiry_from')) {
            $query->whereDate('expiry_date', '>=', $request->expiry_from);
            $filters['expiry_from'] = $request->expiry_from;
        }
        
        if ($request->filled('expiry_to')) {
            $query->whereDate('expiry_date', '<=', $request->expiry_to);
            $filters['expiry_to'] = $request->expiry_to;
        }
        
        // Get asset digitals with pagination
        $assetDigitals = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // Get statistics
        $stats = [
            'total_assets' => $query->count(),
            'total_value' => $query->sum('purchase_price'),
            'active_assets' => $query->where('status', 'active')->count(),
            'inactive_assets' => $query->where('status', 'inactive')->count(),
            'expired_assets' => $query->where('status', 'expired')->count(),
            'suspended_assets' => $query->where('status', 'suspended')->count(),
            'expiring_soon' => $query->where('expiry_date', '<=', Carbon::now()->addDays(30))->where('status', 'active')->count(),
            'total_users' => $query->sum('current_users'),
            'total_max_users' => $query->sum('max_users'),
        ];
        
        // Status options
        $statusOptions = [
            'active' => 'Aktif',
            'inactive' => 'Non-Aktif', 
            'expired' => 'Expired',
            'suspended' => 'Suspended'
        ];
        
        return view('reports.asset-digitals.index', compact(
            'assetDigitals',
            'branches', 
            'licenseTypes',
            'statusOptions',
            'filters',
            'stats'
        ));
    }
    
    /**
     * Export asset digital report to Excel
     */
    public function export(Request $request)
    {
        // Build filename with current date and filters
        $filename = 'laporan-asset-digital-' . date('Y-m-d-H-i-s');
        
        if ($request->filled('branch_id')) {
            $branch = Branch::find($request->branch_id);
            if ($branch) {
                $filename .= '-' . str_replace(' ', '-', strtolower($branch->name));
            }
        }
        
        if ($request->filled('license_type')) {
            $licenseType = Lookup::where('lookup_code', 'LIC_TYPE')->where('name', $request->license_type)->first();
            if ($licenseType) {
                $filename .= '-' . str_replace(' ', '-', strtolower($licenseType->name));
            }
        }
        
        $filename .= '.xlsx';
        
        return Excel::download(new AssetDigitalReportExport($request->all()), $filename);
    }
}
