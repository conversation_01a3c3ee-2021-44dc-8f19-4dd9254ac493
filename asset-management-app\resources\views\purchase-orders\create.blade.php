@extends('layouts.contentNavbarLayout')

@section('title', 'Buat Purchase Order - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi / Purchase Order /</span> Buat Baru
  </h4>

  <form action="{{ route('purchase-orders.store') }}" method="POST" id="poForm">
    @csrf
    
    <!-- Header Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">Informasi Purchase Order</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Nomor PO</label>
            <input type="text" class="form-control" id="po_number_preview" readonly 
                   placeholder="Akan di-generate otomatis">
          </div>
          <div class="col-md-3">
            <label class="form-label">Tanggal PO <span class="text-danger">*</span></label>
            <input type="date" class="form-control @error('po_date') is-invalid @enderror" 
                   name="po_date" value="{{ old('po_date', date('Y-m-d')) }}" required>
            @error('po_date')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Tanggal Pengiriman</label>
            <input type="date" class="form-control @error('delivery_date') is-invalid @enderror" 
                   name="delivery_date" value="{{ old('delivery_date') }}">
            @error('delivery_date')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Cabang <span class="text-danger">*</span></label>
            <select class="form-select @error('branch_id') is-invalid @enderror" name="branch_id" id="branch_id" required>
              <option value="">Pilih Cabang</option>
              @foreach($branches as $branch)
                <option value="{{ $branch->id }}" {{ old('branch_id', $userBranch) == $branch->id ? 'selected' : '' }}>
                  {{ $branch->name }}
                </option>
              @endforeach
            </select>
            @error('branch_id')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>

        <div class="row g-3 mt-2">
          <div class="col-md-6">
            <label class="form-label">Supplier <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="text" class="form-control @error('supplier_id') is-invalid @enderror" 
                     id="supplier_name" readonly placeholder="Pilih supplier...">
              <input type="hidden" name="supplier_id" id="supplier_id" value="{{ old('supplier_id') }}">
              <button type="button" class="btn btn-outline-primary" id="btn-supplier-lookup">
                <i class="ri-search-line"></i>
              </button>
            </div>
            @error('supplier_id')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Pajak (%)</label>
            <input type="number" class="form-control @error('tax_percentage') is-invalid @enderror" 
                   name="tax_percentage" value="{{ old('tax_percentage', 11) }}" min="0" max="100" step="0.01">
            @error('tax_percentage')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-3">
            <label class="form-label">Diskon (%)</label>
            <input type="number" class="form-control @error('discount_percentage') is-invalid @enderror" 
                   name="discount_percentage" value="{{ old('discount_percentage', 0) }}" min="0" max="100" step="0.01">
            @error('discount_percentage')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>

        <div class="row g-3 mt-2">
          <div class="col-md-6">
            <label class="form-label">Syarat Pembayaran</label>
            <input type="text" class="form-control @error('payment_terms') is-invalid @enderror" 
                   name="payment_terms" value="{{ old('payment_terms') }}" placeholder="Contoh: Net 30 days">
            @error('payment_terms')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-6">
            <label class="form-label">Alamat Pengiriman</label>
            <textarea class="form-control @error('delivery_address') is-invalid @enderror" 
                      name="delivery_address" rows="2" placeholder="Alamat lengkap pengiriman">{{ old('delivery_address') }}</textarea>
            @error('delivery_address')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>

    <!-- Items Section -->
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Item Purchase Order</h5>
        <button type="button" class="btn btn-primary btn-sm" id="add-item">
          <i class="ri-add-line me-1"></i>Tambah Item
        </button>
      </div>
      <div class="card-body">
        <div id="items-container">
          <!-- Items will be added here dynamically -->
        </div>
        
        <!-- Totals -->
        <div class="row mt-4">
          <div class="col-md-8"></div>
          <div class="col-md-4">
            <table class="table table-sm">
              <tr>
                <td><strong>Subtotal:</strong></td>
                <td class="text-end"><strong id="subtotal">Rp 0</strong></td>
              </tr>
              <tr>
                <td>Diskon:</td>
                <td class="text-end" id="discount">Rp 0</td>
              </tr>
              <tr>
                <td>Pajak:</td>
                <td class="text-end" id="tax">Rp 0</td>
              </tr>
              <tr class="table-primary">
                <td><strong>Total:</strong></td>
                <td class="text-end"><strong id="total">Rp 0</strong></td>
              </tr>
            </table>
            
            <!-- Terbilang -->
            <div class="mt-3">
              <small class="text-muted">Terbilang:</small>
              <div id="terbilang" class="fw-bold text-primary">-</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">Informasi Tambahan</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-6">
            <label class="form-label">Catatan</label>
            <textarea class="form-control @error('notes') is-invalid @enderror" 
                      name="notes" rows="3" placeholder="Catatan tambahan untuk PO ini">{{ old('notes') }}</textarea>
            @error('notes')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          <div class="col-md-6">
            <label class="form-label">Syarat & Ketentuan</label>
            <textarea class="form-control @error('terms_conditions') is-invalid @enderror" 
                      name="terms_conditions" rows="3" placeholder="Syarat dan ketentuan PO">{{ old('terms_conditions') }}</textarea>
            @error('terms_conditions')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <a href="{{ route('purchase-orders.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
          <div>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line me-1"></i>Simpan Purchase Order
            </button>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- Supplier Lookup Modal -->
<div class="modal fade" id="supplierLookupModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Pilih Supplier</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <input type="text" class="form-control" id="supplier-search" placeholder="Cari supplier...">
        </div>
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Kode</th>
                <th>Nama</th>
                <th>Kontak</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody id="supplier-list">
              @foreach($suppliers as $supplier)
                <tr>
                  <td>{{ $supplier->supplier_code }}</td>
                  <td>{{ $supplier->name }}</td>
                  <td>{{ $supplier->phone }}</td>
                  <td>
                    <button type="button" class="btn btn-sm btn-primary select-supplier" 
                            data-id="{{ $supplier->id }}" data-name="{{ $supplier->name }}">
                      Pilih
                    </button>
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@section('page-script')
<script>
let itemIndex = 0;

$(document).ready(function() {
    // Add first item by default
    addItem();

    // Preview PO number when branch changes
    $('#branch_id').change(function() {
        previewPoNumber();
    });

    // Initial PO number preview
    previewPoNumber();

    // Supplier lookup
    $('#btn-supplier-lookup').click(function() {
        $('#supplierLookupModal').modal('show');
    });

    // Select supplier
    $(document).on('click', '.select-supplier', function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        $('#supplier_id').val(id);
        $('#supplier_name').val(name);
        $('#supplierLookupModal').modal('hide');
    });

    // Supplier search
    $('#supplier-search').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#supplier-list tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add item
    $('#add-item').click(function() {
        addItem();
    });

    // Calculate totals when tax or discount changes
    $('input[name="tax_percentage"], input[name="discount_percentage"]').on('input', function() {
        calculateTotals();
    });
});

function previewPoNumber() {
    const branchId = $('#branch_id').val();
    if (branchId) {
        $.ajax({
            url: '{{ route("purchase-orders.preview-document-number") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                branch_id: branchId
            },
            success: function(response) {
                if (response.success) {
                    $('#po_number_preview').val(response.po_number);
                }
            }
        });
    }
}

function addItem() {
    const itemHtml = `
        <div class="item-row border rounded p-3 mb-3" data-index="${itemIndex}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Item #${itemIndex + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>

            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Nama Item <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][item_name]" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Kode Item</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][item_code]">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Merk</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][brand]">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Model</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][model]">
                </div>
            </div>

            <div class="row g-3 mt-2">
                <div class="col-md-6">
                    <label class="form-label">Deskripsi</label>
                    <textarea class="form-control" name="items[${itemIndex}][item_description]" rows="2"></textarea>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Spesifikasi</label>
                    <textarea class="form-control" name="items[${itemIndex}][specification]" rows="2"></textarea>
                </div>
            </div>

            <div class="row g-3 mt-2">
                <div class="col-md-2">
                    <label class="form-label">Qty <span class="text-danger">*</span></label>
                    <input type="number" class="form-control item-qty" name="items[${itemIndex}][quantity]"
                           min="1" value="1" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Satuan <span class="text-danger">*</span></label>
                    <select class="form-select" name="items[${itemIndex}][unit]" required>
                        <option value="pcs">Pcs</option>
                        <option value="unit">Unit</option>
                        <option value="set">Set</option>
                        <option value="box">Box</option>
                        <option value="kg">Kg</option>
                        <option value="meter">Meter</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Harga Satuan <span class="text-danger">*</span></label>
                    <input type="number" class="form-control item-price" name="items[${itemIndex}][unit_price]"
                           min="0" step="0.01" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Total Harga</label>
                    <input type="text" class="form-control item-total" readonly>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Catatan</label>
                    <textarea class="form-control" name="items[${itemIndex}][notes]" rows="1"></textarea>
                </div>
            </div>
        </div>
    `;

    $('#items-container').append(itemHtml);

    // Bind events for new item
    bindItemEvents(itemIndex);

    itemIndex++;
    updateItemNumbers();
}

function bindItemEvents(index) {
    const row = $(`.item-row[data-index="${index}"]`);

    // Remove item
    row.find('.remove-item').click(function() {
        if ($('.item-row').length > 1) {
            row.remove();
            updateItemNumbers();
            calculateTotals();
        } else {
            alert('Minimal harus ada 1 item');
        }
    });

    // Calculate item total
    row.find('.item-qty, .item-price').on('input', function() {
        calculateItemTotal(row);
        calculateTotals();
    });
}

function calculateItemTotal(row) {
    const qty = parseFloat(row.find('.item-qty').val()) || 0;
    const price = parseFloat(row.find('.item-price').val()) || 0;
    const total = qty * price;

    row.find('.item-total').val(formatCurrency(total));
}

function calculateTotals() {
    let subtotal = 0;

    $('.item-row').each(function() {
        const qty = parseFloat($(this).find('.item-qty').val()) || 0;
        const price = parseFloat($(this).find('.item-price').val()) || 0;
        subtotal += qty * price;
    });

    const discountPercentage = parseFloat($('input[name="discount_percentage"]').val()) || 0;
    const taxPercentage = parseFloat($('input[name="tax_percentage"]').val()) || 0;

    const discountAmount = subtotal * (discountPercentage / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (taxPercentage / 100);
    const total = afterDiscount + taxAmount;

    $('#subtotal').text(formatCurrency(subtotal));
    $('#discount').text(formatCurrency(discountAmount));
    $('#tax').text(formatCurrency(taxAmount));
    $('#total').text(formatCurrency(total));

    // Update terbilang
    updateTerbilang(total);
}

function updateTerbilang(amount) {
    if (amount > 0) {
        $.ajax({
            url: '{{ route("get-terbilang") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                amount: amount
            },
            success: function(response) {
                $('#terbilang').text(response.terbilang);
            },
            error: function() {
                $('#terbilang').text('-');
            }
        });
    } else {
        $('#terbilang').text('-');
    }
}

function updateItemNumbers() {
    $('.item-row').each(function(index) {
        $(this).find('h6').text(`Item #${index + 1}`);
    });
}

function formatCurrency(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
}
</script>
@endsection
