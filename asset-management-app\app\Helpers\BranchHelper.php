<?php

namespace App\Helpers;

use App\Models\Branch;
use App\Models\User;

class BranchHelper
{
    /**
     * Get current user's branch
     */
    public static function getCurrentBranch()
    {
        $branchId = session('user_branch');
        if ($branchId) {
            return Branch::find($branchId);
        }
        return null;
    }

    /**
     * Get current user's branch ID
     */
    public static function getCurrentBranchId()
    {
        return session('user_branch');
    }

    /**
     * Check if current user can access all branches (Super Admin)
     */
    public static function canAccessAllBranches()
    {
        $user = auth()->user();
        return $user && $user->isSuperAdmin();
    }

    /**
     * Get branches accessible by current user
     */
    public static function getAccessibleBranches()
    {
        $user = auth()->user();

        // Super admin can access all branches
        if (self::canAccessAllBranches()) {
            return Branch::active()->get();
        }

        // Admin can access all branches too
        if ($user && $user->isAdmin()) {
            return Branch::active()->get();
        }

        $currentBranch = self::getCurrentBranch();
        return $currentBranch ? collect([$currentBranch]) : Branch::active()->get();
    }

    /**
     * Check if user can access specific branch
     */
    public static function canAccessBranch($branchId)
    {
        if (self::canAccessAllBranches()) {
            return true;
        }

        return self::getCurrentBranchId() == $branchId;
    }

    /**
     * Apply branch filter to query for non-super-admin users
     */
    public static function applyBranchFilter($query, $branchColumn = 'branch_id')
    {
        if (!self::canAccessAllBranches()) {
            $branchId = self::getCurrentBranchId();
            if ($branchId) {
                $query->where($branchColumn, $branchId);
            }
        }

        return $query;
    }

    /**
     * Get branch statistics
     */
    public static function getBranchStats($branchId = null)
    {
        $branchId = $branchId ?: self::getCurrentBranchId();
        
        if (!$branchId) {
            return [
                'total_assets' => 0,
                'active_assets' => 0,
                'maintenance_assets' => 0,
                'total_users' => 0,
            ];
        }

        return [
            'total_assets' => \App\Models\Asset::where('branch_id', $branchId)->count(),
            'active_assets' => \App\Models\Asset::where('branch_id', $branchId)->where('status', 'active')->count(),
            'maintenance_assets' => \App\Models\Asset::where('branch_id', $branchId)->where('status', 'maintenance')->count(),
            'total_users' => User::where('branch_id', $branchId)->where('is_active', true)->count(),
        ];
    }

    /**
     * Switch branch for super admin
     */
    public static function switchBranch($branchId)
    {
        if (!self::canAccessAllBranches()) {
            return false;
        }

        $branch = Branch::find($branchId);
        if ($branch && $branch->is_active) {
            session(['user_branch' => $branchId]);
            return true;
        }

        return false;
    }

    /**
     * Reset branch to user's default branch
     */
    public static function resetToDefaultBranch()
    {
        $user = auth()->user();
        if ($user && $user->branch_id) {
            session(['user_branch' => $user->branch_id]);
            return true;
        }

        return false;
    }
}
