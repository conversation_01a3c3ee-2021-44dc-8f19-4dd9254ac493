<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContractPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create contract permissions
        $permissions = [
            [
                'name' => 'View Contracts',
                'slug' => 'contracts.view',
                'module' => 'contracts',
                'description' => 'Can view contract list and details'
            ],
            [
                'name' => 'Create Contracts',
                'slug' => 'contracts.create',
                'module' => 'contracts',
                'description' => 'Can create new contracts'
            ],
            [
                'name' => 'Edit Contracts',
                'slug' => 'contracts.edit',
                'module' => 'contracts',
                'description' => 'Can edit existing contracts'
            ],
            [
                'name' => 'Delete Contracts',
                'slug' => 'contracts.delete',
                'module' => 'contracts',
                'description' => 'Can delete contracts'
            ],
            [
                'name' => 'Download Contract Files',
                'slug' => 'contracts.download',
                'module' => 'contracts',
                'description' => 'Can download contract files'
            ],
        ];

        foreach ($permissions as $permission) {
            \App\Models\Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }

        // Assign permissions to roles using existing role_permissions table
        $rolePermissions = [
            'super-admin' => ['contracts.view', 'contracts.create', 'contracts.edit', 'contracts.delete', 'contracts.download'],
            'admin' => ['contracts.view', 'contracts.create', 'contracts.edit', 'contracts.download'],
            'manager' => ['contracts.view', 'contracts.create', 'contracts.edit', 'contracts.download'],
            'staff' => ['contracts.view', 'contracts.download']
        ];

        foreach ($rolePermissions as $roleSlug => $permissionSlugs) {
            $role = \App\Models\Role::where('slug', $roleSlug)->first();

            if ($role) {
                foreach ($permissionSlugs as $permSlug) {
                    $permission = \App\Models\Permission::where('slug', $permSlug)->first();

                    if ($permission) {
                        // Check if permission already assigned
                        $exists = \DB::table('role_permissions')
                                    ->where('role_id', $role->id)
                                    ->where('permission_id', $permission->id)
                                    ->exists();

                        if (!$exists) {
                            \DB::table('role_permissions')->insert([
                                'role_id' => $role->id,
                                'permission_id' => $permission->id,
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);
                        }
                    }
                }
            }
        }
    }
}
