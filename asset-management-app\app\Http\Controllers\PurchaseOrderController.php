<?php

namespace App\Http\Controllers;

use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Supplier;
use App\Models\Branch;
use App\Models\DocumentNumber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class PurchaseOrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:purchase-orders.view')->only(['index', 'show']);
        $this->middleware('permission:purchase-orders.create')->only(['create', 'store']);
        $this->middleware('permission:purchase-orders.edit')->only(['edit', 'update']);
        $this->middleware('permission:purchase-orders.delete')->only(['destroy']);
        $this->middleware('permission:purchase-orders.approve')->only(['approve', 'reject']);
    }

    public function index(Request $request)
    {
        $query = PurchaseOrder::with(['supplier', 'branch', 'createdBy', 'approvedBy']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('po_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('po_date', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('po_number', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($supplierQuery) use ($search) {
                      $supplierQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $purchaseOrders = $query->orderBy('created_at', 'desc')->paginate(15);
        $suppliers = Supplier::active()->get();
        $branches = Branch::active()->get();

        return view('purchase-orders.index', compact('purchaseOrders', 'suppliers', 'branches'));
    }

    public function create()
    {
        $suppliers = Supplier::active()->get();
        $branches = Branch::active()->get();
        
        // Get user's branch
        $userBranch = auth()->user()->branch_id ?? 1;
        
        return view('purchase-orders.create', compact('suppliers', 'branches', 'userBranch'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'po_date' => 'required|date',
            'delivery_date' => 'nullable|date|after_or_equal:po_date',
            'supplier_id' => 'required|exists:suppliers,id',
            'branch_id' => 'required|exists:branches,id',
            'notes' => 'nullable|string',
            'terms_conditions' => 'nullable|string',
            'payment_terms' => 'nullable|string',
            'delivery_address' => 'nullable|string',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items' => 'required|array|min:1',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_description' => 'nullable|string',
            'items.*.item_code' => 'nullable|string|max:100',
            'items.*.brand' => 'nullable|string|max:100',
            'items.*.model' => 'nullable|string|max:100',
            'items.*.specification' => 'nullable|string',
            'items.*.unit' => 'required|string|max:50',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            // Generate PO number
            $poNumber = DocumentNumber::getPurchaseOrderNumber($request->branch_id);

            // Create purchase order
            $purchaseOrder = PurchaseOrder::create([
                'po_number' => $poNumber,
                'po_date' => $request->po_date,
                'delivery_date' => $request->delivery_date,
                'supplier_id' => $request->supplier_id,
                'branch_id' => $request->branch_id,
                'created_by' => Auth::id(),
                'status' => 'draft',
                'tax_percentage' => $request->tax_percentage ?? 0,
                'discount_percentage' => $request->discount_percentage ?? 0,
                'notes' => $request->notes,
                'terms_conditions' => $request->terms_conditions,
                'payment_terms' => $request->payment_terms,
                'delivery_address' => $request->delivery_address,
            ]);

            // Create purchase order items
            foreach ($request->items as $index => $itemData) {
                PurchaseOrderItem::create([
                    'purchase_order_id' => $purchaseOrder->id,
                    'item_name' => $itemData['item_name'],
                    'item_description' => $itemData['item_description'],
                    'item_code' => $itemData['item_code'],
                    'brand' => $itemData['brand'],
                    'model' => $itemData['model'],
                    'specification' => $itemData['specification'],
                    'unit' => $itemData['unit'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'notes' => $itemData['notes'],
                    'sort_order' => $index + 1,
                ]);
            }

            // Calculate totals
            $purchaseOrder->calculateTotals();

            DB::commit();

            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('success', 'Purchase Order berhasil dibuat dengan nomor: ' . $poNumber);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()->withErrors(['error' => 'Gagal membuat Purchase Order: ' . $e->getMessage()]);
        }
    }

    public function show(PurchaseOrder $purchaseOrder)
    {
        $purchaseOrder->load(['supplier', 'branch', 'createdBy', 'approvedBy', 'receivedBy', 'items']);
        
        return view('purchase-orders.show', compact('purchaseOrder'));
    }

    public function edit(PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->canBeEdited()) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Purchase Order tidak dapat diedit karena statusnya: ' . $purchaseOrder->status_label);
        }

        $suppliers = Supplier::active()->get();
        $branches = Branch::active()->get();
        $purchaseOrder->load('items');
        
        return view('purchase-orders.edit', compact('purchaseOrder', 'suppliers', 'branches'));
    }

    public function update(Request $request, PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->canBeEdited()) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Purchase Order tidak dapat diedit karena statusnya: ' . $purchaseOrder->status_label);
        }

        $request->validate([
            'po_date' => 'required|date',
            'delivery_date' => 'nullable|date|after_or_equal:po_date',
            'supplier_id' => 'required|exists:suppliers,id',
            'branch_id' => 'required|exists:branches,id',
            'notes' => 'nullable|string',
            'terms_conditions' => 'nullable|string',
            'payment_terms' => 'nullable|string',
            'delivery_address' => 'nullable|string',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items' => 'required|array|min:1',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_description' => 'nullable|string',
            'items.*.item_code' => 'nullable|string|max:100',
            'items.*.brand' => 'nullable|string|max:100',
            'items.*.model' => 'nullable|string|max:100',
            'items.*.specification' => 'nullable|string',
            'items.*.unit' => 'required|string|max:50',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            // Update purchase order
            $purchaseOrder->update([
                'po_date' => $request->po_date,
                'delivery_date' => $request->delivery_date,
                'supplier_id' => $request->supplier_id,
                'branch_id' => $request->branch_id,
                'tax_percentage' => $request->tax_percentage ?? 0,
                'discount_percentage' => $request->discount_percentage ?? 0,
                'notes' => $request->notes,
                'terms_conditions' => $request->terms_conditions,
                'payment_terms' => $request->payment_terms,
                'delivery_address' => $request->delivery_address,
            ]);

            // Delete existing items
            $purchaseOrder->items()->delete();

            // Create new items
            foreach ($request->items as $index => $itemData) {
                PurchaseOrderItem::create([
                    'purchase_order_id' => $purchaseOrder->id,
                    'item_name' => $itemData['item_name'],
                    'item_description' => $itemData['item_description'],
                    'item_code' => $itemData['item_code'],
                    'brand' => $itemData['brand'],
                    'model' => $itemData['model'],
                    'specification' => $itemData['specification'],
                    'unit' => $itemData['unit'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'notes' => $itemData['notes'],
                    'sort_order' => $index + 1,
                ]);
            }

            // Calculate totals
            $purchaseOrder->calculateTotals();

            DB::commit();

            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('success', 'Purchase Order berhasil diupdate.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()->withErrors(['error' => 'Gagal mengupdate Purchase Order: ' . $e->getMessage()]);
        }
    }

    public function destroy(PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->canBeEdited()) {
            return redirect()->route('purchase-orders.index')
                           ->with('error', 'Purchase Order tidak dapat dihapus karena statusnya: ' . $purchaseOrder->status_label);
        }

        try {
            $purchaseOrder->delete();
            return redirect()->route('purchase-orders.index')
                           ->with('success', 'Purchase Order berhasil dihapus.');
        } catch (\Exception $e) {
            return redirect()->route('purchase-orders.index')
                           ->with('error', 'Gagal menghapus Purchase Order: ' . $e->getMessage());
        }
    }

    public function submit(PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->canBeSubmitted()) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Purchase Order tidak dapat diajukan.');
        }

        try {
            $purchaseOrder->update(['status' => 'submitted']);
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('success', 'Purchase Order berhasil diajukan untuk persetujuan.');
        } catch (\Exception $e) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Gagal mengajukan Purchase Order: ' . $e->getMessage());
        }
    }

    public function approve(Request $request, PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->canBeApproved()) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Purchase Order tidak dapat disetujui.');
        }

        $request->validate([
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $purchaseOrder->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
                'approval_notes' => $request->approval_notes,
            ]);

            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('success', 'Purchase Order berhasil disetujui.');
        } catch (\Exception $e) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Gagal menyetujui Purchase Order: ' . $e->getMessage());
        }
    }

    public function reject(Request $request, PurchaseOrder $purchaseOrder)
    {
        if (!$purchaseOrder->canBeRejected()) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Purchase Order tidak dapat ditolak.');
        }

        $request->validate([
            'approval_notes' => 'required|string|max:1000',
        ]);

        try {
            $purchaseOrder->update([
                'status' => 'rejected',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
                'approval_notes' => $request->approval_notes,
            ]);

            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('success', 'Purchase Order berhasil ditolak.');
        } catch (\Exception $e) {
            return redirect()->route('purchase-orders.show', $purchaseOrder)
                           ->with('error', 'Gagal menolak Purchase Order: ' . $e->getMessage());
        }
    }

    public function print(PurchaseOrder $purchaseOrder)
    {
        $purchaseOrder->load(['supplier', 'branch', 'createdBy', 'approvedBy', 'items']);

        $pdf = Pdf::loadView('purchase-orders.print', compact('purchaseOrder'));
        $pdf->setPaper('A4', 'portrait');

        return $pdf->stream('PO-' . $purchaseOrder->po_number . '.pdf');
    }

    public function previewDocumentNumber(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
        ]);

        try {
            $previewNumber = DocumentNumber::getPurchaseOrderNumber($request->branch_id);
            return response()->json([
                'success' => true,
                'po_number' => $previewNumber
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal generate nomor PO: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API endpoint for PO lookup
     */
    public function lookup(Request $request)
    {
        $query = PurchaseOrder::with(['supplier', 'branch'])
            ->where('status', '!=', 'draft')
            ->where('status', '!=', 'rejected')
            ->where('status', '!=', 'cancelled');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('po_number', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('po_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('po_date', '<=', $request->date_to);
        }

        // Apply branch isolation for non-admin users
        $user = auth()->user();
        $userPermissions = session('user_permissions', []);

        if (!in_array($user->role, ['Admin', 'Super Admin', 'IT']) &&
            !in_array('purchase-orders.view.all', $userPermissions)) {
            $query->where('branch_id', $user->branch_id);
        }

        // Order by latest
        $query->orderBy('po_date', 'desc')->orderBy('created_at', 'desc');

        // Paginate results
        $purchaseOrders = $query->paginate(10);

        // Transform data for API response
        $data = $purchaseOrders->getCollection()->map(function ($po) {
            return [
                'id' => $po->id,
                'po_number' => $po->po_number,
                'po_date' => $po->po_date->format('Y-m-d'),
                'supplier_name' => $po->supplier->name ?? 'N/A',
                'total_amount' => $po->total_amount,
                'status' => $po->status,
                'branch_name' => $po->branch->name ?? 'N/A',
            ];
        });

        return response()->json([
            'data' => $data,
            'current_page' => $purchaseOrders->currentPage(),
            'last_page' => $purchaseOrders->lastPage(),
            'per_page' => $purchaseOrders->perPage(),
            'total' => $purchaseOrders->total(),
            'from' => $purchaseOrders->firstItem(),
            'to' => $purchaseOrders->lastItem(),
        ]);
    }
}
