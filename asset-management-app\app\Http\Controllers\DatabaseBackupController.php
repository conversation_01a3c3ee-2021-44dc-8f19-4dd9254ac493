<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Artisan;
use Carbon\Carbon;

class DatabaseBackupController extends Controller
{
    /**
     * Display backup management page
     */
    public function index()
    {
        // Get list of existing backups
        $backups = $this->getBackupList();

        // Get backup statistics
        $stats = $this->getBackupStatistics();

        return view('settings.backup.index', compact('backups', 'stats'));
    }

    /**
     * Create database backup
     */
    public function createBackup(Request $request)
    {
        try {
            $backupName = $request->input('backup_name', 'backup_' . date('Y-m-d_H-i-s'));
            $includeData = $request->boolean('include_data', true);

            // Validate backup name
            $backupName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $backupName);

            $filename = $backupName . '.sql';
            $backupPath = storage_path('app/backups');

            // Create backup directory if not exists
            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
            }

            $fullPath = $backupPath . '/' . $filename;

            // Get database configuration
            $dbHost = config('database.connections.mysql.host');
            $dbPort = config('database.connections.mysql.port');
            $dbName = config('database.connections.mysql.database');
            $dbUser = config('database.connections.mysql.username');
            $dbPass = config('database.connections.mysql.password');

            // Try mysqldump first, fallback to PHP-based backup
            $backupSuccess = false;

            // Check if mysqldump is available
            $mysqldumpPath = $this->findMysqldump();

            if ($mysqldumpPath) {
                // Use mysqldump
                $command = sprintf(
                    '%s --host=%s --port=%s --user=%s --password=%s %s %s > %s',
                    $mysqldumpPath,
                    escapeshellarg($dbHost),
                    escapeshellarg($dbPort),
                    escapeshellarg($dbUser),
                    escapeshellarg($dbPass),
                    $includeData ? '' : '--no-data',
                    escapeshellarg($dbName),
                    escapeshellarg($fullPath)
                );

                $output = [];
                $returnCode = 0;
                exec($command . ' 2>&1', $output, $returnCode);

                if ($returnCode === 0) {
                    $backupSuccess = true;
                }
            }

            // Fallback to PHP-based backup if mysqldump failed or not available
            if (!$backupSuccess) {
                $this->createPhpBackup($fullPath, $includeData);
            }

            // Save backup info to database
            $this->saveBackupInfo($filename, $backupName, $includeData, filesize($fullPath));

            return redirect()->route('backup.index')
                           ->with('success', 'Database backup berhasil dibuat: ' . $filename);

        } catch (\Exception $e) {
            \Log::error('Database backup failed: ' . $e->getMessage());

            return redirect()->route('backup.index')
                           ->with('error', 'Backup gagal: ' . $e->getMessage());
        }
    }

    /**
     * Download backup file
     */
    public function downloadBackup($filename)
    {
        $backupPath = storage_path('app/backups/' . $filename);

        if (!file_exists($backupPath)) {
            return redirect()->route('backup.index')
                           ->with('error', 'File backup tidak ditemukan.');
        }

        return Response::download($backupPath);
    }

    /**
     * Delete backup file
     */
    public function deleteBackup($filename)
    {
        try {
            $backupPath = storage_path('app/backups/' . $filename);

            if (file_exists($backupPath)) {
                unlink($backupPath);
            }

            // Delete from database record
            DB::table('backup_logs')->where('filename', $filename)->delete();

            return redirect()->route('backup.index')
                           ->with('success', 'Backup berhasil dihapus.');

        } catch (\Exception $e) {
            return redirect()->route('backup.index')
                           ->with('error', 'Gagal menghapus backup: ' . $e->getMessage());
        }
    }

    /**
     * Restore database from backup
     */
    public function restoreBackup(Request $request)
    {
        $request->validate([
            'backup_file' => 'required|string',
            'confirm_restore' => 'required|accepted'
        ]);

        try {
            $filename = $request->input('backup_file');
            $backupPath = storage_path('app/backups/' . $filename);

            if (!file_exists($backupPath)) {
                throw new \Exception('File backup tidak ditemukan.');
            }

            // Get database configuration
            $dbHost = config('database.connections.mysql.host');
            $dbPort = config('database.connections.mysql.port');
            $dbName = config('database.connections.mysql.database');
            $dbUser = config('database.connections.mysql.username');
            $dbPass = config('database.connections.mysql.password');

            // Build mysql restore command
            $command = sprintf(
                'mysql --host=%s --port=%s --user=%s --password=%s %s < %s',
                escapeshellarg($dbHost),
                escapeshellarg($dbPort),
                escapeshellarg($dbUser),
                escapeshellarg($dbPass),
                escapeshellarg($dbName),
                escapeshellarg($backupPath)
            );

            // Execute restore command
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new \Exception('Restore failed: ' . implode("\n", $output));
            }

            // Log restore activity
            $this->logRestoreActivity($filename);

            return redirect()->route('backup.index')
                           ->with('success', 'Database berhasil di-restore dari: ' . $filename);

        } catch (\Exception $e) {
            \Log::error('Database restore failed: ' . $e->getMessage());

            return redirect()->route('backup.index')
                           ->with('error', 'Restore gagal: ' . $e->getMessage());
        }
    }

    /**
     * Upload backup file for restore
     */
    public function uploadBackup(Request $request)
    {
        $request->validate([
            'backup_upload' => 'required|file|mimes:sql|max:102400' // Max 100MB
        ]);

        try {
            $file = $request->file('backup_upload');
            $filename = 'uploaded_' . date('Y-m-d_H-i-s') . '.sql';

            $backupPath = storage_path('app/backups');
            if (!file_exists($backupPath)) {
                mkdir($backupPath, 0755, true);
            }

            $file->move($backupPath, $filename);

            // Save upload info
            $this->saveBackupInfo($filename, 'Uploaded Backup', true, $file->getSize(), 'uploaded');

            return redirect()->route('backup.index')
                           ->with('success', 'File backup berhasil diupload: ' . $filename);

        } catch (\Exception $e) {
            return redirect()->route('backup.index')
                           ->with('error', 'Upload gagal: ' . $e->getMessage());
        }
    }

    /**
     * Get list of backup files
     */
    private function getBackupList()
    {
        $backupPath = storage_path('app/backups');
        $backups = [];

        if (file_exists($backupPath)) {
            $files = glob($backupPath . '/*.sql');

            foreach ($files as $file) {
                $filename = basename($file);

                // Get backup info from database if exists
                $backupLog = DB::table('backup_logs')
                              ->leftJoin('users', 'backup_logs.created_by', '=', 'users.id')
                              ->where('backup_logs.filename', $filename)
                              ->select('backup_logs.*', 'users.name as creator_name')
                              ->first();

                $backups[] = [
                    'filename' => $filename,
                    'size' => filesize($file),
                    'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                    'formatted_size' => $this->formatBytes(filesize($file)),
                    'backup_name' => $backupLog->backup_name ?? 'Unknown',
                    'type' => $backupLog->type ?? 'manual',
                    'include_data' => $backupLog->include_data ?? true,
                    'created_by' => $backupLog->creator_name ?? 'Unknown User',
                    'created_by_id' => $backupLog->created_by ?? null
                ];
            }

            // Sort by creation time (newest first)
            usort($backups, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });
        }

        return $backups;
    }

    /**
     * Save backup information to database
     */
    private function saveBackupInfo($filename, $backupName, $includeData, $fileSize, $type = 'manual')
    {
        // Create backup_logs table if not exists
        if (!DB::getSchemaBuilder()->hasTable('backup_logs')) {
            DB::statement("
                CREATE TABLE backup_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    filename VARCHAR(255) NOT NULL,
                    backup_name VARCHAR(255) NOT NULL,
                    type ENUM('manual', 'automatic', 'uploaded') DEFAULT 'manual',
                    include_data BOOLEAN DEFAULT TRUE,
                    file_size BIGINT DEFAULT 0,
                    created_by INT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
        }

        DB::table('backup_logs')->insert([
            'filename' => $filename,
            'backup_name' => $backupName,
            'type' => $type,
            'include_data' => $includeData,
            'file_size' => $fileSize,
            'created_by' => auth()->id(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Log restore activity
     */
    private function logRestoreActivity($filename)
    {
        // Create restore_logs table if not exists
        if (!DB::getSchemaBuilder()->hasTable('restore_logs')) {
            DB::statement("
                CREATE TABLE restore_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    filename VARCHAR(255) NOT NULL,
                    restored_by INT NULL,
                    restored_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
        }

        DB::table('restore_logs')->insert([
            'filename' => $filename,
            'restored_by' => auth()->id(),
            'restored_at' => now()
        ]);
    }

    /**
     * Find mysqldump executable
     */
    private function findMysqldump()
    {
        $paths = [
            'mysqldump', // System PATH
            'C:\\xampp\\mysql\\bin\\mysqldump.exe', // XAMPP Windows
            'C:\\wamp64\\bin\\mysql\\mysql8.0.31\\bin\\mysqldump.exe', // WAMP Windows
            '/usr/bin/mysqldump', // Linux
            '/usr/local/bin/mysqldump', // macOS
        ];

        foreach ($paths as $path) {
            $output = [];
            $returnCode = 0;
            exec($path . ' --version 2>&1', $output, $returnCode);

            if ($returnCode === 0) {
                return $path;
            }
        }

        return null;
    }

    /**
     * Create backup using PHP (fallback method)
     */
    private function createPhpBackup($filePath, $includeData = true)
    {
        $sql = '';
        $sql .= "-- Database Backup\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: " . config('database.connections.mysql.database') . "\n\n";

        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

        // Get all tables
        $tables = DB::select('SHOW TABLES');
        $dbName = config('database.connections.mysql.database');
        $tableKey = 'Tables_in_' . $dbName;

        foreach ($tables as $table) {
            $tableName = $table->$tableKey;

            // Skip certain system tables
            if (in_array($tableName, ['migrations', 'password_resets', 'failed_jobs'])) {
                continue;
            }

            $sql .= "-- Table structure for table `{$tableName}`\n";
            $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";

            // Get CREATE TABLE statement
            $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`");
            $sql .= $createTable[0]->{'Create Table'} . ";\n\n";

            // Get table data if requested
            if ($includeData) {
                $rows = DB::table($tableName)->get();

                if ($rows->count() > 0) {
                    $sql .= "-- Dumping data for table `{$tableName}`\n";

                    foreach ($rows as $row) {
                        $values = [];
                        foreach ($row as $value) {
                            if (is_null($value)) {
                                $values[] = 'NULL';
                            } else {
                                $values[] = "'" . addslashes($value) . "'";
                            }
                        }

                        $columns = implode('`, `', array_keys((array)$row));
                        $sql .= "INSERT INTO `{$tableName}` (`{$columns}`) VALUES (" . implode(', ', $values) . ");\n";
                    }
                    $sql .= "\n";
                }
            }
        }

        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

        // Write to file
        file_put_contents($filePath, $sql);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get backup statistics
     */
    private function getBackupStatistics()
    {
        $stats = [
            'total_backups' => 0,
            'total_size' => 0,
            'last_backup' => null,
            'most_active_user' => null,
            'backup_types' => []
        ];

        // Check if backup_logs table exists
        if (DB::getSchemaBuilder()->hasTable('backup_logs')) {
            // Get total backups and most active user
            $backupStats = DB::table('backup_logs')
                            ->leftJoin('users', 'backup_logs.created_by', '=', 'users.id')
                            ->selectRaw('
                                COUNT(*) as total,
                                SUM(file_size) as total_size,
                                MAX(backup_logs.created_at) as last_backup,
                                users.name as creator_name,
                                COUNT(backup_logs.created_by) as user_backup_count
                            ')
                            ->groupBy('users.id', 'users.name')
                            ->orderBy('user_backup_count', 'desc')
                            ->first();

            if ($backupStats) {
                $stats['total_backups'] = $backupStats->total;
                $stats['total_size'] = $backupStats->total_size;
                $stats['last_backup'] = $backupStats->last_backup;
                $stats['most_active_user'] = $backupStats->creator_name;
            }

            // Get backup types distribution
            $typeStats = DB::table('backup_logs')
                          ->selectRaw('type, COUNT(*) as count')
                          ->groupBy('type')
                          ->get();

            foreach ($typeStats as $typeStat) {
                $stats['backup_types'][$typeStat->type] = $typeStat->count;
            }
        }

        return $stats;
    }
}
