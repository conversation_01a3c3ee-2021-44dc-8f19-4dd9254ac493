<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('approval_workflow_id')->constrained(); // Workflow yang digunakan
            $table->foreignId('approval_level_id')->constrained(); // Level approval
            $table->morphs('approvable'); // Polymorphic relation (asset_requests, purchase_orders, dst)
            $table->foreignId('approver_id')->nullable()->constrained('users'); // User yang melakukan approval
            $table->enum('status', ['pending', 'approved', 'rejected', 'skipped', 'timeout']); // Status approval
            $table->text('notes')->nullable(); // Catatan approver
            $table->timestamp('approved_at')->nullable(); // Waktu approval
            $table->timestamp('timeout_at')->nullable(); // Waktu timeout
            $table->json('metadata')->nullable(); // Data tambahan
            $table->timestamps();

            $table->index(['status', 'created_at']);
            $table->index(['approver_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_histories');
    }
};
