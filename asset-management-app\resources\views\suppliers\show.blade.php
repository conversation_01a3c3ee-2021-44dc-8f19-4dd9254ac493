@extends('layouts/contentNavbarLayout')

@section('title', 'Detail Supplier')

@section('page-style')
<style>
.info-card {
  border-left: 4px solid #007bff;
  background: #f8f9fa;
}

.info-row {
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #6c757d;
  min-width: 120px;
  font-size: 0.875rem;
}

.info-value {
  flex: 1;
  font-weight: 500;
}

.status-active {
  color: #28a745;
}

.status-inactive {
  color: #6c757d;
}

.section-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  margin: -1rem -1rem 1rem -1rem;
  padding: 1rem;
  border-radius: 0.375rem 0.375rem 0 0;
}

.quick-action-btn {
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  transform: translateY(-1px);
}

.supplier-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Supplier Header -->
  <div class="row">
    <div class="col-12">
      <div class="supplier-header">
        <div class="d-flex justify-content-between align-items-start">
          <div class="flex-grow-1">
            <div class="d-flex align-items-center mb-2">
              <div class="avatar avatar-lg me-3">
                <span class="avatar-initial rounded-circle bg-primary">
                  <i class="ri-building-line ri-24px"></i>
                </span>
              </div>
              <div>
                <h4 class="mb-1">{{ $supplier->name }}</h4>
                <div class="d-flex gap-2 align-items-center flex-wrap">
                  <span class="badge bg-primary fs-6">{{ $supplier->supplier_code }}</span>
                  {!! $supplier->status_badge !!}
                  @if($supplier->business_type)
                    <span class="badge bg-info">{{ $supplier->business_type }}</span>
                  @endif
                  <span class="badge bg-secondary">{{ $supplier->payment_terms_text }}</span>
                </div>
              </div>
            </div>

            @if($supplier->company_name)
              <p class="text-muted mb-2">
                <i class="ri-building-2-line me-2"></i>
                <strong>{{ $supplier->company_name }}</strong>
              </p>
            @endif

            <div class="row g-3">
              @if($supplier->city || $supplier->province)
                <div class="col-auto">
                  <small class="text-muted d-block">Lokasi</small>
                  <span class="fw-medium">
                    <i class="ri-map-pin-line me-1"></i>
                    {{ $supplier->city }}{{ $supplier->city && $supplier->province ? ', ' : '' }}{{ $supplier->province }}
                  </span>
                </div>
              @endif

              @if($supplier->phone)
                <div class="col-auto">
                  <small class="text-muted d-block">Telepon</small>
                  <span class="fw-medium">
                    <i class="ri-phone-line me-1"></i>
                    {{ $supplier->phone }}
                  </span>
                </div>
              @endif

              @if($supplier->email)
                <div class="col-auto">
                  <small class="text-muted d-block">Email</small>
                  <span class="fw-medium">
                    <i class="ri-mail-line me-1"></i>
                    {{ $supplier->email }}
                  </span>
                </div>
              @endif

              @if($supplier->credit_limit > 0)
                <div class="col-auto">
                  <small class="text-muted d-block">Limit Kredit</small>
                  <span class="fw-medium text-success">
                    <i class="ri-money-dollar-circle-line me-1"></i>
                    Rp {{ number_format($supplier->credit_limit, 0, ',', '.') }}
                  </span>
                </div>
              @endif
            </div>
          </div>

          <div class="d-flex gap-2">
            <a href="{{ route('suppliers.edit', $supplier) }}" class="btn btn-primary quick-action-btn">
              <i class="ri-edit-line me-1"></i>
              Edit
            </a>
            <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary quick-action-btn">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Left Column: Contact & Address -->
    <div class="col-lg-6 mb-4">
      <div class="card info-card h-100">
        <div class="section-header">
          <h6 class="mb-0">
            <i class="ri-contacts-line me-2"></i>
            Informasi Kontak & Alamat
          </h6>
        </div>
        <div class="card-body">
          <!-- Contact Information -->
          @if($supplier->phone)
            <div class="info-row">
              <span class="info-label">Telepon:</span>
              <div class="info-value">
                <strong>{{ $supplier->phone }}</strong>
                <a href="tel:{{ $supplier->phone }}" class="btn btn-sm btn-outline-primary ms-2">
                  <i class="ri-phone-line"></i>
                </a>
              </div>
            </div>
          @endif

          @if($supplier->fax)
            <div class="info-row">
              <span class="info-label">Fax:</span>
              <span class="info-value"><strong>{{ $supplier->fax }}</strong></span>
            </div>
          @endif

          @if($supplier->email)
            <div class="info-row">
              <span class="info-label">Email:</span>
              <div class="info-value">
                <strong>{{ $supplier->email }}</strong>
                <a href="mailto:{{ $supplier->email }}" class="btn btn-sm btn-outline-primary ms-2">
                  <i class="ri-mail-line"></i>
                </a>
              </div>
            </div>
          @endif

          @if($supplier->website)
            <div class="info-row">
              <span class="info-label">Website:</span>
              <div class="info-value">
                <strong>{{ $supplier->website }}</strong>
                <a href="{{ $supplier->website }}" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                  <i class="ri-external-link-line"></i>
                </a>
              </div>
            </div>
          @endif

          <!-- Address Information -->
          @if($supplier->address)
            <div class="info-row">
              <span class="info-label">Alamat:</span>
              <span class="info-value"><strong>{{ $supplier->address }}</strong></span>
            </div>
          @endif

          @if($supplier->city)
            <div class="info-row">
              <span class="info-label">Kota:</span>
              <span class="info-value"><strong>{{ $supplier->city }}</strong></span>
            </div>
          @endif

          @if($supplier->province)
            <div class="info-row">
              <span class="info-label">Provinsi:</span>
              <span class="info-value"><strong>{{ $supplier->province }}</strong></span>
            </div>
          @endif

          @if($supplier->postal_code)
            <div class="info-row">
              <span class="info-label">Kode Pos:</span>
              <span class="info-value"><strong>{{ $supplier->postal_code }}</strong></span>
            </div>
          @endif

          @if($supplier->country)
            <div class="info-row">
              <span class="info-label">Negara:</span>
              <span class="info-value"><strong>{{ $supplier->country }}</strong></span>
            </div>
          @endif

          <!-- Contact Person -->
          @if($supplier->contact_person || $supplier->contact_phone || $supplier->contact_email)
            <hr class="my-3">
            <h6 class="text-primary mb-3">Kontak Person</h6>

            @if($supplier->contact_person)
              <div class="info-row">
                <span class="info-label">Nama:</span>
                <span class="info-value"><strong>{{ $supplier->contact_person }}</strong></span>
              </div>
            @endif

            @if($supplier->contact_phone)
              <div class="info-row">
                <span class="info-label">Telepon:</span>
                <div class="info-value">
                  <strong>{{ $supplier->contact_phone }}</strong>
                  <a href="tel:{{ $supplier->contact_phone }}" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="ri-phone-line"></i>
                  </a>
                </div>
              </div>
            @endif

            @if($supplier->contact_email)
              <div class="info-row">
                <span class="info-label">Email:</span>
                <div class="info-value">
                  <strong>{{ $supplier->contact_email }}</strong>
                  <a href="mailto:{{ $supplier->contact_email }}" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="ri-mail-line"></i>
                  </a>
                </div>
              </div>
            @endif
          @endif
        </div>
      </div>
    </div>

    <!-- Right Column: Business Information -->
    <div class="col-lg-6 mb-4">
      <div class="card info-card h-100">
        <div class="section-header">
          <h6 class="mb-0">
            <i class="ri-briefcase-line me-2"></i>
            Informasi Bisnis & Dokumen
          </h6>
        </div>
        <div class="card-body">
          <!-- Business Information -->
          <div class="info-row">
            <span class="info-label">Kode Supplier:</span>
            <span class="info-value"><strong class="text-primary">{{ $supplier->supplier_code }}</strong></span>
          </div>

          <div class="info-row">
            <span class="info-label">Nama Supplier:</span>
            <span class="info-value"><strong>{{ $supplier->name }}</strong></span>
          </div>

          @if($supplier->company_name)
            <div class="info-row">
              <span class="info-label">Perusahaan:</span>
              <span class="info-value"><strong>{{ $supplier->company_name }}</strong></span>
            </div>
          @endif

          @if($supplier->business_type)
            <div class="info-row">
              <span class="info-label">Jenis Usaha:</span>
              <span class="info-value">
                <span class="badge bg-info">{{ $supplier->business_type }}</span>
              </span>
            </div>
          @endif

          <div class="info-row">
            <span class="info-label">Syarat Bayar:</span>
            <span class="info-value">
              <span class="badge bg-secondary">{{ $supplier->payment_terms_text }}</span>
            </span>
          </div>

          <div class="info-row">
            <span class="info-label">Limit Kredit:</span>
            <span class="info-value">
              <strong class="text-success">Rp {{ number_format($supplier->credit_limit, 0, ',', '.') }}</strong>
            </span>
          </div>

          <div class="info-row">
            <span class="info-label">Status:</span>
            <span class="info-value">
              {!! $supplier->status_badge !!}
            </span>
          </div>

          <!-- Tax Information -->
          @if($supplier->tax_number || $supplier->npwp_file)
            <hr class="my-3">
            <h6 class="text-primary mb-3">Dokumen Pajak</h6>

            @if($supplier->tax_number)
              <div class="info-row">
                <span class="info-label">Nomor NPWP:</span>
                <span class="info-value"><strong>{{ $supplier->tax_number }}</strong></span>
              </div>
            @endif

            @if($supplier->npwp_file)
              <div class="info-row">
                <span class="info-label">File NPWP:</span>
                <div class="info-value">
                  <div class="d-flex gap-2 flex-wrap">
                    <a href="{{ $supplier->npwp_file_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                      <i class="ri-eye-line me-1"></i>
                      Lihat
                    </a>
                    <a href="{{ route('suppliers.download-npwp', $supplier) }}" class="btn btn-sm btn-outline-success">
                      <i class="ri-download-line me-1"></i>
                      Download
                    </a>
                    <form action="{{ route('suppliers.delete-npwp', $supplier) }}" method="POST" class="d-inline"
                          onsubmit="return confirm('Apakah Anda yakin ingin menghapus file NPWP ini?')">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-sm btn-outline-danger">
                        <i class="ri-delete-bin-line me-1"></i>
                        Hapus
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            @endif
          @endif

          <!-- System Information -->
          <hr class="my-3">
          <h6 class="text-primary mb-3">Informasi Sistem</h6>

          <div class="info-row">
            <span class="info-label">Dibuat:</span>
            <span class="info-value">
              <strong>{{ $supplier->created_at->format('d/m/Y H:i') }}</strong>
              <small class="text-muted">({{ $supplier->created_at->diffForHumans() }})</small>
            </span>
          </div>

          <div class="info-row">
            <span class="info-label">Diperbarui:</span>
            <span class="info-value">
              <strong>{{ $supplier->updated_at->format('d/m/Y H:i') }}</strong>
              <small class="text-muted">({{ $supplier->updated_at->diffForHumans() }})</small>
            </span>
          </div>

          @if($supplier->notes)
            <hr class="my-3">
            <h6 class="text-primary mb-3">Catatan</h6>
            <div class="alert alert-light">
              <i class="ri-sticky-note-line me-2"></i>
              {{ $supplier->notes }}
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card text-center">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <div class="avatar avatar-md me-2">
              <span class="avatar-initial rounded-circle bg-primary">
                <i class="ri-computer-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ $stats['total_assets'] }}</h4>
              <small class="text-muted">Total Asset</small>
            </div>
          </div>
          <div class="d-flex justify-content-center gap-2">
            <span class="badge bg-success">{{ $stats['active_assets'] }} Aktif</span>
            <span class="badge bg-secondary">{{ $stats['total_assets'] - $stats['active_assets'] }} Non-Aktif</span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-center">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <div class="avatar avatar-md me-2">
              <span class="avatar-initial rounded-circle bg-info">
                <i class="ri-file-text-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ $stats['total_contracts'] }}</h4>
              <small class="text-muted">Total Kontrak</small>
            </div>
          </div>
          <div class="d-flex justify-content-center gap-2">
            <span class="badge bg-success">{{ $stats['active_contracts'] }} Aktif</span>
            <span class="badge bg-secondary">{{ $stats['total_contracts'] - $stats['active_contracts'] }} Non-Aktif</span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-center">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <div class="avatar avatar-md me-2">
              <span class="avatar-initial rounded-circle bg-warning">
                <i class="ri-key-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ $stats['total_licenses'] }}</h4>
              <small class="text-muted">Total Lisensi</small>
            </div>
          </div>
          <div class="d-flex justify-content-center gap-2">
            <span class="badge bg-success">{{ $stats['active_licenses'] }} Aktif</span>
            <span class="badge bg-secondary">{{ $stats['total_licenses'] - $stats['active_licenses'] }} Non-Aktif</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Related Data Tabs -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Data Terkait Supplier</h5>
        </div>
        <div class="card-body">
          <!-- Nav tabs -->
          <ul class="nav nav-tabs" id="supplierDataTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="assets-tab" data-bs-toggle="tab" data-bs-target="#assets" type="button" role="tab">
                <i class="ri-computer-line me-1"></i>Asset ({{ $stats['total_assets'] }})
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="contracts-tab" data-bs-toggle="tab" data-bs-target="#contracts" type="button" role="tab">
                <i class="ri-file-text-line me-1"></i>Kontrak ({{ $stats['total_contracts'] }})
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="licenses-tab" data-bs-toggle="tab" data-bs-target="#licenses" type="button" role="tab">
                <i class="ri-key-line me-1"></i>Lisensi Digital ({{ $stats['total_licenses'] }})
              </button>
            </li>
          </ul>

          <!-- Tab panes -->
          <div class="tab-content mt-3" id="supplierDataTabsContent">
            <!-- Assets Tab -->
            <div class="tab-pane fade show active" id="assets" role="tabpanel">
              @if($supplier->assets->count() > 0)
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama</th>
                        <th>Kategori</th>
                        <th>Cabang</th>
                        <th>Status</th>
                        <th>Tanggal Beli</th>
                        <th>Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($supplier->assets->take(10) as $asset)
                      <tr>
                        <td><span class="badge bg-info">{{ $asset->asset_code }}</span></td>
                        <td>{{ $asset->name }}</td>
                        <td>{{ $asset->category->name ?? '-' }}</td>
                        <td>{{ $asset->branch->name ?? '-' }}</td>
                        <td>
                          <span class="badge bg-{{ $asset->status_badge }}">
                            {{ ucfirst($asset->status) }}
                          </span>
                        </td>
                        <td>{{ $asset->purchase_date ? $asset->purchase_date->format('d/m/Y') : '-' }}</td>
                        <td>
                          <a href="{{ route('assets.show', $asset) }}" class="btn btn-sm btn-outline-info">
                            <i class="ri-eye-line"></i>
                          </a>
                        </td>
                      </tr>
                      @endforeach
                    </tbody>
                  </table>
                  @if($supplier->assets->count() > 10)
                    <div class="text-center mt-3">
                      <a href="{{ route('assets.view-all', ['supplier_id' => $supplier->id]) }}" class="btn btn-outline-primary">
                        Lihat Semua Asset ({{ $supplier->assets->count() }})
                      </a>
                    </div>
                  @endif
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-computer-line ri-48px text-muted mb-3"></i>
                  <p class="text-muted">Belum ada asset dari supplier ini</p>
                </div>
              @endif
            </div>

            <!-- Contracts Tab -->
            <div class="tab-pane fade" id="contracts" role="tabpanel">
              @if($supplier->contracts->count() > 0)
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>Nomor Kontrak</th>
                        <th>Nama Kontrak</th>
                        <th>Divisi</th>
                        <th>Tanggal Mulai</th>
                        <th>Tanggal Berakhir</th>
                        <th>Status</th>
                        <th>Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($supplier->contracts->take(10) as $contract)
                      <tr>
                        <td><span class="badge bg-primary">{{ $contract->contract_number }}</span></td>
                        <td>{{ $contract->contract_name }}</td>
                        <td>{{ $contract->division->name ?? '-' }}</td>
                        <td>{{ $contract->start_date->format('d/m/Y') }}</td>
                        <td>{{ $contract->end_date->format('d/m/Y') }}</td>
                        <td>
                          <span class="badge bg-{{ $contract->status_badge }}">
                            {{ $contract->status_text }}
                          </span>
                        </td>
                        <td>
                          <a href="{{ route('contracts.show', $contract) }}" class="btn btn-sm btn-outline-info">
                            <i class="ri-eye-line"></i>
                          </a>
                        </td>
                      </tr>
                      @endforeach
                    </tbody>
                  </table>
                  @if($supplier->contracts->count() > 10)
                    <div class="text-center mt-3">
                      <a href="{{ route('contracts.index', ['supplier_id' => $supplier->id]) }}" class="btn btn-outline-primary">
                        Lihat Semua Kontrak ({{ $supplier->contracts->count() }})
                      </a>
                    </div>
                  @endif
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-file-text-line ri-48px text-muted mb-3"></i>
                  <p class="text-muted">Belum ada kontrak dengan supplier ini</p>
                </div>
              @endif
            </div>

            <!-- Licenses Tab -->
            <div class="tab-pane fade" id="licenses" role="tabpanel">
              @if($supplier->assetDigitals->count() > 0)
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>Kode Asset</th>
                        <th>Nama Lisensi</th>
                        <th>Tipe Lisensi</th>
                        <th>Cabang</th>
                        <th>Assigned To</th>
                        <th>Tanggal Expired</th>
                        <th>Status</th>
                        <th>Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($supplier->assetDigitals->take(10) as $license)
                      <tr>
                        <td><span class="badge bg-warning">{{ $license->asset_code }}</span></td>
                        <td>{{ $license->name }}</td>
                        <td>{{ $license->license_type }}</td>
                        <td>{{ $license->branch->name ?? '-' }}</td>
                        <td>{{ $license->assignedTo->name ?? '-' }}</td>
                        <td>{{ $license->expiry_date ? $license->expiry_date->format('d/m/Y') : '-' }}</td>
                        <td>
                          <span class="badge bg-{{ $license->status == 'active' ? 'success' : 'secondary' }}">
                            {{ ucfirst($license->status) }}
                          </span>
                        </td>
                        <td>
                          <a href="{{ route('asset-digitals.show', $license) }}" class="btn btn-sm btn-outline-info">
                            <i class="ri-eye-line"></i>
                          </a>
                        </td>
                      </tr>
                      @endforeach
                    </tbody>
                  </table>
                  @if($supplier->assetDigitals->count() > 10)
                    <div class="text-center mt-3">
                      <a href="{{ route('asset-digitals.index', ['supplier_id' => $supplier->id]) }}" class="btn btn-outline-primary">
                        Lihat Semua Lisensi ({{ $supplier->assetDigitals->count() }})
                      </a>
                    </div>
                  @endif
                </div>
              @else
                <div class="text-center py-4">
                  <i class="ri-key-line ri-48px text-muted mb-3"></i>
                  <p class="text-muted">Belum ada lisensi digital dari supplier ini</p>
                </div>
              @endif
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="ri-settings-line me-2"></i>
              Aksi Cepat
            </h6>
            <div class="d-flex gap-2">
              <a href="{{ route('suppliers.edit', $supplier) }}" class="btn btn-primary quick-action-btn">
                <i class="ri-edit-line me-1"></i>
                Edit Supplier
              </a>
              <form action="{{ route('suppliers.toggle-status', $supplier) }}" method="POST" class="d-inline">
                @csrf
                @method('PATCH')
                <button type="submit" class="btn btn-{{ $supplier->is_active ? 'warning' : 'success' }} quick-action-btn">
                  <i class="ri-{{ $supplier->is_active ? 'pause' : 'play' }}-line me-1"></i>
                  {{ $supplier->is_active ? 'Nonaktifkan' : 'Aktifkan' }}
                </button>
              </form>
              <form action="{{ route('suppliers.destroy', $supplier) }}" method="POST"
                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus supplier ini?')" class="d-inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger quick-action-btn">
                  <i class="ri-delete-bin-line me-1"></i>
                  Hapus
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
