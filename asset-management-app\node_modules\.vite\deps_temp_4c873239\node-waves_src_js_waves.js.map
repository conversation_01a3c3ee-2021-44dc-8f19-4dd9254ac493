{"version": 3, "sources": ["../../node-waves/src/js/waves.js"], "sourcesContent": ["/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves\n *\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> and other contributors\n * Released under the MIT license\n * https://github.com/fians/Waves/blob/master/LICENSE\n */\n\n;(function(window, factory) {\n    'use strict';\n\n    // AMD. Register as an anonymous module.  Wrap in function so we have access\n    // to root via `this`.\n    if (typeof define === 'function' && define.amd) {\n        define([], function() {\n            window.Waves = factory.call(window);\n            return window.Waves;\n        });\n    }\n\n    // Node. Does not work with strict CommonJS, but only CommonJS-like\n    // environments that support module.exports, like Node.\n    else if (typeof exports === 'object') {\n        module.exports = factory.call(window);\n    }\n\n    // Browser globals.\n    else {\n        window.Waves = factory.call(window);\n    }\n})(typeof global === 'object' ? global : this, function() {\n    'use strict';\n\n    var Waves            = Waves || {};\n    var $$               = document.querySelectorAll.bind(document);\n    var toString         = Object.prototype.toString;\n    var isTouchAvailable = 'ontouchstart' in window;\n\n\n    // Find exact position of element\n    function isWindow(obj) {\n        return obj !== null && obj === obj.window;\n    }\n\n    function getWindow(elem) {\n        return isWindow(elem) ? elem : elem.nodeType === 9 && elem.defaultView;\n    }\n\n    function isObject(value) {\n        var type = typeof value;\n        return type === 'function' || type === 'object' && !!value;\n    }\n\n    function isDOMNode(obj) {\n        return isObject(obj) && obj.nodeType > 0;\n    }\n\n    function getWavesElements(nodes) {\n        var stringRepr = toString.call(nodes);\n\n        if (stringRepr === '[object String]') {\n            return $$(nodes);\n        } else if (isObject(nodes) && /^\\[object (Array|HTMLCollection|NodeList|Object)\\]$/.test(stringRepr) && nodes.hasOwnProperty('length')) {\n            return nodes;\n        } else if (isDOMNode(nodes)) {\n            return [nodes];\n        }\n\n        return [];\n    }\n\n    function offset(elem) {\n        var docElem, win,\n            box = { top: 0, left: 0 },\n            doc = elem && elem.ownerDocument;\n\n        docElem = doc.documentElement;\n\n        if (typeof elem.getBoundingClientRect !== typeof undefined) {\n            box = elem.getBoundingClientRect();\n        }\n        win = getWindow(doc);\n        return {\n            top: box.top + win.pageYOffset - docElem.clientTop,\n            left: box.left + win.pageXOffset - docElem.clientLeft\n        };\n    }\n\n    function convertStyle(styleObj) {\n        var style = '';\n\n        for (var prop in styleObj) {\n            if (styleObj.hasOwnProperty(prop)) {\n                style += (prop + ':' + styleObj[prop] + ';');\n            }\n        }\n\n        return style;\n    }\n\n    var Effect = {\n\n        // Effect duration\n        duration: 750,\n\n        // Effect delay (check for scroll before showing effect)\n        delay: 200,\n\n        show: function(e, element, velocity) {\n\n            // Disable right click\n            if (e.button === 2) {\n                return false;\n            }\n\n            element = element || this;\n\n            // Create ripple\n            var ripple = document.createElement('div');\n            ripple.className = 'waves-ripple waves-rippling';\n            element.appendChild(ripple);\n\n            // Get click coordinate and element width\n            var pos       = offset(element);\n            var relativeY = 0;\n            var relativeX = 0;\n            // Support for touch devices\n            if('touches' in e && e.touches.length) {\n                relativeY   = (e.touches[0].pageY - pos.top);\n                relativeX   = (e.touches[0].pageX - pos.left);\n            }\n            //Normal case\n            else {\n                relativeY   = (e.pageY - pos.top);\n                relativeX   = (e.pageX - pos.left);\n            }\n            // Support for synthetic events\n            relativeX = relativeX >= 0 ? relativeX : 0;\n            relativeY = relativeY >= 0 ? relativeY : 0;\n\n            var scale     = 'scale(' + ((element.clientWidth / 100) * 3) + ')';\n            var translate = 'translate(0,0)';\n\n            if (velocity) {\n                translate = 'translate(' + (velocity.x) + 'px, ' + (velocity.y) + 'px)';\n            }\n\n            // Attach data to element\n            ripple.setAttribute('data-hold', Date.now());\n            ripple.setAttribute('data-x', relativeX);\n            ripple.setAttribute('data-y', relativeY);\n            ripple.setAttribute('data-scale', scale);\n            ripple.setAttribute('data-translate', translate);\n\n            // Set ripple position\n            var rippleStyle = {\n                top: relativeY + 'px',\n                left: relativeX + 'px'\n            };\n\n            ripple.classList.add('waves-notransition');\n            ripple.setAttribute('style', convertStyle(rippleStyle));\n            ripple.classList.remove('waves-notransition');\n\n            // Scale the ripple\n            rippleStyle['-webkit-transform'] = scale + ' ' + translate;\n            rippleStyle['-moz-transform'] = scale + ' ' + translate;\n            rippleStyle['-ms-transform'] = scale + ' ' + translate;\n            rippleStyle['-o-transform'] = scale + ' ' + translate;\n            rippleStyle.transform = scale + ' ' + translate;\n            rippleStyle.opacity = '1';\n\n            var duration = e.type === 'mousemove' ? 2500 : Effect.duration;\n            rippleStyle['-webkit-transition-duration'] = duration + 'ms';\n            rippleStyle['-moz-transition-duration']    = duration + 'ms';\n            rippleStyle['-o-transition-duration']      = duration + 'ms';\n            rippleStyle['transition-duration']         = duration + 'ms';\n\n            ripple.setAttribute('style', convertStyle(rippleStyle));\n        },\n\n        hide: function(e, element) {\n            element = element || this;\n\n            var ripples = element.getElementsByClassName('waves-rippling');\n\n            for (var i = 0, len = ripples.length; i < len; i++) {\n                removeRipple(e, element, ripples[i]);\n            }\n\n            if (isTouchAvailable) {\n                element.removeEventListener('touchend', Effect.hide);\n                element.removeEventListener('touchcancel', Effect.hide);\n            }\n\n            element.removeEventListener('mouseup', Effect.hide);\n            element.removeEventListener('mouseleave', Effect.hide);\n        }\n    };\n\n    /**\n     * Collection of wrapper for HTML element that only have single tag\n     * like <input> and <img>\n     */\n    var TagWrapper = {\n\n        // Wrap <input> tag so it can perform the effect\n        input: function(element) {\n\n            var parent = element.parentNode;\n\n            // If input already have parent just pass through\n            if (parent.tagName.toLowerCase() === 'i' && parent.classList.contains('waves-effect')) {\n                return;\n            }\n\n            // Put element class and style to the specified parent\n            var wrapper       = document.createElement('i');\n            wrapper.className = element.className + ' waves-input-wrapper';\n            element.className = 'waves-button-input';\n\n            // Put element as child\n            parent.replaceChild(wrapper, element);\n            wrapper.appendChild(element);\n\n            // Apply element color and background color to wrapper\n            var elementStyle    = window.getComputedStyle(element, null);\n            var color           = elementStyle.color;\n            var backgroundColor = elementStyle.backgroundColor;\n\n            wrapper.setAttribute('style', 'color:' + color + ';background:' + backgroundColor);\n            element.setAttribute('style', 'background-color:rgba(0,0,0,0);');\n\n        },\n\n        // Wrap <img> tag so it can perform the effect\n        img: function(element) {\n\n            var parent = element.parentNode;\n\n            // If input already have parent just pass through\n            if (parent.tagName.toLowerCase() === 'i' && parent.classList.contains('waves-effect')) {\n                return;\n            }\n\n            // Put element as child\n            var wrapper  = document.createElement('i');\n            parent.replaceChild(wrapper, element);\n            wrapper.appendChild(element);\n\n        }\n    };\n\n    /**\n     * Hide the effect and remove the ripple. Must be\n     * a separate function to pass the JSLint...\n     */\n    function removeRipple(e, el, ripple) {\n\n        // Check if the ripple still exist\n        if (!ripple) {\n            return;\n        }\n\n        ripple.classList.remove('waves-rippling');\n\n        var relativeX = ripple.getAttribute('data-x');\n        var relativeY = ripple.getAttribute('data-y');\n        var scale     = ripple.getAttribute('data-scale');\n        var translate = ripple.getAttribute('data-translate');\n\n        // Get delay beetween mousedown and mouse leave\n        var diff = Date.now() - Number(ripple.getAttribute('data-hold'));\n        var delay = 350 - diff;\n\n        if (delay < 0) {\n            delay = 0;\n        }\n\n        if (e.type === 'mousemove') {\n            delay = 150;\n        }\n\n        // Fade out ripple after delay\n        var duration = e.type === 'mousemove' ? 2500 : Effect.duration;\n\n        setTimeout(function() {\n\n            var style = {\n                top: relativeY + 'px',\n                left: relativeX + 'px',\n                opacity: '0',\n\n                // Duration\n                '-webkit-transition-duration': duration + 'ms',\n                '-moz-transition-duration': duration + 'ms',\n                '-o-transition-duration': duration + 'ms',\n                'transition-duration': duration + 'ms',\n                '-webkit-transform': scale + ' ' + translate,\n                '-moz-transform': scale + ' ' + translate,\n                '-ms-transform': scale + ' ' + translate,\n                '-o-transform': scale + ' ' + translate,\n                'transform': scale + ' ' + translate\n            };\n\n            ripple.setAttribute('style', convertStyle(style));\n\n            setTimeout(function() {\n                try {\n                    el.removeChild(ripple);\n                } catch (e) {\n                    return false;\n                }\n            }, duration);\n\n        }, delay);\n    }\n\n\n    /**\n     * Disable mousedown event for 500ms during and after touch\n     */\n    var TouchHandler = {\n\n        /* uses an integer rather than bool so there's no issues with\n         * needing to clear timeouts if another touch event occurred\n         * within the 500ms. Cannot mouseup between touchstart and\n         * touchend, nor in the 500ms after touchend. */\n        touches: 0,\n\n        allowEvent: function(e) {\n\n            var allow = true;\n\n            if (/^(mousedown|mousemove)$/.test(e.type) && TouchHandler.touches) {\n                allow = false;\n            }\n\n            return allow;\n        },\n        registerEvent: function(e) {\n            var eType = e.type;\n\n            if (eType === 'touchstart') {\n\n                TouchHandler.touches += 1; // push\n\n            } else if (/^(touchend|touchcancel)$/.test(eType)) {\n\n                setTimeout(function() {\n                    if (TouchHandler.touches) {\n                        TouchHandler.touches -= 1; // pop after 500ms\n                    }\n                }, 500);\n\n            }\n        }\n    };\n\n\n    /**\n     * Delegated click handler for .waves-effect element.\n     * returns null when .waves-effect element not in \"click tree\"\n     */\n    function getWavesEffectElement(e) {\n\n        if (TouchHandler.allowEvent(e) === false) {\n            return null;\n        }\n\n        var element = null;\n        var target = e.target || e.srcElement;\n\n        while (target.parentElement) {\n            if ( (!(target instanceof SVGElement)) && target.classList.contains('waves-effect')) {\n                element = target;\n                break;\n            }\n            target = target.parentElement;\n        }\n\n        return element;\n    }\n\n    /**\n     * Bubble the click and show effect if .waves-effect elem was found\n     */\n    function showEffect(e) {\n\n        // Disable effect if element has \"disabled\" property on it\n        // In some cases, the event is not triggered by the current element\n        // if (e.target.getAttribute('disabled') !== null) {\n        //     return;\n        // }\n\n        var element = getWavesEffectElement(e);\n\n        if (element !== null) {\n\n            // Make it sure the element has either disabled property, disabled attribute or 'disabled' class\n            if (element.disabled || element.getAttribute('disabled') || element.classList.contains('disabled')) {\n                return;\n            }\n\n            TouchHandler.registerEvent(e);\n\n            if (e.type === 'touchstart' && Effect.delay) {\n\n                var hidden = false;\n\n                var timer = setTimeout(function () {\n                    timer = null;\n                    Effect.show(e, element);\n                }, Effect.delay);\n\n                var hideEffect = function(hideEvent) {\n\n                    // if touch hasn't moved, and effect not yet started: start effect now\n                    if (timer) {\n                        clearTimeout(timer);\n                        timer = null;\n                        Effect.show(e, element);\n                    }\n                    if (!hidden) {\n                        hidden = true;\n                        Effect.hide(hideEvent, element);\n                    }\n\n                    removeListeners();\n                };\n\n                var touchMove = function(moveEvent) {\n                    if (timer) {\n                        clearTimeout(timer);\n                        timer = null;\n                    }\n                    hideEffect(moveEvent);\n\n                    removeListeners();\n                };\n\n                element.addEventListener('touchmove', touchMove, false);\n                element.addEventListener('touchend', hideEffect, false);\n                element.addEventListener('touchcancel', hideEffect, false);\n\n                var removeListeners = function() {\n                    element.removeEventListener('touchmove', touchMove);\n                    element.removeEventListener('touchend', hideEffect);\n                    element.removeEventListener('touchcancel', hideEffect);\n                };\n            } else {\n\n                Effect.show(e, element);\n\n                if (isTouchAvailable) {\n                    element.addEventListener('touchend', Effect.hide, false);\n                    element.addEventListener('touchcancel', Effect.hide, false);\n                }\n\n                element.addEventListener('mouseup', Effect.hide, false);\n                element.addEventListener('mouseleave', Effect.hide, false);\n            }\n        }\n    }\n\n    Waves.init = function(options) {\n        var body = document.body;\n\n        options = options || {};\n\n        if ('duration' in options) {\n            Effect.duration = options.duration;\n        }\n\n        if ('delay' in options) {\n            Effect.delay = options.delay;\n        }\n\n        if (isTouchAvailable) {\n            body.addEventListener('touchstart', showEffect, false);\n            body.addEventListener('touchcancel', TouchHandler.registerEvent, false);\n            body.addEventListener('touchend', TouchHandler.registerEvent, false);\n        }\n\n        body.addEventListener('mousedown', showEffect, false);\n    };\n\n\n    /**\n     * Attach Waves to dynamically loaded inputs, or add .waves-effect and other\n     * waves classes to a set of elements. Set drag to true if the ripple mouseover\n     * or skimming effect should be applied to the elements.\n     */\n    Waves.attach = function(elements, classes) {\n\n        elements = getWavesElements(elements);\n\n        if (toString.call(classes) === '[object Array]') {\n            classes = classes.join(' ');\n        }\n\n        classes = classes ? ' ' + classes : '';\n\n        var element, tagName;\n\n        for (var i = 0, len = elements.length; i < len; i++) {\n\n            element = elements[i];\n            tagName = element.tagName.toLowerCase();\n\n            if (['input', 'img'].indexOf(tagName) !== -1) {\n                TagWrapper[tagName](element);\n                element = element.parentElement;\n            }\n\n            if (element.className.indexOf('waves-effect') === -1) {\n                element.className += ' waves-effect' + classes;\n            }\n        }\n    };\n\n\n    /**\n     * Cause a ripple to appear in an element via code.\n     */\n    Waves.ripple = function(elements, options) {\n        elements = getWavesElements(elements);\n        var elementsLen = elements.length;\n\n        options          = options || {};\n        options.wait     = options.wait || 0;\n        options.position = options.position || null; // default = centre of element\n\n\n        if (elementsLen) {\n            var element, pos, off, centre = {}, i = 0;\n            var mousedown = {\n                type: 'mousedown',\n                button: 1\n            };\n            var hideRipple = function(mouseup, element) {\n                return function() {\n                    Effect.hide(mouseup, element);\n                };\n            };\n\n            for (; i < elementsLen; i++) {\n                element = elements[i];\n                pos = options.position || {\n                    x: element.clientWidth / 2,\n                    y: element.clientHeight / 2\n                };\n\n                off      = offset(element);\n                centre.x = off.left + pos.x;\n                centre.y = off.top + pos.y;\n\n                mousedown.pageX = centre.x;\n                mousedown.pageY = centre.y;\n\n                Effect.show(mousedown, element);\n\n                if (options.wait >= 0 && options.wait !== null) {\n                    var mouseup = {\n                        type: 'mouseup',\n                        button: 1\n                    };\n\n                    setTimeout(hideRipple(mouseup, element), options.wait);\n                }\n            }\n        }\n    };\n\n    /**\n     * Remove all ripples from an element.\n     */\n    Waves.calm = function(elements) {\n        elements = getWavesElements(elements);\n        var mouseup = {\n            type: 'mouseup',\n            button: 1\n        };\n\n        for (var i = 0, len = elements.length; i < len; i++) {\n            Effect.hide(mouseup, elements[i]);\n        }\n    };\n\n    /**\n     * Deprecated API fallback\n     */\n    Waves.displayEffect = function(options) {\n        console.error('Waves.displayEffect() has been deprecated and will be removed in future version. Please use Waves.init() to initialize Waves effect');\n        Waves.init(options);\n    };\n\n    return Waves;\n});\n"], "mappings": ";;;;;AAAA;AAAA;AASC,KAAC,SAASA,SAAQ,SAAS;AACxB;AAIA,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC5C,eAAO,CAAC,GAAG,WAAW;AAClB,UAAAA,QAAO,QAAQ,QAAQ,KAAKA,OAAM;AAClC,iBAAOA,QAAO;AAAA,QAClB,CAAC;AAAA,MACL,WAIS,OAAO,YAAY,UAAU;AAClC,eAAO,UAAU,QAAQ,KAAKA,OAAM;AAAA,MACxC,OAGK;AACD,QAAAA,QAAO,QAAQ,QAAQ,KAAKA,OAAM;AAAA,MACtC;AAAA,IACJ,GAAG,OAAO,WAAW,WAAW,SAAS,SAAM,WAAW;AACtD;AAEA,UAAI,QAAmB,SAAS,CAAC;AACjC,UAAI,KAAmB,SAAS,iBAAiB,KAAK,QAAQ;AAC9D,UAAI,WAAmB,OAAO,UAAU;AACxC,UAAI,mBAAmB,kBAAkB;AAIzC,eAAS,SAAS,KAAK;AACnB,eAAO,QAAQ,QAAQ,QAAQ,IAAI;AAAA,MACvC;AAEA,eAAS,UAAU,MAAM;AACrB,eAAO,SAAS,IAAI,IAAI,OAAO,KAAK,aAAa,KAAK,KAAK;AAAA,MAC/D;AAEA,eAAS,SAAS,OAAO;AACrB,YAAI,OAAO,OAAO;AAClB,eAAO,SAAS,cAAc,SAAS,YAAY,CAAC,CAAC;AAAA,MACzD;AAEA,eAAS,UAAU,KAAK;AACpB,eAAO,SAAS,GAAG,KAAK,IAAI,WAAW;AAAA,MAC3C;AAEA,eAAS,iBAAiB,OAAO;AAC7B,YAAI,aAAa,SAAS,KAAK,KAAK;AAEpC,YAAI,eAAe,mBAAmB;AAClC,iBAAO,GAAG,KAAK;AAAA,QACnB,WAAW,SAAS,KAAK,KAAK,sDAAsD,KAAK,UAAU,KAAK,MAAM,eAAe,QAAQ,GAAG;AACpI,iBAAO;AAAA,QACX,WAAW,UAAU,KAAK,GAAG;AACzB,iBAAO,CAAC,KAAK;AAAA,QACjB;AAEA,eAAO,CAAC;AAAA,MACZ;AAEA,eAAS,OAAO,MAAM;AAClB,YAAI,SAAS,KACT,MAAM,EAAE,KAAK,GAAG,MAAM,EAAE,GACxB,MAAM,QAAQ,KAAK;AAEvB,kBAAU,IAAI;AAEd,YAAI,OAAO,KAAK,0BAA0B,aAAkB;AACxD,gBAAM,KAAK,sBAAsB;AAAA,QACrC;AACA,cAAM,UAAU,GAAG;AACnB,eAAO;AAAA,UACH,KAAK,IAAI,MAAM,IAAI,cAAc,QAAQ;AAAA,UACzC,MAAM,IAAI,OAAO,IAAI,cAAc,QAAQ;AAAA,QAC/C;AAAA,MACJ;AAEA,eAAS,aAAa,UAAU;AAC5B,YAAI,QAAQ;AAEZ,iBAAS,QAAQ,UAAU;AACvB,cAAI,SAAS,eAAe,IAAI,GAAG;AAC/B,qBAAU,OAAO,MAAM,SAAS,IAAI,IAAI;AAAA,UAC5C;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAEA,UAAI,SAAS;AAAA;AAAA,QAGT,UAAU;AAAA;AAAA,QAGV,OAAO;AAAA,QAEP,MAAM,SAAS,GAAG,SAAS,UAAU;AAGjC,cAAI,EAAE,WAAW,GAAG;AAChB,mBAAO;AAAA,UACX;AAEA,oBAAU,WAAW;AAGrB,cAAI,SAAS,SAAS,cAAc,KAAK;AACzC,iBAAO,YAAY;AACnB,kBAAQ,YAAY,MAAM;AAG1B,cAAI,MAAY,OAAO,OAAO;AAC9B,cAAI,YAAY;AAChB,cAAI,YAAY;AAEhB,cAAG,aAAa,KAAK,EAAE,QAAQ,QAAQ;AACnC,wBAAe,EAAE,QAAQ,CAAC,EAAE,QAAQ,IAAI;AACxC,wBAAe,EAAE,QAAQ,CAAC,EAAE,QAAQ,IAAI;AAAA,UAC5C,OAEK;AACD,wBAAe,EAAE,QAAQ,IAAI;AAC7B,wBAAe,EAAE,QAAQ,IAAI;AAAA,UACjC;AAEA,sBAAY,aAAa,IAAI,YAAY;AACzC,sBAAY,aAAa,IAAI,YAAY;AAEzC,cAAI,QAAY,WAAa,QAAQ,cAAc,MAAO,IAAK;AAC/D,cAAI,YAAY;AAEhB,cAAI,UAAU;AACV,wBAAY,eAAgB,SAAS,IAAK,SAAU,SAAS,IAAK;AAAA,UACtE;AAGA,iBAAO,aAAa,aAAa,KAAK,IAAI,CAAC;AAC3C,iBAAO,aAAa,UAAU,SAAS;AACvC,iBAAO,aAAa,UAAU,SAAS;AACvC,iBAAO,aAAa,cAAc,KAAK;AACvC,iBAAO,aAAa,kBAAkB,SAAS;AAG/C,cAAI,cAAc;AAAA,YACd,KAAK,YAAY;AAAA,YACjB,MAAM,YAAY;AAAA,UACtB;AAEA,iBAAO,UAAU,IAAI,oBAAoB;AACzC,iBAAO,aAAa,SAAS,aAAa,WAAW,CAAC;AACtD,iBAAO,UAAU,OAAO,oBAAoB;AAG5C,sBAAY,mBAAmB,IAAI,QAAQ,MAAM;AACjD,sBAAY,gBAAgB,IAAI,QAAQ,MAAM;AAC9C,sBAAY,eAAe,IAAI,QAAQ,MAAM;AAC7C,sBAAY,cAAc,IAAI,QAAQ,MAAM;AAC5C,sBAAY,YAAY,QAAQ,MAAM;AACtC,sBAAY,UAAU;AAEtB,cAAI,WAAW,EAAE,SAAS,cAAc,OAAO,OAAO;AACtD,sBAAY,6BAA6B,IAAI,WAAW;AACxD,sBAAY,0BAA0B,IAAO,WAAW;AACxD,sBAAY,wBAAwB,IAAS,WAAW;AACxD,sBAAY,qBAAqB,IAAY,WAAW;AAExD,iBAAO,aAAa,SAAS,aAAa,WAAW,CAAC;AAAA,QAC1D;AAAA,QAEA,MAAM,SAAS,GAAG,SAAS;AACvB,oBAAU,WAAW;AAErB,cAAI,UAAU,QAAQ,uBAAuB,gBAAgB;AAE7D,mBAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAChD,yBAAa,GAAG,SAAS,QAAQ,CAAC,CAAC;AAAA,UACvC;AAEA,cAAI,kBAAkB;AAClB,oBAAQ,oBAAoB,YAAY,OAAO,IAAI;AACnD,oBAAQ,oBAAoB,eAAe,OAAO,IAAI;AAAA,UAC1D;AAEA,kBAAQ,oBAAoB,WAAW,OAAO,IAAI;AAClD,kBAAQ,oBAAoB,cAAc,OAAO,IAAI;AAAA,QACzD;AAAA,MACJ;AAMA,UAAI,aAAa;AAAA;AAAA,QAGb,OAAO,SAAS,SAAS;AAErB,cAAI,SAAS,QAAQ;AAGrB,cAAI,OAAO,QAAQ,YAAY,MAAM,OAAO,OAAO,UAAU,SAAS,cAAc,GAAG;AACnF;AAAA,UACJ;AAGA,cAAI,UAAgB,SAAS,cAAc,GAAG;AAC9C,kBAAQ,YAAY,QAAQ,YAAY;AACxC,kBAAQ,YAAY;AAGpB,iBAAO,aAAa,SAAS,OAAO;AACpC,kBAAQ,YAAY,OAAO;AAG3B,cAAI,eAAkB,OAAO,iBAAiB,SAAS,IAAI;AAC3D,cAAI,QAAkB,aAAa;AACnC,cAAI,kBAAkB,aAAa;AAEnC,kBAAQ,aAAa,SAAS,WAAW,QAAQ,iBAAiB,eAAe;AACjF,kBAAQ,aAAa,SAAS,iCAAiC;AAAA,QAEnE;AAAA;AAAA,QAGA,KAAK,SAAS,SAAS;AAEnB,cAAI,SAAS,QAAQ;AAGrB,cAAI,OAAO,QAAQ,YAAY,MAAM,OAAO,OAAO,UAAU,SAAS,cAAc,GAAG;AACnF;AAAA,UACJ;AAGA,cAAI,UAAW,SAAS,cAAc,GAAG;AACzC,iBAAO,aAAa,SAAS,OAAO;AACpC,kBAAQ,YAAY,OAAO;AAAA,QAE/B;AAAA,MACJ;AAMA,eAAS,aAAa,GAAG,IAAI,QAAQ;AAGjC,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AAEA,eAAO,UAAU,OAAO,gBAAgB;AAExC,YAAI,YAAY,OAAO,aAAa,QAAQ;AAC5C,YAAI,YAAY,OAAO,aAAa,QAAQ;AAC5C,YAAI,QAAY,OAAO,aAAa,YAAY;AAChD,YAAI,YAAY,OAAO,aAAa,gBAAgB;AAGpD,YAAI,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO,aAAa,WAAW,CAAC;AAC/D,YAAI,QAAQ,MAAM;AAElB,YAAI,QAAQ,GAAG;AACX,kBAAQ;AAAA,QACZ;AAEA,YAAI,EAAE,SAAS,aAAa;AACxB,kBAAQ;AAAA,QACZ;AAGA,YAAI,WAAW,EAAE,SAAS,cAAc,OAAO,OAAO;AAEtD,mBAAW,WAAW;AAElB,cAAI,QAAQ;AAAA,YACR,KAAK,YAAY;AAAA,YACjB,MAAM,YAAY;AAAA,YAClB,SAAS;AAAA;AAAA,YAGT,+BAA+B,WAAW;AAAA,YAC1C,4BAA4B,WAAW;AAAA,YACvC,0BAA0B,WAAW;AAAA,YACrC,uBAAuB,WAAW;AAAA,YAClC,qBAAqB,QAAQ,MAAM;AAAA,YACnC,kBAAkB,QAAQ,MAAM;AAAA,YAChC,iBAAiB,QAAQ,MAAM;AAAA,YAC/B,gBAAgB,QAAQ,MAAM;AAAA,YAC9B,aAAa,QAAQ,MAAM;AAAA,UAC/B;AAEA,iBAAO,aAAa,SAAS,aAAa,KAAK,CAAC;AAEhD,qBAAW,WAAW;AAClB,gBAAI;AACA,iBAAG,YAAY,MAAM;AAAA,YACzB,SAASC,IAAG;AACR,qBAAO;AAAA,YACX;AAAA,UACJ,GAAG,QAAQ;AAAA,QAEf,GAAG,KAAK;AAAA,MACZ;AAMA,UAAI,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,QAMf,SAAS;AAAA,QAET,YAAY,SAAS,GAAG;AAEpB,cAAI,QAAQ;AAEZ,cAAI,0BAA0B,KAAK,EAAE,IAAI,KAAK,aAAa,SAAS;AAChE,oBAAQ;AAAA,UACZ;AAEA,iBAAO;AAAA,QACX;AAAA,QACA,eAAe,SAAS,GAAG;AACvB,cAAI,QAAQ,EAAE;AAEd,cAAI,UAAU,cAAc;AAExB,yBAAa,WAAW;AAAA,UAE5B,WAAW,2BAA2B,KAAK,KAAK,GAAG;AAE/C,uBAAW,WAAW;AAClB,kBAAI,aAAa,SAAS;AACtB,6BAAa,WAAW;AAAA,cAC5B;AAAA,YACJ,GAAG,GAAG;AAAA,UAEV;AAAA,QACJ;AAAA,MACJ;AAOA,eAAS,sBAAsB,GAAG;AAE9B,YAAI,aAAa,WAAW,CAAC,MAAM,OAAO;AACtC,iBAAO;AAAA,QACX;AAEA,YAAI,UAAU;AACd,YAAI,SAAS,EAAE,UAAU,EAAE;AAE3B,eAAO,OAAO,eAAe;AACzB,cAAM,EAAE,kBAAkB,eAAgB,OAAO,UAAU,SAAS,cAAc,GAAG;AACjF,sBAAU;AACV;AAAA,UACJ;AACA,mBAAS,OAAO;AAAA,QACpB;AAEA,eAAO;AAAA,MACX;AAKA,eAAS,WAAW,GAAG;AAQnB,YAAI,UAAU,sBAAsB,CAAC;AAErC,YAAI,YAAY,MAAM;AAGlB,cAAI,QAAQ,YAAY,QAAQ,aAAa,UAAU,KAAK,QAAQ,UAAU,SAAS,UAAU,GAAG;AAChG;AAAA,UACJ;AAEA,uBAAa,cAAc,CAAC;AAE5B,cAAI,EAAE,SAAS,gBAAgB,OAAO,OAAO;AAEzC,gBAAI,SAAS;AAEb,gBAAI,QAAQ,WAAW,WAAY;AAC/B,sBAAQ;AACR,qBAAO,KAAK,GAAG,OAAO;AAAA,YAC1B,GAAG,OAAO,KAAK;AAEf,gBAAI,aAAa,SAAS,WAAW;AAGjC,kBAAI,OAAO;AACP,6BAAa,KAAK;AAClB,wBAAQ;AACR,uBAAO,KAAK,GAAG,OAAO;AAAA,cAC1B;AACA,kBAAI,CAAC,QAAQ;AACT,yBAAS;AACT,uBAAO,KAAK,WAAW,OAAO;AAAA,cAClC;AAEA,8BAAgB;AAAA,YACpB;AAEA,gBAAI,YAAY,SAAS,WAAW;AAChC,kBAAI,OAAO;AACP,6BAAa,KAAK;AAClB,wBAAQ;AAAA,cACZ;AACA,yBAAW,SAAS;AAEpB,8BAAgB;AAAA,YACpB;AAEA,oBAAQ,iBAAiB,aAAa,WAAW,KAAK;AACtD,oBAAQ,iBAAiB,YAAY,YAAY,KAAK;AACtD,oBAAQ,iBAAiB,eAAe,YAAY,KAAK;AAEzD,gBAAI,kBAAkB,WAAW;AAC7B,sBAAQ,oBAAoB,aAAa,SAAS;AAClD,sBAAQ,oBAAoB,YAAY,UAAU;AAClD,sBAAQ,oBAAoB,eAAe,UAAU;AAAA,YACzD;AAAA,UACJ,OAAO;AAEH,mBAAO,KAAK,GAAG,OAAO;AAEtB,gBAAI,kBAAkB;AAClB,sBAAQ,iBAAiB,YAAY,OAAO,MAAM,KAAK;AACvD,sBAAQ,iBAAiB,eAAe,OAAO,MAAM,KAAK;AAAA,YAC9D;AAEA,oBAAQ,iBAAiB,WAAW,OAAO,MAAM,KAAK;AACtD,oBAAQ,iBAAiB,cAAc,OAAO,MAAM,KAAK;AAAA,UAC7D;AAAA,QACJ;AAAA,MACJ;AAEA,YAAM,OAAO,SAAS,SAAS;AAC3B,YAAI,OAAO,SAAS;AAEpB,kBAAU,WAAW,CAAC;AAEtB,YAAI,cAAc,SAAS;AACvB,iBAAO,WAAW,QAAQ;AAAA,QAC9B;AAEA,YAAI,WAAW,SAAS;AACpB,iBAAO,QAAQ,QAAQ;AAAA,QAC3B;AAEA,YAAI,kBAAkB;AAClB,eAAK,iBAAiB,cAAc,YAAY,KAAK;AACrD,eAAK,iBAAiB,eAAe,aAAa,eAAe,KAAK;AACtE,eAAK,iBAAiB,YAAY,aAAa,eAAe,KAAK;AAAA,QACvE;AAEA,aAAK,iBAAiB,aAAa,YAAY,KAAK;AAAA,MACxD;AAQA,YAAM,SAAS,SAAS,UAAU,SAAS;AAEvC,mBAAW,iBAAiB,QAAQ;AAEpC,YAAI,SAAS,KAAK,OAAO,MAAM,kBAAkB;AAC7C,oBAAU,QAAQ,KAAK,GAAG;AAAA,QAC9B;AAEA,kBAAU,UAAU,MAAM,UAAU;AAEpC,YAAI,SAAS;AAEb,iBAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AAEjD,oBAAU,SAAS,CAAC;AACpB,oBAAU,QAAQ,QAAQ,YAAY;AAEtC,cAAI,CAAC,SAAS,KAAK,EAAE,QAAQ,OAAO,MAAM,IAAI;AAC1C,uBAAW,OAAO,EAAE,OAAO;AAC3B,sBAAU,QAAQ;AAAA,UACtB;AAEA,cAAI,QAAQ,UAAU,QAAQ,cAAc,MAAM,IAAI;AAClD,oBAAQ,aAAa,kBAAkB;AAAA,UAC3C;AAAA,QACJ;AAAA,MACJ;AAMA,YAAM,SAAS,SAAS,UAAU,SAAS;AACvC,mBAAW,iBAAiB,QAAQ;AACpC,YAAI,cAAc,SAAS;AAE3B,kBAAmB,WAAW,CAAC;AAC/B,gBAAQ,OAAW,QAAQ,QAAQ;AACnC,gBAAQ,WAAW,QAAQ,YAAY;AAGvC,YAAI,aAAa;AACb,cAAI,SAAS,KAAK,KAAK,SAAS,CAAC,GAAG,IAAI;AACxC,cAAI,YAAY;AAAA,YACZ,MAAM;AAAA,YACN,QAAQ;AAAA,UACZ;AACA,cAAI,aAAa,SAASC,UAASC,UAAS;AACxC,mBAAO,WAAW;AACd,qBAAO,KAAKD,UAASC,QAAO;AAAA,YAChC;AAAA,UACJ;AAEA,iBAAO,IAAI,aAAa,KAAK;AACzB,sBAAU,SAAS,CAAC;AACpB,kBAAM,QAAQ,YAAY;AAAA,cACtB,GAAG,QAAQ,cAAc;AAAA,cACzB,GAAG,QAAQ,eAAe;AAAA,YAC9B;AAEA,kBAAW,OAAO,OAAO;AACzB,mBAAO,IAAI,IAAI,OAAO,IAAI;AAC1B,mBAAO,IAAI,IAAI,MAAM,IAAI;AAEzB,sBAAU,QAAQ,OAAO;AACzB,sBAAU,QAAQ,OAAO;AAEzB,mBAAO,KAAK,WAAW,OAAO;AAE9B,gBAAI,QAAQ,QAAQ,KAAK,QAAQ,SAAS,MAAM;AAC5C,kBAAI,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,QAAQ;AAAA,cACZ;AAEA,yBAAW,WAAW,SAAS,OAAO,GAAG,QAAQ,IAAI;AAAA,YACzD;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAKA,YAAM,OAAO,SAAS,UAAU;AAC5B,mBAAW,iBAAiB,QAAQ;AACpC,YAAI,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ;AAEA,iBAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACjD,iBAAO,KAAK,SAAS,SAAS,CAAC,CAAC;AAAA,QACpC;AAAA,MACJ;AAKA,YAAM,gBAAgB,SAAS,SAAS;AACpC,gBAAQ,MAAM,qIAAqI;AACnJ,cAAM,KAAK,OAAO;AAAA,MACtB;AAEA,aAAO;AAAA,IACX,CAAC;AAAA;AAAA;", "names": ["window", "e", "mouseup", "element"]}