@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Role - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Role /</span> Tambah Role
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah Role</h5>
          <a href="{{ route('master.roles.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.roles.store') }}" method="POST">
            @csrf
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name">Nama Role <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" 
                       placeholder="Contoh: Manager">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="slug">Slug <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                       id="slug" name="slug" value="{{ old('slug') }}" 
                       placeholder="Contoh: manager">
                @error('slug')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Slug akan auto-generate dari nama jika dikosongkan</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi role dan tanggung jawabnya">{{ old('description') }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <!-- Permissions Section -->
            <div class="mb-4">
              <label class="form-label">Permissions</label>
              <div class="card">
                <div class="card-body">
                  @if($permissions->count() > 0)
                    @foreach($permissions as $module => $modulePermissions)
                    <div class="mb-4">
                      <div class="d-flex align-items-center mb-3">
                        <div class="form-check me-3">
                          <input class="form-check-input module-checkbox" type="checkbox" 
                                 id="module_{{ $module }}" data-module="{{ $module }}">
                          <label class="form-check-label fw-bold text-primary" for="module_{{ $module }}">
                            <i class="ri-folder-line me-1"></i>{{ ucfirst($module) }}
                          </label>
                        </div>
                        <small class="text-muted">({{ $modulePermissions->count() }} permissions)</small>
                      </div>
                      
                      <div class="row ms-3">
                        @foreach($modulePermissions as $permission)
                        <div class="col-md-6 mb-2">
                          <div class="form-check">
                            <input class="form-check-input permission-checkbox" type="checkbox" 
                                   name="permissions[]" value="{{ $permission->id }}" 
                                   id="permission_{{ $permission->id }}" 
                                   data-module="{{ $module }}"
                                   {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                              {{ $permission->name }}
                            </label>
                          </div>
                        </div>
                        @endforeach
                      </div>
                    </div>
                    @endforeach
                  @else
                    <div class="text-center py-4">
                      <i class="ri-shield-line display-4 text-muted mb-2"></i>
                      <p class="text-muted">Tidak ada permission yang tersedia.</p>
                    </div>
                  @endif
                </div>
              </div>
              @error('permissions')
                <div class="text-danger mt-1">{{ $message }}</div>
              @enderror
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.roles.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Pengisian</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips:</h6>
            <ul class="mb-0">
              <li><strong>Nama Role:</strong> Gunakan nama yang jelas dan mudah dipahami</li>
              <li><strong>Slug:</strong> Akan auto-generate dari nama, atau bisa diisi manual</li>
              <li><strong>Permissions:</strong> Pilih permission sesuai dengan tanggung jawab role</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Slug harus unik dan tidak boleh sama</li>
              <li>Pilih permission dengan hati-hati</li>
              <li>Role yang sudah dibuat bisa diedit kapan saja</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Contoh Role:</h6>
            <ul class="mb-0">
              <li><strong>Manager:</strong> Akses penuh untuk cabang</li>
              <li><strong>Staff:</strong> Akses terbatas untuk operasional</li>
              <li><strong>Viewer:</strong> Hanya bisa melihat data</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Quick Actions</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllPermissions()">
              <i class="ri-check-double-line me-1"></i>Pilih Semua
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllPermissions()">
              <i class="ri-close-line me-1"></i>Hapus Semua
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto generate slug from name
  const nameInput = document.getElementById('name');
  const slugInput = document.getElementById('slug');
  
  nameInput.addEventListener('input', function() {
    if (!slugInput.dataset.manual) {
      const slug = this.value.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      slugInput.value = slug;
    }
  });

  // Mark slug as manually edited
  slugInput.addEventListener('input', function() {
    this.dataset.manual = 'true';
  });

  // Module checkbox functionality
  const moduleCheckboxes = document.querySelectorAll('.module-checkbox');
  moduleCheckboxes.forEach(function(moduleCheckbox) {
    moduleCheckbox.addEventListener('change', function() {
      const module = this.dataset.module;
      const permissionCheckboxes = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);
      
      permissionCheckboxes.forEach(function(checkbox) {
        checkbox.checked = moduleCheckbox.checked;
      });
    });
  });

  // Permission checkbox functionality
  const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
  permissionCheckboxes.forEach(function(permissionCheckbox) {
    permissionCheckbox.addEventListener('change', function() {
      const module = this.dataset.module;
      const moduleCheckbox = document.querySelector(`input[data-module="${module}"].module-checkbox`);
      const modulePermissions = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);
      
      const checkedCount = Array.from(modulePermissions).filter(cb => cb.checked).length;
      const totalCount = modulePermissions.length;
      
      if (checkedCount === 0) {
        moduleCheckbox.checked = false;
        moduleCheckbox.indeterminate = false;
      } else if (checkedCount === totalCount) {
        moduleCheckbox.checked = true;
        moduleCheckbox.indeterminate = false;
      } else {
        moduleCheckbox.checked = false;
        moduleCheckbox.indeterminate = true;
      }
    });
  });

  // Initialize module checkboxes state
  moduleCheckboxes.forEach(function(moduleCheckbox) {
    const module = moduleCheckbox.dataset.module;
    const modulePermissions = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);
    const checkedCount = Array.from(modulePermissions).filter(cb => cb.checked).length;
    const totalCount = modulePermissions.length;
    
    if (checkedCount === 0) {
      moduleCheckbox.checked = false;
      moduleCheckbox.indeterminate = false;
    } else if (checkedCount === totalCount) {
      moduleCheckbox.checked = true;
      moduleCheckbox.indeterminate = false;
    } else {
      moduleCheckbox.checked = false;
      moduleCheckbox.indeterminate = true;
    }
  });
});

function selectAllPermissions() {
  const checkboxes = document.querySelectorAll('.permission-checkbox, .module-checkbox');
  checkboxes.forEach(function(checkbox) {
    checkbox.checked = true;
    checkbox.indeterminate = false;
  });
}

function clearAllPermissions() {
  const checkboxes = document.querySelectorAll('.permission-checkbox, .module-checkbox');
  checkboxes.forEach(function(checkbox) {
    checkbox.checked = false;
    checkbox.indeterminate = false;
  });
}
</script>
@endsection
