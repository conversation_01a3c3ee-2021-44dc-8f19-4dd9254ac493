<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Division;
use Illuminate\Http\Request;

class DivisionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Division::withCount(['users', 'users as active_users_count' => function ($query) {
            $query->where('is_active', true);
        }]);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('head_name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $divisions = $query->latest()->paginate(15);

        return view('master.divisions.index', compact('divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('master.divisions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:divisions,code',
            'description' => 'nullable|string',
            'head_name' => 'nullable|string|max:255',
            'head_email' => 'nullable|email|max:255',
            'head_phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        Division::create($validated);

        return redirect()->route('master.divisions.index')->with('success', 'Divisi berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Division $division)
    {
        $division->load(['users' => function ($query) {
            $query->with(['role', 'branch'])->latest();
        }]);

        return view('master.divisions.show', compact('division'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Division $division)
    {
        return view('master.divisions.edit', compact('division'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Division $division)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:divisions,code,' . $division->id,
            'description' => 'nullable|string',
            'head_name' => 'nullable|string|max:255',
            'head_email' => 'nullable|email|max:255',
            'head_phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        $division->update($validated);

        return redirect()->route('master.divisions.index')->with('success', 'Divisi berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Division $division)
    {
        // Check if division has users
        if ($division->users()->count() > 0) {
            return redirect()->route('master.divisions.index')->with('error', 'Tidak dapat menghapus divisi yang memiliki user.');
        }

        $division->delete();

        return redirect()->route('master.divisions.index')->with('success', 'Divisi berhasil dihapus.');
    }
}
