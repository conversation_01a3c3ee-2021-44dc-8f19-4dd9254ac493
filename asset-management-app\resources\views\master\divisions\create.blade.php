@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Divisi - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Divisi /</span> Tambah Divisi
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah Divisi</h5>
          <a href="{{ route('master.divisions.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.divisions.store') }}" method="POST">
            @csrf
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name">Nama Divisi <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" 
                       placeholder="Contoh: Information Technology">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Divisi <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                       id="code" name="code" value="{{ old('code') }}" 
                       placeholder="Contoh: IT" maxlength="10">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode unik untuk divisi (maksimal 10 karakter)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi divisi dan tanggung jawabnya">{{ old('description') }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <hr class="my-4">
            <h6 class="mb-3">Informasi Kepala Divisi</h6>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="head_name">Nama Kepala Divisi</label>
                <input type="text" class="form-control @error('head_name') is-invalid @enderror" 
                       id="head_name" name="head_name" value="{{ old('head_name') }}" 
                       placeholder="Contoh: Budi Santoso">
                @error('head_name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="head_email">Email Kepala Divisi</label>
                <input type="email" class="form-control @error('head_email') is-invalid @enderror" 
                       id="head_email" name="head_email" value="{{ old('head_email') }}" 
                       placeholder="Contoh: <EMAIL>">
                @error('head_email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="head_phone">Telepon Kepala Divisi</label>
                <input type="text" class="form-control @error('head_phone') is-invalid @enderror" 
                       id="head_phone" name="head_phone" value="{{ old('head_phone') }}" 
                       placeholder="Contoh: 08123456789">
                @error('head_phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label">&nbsp;</label>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                           {{ old('is_active', true) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                      Aktif
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.divisions.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Pengisian</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips:</h6>
            <ul class="mb-0">
              <li><strong>Nama Divisi:</strong> Gunakan nama yang jelas dan mudah dipahami</li>
              <li><strong>Kode Divisi:</strong> Gunakan kode singkat dan unik (contoh: IT, HR, FIN)</li>
              <li><strong>Deskripsi:</strong> Jelaskan fungsi dan tanggung jawab divisi</li>
              <li><strong>Kepala Divisi:</strong> Informasi kontak kepala divisi (opsional)</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Kode divisi harus unik dan tidak boleh sama</li>
              <li>Kode akan digunakan untuk identifikasi divisi</li>
              <li>Pastikan nama divisi sesuai dengan struktur organisasi</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Contoh Divisi:</h6>
            <ul class="mb-0">
              <li><strong>IT:</strong> Information Technology</li>
              <li><strong>HR:</strong> Human Resources</li>
              <li><strong>FIN:</strong> Finance & Accounting</li>
              <li><strong>MKT:</strong> Marketing & Sales</li>
              <li><strong>OPS:</strong> Operations</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase code
  const codeInput = document.getElementById('code');
  codeInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  });

  // Auto generate code from name
  const nameInput = document.getElementById('name');
  nameInput.addEventListener('input', function() {
    if (!codeInput.value) {
      const words = this.value.split(' ');
      let code = '';
      
      if (words.length === 1) {
        code = words[0].substring(0, 5).toUpperCase();
      } else {
        code = words.map(word => word.substring(0, 2).toUpperCase()).join('').substring(0, 10);
      }
      
      codeInput.value = code.replace(/[^A-Z0-9]/g, '');
    }
  });

  // Format phone number
  const phoneInput = document.getElementById('head_phone');
  phoneInput.addEventListener('input', function() {
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });
});
</script>
@endsection
