<!-- Supplier Lookup Modal -->
<div class="modal fade" id="supplierLookupModal" tabindex="-1" aria-labelledby="supplierLookupModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="supplierLookupModalLabel">
          <i class="ri-search-line me-2"></i>Pilih Supplier
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Search -->
        <div class="row mb-3">
          <div class="col-md-8">
            <input type="text" id="supplierSearch" class="form-control" placeholder="Cari supplier...">
          </div>
          <div class="col-md-4">
            <button type="button" id="searchSupplierBtn" class="btn btn-primary">
              <i class="ri-search-line me-1"></i>Cari
            </button>
          </div>
        </div>

        <!-- Loading -->
        <div id="supplierLoading" class="text-center py-4" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 text-muted">Memuat data supplier...</p>
        </div>

        <!-- Error -->
        <div id="supplierError" class="alert alert-danger" style="display: none;">
          <i class="ri-error-warning-line me-2"></i>
          <span id="supplierErrorMessage"></span>
        </div>

        <!-- Supplier List -->
        <div id="supplierList" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Kode</th>
                <th>Nama</th>
                <th>Perusahaan</th>
                <th>Kota</th>
                <th>Telepon</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody id="supplierTableBody">
              <!-- Supplier data will be loaded here -->
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div id="supplierPagination" class="d-flex justify-content-between align-items-center mt-3">
          <div id="supplierInfo" class="text-muted"></div>
          <nav>
            <ul id="supplierPaginationList" class="pagination pagination-sm mb-0">
              <!-- Pagination will be loaded here -->
            </ul>
          </nav>
        </div>

        <!-- No Data -->
        <div id="supplierNoData" class="text-center py-4" style="display: none;">
          <i class="ri-inbox-line text-muted" style="font-size: 3rem;"></i>
          <p class="text-muted mt-2">Tidak ada supplier yang ditemukan</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="ri-close-line me-1"></i>Tutup
        </button>
      </div>
    </div>
  </div>
</div>

<script>
class SupplierLookup {
    constructor(options = {}) {
        this.options = {
            apiUrl: '/api/supplier-lookup',
            modalId: 'supplierLookupModal',
            onSelect: null,
            ...options
        };
        
        this.currentPage = 1;
        this.searchTerm = '';
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // Search input
        document.getElementById('supplierSearch').addEventListener('input', (e) => {
            this.searchTerm = e.target.value;
            this.currentPage = 1;
            this.debounceSearch();
        });

        // Search button
        document.getElementById('searchSupplierBtn').addEventListener('click', () => {
            this.loadSuppliers();
        });

        // Enter key on search
        document.getElementById('supplierSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.loadSuppliers();
            }
        });
    }

    debounceSearch() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadSuppliers();
        }, 500);
    }

    openModal() {
        const modal = new bootstrap.Modal(document.getElementById(this.options.modalId));
        modal.show();
        this.loadSuppliers();
    }

    async loadSuppliers() {
        this.showLoading();

        try {
            const params = new URLSearchParams({
                search: this.searchTerm,
                page: this.currentPage,
                per_page: 10
            });

            const response = await fetch(`${this.options.apiUrl}?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderSuppliers(data.data);
                this.renderPagination(data.pagination);
            } else {
                this.showError('Gagal memuat data supplier');
            }
        } catch (error) {
            console.error('Error loading suppliers:', error);
            this.showError('Terjadi kesalahan saat memuat data');
        }
    }

    renderSuppliers(suppliers) {
        const tbody = document.getElementById('supplierTableBody');
        
        if (suppliers.length === 0) {
            this.showNoData();
            return;
        }

        this.hideLoading();
        this.hideError();
        this.hideNoData();

        tbody.innerHTML = suppliers.map(supplier => `
            <tr style="cursor: pointer;" ondblclick="window.supplierLookup.selectSupplier(${supplier.id}, '${supplier.name}', '${supplier.company_name || ''}')">
                <td><strong>${supplier.supplier_code}</strong></td>
                <td>${supplier.name}</td>
                <td>${supplier.company_name || '-'}</td>
                <td>${supplier.city || '-'}</td>
                <td>${supplier.phone || '-'}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary" 
                            onclick="window.supplierLookup.selectSupplier(${supplier.id}, '${supplier.name}', '${supplier.company_name || ''}')">
                        <i class="ri-check-line"></i> Pilih
                    </button>
                </td>
            </tr>
        `).join('');
    }

    renderPagination(pagination) {
        const info = document.getElementById('supplierInfo');
        const paginationList = document.getElementById('supplierPaginationList');

        // Update info
        info.textContent = `Menampilkan ${pagination.per_page * (pagination.current_page - 1) + 1} - ${Math.min(pagination.per_page * pagination.current_page, pagination.total)} dari ${pagination.total} supplier`;

        // Update pagination
        let paginationHTML = '';

        // Previous button
        if (pagination.current_page > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="window.supplierLookup.goToPage(${pagination.current_page - 1})">Previous</a></li>`;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            paginationHTML += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="window.supplierLookup.goToPage(${i})">${i}</a></li>`;
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="window.supplierLookup.goToPage(${pagination.current_page + 1})">Next</a></li>`;
        }

        paginationList.innerHTML = paginationHTML;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadSuppliers();
    }

    selectSupplier(id, name, companyName) {
        if (this.options.onSelect) {
            this.options.onSelect(id, name, companyName);
        }
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById(this.options.modalId));
        modal.hide();
    }

    showLoading() {
        document.getElementById('supplierLoading').style.display = 'block';
        document.getElementById('supplierList').style.display = 'none';
        document.getElementById('supplierError').style.display = 'none';
        document.getElementById('supplierNoData').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('supplierLoading').style.display = 'none';
        document.getElementById('supplierList').style.display = 'block';
    }

    showError(message) {
        document.getElementById('supplierError').style.display = 'block';
        document.getElementById('supplierErrorMessage').textContent = message;
        document.getElementById('supplierLoading').style.display = 'none';
        document.getElementById('supplierList').style.display = 'none';
        document.getElementById('supplierNoData').style.display = 'none';
    }

    hideError() {
        document.getElementById('supplierError').style.display = 'none';
    }

    showNoData() {
        document.getElementById('supplierNoData').style.display = 'block';
        document.getElementById('supplierLoading').style.display = 'none';
        document.getElementById('supplierList').style.display = 'none';
        document.getElementById('supplierError').style.display = 'none';
    }

    hideNoData() {
        document.getElementById('supplierNoData').style.display = 'none';
    }
}

// Initialize supplier lookup when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.supplierLookup = new SupplierLookup({
        onSelect: function(id, name, companyName) {
            // This will be overridden by the form that uses this component
            console.log('Supplier selected:', id, name, companyName);
        }
    });
});
</script>
