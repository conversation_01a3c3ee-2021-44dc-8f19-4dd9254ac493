<?php $__env->startSection('title', 'Detail Asset Digital'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Detail Asset Digital</h5>
        <div class="d-flex gap-2">
          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('asset-digitals.edit')): ?>
          <a href="<?php echo e(route('asset-digitals.edit', $assetDigital)); ?>" class="btn btn-primary">
            <i class="ri-pencil-line me-1"></i>Edit
          </a>
          <?php endif; ?>
          <a href="<?php echo e(route('asset-digitals.index')); ?>" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
      </div>
      
      <div class="card-body">
        <div class="row">
          <!-- Basic Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Dasar</h6>
            
            <div class="mb-3">
              <label class="form-label text-muted">Kode Asset</label>
              <div class="fw-bold"><?php echo e($assetDigital->asset_code); ?></div>
            </div>

            <div class="mb-3">
              <label class="form-label text-muted">Nama Asset Digital</label>
              <div class="fw-bold"><?php echo e($assetDigital->name); ?></div>
            </div>

            <div class="mb-3">
              <label class="form-label text-muted">Tipe Lisensi</label>
              <div>
                <span class="badge bg-label-info"><?php echo e($assetDigital->license_type); ?></span>
              </div>
            </div>

            <?php if($assetDigital->license_key): ?>
            <div class="mb-3">
              <label class="form-label text-muted">License Key</label>
              <div class="d-flex align-items-center">
                <code id="licenseKey" class="me-2"><?php echo e($assetDigital->license_key); ?></code>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('licenseKey')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->supplier || $assetDigital->vendor): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Supplier</label>
              <div>
                <?php if($assetDigital->supplier): ?>
                  <strong><?php echo e($assetDigital->supplier->name); ?></strong>
                  <?php if($assetDigital->supplier->company_name): ?>
                    <br><small class="text-muted"><?php echo e($assetDigital->supplier->company_name); ?></small>
                  <?php endif; ?>
                  <?php if($assetDigital->supplier->phone): ?>
                    <br><small class="text-muted"><i class="ri-phone-line me-1"></i><?php echo e($assetDigital->supplier->phone); ?></small>
                  <?php endif; ?>
                  <?php if($assetDigital->supplier->email): ?>
                    <br><small class="text-muted"><i class="ri-mail-line me-1"></i><?php echo e($assetDigital->supplier->email); ?></small>
                  <?php endif; ?>
                <?php else: ?>
                  <?php echo e($assetDigital->vendor); ?>

                <?php endif; ?>
              </div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->purchaseOrder): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Purchase Order</label>
              <div>
                <strong><?php echo e($assetDigital->purchaseOrder->po_number); ?></strong>
                <br>
                <small class="text-muted">
                  <i class="ri-calendar-line me-1"></i>
                  <?php echo e($assetDigital->purchaseOrder->po_date->format('d/m/Y')); ?>

                </small>
                <br>
                <small class="text-muted">
                  <i class="ri-building-line me-1"></i>
                  <?php echo e($assetDigital->purchaseOrder->supplier->name ?? 'N/A'); ?>

                </small>
                <br>
                <small class="text-muted">
                  <i class="ri-money-dollar-circle-line me-1"></i>
                  <?php echo e('Rp ' . number_format($assetDigital->purchaseOrder->total_amount, 0, ',', '.')); ?>

                </small>
                <br>
                <span class="badge bg-<?php echo e($assetDigital->purchaseOrder->status == 'approved' ? 'success' : ($assetDigital->purchaseOrder->status == 'completed' ? 'primary' : 'warning')); ?>">
                  <?php echo e(ucfirst(str_replace('_', ' ', $assetDigital->purchaseOrder->status))); ?>

                </span>
              </div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->version): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Versi</label>
              <div><?php echo e($assetDigital->version); ?></div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->description): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Deskripsi</label>
              <div><?php echo e($assetDigital->description); ?></div>
            </div>
            <?php endif; ?>

            <div class="mb-3">
              <label class="form-label text-muted">Status</label>
              <div>
                <span class="badge bg-label-<?php echo e($assetDigital->status === 'active' ? 'success' : 
                  ($assetDigital->status === 'inactive' ? 'secondary' : 
                  ($assetDigital->status === 'expired' ? 'danger' : 'warning'))); ?>">
                  <?php echo e(ucfirst($assetDigital->status)); ?>

                </span>
                <?php if($assetDigital->isExpired()): ?>
                  <span class="badge bg-label-danger ms-1">Expired</span>
                <?php elseif($assetDigital->isExpiringSoon()): ?>
                  <span class="badge bg-label-warning ms-1">Expiring Soon</span>
                <?php endif; ?>
              </div>
            </div>
          </div>

          <!-- Login & Access Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Login & Akses</h6>
            
            <?php if($assetDigital->username): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Username</label>
              <div class="d-flex align-items-center">
                <code id="username" class="me-2"><?php echo e($assetDigital->username); ?></code>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('username')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->password): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Password</label>
              <div class="d-flex align-items-center">
                <code id="password" class="me-2"><?php echo e($assetDigital->decrypted_password); ?></code>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('password')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->login_url): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Login URL</label>
              <div class="d-flex align-items-center">
                <a href="<?php echo e($assetDigital->login_url); ?>" target="_blank" class="me-2">
                  <?php echo e($assetDigital->login_url); ?>

                </a>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('loginUrl')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
              <div id="loginUrl" class="d-none"><?php echo e($assetDigital->login_url); ?></div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->max_users): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Usage</label>
              <div>
                <div class="d-flex justify-content-between mb-1">
                  <span><?php echo e($assetDigital->current_users ?? 0); ?> / <?php echo e($assetDigital->max_users); ?> users</span>
                  <span><?php echo e($assetDigital->getUsagePercentage()); ?>%</span>
                </div>
                <div class="progress" style="height: 6px;">
                  <div class="progress-bar" role="progressbar" 
                       style="width: <?php echo e($assetDigital->getUsagePercentage()); ?>%"
                       aria-valuenow="<?php echo e($assetDigital->getUsagePercentage()); ?>" 
                       aria-valuemin="0" aria-valuemax="100">
                  </div>
                </div>
              </div>
            </div>
            <?php endif; ?>

            <div class="mb-3">
              <label class="form-label text-muted">Cabang</label>
              <div><?php echo e($assetDigital->branch->name); ?> (<?php echo e($assetDigital->branch->code); ?>)</div>
            </div>

            <?php if($assetDigital->assignedTo): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Assigned To</label>
              <div>
                <strong><?php echo e($assetDigital->assignedTo->name); ?></strong>
                <small class="text-muted d-block">NIK: <?php echo e($assetDigital->assignedTo->nik); ?></small>
                <?php if($assetDigital->assignedTo->email): ?>
                  <small class="text-muted d-block">Email: <?php echo e($assetDigital->assignedTo->email); ?></small>
                <?php endif; ?>
              </div>
            </div>
            <?php endif; ?>
          </div>
        </div>

        <hr class="my-4">

        <div class="row">
          <!-- Purchase & Financial Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Pembelian</h6>
            
            <?php if($assetDigital->purchase_date): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Pembelian</label>
              <div><?php echo e($assetDigital->purchase_date->format('d/m/Y')); ?></div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->expiry_date): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Kadaluarsa</label>
              <div>
                <?php echo e($assetDigital->expiry_date->format('d/m/Y')); ?>

                <?php if($assetDigital->getDaysUntilExpiry() !== null): ?>
                  <small class="text-muted d-block">
                    <?php if($assetDigital->isExpired()): ?>
                      Expired <?php echo e(abs($assetDigital->getDaysUntilExpiry())); ?> days ago
                    <?php else: ?>
                      <?php echo e($assetDigital->getDaysUntilExpiry()); ?> days remaining
                    <?php endif; ?>
                  </small>
                <?php endif; ?>
              </div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->purchase_price): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Harga Pembelian</label>
              <div>Rp <?php echo e(number_format($assetDigital->purchase_price, 2, ',', '.')); ?></div>
            </div>
            <?php endif; ?>
          </div>

          <!-- Additional Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Tambahan</h6>
            
            <div class="mb-3">
              <label class="form-label text-muted">Dibuat Oleh</label>
              <div><?php echo e($assetDigital->createdBy->name ?? 'System'); ?></div>
            </div>

            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Dibuat</label>
              <div><?php echo e($assetDigital->created_at->format('d/m/Y H:i')); ?></div>
            </div>

            <?php if($assetDigital->updated_at != $assetDigital->created_at): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Terakhir Diperbarui</label>
              <div><?php echo e($assetDigital->updated_at->format('d/m/Y H:i')); ?></div>
            </div>
            <?php endif; ?>

            <?php if($assetDigital->notes): ?>
            <div class="mb-3">
              <label class="form-label text-muted">Catatan</label>
              <div><?php echo e($assetDigital->notes); ?></div>
            </div>
            <?php endif; ?>
          </div>
        </div>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('asset-digitals.delete')): ?>
        <hr class="my-4">
        <div class="d-flex justify-content-end">
          <form action="<?php echo e(route('asset-digitals.destroy', $assetDigital)); ?>" method="POST" 
                onsubmit="return confirm('Apakah Anda yakin ingin menghapus asset digital ini?')">
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <button type="submit" class="btn btn-danger">
              <i class="ri-delete-bin-line me-1"></i>Hapus Asset Digital
            </button>
          </form>
        </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<script>
function copyToClipboard(elementId) {
  const element = document.getElementById(elementId);
  const text = element.textContent || element.innerText;
  
  navigator.clipboard.writeText(text).then(function() {
    // Show success message
    Swal.fire({
      icon: 'success',
      title: 'Copied!',
      text: 'Text copied to clipboard',
      timer: 1500,
      showConfirmButton: false
    });
  }).catch(function(err) {
    console.error('Could not copy text: ', err);
  });
}
</script>

<?php if(session('success')): ?>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
      icon: 'success',
      title: 'Berhasil!',
      text: '<?php echo e(session('success')); ?>',
      timer: 3000,
      showConfirmButton: false
    });
  });
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/asset-digitals/show.blade.php ENDPATH**/ ?>