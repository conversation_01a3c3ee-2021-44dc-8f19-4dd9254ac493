<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_workflows', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama workflow
            $table->string('code')->unique(); // Kode unik workflow
            $table->text('description')->nullable(); // Deskripsi workflow
            $table->enum('module', ['asset_requests', 'purchase_orders', 'budget_requests'])->default('asset_requests'); // Module yang menggunakan workflow
            $table->json('conditions'); // Kondisi untuk menggunakan workflow ini (JSON)
            $table->boolean('is_active')->default(true); // Status aktif
            $table->integer('priority')->default(0); // Prioritas workflow (semakin tinggi semakin prioritas)
            $table->timestamps();

            $table->index(['module', 'is_active']);
            $table->index(['priority', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_workflows');
    }
};
