<?php $__env->startSection('title', 'Detail Purchase Order - Asset Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi / Purchase Order /</span> <?php echo e($purchaseOrder->po_number); ?>

  </h4>

  <!-- Header Information -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Informasi Purchase Order</h5>
      <div>
        <span class="badge bg-<?php echo e($purchaseOrder->status_badge); ?> me-2"><?php echo e($purchaseOrder->status_label); ?></span>
        
        <?php if($purchaseOrder->canBeEdited()): ?>
          <a href="<?php echo e(route('purchase-orders.edit', $purchaseOrder)); ?>" class="btn btn-sm btn-primary">
            <i class="ri-edit-line me-1"></i>Edit
          </a>
        <?php endif; ?>
        
        <a href="<?php echo e(route('purchase-orders.print', $purchaseOrder)); ?>" class="btn btn-sm btn-outline-primary" target="_blank">
          <i class="ri-printer-line me-1"></i>Print PDF
        </a>
      </div>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label fw-bold">Nomor PO</label>
          <p class="mb-0"><?php echo e($purchaseOrder->po_number); ?></p>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">Tanggal PO</label>
          <p class="mb-0"><?php echo e($purchaseOrder->po_date->format('d/m/Y')); ?></p>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">Tanggal Pengiriman</label>
          <p class="mb-0"><?php echo e($purchaseOrder->delivery_date ? $purchaseOrder->delivery_date->format('d/m/Y') : '-'); ?></p>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">Cabang</label>
          <p class="mb-0"><?php echo e($purchaseOrder->branch->name); ?></p>
        </div>
      </div>

      <div class="row g-3 mt-2">
        <div class="col-md-6">
          <label class="form-label fw-bold">Supplier</label>
          <p class="mb-0"><?php echo e($purchaseOrder->supplier->name); ?></p>
          <small class="text-muted"><?php echo e($purchaseOrder->supplier->supplier_code); ?></small>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">Pajak</label>
          <p class="mb-0"><?php echo e($purchaseOrder->tax_percentage); ?>%</p>
        </div>
        <div class="col-md-3">
          <label class="form-label fw-bold">Diskon</label>
          <p class="mb-0"><?php echo e($purchaseOrder->discount_percentage); ?>%</p>
        </div>
      </div>

      <?php if($purchaseOrder->payment_terms || $purchaseOrder->delivery_address): ?>
        <div class="row g-3 mt-2">
          <?php if($purchaseOrder->payment_terms): ?>
            <div class="col-md-6">
              <label class="form-label fw-bold">Syarat Pembayaran</label>
              <p class="mb-0"><?php echo e($purchaseOrder->payment_terms); ?></p>
            </div>
          <?php endif; ?>
          <?php if($purchaseOrder->delivery_address): ?>
            <div class="col-md-6">
              <label class="form-label fw-bold">Alamat Pengiriman</label>
              <p class="mb-0"><?php echo e($purchaseOrder->delivery_address); ?></p>
            </div>
          <?php endif; ?>
        </div>
      <?php endif; ?>

      <div class="row g-3 mt-2">
        <div class="col-md-3">
          <label class="form-label fw-bold">Dibuat Oleh</label>
          <p class="mb-0"><?php echo e($purchaseOrder->createdBy->name); ?></p>
          <small class="text-muted"><?php echo e($purchaseOrder->created_at->format('d/m/Y H:i')); ?></small>
        </div>
        <?php if($purchaseOrder->approved_by): ?>
          <div class="col-md-3">
            <label class="form-label fw-bold">Disetujui Oleh</label>
            <p class="mb-0"><?php echo e($purchaseOrder->approvedBy->name); ?></p>
            <small class="text-muted"><?php echo e($purchaseOrder->approved_at->format('d/m/Y H:i')); ?></small>
          </div>
        <?php endif; ?>
        <?php if($purchaseOrder->approval_notes): ?>
          <div class="col-md-6">
            <label class="form-label fw-bold">Catatan Persetujuan</label>
            <p class="mb-0"><?php echo e($purchaseOrder->approval_notes); ?></p>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>

  <!-- Items Section -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">Item Purchase Order</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered">
          <thead class="table-light">
            <tr>
              <th width="5%">No</th>
              <th width="20%">Nama Item</th>
              <th width="10%">Kode</th>
              <th width="15%">Merk/Model</th>
              <th width="20%">Spesifikasi</th>
              <th width="8%">Qty</th>
              <th width="8%">Satuan</th>
              <th width="12%">Harga Satuan</th>
              <th width="12%">Total</th>
            </tr>
          </thead>
          <tbody>
            <?php $__currentLoopData = $purchaseOrder->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <tr>
                <td class="text-center"><?php echo e($index + 1); ?></td>
                <td>
                  <strong><?php echo e($item->item_name); ?></strong>
                  <?php if($item->item_description): ?>
                    <br><small class="text-muted"><?php echo e($item->item_description); ?></small>
                  <?php endif; ?>
                </td>
                <td><?php echo e($item->item_code ?: '-'); ?></td>
                <td>
                  <?php if($item->brand || $item->model): ?>
                    <?php echo e($item->brand); ?><?php echo e($item->brand && $item->model ? ' / ' : ''); ?><?php echo e($item->model); ?>

                  <?php else: ?>
                    -
                  <?php endif; ?>
                </td>
                <td><?php echo e($item->specification ?: '-'); ?></td>
                <td class="text-center"><?php echo e(number_format($item->quantity, 0, ',', '.')); ?></td>
                <td class="text-center"><?php echo e($item->unit); ?></td>
                <td class="text-end">Rp <?php echo e(number_format($item->unit_price, 0, ',', '.')); ?></td>
                <td class="text-end"><strong>Rp <?php echo e(number_format($item->total_price, 0, ',', '.')); ?></strong></td>
              </tr>
              <?php if($item->notes): ?>
                <tr>
                  <td></td>
                  <td colspan="8">
                    <small class="text-muted"><em>Catatan: <?php echo e($item->notes); ?></em></small>
                  </td>
                </tr>
              <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </tbody>
        </table>
      </div>
      
      <!-- Totals -->
      <div class="row mt-4">
        <div class="col-md-8"></div>
        <div class="col-md-4">
          <table class="table table-sm">
            <tr>
              <td><strong>Subtotal:</strong></td>
              <td class="text-end"><strong>Rp <?php echo e(number_format($purchaseOrder->subtotal_amount, 0, ',', '.')); ?></strong></td>
            </tr>
            <?php if($purchaseOrder->discount_amount > 0): ?>
              <tr>
                <td>Diskon (<?php echo e($purchaseOrder->discount_percentage); ?>%):</td>
                <td class="text-end">Rp <?php echo e(number_format($purchaseOrder->discount_amount, 0, ',', '.')); ?></td>
              </tr>
            <?php endif; ?>
            <?php if($purchaseOrder->tax_amount > 0): ?>
              <tr>
                <td>Pajak (<?php echo e($purchaseOrder->tax_percentage); ?>%):</td>
                <td class="text-end">Rp <?php echo e(number_format($purchaseOrder->tax_amount, 0, ',', '.')); ?></td>
              </tr>
            <?php endif; ?>
            <tr class="table-primary">
              <td><strong>Total:</strong></td>
              <td class="text-end"><strong>Rp <?php echo e(number_format($purchaseOrder->total_amount, 0, ',', '.')); ?></strong></td>
            </tr>
          </table>
          
          <!-- Terbilang -->
          <div class="mt-3">
            <small class="text-muted">Terbilang:</small>
            <div class="fw-bold text-primary"><?php echo e(terbilang_rupiah($purchaseOrder->total_amount)); ?></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Information -->
  <?php if($purchaseOrder->notes || $purchaseOrder->terms_conditions): ?>
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">Informasi Tambahan</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <?php if($purchaseOrder->notes): ?>
            <div class="col-md-6">
              <label class="form-label fw-bold">Catatan</label>
              <p class="mb-0"><?php echo e($purchaseOrder->notes); ?></p>
            </div>
          <?php endif; ?>
          <?php if($purchaseOrder->terms_conditions): ?>
            <div class="col-md-6">
              <label class="form-label fw-bold">Syarat & Ketentuan</label>
              <p class="mb-0"><?php echo e($purchaseOrder->terms_conditions); ?></p>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  <?php endif; ?>

  <!-- Action Buttons -->
  <div class="card">
    <div class="card-body">
      <div class="d-flex justify-content-between align-items-center">
        <a href="<?php echo e(route('purchase-orders.index')); ?>" class="btn btn-outline-secondary">
          <i class="ri-arrow-left-line me-1"></i>Kembali ke Daftar
        </a>
        
        <div class="d-flex gap-2">
          <?php if($purchaseOrder->canBeSubmitted()): ?>
            <form action="<?php echo e(route('purchase-orders.submit', $purchaseOrder)); ?>" method="POST" class="d-inline">
              <?php echo csrf_field(); ?>
              <button type="submit" class="btn btn-success" onclick="return confirm('Yakin ingin mengajukan PO ini untuk persetujuan?')">
                <i class="ri-send-plane-line me-1"></i>Ajukan untuk Persetujuan
              </button>
            </form>
          <?php endif; ?>
          
          <?php if($purchaseOrder->canBeApproved()): ?>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#approveModal">
              <i class="ri-check-line me-1"></i>Setujui
            </button>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
              <i class="ri-close-line me-1"></i>Tolak
            </button>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Approve Modal -->
<?php if($purchaseOrder->canBeApproved()): ?>
  <div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <form action="<?php echo e(route('purchase-orders.approve', $purchaseOrder)); ?>" method="POST">
          <?php echo csrf_field(); ?>
          <div class="modal-header">
            <h5 class="modal-title">Setujui Purchase Order</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>Yakin ingin menyetujui Purchase Order <strong><?php echo e($purchaseOrder->po_number); ?></strong>?</p>
            <div class="mb-3">
              <label class="form-label">Catatan Persetujuan (Opsional)</label>
              <textarea class="form-control" name="approval_notes" rows="3" placeholder="Catatan tambahan..."></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-primary">Setujui</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Reject Modal -->
  <div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <form action="<?php echo e(route('purchase-orders.reject', $purchaseOrder)); ?>" method="POST">
          <?php echo csrf_field(); ?>
          <div class="modal-header">
            <h5 class="modal-title">Tolak Purchase Order</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>Yakin ingin menolak Purchase Order <strong><?php echo e($purchaseOrder->po_number); ?></strong>?</p>
            <div class="mb-3">
              <label class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
              <textarea class="form-control" name="approval_notes" rows="3" placeholder="Jelaskan alasan penolakan..." required></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
            <button type="submit" class="btn btn-danger">Tolak</button>
          </div>
        </form>
      </div>
    </div>
  </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/purchase-orders/show.blade.php ENDPATH**/ ?>