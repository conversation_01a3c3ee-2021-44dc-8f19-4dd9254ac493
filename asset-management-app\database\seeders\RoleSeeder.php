<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'slug' => 'super-admin',
                'description' => 'Full access to all system features',
                'is_active' => true,
            ],
            [
                'name' => 'Admin',
                'slug' => 'admin',
                'description' => 'Administrative access with some restrictions',
                'is_active' => true,
            ],
            [
                'name' => 'Manager',
                'slug' => 'manager',
                'description' => 'Branch manager with limited administrative access',
                'is_active' => true,
            ],
            [
                'name' => 'Staff',
                'slug' => 'staff',
                'description' => 'Basic staff access for asset viewing and basic operations',
                'is_active' => true,
            ],
            [
                'name' => 'Viewer',
                'slug' => 'viewer',
                'description' => 'Read-only access to view assets and reports',
                'is_active' => true,
            ],
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insert([
                'name' => $role['name'],
                'slug' => $role['slug'],
                'description' => $role['description'],
                'is_active' => $role['is_active'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Assign all permissions to Super Admin
        $superAdminId = DB::table('roles')->where('slug', 'super-admin')->first()->id;
        $permissions = DB::table('permissions')->get();

        foreach ($permissions as $permission) {
            DB::table('role_permissions')->insert([
                'role_id' => $superAdminId,
                'permission_id' => $permission->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Assign specific permissions to User role
        $userId = DB::table('roles')->where('slug', 'user')->first()->id;
        $userPermissions = [
            'reports.view',
            'reports.assets.view',
            'reports.assets.export',
            'assets.view',
            'assets.show',
        ];

        foreach ($userPermissions as $permissionSlug) {
            $permission = DB::table('permissions')->where('slug', $permissionSlug)->first();
            if ($permission) {
                DB::table('role_permissions')->insert([
                    'role_id' => $userId,
                    'permission_id' => $permission->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
