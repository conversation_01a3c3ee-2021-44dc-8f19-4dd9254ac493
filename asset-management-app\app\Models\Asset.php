<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Asset extends Model
{
    use HasFactory;

    protected $fillable = [
        'asset_code',
        'name',
        'asset_category_id',
        'asset_type_id',
        'branch_id',
        'supplier_id',
        'description',
        'brand',
        'model',
        'serial_number',
        'purchase_date',
        'purchase_price',
        'condition',
        'status',
        'location',
        'assigned_to',
        'assigned_at',
        'assigned_by',
        'assignment_notes',
        'assignment_status',
        'notes',
        'image',
        'dynamic_fields',
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'purchase_price' => 'decimal:2',
        'assigned_at' => 'datetime',
        'dynamic_fields' => 'array',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(AssetCategory::class, 'asset_category_id');
    }

    public function assetCategory()
    {
        return $this->belongsTo(AssetCategory::class, 'asset_category_id');
    }

    public function assetType()
    {
        return $this->belongsTo(AssetType::class, 'asset_type_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function assignedEmployee()
    {
        return $this->belongsTo(Employee::class, 'assigned_to');
    }

    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function assignments()
    {
        return $this->hasMany(AssetAssignment::class);
    }

    public function currentAssignment()
    {
        return $this->hasOne(AssetAssignment::class)->where('status', 'active');
    }

    public function assignmentHistory()
    {
        return $this->hasMany(AssetAssignment::class)->orderBy('assigned_at', 'desc');
    }

    public function getAssignmentStatusTextAttribute()
    {
        return match($this->assignment_status) {
            'assigned' => 'Di-assign',
            'returned' => 'Dikembalikan',
            'transferred' => 'Dipindahkan',
            default => 'Tersedia'
        };
    }

    public function getIsAssignedAttribute()
    {
        return !is_null($this->assigned_to);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('asset_category_id', $categoryId);
    }

    public function scopeByCondition($query, $condition)
    {
        return $query->where('condition', $condition);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Accessors
    public function getFormattedPurchasePriceAttribute()
    {
        return 'Rp ' . number_format((float)$this->purchase_price, 0, ',', '.');
    }

    public function getConditionBadgeAttribute()
    {
        $badges = [
            'excellent' => 'success',
            'good' => 'primary',
            'fair' => 'warning',
            'poor' => 'danger',
        ];

        return $badges[$this->condition] ?? 'secondary';
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => 'success',
            'inactive' => 'secondary',
            'maintenance' => 'warning',
            'disposed' => 'danger',
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    // Mutators
    public function setAssetCodeAttribute($value)
    {
        $this->attributes['asset_code'] = strtoupper($value);
    }

    // Static methods
    public static function generateAssetCode($categoryId, $branchId)
    {
        return \App\Models\DocumentNumber::getAssetNumber($categoryId, $branchId);
    }

    // Dynamic Fields Methods
    public function getDynamicFieldValue($fieldName, $default = null)
    {
        return $this->dynamic_fields[$fieldName] ?? $default;
    }

    public function setDynamicFieldValue($fieldName, $value)
    {
        $dynamicFields = $this->dynamic_fields ?? [];
        $dynamicFields[$fieldName] = $value;
        $this->dynamic_fields = $dynamicFields;
    }

    public function getFieldConfigurations()
    {
        return AssetFieldConfiguration::forCategory($this->asset_category_id)
            ->active()
            ->ordered()
            ->get();
    }

    public function getFieldConfigurationsByGroup()
    {
        return $this->getFieldConfigurations()->groupBy('field_group');
    }
}
