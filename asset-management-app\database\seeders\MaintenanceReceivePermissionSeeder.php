<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MaintenanceReceivePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create maintenance receive permission
        $permission = \App\Models\Permission::firstOrCreate([
            'name' => 'Receive Maintenance',
            'slug' => 'maintenance_receive',
            'module' => 'maintenance',
            'description' => 'Can receive and confirm maintenance completion'
        ]);

        // Assign to roles
        $roles = [
            'super-admin',
            'admin',
            'manager',
            'staff' // Staff can receive maintenance for their assets
        ];

        foreach ($roles as $roleSlug) {
            $role = \App\Models\Role::where('slug', $roleSlug)->first();
            if ($role) {
                $role->givePermissionTo($permission);
            }
        }
    }
}
