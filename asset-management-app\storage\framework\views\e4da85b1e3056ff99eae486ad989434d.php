<?php $__env->startSection('title', 'Asset Digital'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Daftar Asset Digital</h5>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('asset-digitals.create')): ?>
        <a href="<?php echo e(route('asset-digitals.create')); ?>" class="btn btn-primary">
          <i class="ri-add-line me-1"></i>Tambah Asset Digital
        </a>
        <?php endif; ?>
      </div>
      
      <!-- Filters -->
      <div class="card-body">
        <form method="GET" action="<?php echo e(route('asset-digitals.index')); ?>" class="mb-4">
          <div class="row g-3">
            <div class="col-md-3">
              <label class="form-label">Pencarian</label>
              <input type="text" name="search" class="form-control" placeholder="Nama, kode, supplier, username..." value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-2">
              <label class="form-label">Status</label>
              <select name="status" class="form-select">
                <option value="">Semua Status</option>
                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($status); ?>" <?php echo e(request('status') == $status ? 'selected' : ''); ?>>
                    <?php echo e(ucfirst($status)); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Tipe Lisensi</label>
              <select name="license_type" class="form-select">
                <option value="">Semua Tipe</option>
                <?php $__currentLoopData = $licenseTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($type->lookup_name); ?>" <?php echo e(request('license_type') == $type->lookup_name ? 'selected' : ''); ?>>
                    <?php echo e($type->lookup_name); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <?php if(auth()->user()->isSuperAdmin()): ?>
            <div class="col-md-2">
              <label class="form-label">Cabang</label>
              <select name="branch_id" class="form-select">
                <option value="">Semua Cabang</option>
                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                    <?php echo e($branch->name); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <?php endif; ?>
            <div class="col-md-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                  <i class="ri-search-line me-1"></i>Filter
                </button>
                <a href="<?php echo e(route('asset-digitals.index')); ?>" class="btn btn-outline-secondary">
                  <i class="ri-refresh-line me-1"></i>Reset
                </a>
              </div>
            </div>
          </div>
        </form>

        <!-- Data Table -->
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Kode Asset</th>
                <th>Nama</th>
                <th>Tipe Lisensi</th>
                <th>Supplier</th>
                <th>Username</th>
                <th>Status</th>
                <th>Assigned To</th>
                <th>Expiry Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <?php $__empty_1 = true; $__currentLoopData = $assetDigitals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $asset): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                  <td>
                    <strong><?php echo e($asset->asset_code); ?></strong>
                  </td>
                  <td>
                    <div>
                      <strong><?php echo e($asset->name); ?></strong>
                      <?php if($asset->version): ?>
                        <small class="text-muted d-block">v<?php echo e($asset->version); ?></small>
                      <?php endif; ?>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-label-info"><?php echo e($asset->license_type); ?></span>
                  </td>
                  <td><?php echo e($asset->supplier ? $asset->supplier->name : ($asset->vendor ?? '-')); ?></td>
                  <td><?php echo e($asset->username ?? '-'); ?></td>
                  <td>
                    <span class="badge bg-label-<?php echo e($asset->status === 'active' ? 'success' : 
                      ($asset->status === 'inactive' ? 'secondary' : 
                      ($asset->status === 'expired' ? 'danger' : 'warning'))); ?>">
                      <?php echo e(ucfirst($asset->status)); ?>

                    </span>
                    <?php if($asset->isExpiringSoon()): ?>
                      <small class="text-warning d-block">
                        <i class="ri-time-line"></i> Expires in <?php echo e($asset->getDaysUntilExpiry()); ?> days
                      </small>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($asset->assignedTo): ?>
                      <div>
                        <strong><?php echo e($asset->assignedTo->name); ?></strong>
                        <small class="text-muted d-block"><?php echo e($asset->assignedTo->nik); ?></small>
                      </div>
                    <?php else: ?>
                      <span class="text-muted">Unassigned</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($asset->expiry_date): ?>
                      <div>
                        <?php echo e($asset->expiry_date->format('d/m/Y')); ?>

                        <?php if($asset->isExpired()): ?>
                          <small class="text-danger d-block">Expired</small>
                        <?php elseif($asset->isExpiringSoon()): ?>
                          <small class="text-warning d-block">Expiring Soon</small>
                        <?php endif; ?>
                      </div>
                    <?php else: ?>
                      <span class="text-muted">No Expiry</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        Actions
                      </button>
                      <div class="dropdown-menu">
                        <a class="dropdown-item" href="<?php echo e(route('asset-digitals.show', $asset)); ?>">
                          <i class="ri-eye-line me-2"></i>Detail
                        </a>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('asset-digitals.edit')): ?>
                        <a class="dropdown-item" href="<?php echo e(route('asset-digitals.edit', $asset)); ?>">
                          <i class="ri-pencil-line me-2"></i>Edit
                        </a>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('asset-digitals.delete')): ?>
                        <div class="dropdown-divider"></div>
                        <form action="<?php echo e(route('asset-digitals.destroy', $asset)); ?>" method="POST" class="d-inline" 
                              onsubmit="return confirm('Apakah Anda yakin ingin menghapus asset digital ini?')">
                          <?php echo csrf_field(); ?>
                          <?php echo method_field('DELETE'); ?>
                          <button type="submit" class="dropdown-item text-danger">
                            <i class="ri-delete-bin-line me-2"></i>Hapus
                          </button>
                        </form>
                        <?php endif; ?>
                      </div>
                    </div>
                  </td>
                </tr>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                  <td colspan="9" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                      <i class="ri-database-2-line ri-48px text-muted mb-2"></i>
                      <p class="mb-0">Belum ada data asset digital</p>
                      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('asset-digitals.create')): ?>
                      <a href="<?php echo e(route('asset-digitals.create')); ?>" class="btn btn-primary btn-sm mt-2">
                        <i class="ri-add-line me-1"></i>Tambah Asset Digital Pertama
                      </a>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <?php if($assetDigitals->hasPages()): ?>
        <div class="d-flex justify-content-center mt-4">
          <?php echo e($assetDigitals->withQueryString()->links()); ?>

        </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<?php if(session('success')): ?>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
      icon: 'success',
      title: 'Berhasil!',
      text: '<?php echo e(session('success')); ?>',
      timer: 3000,
      showConfirmButton: false
    });
  });
</script>
<?php endif; ?>

<?php if(session('error')): ?>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
      icon: 'error',
      title: 'Error!',
      text: '<?php echo e(session('error')); ?>',
      timer: 3000,
      showConfirmButton: false
    });
  });
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/asset-digitals/index.blade.php ENDPATH**/ ?>