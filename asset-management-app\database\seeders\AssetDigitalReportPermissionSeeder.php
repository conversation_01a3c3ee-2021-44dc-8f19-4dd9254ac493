<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class AssetDigitalReportPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create asset digital report permissions
        $permissions = [
            [
                'name' => 'View Asset Digital Reports',
                'slug' => 'reports.asset-digitals.view',
                'module' => 'Reports'
            ],
            [
                'name' => 'Export Asset Digital Reports',
                'slug' => 'reports.asset-digitals.export',
                'module' => 'Reports'
            ]
        ];

        foreach ($permissions as $permissionData) {
            Permission::updateOrInsert(
                ['slug' => $permissionData['slug']],
                [
                    'name' => $permissionData['name'],
                    'module' => $permissionData['module'],
                    'description' => $permissionData['name']
                ]
            );
        }

        // Assign permissions to roles
        $roles = ['staff', 'viewer', 'manager', 'admin'];
        
        foreach ($roles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            
            if ($role) {
                foreach ($permissions as $permissionData) {
                    $permission = Permission::where('slug', $permissionData['slug'])->first();
                    
                    if ($permission && !$role->permissions()->where('permission_id', $permission->id)->exists()) {
                        $role->permissions()->attach($permission->id);
                        $this->command->info("Assigned permission '{$permission->slug}' to role '{$role->name}'");
                    }
                }
            }
        }

        // Also assign to super-admin role
        $superAdminRole = Role::where('slug', 'super-admin')->first();
        if ($superAdminRole) {
            foreach ($permissions as $permissionData) {
                $permission = Permission::where('slug', $permissionData['slug'])->first();
                
                if ($permission && !$superAdminRole->permissions()->where('permission_id', $permission->id)->exists()) {
                    $superAdminRole->permissions()->attach($permission->id);
                    $this->command->info("Assigned permission '{$permission->slug}' to role '{$superAdminRole->name}'");
                }
            }
        }

        // Also assign to user role for basic viewing
        $userRole = Role::where('slug', 'user')->first();
        if ($userRole) {
            $viewPermission = Permission::where('slug', 'reports.asset-digitals.view')->first();
            
            if ($viewPermission && !$userRole->permissions()->where('permission_id', $viewPermission->id)->exists()) {
                $userRole->permissions()->attach($viewPermission->id);
                $this->command->info("Assigned permission '{$viewPermission->slug}' to role '{$userRole->name}'");
            }
        }
    }
}
