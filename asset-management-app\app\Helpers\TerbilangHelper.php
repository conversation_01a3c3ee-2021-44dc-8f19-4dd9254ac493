<?php

namespace App\Helpers;

class TerbilangHelper
{
    private static $angka = [
        '', 'satu', 'dua', 'tiga', 'empat', 'lima', 'enam', 'tujuh', 'delapan', 'sembilan',
        'sepuluh', 'sebelas', 'dua belas', 'tiga belas', 'empat belas', 'lima belas',
        'enam belas', 'tujuh belas', 'delapan belas', 'sembilan belas'
    ];

    private static $satuan = [
        '', 'ribu', 'juta', 'miliar', 'triliun'
    ];

    /**
     * Convert number to Indonesian words (terbilang)
     * 
     * @param int|float $number
     * @return string
     */
    public static function convert($number)
    {
        if ($number == 0) {
            return 'nol';
        }

        $number = abs($number);
        $result = '';

        // Handle decimal part
        if (is_float($number) || strpos($number, '.') !== false) {
            $parts = explode('.', $number);
            $integerPart = (int) $parts[0];
            $decimalPart = isset($parts[1]) ? (int) $parts[1] : 0;

            $result = self::convertInteger($integerPart);

            if ($decimalPart > 0) {
                $result .= ' koma ' . self::convertInteger($decimalPart);
            }
        } else {
            $result = self::convertInteger((int) $number);
        }

        return $result;
    }

    /**
     * Convert number to rupiah format with terbilang
     * 
     * @param int|float $number
     * @return string
     */
    public static function rupiah($number)
    {
        if ($number == 0) {
            return 'nol rupiah';
        }

        $number = abs($number);
        $parts = explode('.', number_format($number, 2, '.', ''));
        $integerPart = (int) $parts[0];
        $decimalPart = (int) $parts[1];

        $result = self::convertInteger($integerPart) . ' rupiah';

        if ($decimalPart > 0) {
            $result .= ' ' . self::convertInteger($decimalPart) . ' sen';
        }

        return $result;
    }

    /**
     * Convert integer to Indonesian words
     * 
     * @param int $number
     * @return string
     */
    private static function convertInteger($number)
    {
        if ($number == 0) {
            return '';
        }

        if ($number < 20) {
            return self::$angka[$number];
        }

        if ($number < 100) {
            $tens = intval($number / 10);
            $ones = $number % 10;
            
            if ($tens == 1) {
                return self::$angka[$number];
            }
            
            return self::$angka[$tens] . ' puluh' . (($ones > 0) ? ' ' . self::$angka[$ones] : '');
        }

        if ($number < 200) {
            return 'seratus' . (($number > 100) ? ' ' . self::convertInteger($number - 100) : '');
        }

        if ($number < 1000) {
            $hundreds = intval($number / 100);
            $remainder = $number % 100;
            
            return self::$angka[$hundreds] . ' ratus' . (($remainder > 0) ? ' ' . self::convertInteger($remainder) : '');
        }

        if ($number < 2000) {
            return 'seribu' . (($number > 1000) ? ' ' . self::convertInteger($number - 1000) : '');
        }

        // Handle thousands, millions, billions, trillions
        $groups = [];
        $groupIndex = 0;

        while ($number > 0) {
            $group = $number % 1000;
            if ($group > 0) {
                $groupText = self::convertInteger($group);
                if ($groupIndex > 0) {
                    $groupText .= ' ' . self::$satuan[$groupIndex];
                }
                array_unshift($groups, $groupText);
            }
            $number = intval($number / 1000);
            $groupIndex++;
        }

        return implode(' ', $groups);
    }

    /**
     * Format number as currency with terbilang
     * 
     * @param int|float $number
     * @param string $currency
     * @return array
     */
    public static function formatCurrency($number, $currency = 'Rupiah')
    {
        return [
            'numeric' => number_format($number, 2, ',', '.'),
            'terbilang' => ucfirst(self::rupiah($number)),
            'currency' => $currency
        ];
    }

    /**
     * Get terbilang for display in forms/documents
     * 
     * @param int|float $number
     * @return string
     */
    public static function getTerbilang($number)
    {
        return ucfirst(self::rupiah($number));
    }
}
