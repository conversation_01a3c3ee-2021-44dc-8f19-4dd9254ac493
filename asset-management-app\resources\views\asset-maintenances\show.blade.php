@extends('layouts/contentNavbarLayout')

@section('title', 'Detail Maintenance Asset')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-tools-line me-2"></i>
              Detail Maintenance Asset
            </h5>
            <small class="text-muted">{{ $assetMaintenance->maintenance_number }}</small>
          </div>
          <div class="d-flex gap-2">
            @if($assetMaintenance->canBeStarted())
              @php
                $userPermissions = session('user_permissions', []);
                $canStart = auth()->user()->isSuperAdmin() || in_array('asset_maintenance_start', $userPermissions);
              @endphp
              @if($canStart)
                <form method="POST" action="{{ route('asset-maintenances.start', $assetMaintenance) }}"
                      onsubmit="return confirm('Mulai maintenance ini?')" class="d-inline">
                  @csrf
                  <button type="submit" class="btn btn-success">
                    <i class="ri-play-line me-1"></i>
                    Mulai Maintenance
                  </button>
                </form>
              @endif
            @endif

            @if($assetMaintenance->canBeCompleted())
              @php
                $userPermissions = session('user_permissions', []);
                $canComplete = auth()->user()->isSuperAdmin() || in_array('asset_maintenance_complete', $userPermissions);
              @endphp
              @if($canComplete)
                <form method="POST" action="{{ route('asset-maintenances.complete', $assetMaintenance) }}"
                      onsubmit="return confirm('Selesaikan maintenance ini?')" class="d-inline">
                  @csrf
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-check-line me-1"></i>
                    Selesaikan
                  </button>
                </form>
              @endif
            @endif
            
            @if($assetMaintenance->status !== 'completed')
              <a href="{{ route('asset-maintenances.edit', $assetMaintenance) }}" class="btn btn-outline-primary">
                <i class="ri-edit-line me-1"></i>
                Edit
              </a>
            @endif
            
            @if($assetMaintenance->canBeCancelled())
              <form method="POST" action="{{ route('asset-maintenances.cancel', $assetMaintenance) }}"
                    onsubmit="return confirm('Batalkan maintenance ini?')" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-outline-warning">
                  <i class="ri-close-line me-1"></i>
                  Batalkan
                </button>
              </form>
            @endif

            <!-- Print PDF Button -->
            <div class="btn-group">
              <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="ri-printer-line me-1"></i>
                Cetak PDF
              </button>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item" href="{{ route('maintenance.pdf.form', $assetMaintenance->id) }}" target="_blank">
                    <i class="ri-file-text-line me-2"></i>
                    Form Maintenance
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="{{ route('maintenance.pdf.receipt', $assetMaintenance->id) }}" target="_blank">
                    <i class="ri-receipt-line me-2"></i>
                    Tanda Terima
                  </a>
                </li>
              </ul>
            </div>

            <!-- QR Code Tracking Button -->
            <a href="{{ route('maintenance.track', $assetMaintenance->id) }}" class="btn btn-outline-info" target="_blank">
              <i class="ri-qr-code-line me-1"></i>
              QR Tracking
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Main Information -->
    <div class="col-md-8">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Maintenance
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nomor Maintenance</label>
                <p class="fw-bold">{{ $assetMaintenance->maintenance_number }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge {{ $assetMaintenance->status_badge_class }} fs-6">
                    {{ $assetMaintenance->status_text }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tipe Maintenance</label>
                <p>
                  <span class="badge bg-info fs-6">{{ $assetMaintenance->maintenance_type_text }}</span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Prioritas</label>
                <p>
                  <span class="badge {{ $assetMaintenance->priority_badge_class }} fs-6">
                    {{ $assetMaintenance->priority_text }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-12">
              <div class="mb-3">
                <label class="form-label text-muted">Judul</label>
                <p class="fw-bold">{{ $assetMaintenance->title }}</p>
              </div>
            </div>
            <div class="col-12">
              <div class="mb-3">
                <label class="form-label text-muted">Deskripsi</label>
                <p>{{ $assetMaintenance->description }}</p>
              </div>
            </div>
            @if($assetMaintenance->problem_description)
              <div class="col-12">
                <div class="mb-3">
                  <label class="form-label text-muted">Deskripsi Masalah</label>
                  <p>{{ $assetMaintenance->problem_description }}</p>
                </div>
              </div>
            @endif
            @if($assetMaintenance->solution_description)
              <div class="col-12">
                <div class="mb-3">
                  <label class="form-label text-muted">Solusi</label>
                  <p>{{ $assetMaintenance->solution_description }}</p>
                </div>
              </div>
            @endif
            @if($assetMaintenance->notes)
              <div class="col-12">
                <div class="mb-3">
                  <label class="form-label text-muted">Catatan</label>
                  <p>{{ $assetMaintenance->notes }}</p>
                </div>
              </div>
            @endif
          </div>
        </div>
      </div>

      <!-- Asset Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-computer-line me-2"></i>
            Informasi Asset
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Asset</label>
                <p class="fw-bold">{{ $assetMaintenance->asset->asset_code }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Asset</label>
                <p>{{ $assetMaintenance->asset->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kategori</label>
                <p>{{ $assetMaintenance->asset->assetCategory->name ?? '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Tipe</label>
                <p>{{ $assetMaintenance->asset->assetType->name ?? '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Cabang</label>
                <p>{{ $assetMaintenance->asset->branch->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Brand/Model</label>
                <p>{{ $assetMaintenance->asset->brand }} {{ $assetMaintenance->asset->model }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Supplier Information -->
      @if($assetMaintenance->supplier)
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-building-line me-2"></i>
              Informasi Supplier
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Kode Supplier</label>
                  <p class="fw-bold text-primary">{{ $assetMaintenance->supplier->supplier_code }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label class="form-label text-muted">Nama Supplier</label>
                  <p class="fw-bold">{{ $assetMaintenance->supplier->name }}</p>
                </div>
              </div>
              @if($assetMaintenance->supplier->company_name)
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label text-muted">Perusahaan</label>
                    <p>{{ $assetMaintenance->supplier->company_name }}</p>
                  </div>
                </div>
              @endif
              @if($assetMaintenance->supplier->phone)
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label text-muted">Telepon</label>
                    <p>
                      {{ $assetMaintenance->supplier->phone }}
                      <a href="tel:{{ $assetMaintenance->supplier->phone }}" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="ri-phone-line"></i>
                      </a>
                    </p>
                  </div>
                </div>
              @endif
              @if($assetMaintenance->supplier->email)
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label text-muted">Email</label>
                    <p>
                      {{ $assetMaintenance->supplier->email }}
                      <a href="mailto:{{ $assetMaintenance->supplier->email }}" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="ri-mail-line"></i>
                      </a>
                    </p>
                  </div>
                </div>
              @endif
              @if($assetMaintenance->supplier->city || $assetMaintenance->supplier->province)
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label text-muted">Lokasi</label>
                    <p>
                      <i class="ri-map-pin-line me-1"></i>
                      {{ $assetMaintenance->supplier->city }}{{ $assetMaintenance->supplier->city && $assetMaintenance->supplier->province ? ', ' : '' }}{{ $assetMaintenance->supplier->province }}
                    </p>
                  </div>
                </div>
              @endif
            </div>
          </div>
        </div>
      @endif
    </div>

    <!-- Sidebar Information -->
    <div class="col-md-4">
      <!-- Timeline -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-calendar-line me-2"></i>
            Timeline
          </h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label text-muted">Tanggal Jadwal</label>
            <p class="fw-bold">{{ $assetMaintenance->scheduled_date->format('d/m/Y') }}</p>
          </div>
          @if($assetMaintenance->started_date)
            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Mulai</label>
              <p>{{ $assetMaintenance->started_date->format('d/m/Y') }}</p>
            </div>
          @endif
          @if($assetMaintenance->completed_date)
            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Selesai</label>
              <p>{{ $assetMaintenance->completed_date->format('d/m/Y') }}</p>
            </div>
            @if($assetMaintenance->getDurationInDays())
              <div class="mb-3">
                <label class="form-label text-muted">Durasi</label>
                <p>{{ $assetMaintenance->getDurationInDays() }} hari</p>
              </div>
            @endif
          @endif
          @if($assetMaintenance->isOverdue())
            <div class="alert alert-warning">
              <i class="ri-alarm-warning-line me-2"></i>
              Maintenance terlambat!
            </div>
          @endif
        </div>
      </div>

      <!-- Cost Information -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-money-dollar-circle-line me-2"></i>
            Informasi Biaya
          </h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label text-muted">Estimasi Biaya</label>
            <p class="fw-bold">
              @if($assetMaintenance->estimated_cost)
                Rp {{ number_format($assetMaintenance->estimated_cost, 0, ',', '.') }}
              @else
                -
              @endif
            </p>
          </div>
          @if($assetMaintenance->actual_cost)
            <div class="mb-3">
              <label class="form-label text-muted">Biaya Aktual</label>
              <p class="fw-bold">Rp {{ number_format($assetMaintenance->actual_cost, 0, ',', '.') }}</p>
            </div>
          @endif
        </div>
      </div>

      <!-- People Involved -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-team-line me-2"></i>
            Orang Terlibat
          </h6>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label text-muted">Diminta Oleh</label>
            <p>{{ $assetMaintenance->requestedBy->name }}</p>
          </div>
          @if($assetMaintenance->assignedTo)
            <div class="mb-3">
              <label class="form-label text-muted">Ditugaskan Kepada</label>
              <p>{{ $assetMaintenance->assignedTo->name }}</p>
            </div>
          @endif
          @if($assetMaintenance->approvedBy)
            <div class="mb-3">
              <label class="form-label text-muted">Disetujui Oleh</label>
              <p>{{ $assetMaintenance->approvedBy->name }}</p>
              <small class="text-muted">{{ $assetMaintenance->approved_at->format('d/m/Y H:i') }}</small>
            </div>
          @endif
        </div>
      </div>

      <!-- Vendor Information -->
      @if($assetMaintenance->vendor_name || $assetMaintenance->vendor_contact)
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="ri-building-line me-2"></i>
              Informasi Vendor
            </h6>
          </div>
          <div class="card-body">
            @if($assetMaintenance->vendor_name)
              <div class="mb-3">
                <label class="form-label text-muted">Nama Vendor</label>
                <p>{{ $assetMaintenance->vendor_name }}</p>
              </div>
            @endif
            @if($assetMaintenance->vendor_contact)
              <div class="mb-3">
                <label class="form-label text-muted">Kontak Vendor</label>
                <p>{{ $assetMaintenance->vendor_contact }}</p>
              </div>
            @endif
          </div>
        </div>
      @endif
    </div>
  </div>

  <!-- Back Button -->
  <div class="row">
    <div class="col-12">
      <a href="{{ route('asset-maintenances.index') }}" class="btn btn-outline-secondary">
        <i class="ri-arrow-left-line me-1"></i>
        Kembali ke Daftar
      </a>
    </div>
  </div>
</div>
@endsection
