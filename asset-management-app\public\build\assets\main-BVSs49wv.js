let a;(function(){typeof Waves<"u"&&(Waves.init(),Waves.attach(".btn[class*='btn-']:not(.position-relative):not([class*='btn-outline-']):not([class*='btn-label-'])",["waves-light"]),Waves.attach("[class*='btn-outline-']:not(.position-relative)"),Waves.attach(".pagination .page-item .page-link"),Waves.attach(".dropdown-menu .dropdown-item"),Waves.attach(".light-style .list-group .list-group-item-action"),Waves.attach(".dark-style .list-group .list-group-item-action",["waves-light"]),Waves.attach(".nav-tabs:not(.nav-tabs-widget) .nav-item .nav-link"),Waves.attach(".nav-pills .nav-item .nav-link",["waves-light"]),Waves.attach(".menu-vertical .menu-item .menu-link.menu-toggle"));function l(){var e=document.querySelector(".layout-page");e&&(window.pageYOffset>0?e.classList.add("window-scrolled"):e.classList.remove("window-scrolled"))}setTimeout(()=>{l()},200),window.onscroll=function(){l()},document.querySelectorAll("#layout-menu").forEach(function(e){a=new Menu(e,{orientation:"vertical",closeChildren:!1}),window.Helpers.scrollToActive(!1),window.Helpers.mainMenu=a}),document.querySelectorAll(".layout-menu-toggle").forEach(e=>{e.addEventListener("click",t=>{t.preventDefault(),window.Helpers.toggleCollapsed()})});let c=function(e,t){let o=null;e.onmouseenter=function(){Helpers.isSmallScreen()?o=setTimeout(t,0):o=setTimeout(t,300)},e.onmouseleave=function(){document.querySelector(".layout-menu-toggle").classList.remove("d-block"),clearTimeout(o)}};document.getElementById("layout-menu")&&c(document.getElementById("layout-menu"),function(){Helpers.isSmallScreen()||document.querySelector(".layout-menu-toggle").classList.add("d-block")});let i=document.getElementsByClassName("menu-inner"),n=document.getElementsByClassName("menu-inner-shadow")[0];i.length>0&&n&&i[0].addEventListener("ps-scroll-y",function(){this.querySelector(".ps__thumb-y").offsetTop?n.style.display="block":n.style.display="none"}),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new bootstrap.Tooltip(e)});const s=function(e){var t,o;e.type=="show.bs.collapse"||e.type=="show.bs.collapse"?(e.target.closest(".accordion-item").classList.add("active"),(t=e.target.closest(".accordion-item").previousElementSibling)==null||t.classList.add("previous-active")):(e.target.closest(".accordion-item").classList.remove("active"),(o=e.target.closest(".accordion-item").previousElementSibling)==null||o.classList.remove("previous-active"))};[].slice.call(document.querySelectorAll(".accordion")).map(function(e){e.addEventListener("show.bs.collapse",s),e.addEventListener("hide.bs.collapse",s)}),window.Helpers.setAutoUpdate(!0),window.Helpers.initPasswordToggle(),window.Helpers.initSpeechToText(),window.Helpers.navTabsAnimation(),!window.Helpers.isSmallScreen()&&window.Helpers.setCollapsed(!0,!1)})();
