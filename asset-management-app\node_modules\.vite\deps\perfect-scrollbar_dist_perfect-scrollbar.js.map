{"version": 3, "sources": ["../../perfect-scrollbar/src/lib/css.js", "../../perfect-scrollbar/src/lib/dom.js", "../../perfect-scrollbar/src/lib/class-names.js", "../../perfect-scrollbar/src/lib/event-manager.js", "../../perfect-scrollbar/src/process-scroll-diff.js", "../../perfect-scrollbar/src/lib/util.js", "../../perfect-scrollbar/src/update-geometry.js", "../../perfect-scrollbar/src/handlers/click-rail.js", "../../perfect-scrollbar/src/handlers/drag-thumb.js", "../../perfect-scrollbar/src/handlers/keyboard.js", "../../perfect-scrollbar/src/handlers/mouse-wheel.js", "../../perfect-scrollbar/src/handlers/touch.js", "../../perfect-scrollbar/src/index.js"], "sourcesContent": ["export function get(element) {\n  return getComputedStyle(element);\n}\n\nexport function set(element, obj) {\n  for (const key in obj) {\n    let val = obj[key];\n    if (typeof val === 'number') {\n      val = `${val}px`;\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n", "export function div(className) {\n  const div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nconst elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nexport function matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nexport function remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nexport function queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, child =>\n    matches(child, selector)\n  );\n}\n", "const cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: x => `ps__thumb-${x}`,\n    rail: x => `ps__rail-${x}`,\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: x => `ps--active-${x}`,\n    scrolling: x => `ps--scrolling-${x}`,\n  },\n};\n\nexport default cls;\n\n/*\n * Helper methods\n */\nconst scrollingClassTimeout = { x: null, y: null };\n\nexport function addScrollingClass(i, x) {\n  const classList = i.element.classList;\n  const className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nexport function removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    () => i.isAlive && i.element.classList.remove(cls.state.scrolling(x)),\n    i.settings.scrollingThreshold\n  );\n}\n\nexport function setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n", "class EventElement {\n  constructor(element) {\n    this.element = element;\n    this.handlers = {};\n  }\n\n  bind(eventName, handler) {\n    if (typeof this.handlers[eventName] === 'undefined') {\n      this.handlers[eventName] = [];\n    }\n    this.handlers[eventName].push(handler);\n    this.element.addEventListener(eventName, handler, false);\n  }\n\n  unbind(eventName, target) {\n    this.handlers[eventName] = this.handlers[eventName].filter(handler => {\n      if (target && handler !== target) {\n        return true;\n      }\n      this.element.removeEventListener(eventName, handler, false);\n      return false;\n    });\n  }\n\n  unbindAll() {\n    for (const name in this.handlers) {\n      this.unbind(name);\n    }\n  }\n\n  get isEmpty() {\n    return Object.keys(this.handlers).every(\n      key => this.handlers[key].length === 0\n    );\n  }\n}\n\nexport default class EventManager {\n  constructor() {\n    this.eventElements = [];\n  }\n\n  eventElement(element) {\n    let ee = this.eventElements.filter(ee => ee.element === element)[0];\n    if (!ee) {\n      ee = new EventElement(element);\n      this.eventElements.push(ee);\n    }\n    return ee;\n  }\n\n  bind(element, eventName, handler) {\n    this.eventElement(element).bind(eventName, handler);\n  }\n\n  unbind(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    ee.unbind(eventName, handler);\n\n    if (ee.isEmpty) {\n      // remove\n      this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n    }\n  }\n\n  unbindAll() {\n    this.eventElements.forEach(e => e.unbindAll());\n    this.eventElements = [];\n  }\n\n  once(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    const onceHandler = evt => {\n      ee.unbind(eventName, onceHandler);\n      handler(evt);\n    };\n    ee.bind(eventName, onceHandler);\n  }\n}\n", "import { setScrollingClassInstantly } from './lib/class-names';\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  }\n\n  const evt = document.createEvent('CustomEvent');\n  evt.initCustomEvent(name, false, false, undefined);\n  return evt;\n}\n\nexport default function (i, axis, diff, useScrollingClass = true, forceFireReachEvent = false) {\n  let fields;\n  if (axis === 'top') {\n    fields = ['contentHeight', 'containerHeight', 'scrollTop', 'y', 'up', 'down'];\n  } else if (axis === 'left') {\n    fields = ['contentWidth', 'containerWidth', 'scrollLeft', 'x', 'left', 'right'];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff(\n  i,\n  diff,\n  [contentHeight, containerHeight, scrollTop, y, up, down],\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  const element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent(`ps-scroll-${y}`));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${up}`));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${down}`));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(`ps-${y}-reach-${i.reach[y]}`));\n  }\n}\n", "import * as CSS from './css';\nimport * as DOM from './dom';\n\nexport function toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nexport function isEditable(el) {\n  return (\n    DOM.matches(el, 'input,[contenteditable]') ||\n    DOM.matches(el, 'select,[contenteditable]') ||\n    DOM.matches(el, 'textarea,[contenteditable]') ||\n    DOM.matches(el, 'button,[contenteditable]')\n  );\n}\n\nexport function outerWidth(element) {\n  const styles = CSS.get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nexport const env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport { toInt } from './lib/util';\n\n/* eslint-disable no-lonely-if */\n\nexport default function (i) {\n  const element = i.element;\n  const roundedScrollTop = Math.floor(element.scrollTop);\n  const rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.floor(rect.width);\n  i.containerHeight = Math.floor(rect.height);\n\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('x')).forEach((el) => DOM.remove(el));\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('y')).forEach((el) => DOM.remove(el));\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(i, toInt((i.railXWidth * i.containerWidth) / i.contentWidth));\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) * (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  const xRailOffset = { width: i.railXWidth };\n  const roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth - i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  CSS.set(i.scrollbarXRail, xRailOffset);\n\n  const yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  CSS.set(i.scrollbarYRail, yRailOffset);\n\n  CSS.set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  CSS.set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n", "/* eslint-disable */\n\nimport updateGeometry from '../update-geometry';\n\nexport default function (i) {\n  // const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', (e) => e.stopPropagation());\n  i.event.bind(i.scrollbarYRail, 'mousedown', (e) => {\n    const positionTop = e.pageY - window.pageYOffset - i.scrollbarYRail.getBoundingClientRect().top;\n    const direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', (e) => e.stopPropagation());\n  i.event.bind(i.scrollbarXRail, 'mousedown', (e) => {\n    const positionLeft =\n      e.pageX - window.pageXOffset - i.scrollbarXRail.getBoundingClientRect().left;\n    const direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n", "import cls, { addScrollingClass, removeScrollingClass } from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\n\nlet activeSlider = null; // Variable to track the currently active slider\n\nexport default function setupScrollHandlers(i) {\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail',\n  ]);\n\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail',\n  ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  [\n    containerDimension,\n    contentDimension,\n    pageAxis,\n    railDimension,\n    scrollbarAxis,\n    scrollbarDimension,\n    scrollAxis,\n    axis,\n    scrollbarRail,\n  ]\n) {\n  const element = i.element;\n  let startingScrollPosition = null;\n  let startingMousePagePosition = null;\n  let scrollBy = null;\n\n  function moveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageAxis] = e.touches[0][`page${axis.toUpperCase()}`];\n    }\n\n    // Only move if the active slider is the one we started with\n    if (activeSlider === scrollbarAxis) {\n      element[scrollAxis] =\n        startingScrollPosition + scrollBy * (e[pageAxis] - startingMousePagePosition);\n      addScrollingClass(i, axis);\n      updateGeometry(i);\n\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  function endHandler() {\n    removeScrollingClass(i, axis);\n    i[scrollbarRail].classList.remove(cls.state.clicking);\n    document.removeEventListener('mousemove', moveHandler);\n    document.removeEventListener('mouseup', endHandler);\n    document.removeEventListener('touchmove', moveHandler);\n    document.removeEventListener('touchend', endHandler);\n    activeSlider = null; // Reset active slider when interaction ends\n  }\n\n  function bindMoves(e) {\n    if (activeSlider === null) {\n      // Only bind if no slider is currently active\n      activeSlider = scrollbarAxis; // Set current slider as active\n\n      startingScrollPosition = element[scrollAxis];\n      if (e.touches) {\n        e[pageAxis] = e.touches[0][`page${axis.toUpperCase()}`];\n      }\n      startingMousePagePosition = e[pageAxis];\n      scrollBy =\n        (i[contentDimension] - i[containerDimension]) / (i[railDimension] - i[scrollbarDimension]);\n\n      if (!e.touches) {\n        document.addEventListener('mousemove', moveHandler);\n        document.addEventListener('mouseup', endHandler);\n      } else {\n        document.addEventListener('touchmove', moveHandler, { passive: false });\n        document.addEventListener('touchend', endHandler);\n      }\n\n      i[scrollbarRail].classList.add(cls.state.clicking);\n    }\n\n    e.stopPropagation();\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  i[scrollbarAxis].addEventListener('mousedown', bindMoves);\n  i[scrollbarAxis].addEventListener('touchstart', bindMoves);\n}\n", "/* eslint-disable */\n\nimport * as DOM from '../lib/dom';\nimport updateGeometry from '../update-geometry';\nimport { isEditable } from '../lib/util';\n\nexport default function (i) {\n  const element = i.element;\n\n  const elementHovered = () => DOM.matches(element, ':hover');\n  const scrollbarFocused = () =>\n    DOM.matches(i.scrollbarX, ':focus') || DOM.matches(i.scrollbarY, ':focus');\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    const scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', (e) => {\n    if ((e.isDefaultPrevented && e.isDefaultPrevented()) || e.defaultPrevented) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    let activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    let deltaX = 0;\n    let deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n", "/* eslint-disable */\n\nimport * as CSS from '../lib/css';\nimport cls from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { env } from '../lib/util';\n\nexport default function (i) {\n  const element = i.element;\n\n  let shouldPrevent = false;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const roundedScrollTop = Math.floor(element.scrollTop);\n    const isTop = element.scrollTop === 0;\n    const isBottom = roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    const isLeft = element.scrollLeft === 0;\n    const isRight = element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    let hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    let deltaX = e.deltaX;\n    let deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    const [deltaX, deltaY] = getDeltaFromEvent(e);\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    let shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n", "import updateGeometry from '../update-geometry';\nimport cls from '../lib/class-names';\nimport * as CSS from '../lib/css';\nimport { env } from '../lib/util';\n\nexport default function (i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  const element = i.element;\n\n  const state = {\n    startOffset: {},\n    startTime: 0,\n    speed: {},\n    easingLoop: null,\n  };\n\n  function shouldPrevent(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    const scrollLeft = element.scrollLeft;\n    const magnitudeX = Math.abs(deltaX);\n    const magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    }\n    // Maybe IE pointer\n    return e;\n  }\n\n  function shouldHandle(e) {\n    if (e.target === i.scrollbarX || e.target === i.scrollbarY) {\n      return false;\n    }\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (e.pointerType && e.pointerType !== 'mouse' && e.pointerType !== e.MSPOINTER_TYPE_MOUSE) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    const touch = getTouch(e);\n\n    state.startOffset.pageX = touch.pageX;\n    state.startOffset.pageY = touch.pageY;\n\n    state.startTime = new Date().getTime();\n\n    if (state.easingLoop !== null) {\n      clearInterval(state.easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      const touch = getTouch(e);\n\n      const currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      const differenceX = currentOffset.pageX - state.startOffset.pageX;\n      const differenceY = currentOffset.pageY - state.startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      state.startOffset = currentOffset;\n\n      const currentTime = new Date().getTime();\n\n      const timeGap = currentTime - state.startTime;\n      if (timeGap > 0) {\n        state.speed.x = differenceX / timeGap;\n        state.speed.y = differenceY / timeGap;\n        state.startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        // Prevent the default behavior if the event is cancelable\n        if (e.cancelable) {\n          e.preventDefault();\n        }\n      }\n    }\n  }\n\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(state.easingLoop);\n      state.easingLoop = setInterval(() => {\n        if (i.isInitialized) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        if (!state.speed.x && !state.speed.y) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        if (Math.abs(state.speed.x) < 0.01 && Math.abs(state.speed.y) < 0.01) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        applyTouchMove(state.speed.x * 30, state.speed.y * 30);\n\n        state.speed.x *= 0.8;\n        state.speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n", "/* eslint-disable */\n\nimport * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport EventManager from './lib/event-manager';\nimport processScrollDiff from './process-scroll-diff';\nimport updateGeometry from './update-geometry';\nimport { toInt, outerWidth } from './lib/util';\n\nimport clickRail from './handlers/click-rail';\nimport dragThumb from './handlers/drag-thumb';\nimport keyboard from './handlers/keyboard';\nimport wheel from './handlers/mouse-wheel';\nimport touch from './handlers/touch';\n\nconst defaultSettings = () => ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n});\n\nconst handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard,\n  wheel,\n  touch,\n};\n\nexport default class PerfectScrollbar {\n  constructor(element, userSettings = {}) {\n    if (typeof element === 'string') {\n      element = document.querySelector(element);\n    }\n\n    if (!element || !element.nodeName) {\n      throw new Error('no element is specified to initialize PerfectScrollbar');\n    }\n\n    this.element = element;\n\n    element.classList.add(cls.main);\n\n    this.settings = defaultSettings();\n    for (const key in userSettings) {\n      this.settings[key] = userSettings[key];\n    }\n\n    this.containerWidth = null;\n    this.containerHeight = null;\n    this.contentWidth = null;\n    this.contentHeight = null;\n\n    const focus = () => element.classList.add(cls.state.focus);\n    const blur = () => element.classList.remove(cls.state.focus);\n\n    this.isRtl = CSS.get(element).direction === 'rtl';\n    if (this.isRtl === true) {\n      element.classList.add(cls.rtl);\n    }\n    this.isNegativeScroll = (() => {\n      const originalScrollLeft = element.scrollLeft;\n      let result = null;\n      element.scrollLeft = -1;\n      result = element.scrollLeft < 0;\n      element.scrollLeft = originalScrollLeft;\n      return result;\n    })();\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? element.scrollWidth - element.clientWidth\n      : 0;\n    this.event = new EventManager();\n    this.ownerDocument = element.ownerDocument || document;\n\n    this.scrollbarXRail = DOM.div(cls.element.rail('x'));\n    element.appendChild(this.scrollbarXRail);\n    this.scrollbarX = DOM.div(cls.element.thumb('x'));\n    this.scrollbarXRail.appendChild(this.scrollbarX);\n    this.scrollbarX.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarX, 'focus', focus);\n    this.event.bind(this.scrollbarX, 'blur', blur);\n    this.scrollbarXActive = null;\n    this.scrollbarXWidth = null;\n    this.scrollbarXLeft = null;\n    const railXStyle = CSS.get(this.scrollbarXRail);\n    this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n    if (isNaN(this.scrollbarXBottom)) {\n      this.isScrollbarXUsingBottom = false;\n      this.scrollbarXTop = toInt(railXStyle.top);\n    } else {\n      this.isScrollbarXUsingBottom = true;\n    }\n    this.railBorderXWidth = toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n    // Set rail to display:block to calculate margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    this.railXMarginWidth = toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n    CSS.set(this.scrollbarXRail, { display: '' });\n    this.railXWidth = null;\n    this.railXRatio = null;\n\n    this.scrollbarYRail = DOM.div(cls.element.rail('y'));\n    element.appendChild(this.scrollbarYRail);\n    this.scrollbarY = DOM.div(cls.element.thumb('y'));\n    this.scrollbarYRail.appendChild(this.scrollbarY);\n    this.scrollbarY.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarY, 'focus', focus);\n    this.event.bind(this.scrollbarY, 'blur', blur);\n    this.scrollbarYActive = null;\n    this.scrollbarYHeight = null;\n    this.scrollbarYTop = null;\n    const railYStyle = CSS.get(this.scrollbarYRail);\n    this.scrollbarYRight = parseInt(railYStyle.right, 10);\n    if (isNaN(this.scrollbarYRight)) {\n      this.isScrollbarYUsingRight = false;\n      this.scrollbarYLeft = toInt(railYStyle.left);\n    } else {\n      this.isScrollbarYUsingRight = true;\n    }\n    this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n    this.railBorderYWidth = toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railYMarginHeight = toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n    CSS.set(this.scrollbarYRail, { display: '' });\n    this.railYHeight = null;\n    this.railYRatio = null;\n\n    this.reach = {\n      x:\n        element.scrollLeft <= 0\n          ? 'start'\n          : element.scrollLeft >= this.contentWidth - this.containerWidth\n          ? 'end'\n          : null,\n      y:\n        element.scrollTop <= 0\n          ? 'start'\n          : element.scrollTop >= this.contentHeight - this.containerHeight\n          ? 'end'\n          : null,\n    };\n\n    this.isAlive = true;\n\n    this.settings.handlers.forEach((handlerName) => handlers[handlerName](this));\n\n    this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n    this.lastScrollLeft = element.scrollLeft; // for onScroll only\n    this.event.bind(this.element, 'scroll', (e) => this.onScroll(e));\n    updateGeometry(this);\n  }\n\n  update() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    // Recalcuate negative scrollLeft adjustment\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? this.element.scrollWidth - this.element.clientWidth\n      : 0;\n\n    // Recalculate rail margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(CSS.get(this.scrollbarXRail).marginLeft) +\n      toInt(CSS.get(this.scrollbarXRail).marginRight);\n    this.railYMarginHeight =\n      toInt(CSS.get(this.scrollbarYRail).marginTop) +\n      toInt(CSS.get(this.scrollbarYRail).marginBottom);\n\n    // Hide scrollbars not to affect scrollWidth and scrollHeight\n    CSS.set(this.scrollbarXRail, { display: 'none' });\n    CSS.set(this.scrollbarYRail, { display: 'none' });\n\n    updateGeometry(this);\n\n    processScrollDiff(this, 'top', 0, false, true);\n    processScrollDiff(this, 'left', 0, false, true);\n\n    CSS.set(this.scrollbarXRail, { display: '' });\n    CSS.set(this.scrollbarYRail, { display: '' });\n  }\n\n  onScroll(e) {\n    if (!this.isAlive) {\n      return;\n    }\n\n    updateGeometry(this);\n    processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n    processScrollDiff(this, 'left', this.element.scrollLeft - this.lastScrollLeft);\n\n    this.lastScrollTop = Math.floor(this.element.scrollTop);\n    this.lastScrollLeft = this.element.scrollLeft;\n  }\n\n  destroy() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    this.event.unbindAll();\n    DOM.remove(this.scrollbarX);\n    DOM.remove(this.scrollbarY);\n    DOM.remove(this.scrollbarXRail);\n    DOM.remove(this.scrollbarYRail);\n    this.removePsClasses();\n\n    // unset elements\n    this.element = null;\n    this.scrollbarX = null;\n    this.scrollbarY = null;\n    this.scrollbarXRail = null;\n    this.scrollbarYRail = null;\n\n    this.isAlive = false;\n  }\n\n  removePsClasses() {\n    this.element.className = this.element.className\n      .split(' ')\n      .filter((name) => !name.match(/^ps([-_].+|)$/))\n      .join(' ');\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAAO,eAAS,IAAI,SAAS;AAC3B,eAAO,iBAAiB,OAAO;;AAG1B,eAAS,IAAI,SAAS,KAAK;AAChC,iBAAW,OAAO,KAAK;AACrBA,cAAI,MAAM,IAAI,GAAG;AACjB,cAAI,OAAO,QAAQ,UAAU;AAC3B,kBAAS,MAAG;;AAEd,kBAAQ,MAAM,GAAG,IAAI;;AAEvB,eAAO;;ACZF,eAAS,IAAI,WAAW;AAC7BC,YAAMC,OAAM,SAAS,cAAc,KAAK;AACxC,QAAAA,KAAI,YAAY;AAChB,eAAOA;;AAGTD,UAAM,YACJ,OAAO,YAAY,gBAClB,QAAQ,UAAU,WACjB,QAAQ,UAAU,yBAClB,QAAQ,UAAU,sBAClB,QAAQ,UAAU;AAEf,eAAS,QAAQ,SAAS,OAAO;AACtC,YAAI,CAAC,WAAW;AACd,gBAAM,IAAI,MAAM,sCAAsC;;AAGxD,eAAO,UAAU,KAAK,SAAS,KAAK;;AAG/B,eAAS,OAAO,SAAS;AAC9B,YAAI,QAAQ,QAAQ;AAClB,kBAAQ,OAAM;eACT;AACL,cAAI,QAAQ,YAAY;AACtB,oBAAQ,WAAW,YAAY,OAAO;;;;AAKrC,eAAS,cAAc,SAAS,UAAU;AAC/C,eAAO,MAAM,UAAU,OAAO;UAAK,QAAQ;UAAQ,SAAE,OAAM;AAAA,mBACzD,QAAQ,OAAO,QAAQ;UAAA;;;ACjC3BA,UAAM,MAAM;QACV,MAAM;QACN,KAAK;QACL,SAAS;UACP,OAAK,SAAE,GAAE;AAAA,mBAAA,eAAgB;UAAC;UAC1B,MAAI,SAAE,GAAE;AAAA,mBAAA,cAAe;UAAC;UACxB,WAAW;;QAEb,OAAO;UACL,OAAO;UACP,UAAU;UACV,QAAM,SAAE,GAAE;AAAA,mBAAA,gBAAiB;UAAC;UAC5B,WAAS,SAAE,GAAE;AAAA,mBAAA,mBAAoB;UAAC;;;AAStCA,UAAM,wBAAwB,EAAE,GAAG,MAAM,GAAG,KAAI;AAEzC,eAAS,kBAAkB,GAAG,GAAG;AACtCA,YAAM,YAAY,EAAE,QAAQ;AAC5BA,YAAM,YAAY,IAAI,MAAM,UAAU,CAAC;AAEvC,YAAI,UAAU,SAAS,SAAS,GAAG;AACjC,uBAAa,sBAAsB,CAAC,CAAC;eAChC;AACL,oBAAU,IAAI,SAAS;;;AAIpB,eAAS,qBAAqB,GAAG,GAAG;AACzC,8BAAsB,CAAC,IAAI;qBACtB;AAAA,mBAAG,EAAE,WAAW,EAAE,QAAQ,UAAU,OAAO,IAAI,MAAM,UAAU,CAAC,CAAC;UAAA;UACpE,EAAE,SAAS;;;AAIR,eAAS,2BAA2B,GAAG,GAAG;AAC/C,0BAAkB,GAAG,CAAC;AACtB,6BAAqB,GAAG,CAAC;;AC3C3B,UAAM,eACJ,SAAAE,cAAY,SAAS;AACnB,aAAK,UAAU;AACf,aAAK,WAAW,CAAA;MACpB;;AAEA,mBAAA,UAAE,OAAA,SAAA,KAAK,WAAW,SAAS;AACzB,YAAM,OAAO,KAAK,SAAS,SAAS,MAAM,aAAa;AACrD,eAAO,SAAS,SAAS,IAAI,CAAA;;AAE/B,aAAO,SAAS,SAAS,EAAE,KAAK,OAAO;AACrC,aAAK,QAAQ,iBAAiB,WAAW,SAAS,KAAK;MACzD;AAEF,mBAAA,UAAE,SAAA,SAAA,OAAO,WAAW,QAAQ;;AACxB,aAAK,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,OAAM,SAAC,SAAQ;AACjE,cAAI,UAAU,YAAY,QAAQ;AAClC,mBAAS;;AAETC,iBAAK,QAAQ,oBAAoB,WAAW,SAAS,KAAK;AAC5D,iBAAS;SACR;MACH;AAEF,mBAAA,UAAE,YAAA,SAAA,YAAY;AACZ,iBAAa,QAAQ,KAAK,UAAU;AAChC,eAAK,OAAO,IAAI;;MAEpB;AAEF,yBAAM,QAAA,MAAA,WAAU;;AACd,eAAS,OAAO,KAAK,KAAK,QAAQ,EAAE;UAClC,SAAE,KAAI;AAAA,mBAAGA,OAAK,SAAS,GAAG,EAAE,WAAW;UAAA;;MAEzC;;AAGa,UAAM,eACnB,SAAAC,gBAAc;AACZ,aAAK,gBAAgB,CAAA;MACvB;AAEF,mBAAA,UAAE,eAAA,SAAA,aAAa,SAAS;AACtB,YAAM,KAAK,KAAK,cAAc,OAAM,SAACC,KAAG;AAAA,iBAAGA,IAAG,YAAY;QAAA,CAAO,EAAE,CAAC;AACpE,YAAM,CAAC,IAAI;AACP,eAAK,IAAI,aAAa,OAAO;AAC/B,eAAO,cAAc,KAAK,EAAE;;AAE9B,eAAS;MACT;AAEF,mBAAA,UAAE,OAAA,SAAA,KAAK,SAAS,WAAW,SAAS;AAChC,aAAK,aAAa,OAAO,EAAE,KAAK,WAAW,OAAO;MACpD;AAEF,mBAAA,UAAE,SAAA,SAAA,OAAO,SAAS,WAAW,SAAS;AACpC,YAAQ,KAAK,KAAK,aAAa,OAAO;AACtC,WAAK,OAAO,WAAW,OAAO;AAE5B,YAAI,GAAG,SAAS;AAEd,eAAK,cAAc,OAAO,KAAK,cAAc,QAAQ,EAAE,GAAG,CAAC;;MAE/D;AAEF,mBAAA,UAAE,YAAA,SAAA,YAAY;AACV,aAAK,cAAc,QAAO,SAAC,GAAE;AAAA,iBAAG,EAAE,UAAS;QAAA,CAAE;AAC7C,aAAK,gBAAgB,CAAA;MACvB;AAEF,mBAAA,UAAE,OAAA,SAAA,KAAK,SAAS,WAAW,SAAS;AAClC,YAAQ,KAAK,KAAK,aAAa,OAAO;AACpCL,YAAM,cAAW,SAAG,KAAI;AACxB,aAAK,OAAO,WAAW,WAAW;AAChC,kBAAQ,GAAG;;AAEf,WAAK,KAAK,WAAW,WAAW;MAChC;AC3EF,eAAS,YAAY,MAAM;AACzB,YAAI,OAAO,OAAO,gBAAgB,YAAY;AAC5C,iBAAO,IAAI,YAAY,IAAI;;AAG7BA,YAAM,MAAM,SAAS,YAAY,aAAa;AAC9C,YAAI,gBAAgB,MAAM,OAAO,OAAO,MAAS;AACjD,eAAO;;AAGM,eAAA,kBAAU,GAAG,MAAM,MAAM,mBAA0B,qBAA6B;8DAAnC;kEAA4B;AACtFD,YAAI;AACJ,YAAI,SAAS,OAAO;AAClB,mBAAS,CAAC,iBAAiB,mBAAmB,aAAa,KAAK,MAAM,MAAM;mBACnE,SAAS,QAAQ;AAC1B,mBAAS,CAAC,gBAAgB,kBAAkB,cAAc,KAAK,QAAQ,OAAO;eACzE;AACL,gBAAM,IAAI,MAAM,kCAAkC;;AAGpDO,4BAAkB,GAAG,MAAM,QAAQ,mBAAmB,mBAAmB;;AAG3E,eAASA,oBACP,GACA,MACA,KACA,mBACA,qBACA;;;;;;;8DAFoB;kEACE;AAEtBN,YAAM,UAAU,EAAE;AAGlB,UAAE,MAAM,CAAC,IAAI;AAGb,YAAI,QAAQ,SAAS,IAAI,GAAG;AAC1B,YAAE,MAAM,CAAC,IAAI;;AAIf,YAAI,QAAQ,SAAS,IAAI,EAAE,aAAa,IAAI,EAAE,eAAe,IAAI,GAAG;AAClE,YAAE,MAAM,CAAC,IAAI;;AAGf,YAAI,MAAM;AACR,kBAAQ,cAAc,YAAW,eAAc,CAAC,CAAG;AAEnD,cAAI,OAAO,GAAG;AACZ,oBAAQ,cAAc,YAAW,eAAc,EAAE,CAAG;qBAC3C,OAAO,GAAG;AACnB,oBAAQ,cAAc,YAAW,eAAc,IAAI,CAAG;;AAGxD,cAAI,mBAAmB;AACrB,uCAA2B,GAAG,CAAC;;;AAInC,YAAI,EAAE,MAAM,CAAC,MAAM,QAAQ,sBAAsB;AAC/C,kBAAQ,cAAc,YAAW,QAAO,IAAC,YAAU,EAAE,MAAM,CAAC,CAAC,CAAG;;;AC3D7D,eAAS,MAAM,GAAG;AACvB,eAAO,SAAS,GAAG,EAAE,KAAK;;AAGrB,eAAS,WAAW,IAAI;AAC7B,eACEO,QAAY,IAAI,yBAAyB,KACzCA,QAAY,IAAI,0BAA0B,KAC1CA,QAAY,IAAI,4BAA4B,KAC5CA,QAAY,IAAI,0BAA0B;;AAIvC,eAAS,WAAW,SAAS;AAClCP,YAAM,SAASQ,IAAQ,OAAO;AAC9B,eACE,MAAM,OAAO,KAAK,IAClB,MAAM,OAAO,WAAW,IACxB,MAAM,OAAO,YAAY,IACzB,MAAM,OAAO,eAAe,IAC5B,MAAM,OAAO,gBAAgB;;AAI1BR,UAAM,MAAM;QACjB,UACE,OAAO,aAAa,eACpB,sBAAsB,SAAS,gBAAgB;QACjD,eACE,OAAO,WAAW,gBACjB,kBAAkB,UAChB,oBAAoB,OAAO,aAC1B,OAAO,UAAU,iBAAiB,KACnC,OAAO,iBAAiB,oBAAoB,OAAO;QACxD,mBACE,OAAO,cAAc,eAAe,UAAU;QAChD,UACE,OAAO,cAAc,eACrB,UAAU,KAAK,aAAa,UAAU,SAAS;;AClCpC,eAAA,eAAU,GAAG;AAC1BA,YAAM,UAAU,EAAE;AAClBA,YAAM,mBAAmB,KAAK,MAAM,QAAQ,SAAS;AACrDA,YAAM,OAAO,QAAQ,sBAAqB;AAE1C,UAAE,iBAAiB,KAAK,MAAM,KAAK,KAAK;AACxC,UAAE,kBAAkB,KAAK,MAAM,KAAK,MAAM;AAE1C,UAAE,eAAe,QAAQ;AACzB,UAAE,gBAAgB,QAAQ;AAE1B,YAAI,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAEvCS,wBAAkB,SAAS,IAAI,QAAQ,KAAK,GAAG,CAAC,EAAE,QAAO,SAAE,IAAI;AAAA,mBAAGC,OAAW,EAAE;UAAA,CAAC;AAChF,kBAAQ,YAAY,EAAE,cAAc;;AAEtC,YAAI,CAAC,QAAQ,SAAS,EAAE,cAAc,GAAG;AAEvCD,wBAAkB,SAAS,IAAI,QAAQ,KAAK,GAAG,CAAC,EAAE,QAAO,SAAE,IAAI;AAAA,mBAAGC,OAAW,EAAE;UAAA,CAAC;AAChF,kBAAQ,YAAY,EAAE,cAAc;;AAGtC,YACE,CAAC,EAAE,SAAS,mBACZ,EAAE,iBAAiB,EAAE,SAAS,sBAAsB,EAAE,cACtD;AACA,YAAE,mBAAmB;AACrB,YAAE,aAAa,EAAE,iBAAiB,EAAE;AACpC,YAAE,aAAa,EAAE,iBAAiB,EAAE;AACpC,YAAE,kBAAkB,aAAa,GAAG,MAAO,EAAE,aAAa,EAAE,iBAAkB,EAAE,YAAY,CAAC;AAC7F,YAAE,iBAAiB;aACf,EAAE,2BAA2B,QAAQ,eAAe,EAAE,aAAa,EAAE,oBACpE,EAAE,eAAe,EAAE;;eAEnB;AACL,YAAE,mBAAmB;;AAGvB,YACE,CAAC,EAAE,SAAS,mBACZ,EAAE,kBAAkB,EAAE,SAAS,sBAAsB,EAAE,eACvD;AACA,YAAE,mBAAmB;AACrB,YAAE,cAAc,EAAE,kBAAkB,EAAE;AACtC,YAAE,aAAa,EAAE,kBAAkB,EAAE;AACrC,YAAE,mBAAmB;YACnB;YACA,MAAO,EAAE,cAAc,EAAE,kBAAmB,EAAE,aAAa;;AAE7D,YAAE,gBAAgB;YACf,oBAAoB,EAAE,cAAc,EAAE,qBACpC,EAAE,gBAAgB,EAAE;;eAEpB;AACL,YAAE,mBAAmB;;AAGvB,YAAI,EAAE,kBAAkB,EAAE,aAAa,EAAE,iBAAiB;AACxD,YAAE,iBAAiB,EAAE,aAAa,EAAE;;AAEtC,YAAI,EAAE,iBAAiB,EAAE,cAAc,EAAE,kBAAkB;AACzD,YAAE,gBAAgB,EAAE,cAAc,EAAE;;AAGtC,kBAAU,SAAS,CAAC;AAEpB,YAAI,EAAE,kBAAkB;AACtB,kBAAQ,UAAU,IAAI,IAAI,MAAM,OAAO,GAAG,CAAC;eACtC;AACL,kBAAQ,UAAU,OAAO,IAAI,MAAM,OAAO,GAAG,CAAC;AAC9C,YAAE,kBAAkB;AACpB,YAAE,iBAAiB;AACnB,kBAAQ,aAAa,EAAE,UAAU,OAAO,EAAE,eAAe;;AAE3D,YAAI,EAAE,kBAAkB;AACtB,kBAAQ,UAAU,IAAI,IAAI,MAAM,OAAO,GAAG,CAAC;eACtC;AACL,kBAAQ,UAAU,OAAO,IAAI,MAAM,OAAO,GAAG,CAAC;AAC9C,YAAE,mBAAmB;AACrB,YAAE,gBAAgB;AAClB,kBAAQ,YAAY;;;AAIxB,eAAS,aAAa,GAAG,WAAW;AAClC,YAAI,EAAE,SAAS,oBAAoB;AACjC,sBAAY,KAAK,IAAI,WAAW,EAAE,SAAS,kBAAkB;;AAE/D,YAAI,EAAE,SAAS,oBAAoB;AACjC,sBAAY,KAAK,IAAI,WAAW,EAAE,SAAS,kBAAkB;;AAE/D,eAAO;;AAGT,eAAS,UAAU,SAAS,GAAG;AAC7BV,YAAM,cAAc,EAAE,OAAO,EAAE,WAAU;AACzCA,YAAM,mBAAmB,KAAK,MAAM,QAAQ,SAAS;AAErD,YAAI,EAAE,OAAO;AACX,sBAAY,OACV,EAAE,2BAA2B,QAAQ,aAAa,EAAE,iBAAiB,EAAE;eACpE;AACL,sBAAY,OAAO,QAAQ;;AAE7B,YAAI,EAAE,yBAAyB;AAC7B,sBAAY,SAAS,EAAE,mBAAmB;eACrC;AACL,sBAAY,MAAM,EAAE,gBAAgB;;AAEtCW,YAAQ,EAAE,gBAAgB,WAAW;AAErCX,YAAM,cAAc,EAAE,KAAK,kBAAkB,QAAQ,EAAE,YAAW;AAClE,YAAI,EAAE,wBAAwB;AAC5B,cAAI,EAAE,OAAO;AACX,wBAAY,QACV,EAAE,gBACD,EAAE,2BAA2B,QAAQ,cACtC,EAAE,kBACF,EAAE,uBACF;iBACG;AACL,wBAAY,QAAQ,EAAE,kBAAkB,QAAQ;;eAE7C;AACL,cAAI,EAAE,OAAO;AACX,wBAAY,OACV,EAAE,2BACF,QAAQ,aACR,EAAE,iBAAiB,IACnB,EAAE,eACF,EAAE,iBACF,EAAE;iBACC;AACL,wBAAY,OAAO,EAAE,iBAAiB,QAAQ;;;AAGlDW,YAAQ,EAAE,gBAAgB,WAAW;AAErCA,YAAQ,EAAE,YAAY;UACpB,MAAM,EAAE;UACR,OAAO,EAAE,kBAAkB,EAAE;SAC9B;AACDA,YAAQ,EAAE,YAAY;UACpB,KAAK,EAAE;UACP,QAAQ,EAAE,mBAAmB,EAAE;SAChC;;ACpJY,eAAA,UAAU,GAAG;AAG1B,UAAE,MAAM,KAAK,EAAE,YAAY,aAAW,SAAG,GAAG;AAAA,iBAAG,EAAE,gBAAe;QAAA,CAAE;AAClE,UAAE,MAAM,KAAK,EAAE,gBAAgB,aAAW,SAAG,GAAG;AAC9CX,cAAM,cAAc,EAAE,QAAQ,OAAO,cAAc,EAAE,eAAe,sBAAqB,EAAG;AAC5FA,cAAM,YAAY,cAAc,EAAE,gBAAgB,IAAI;AAEtD,YAAE,QAAQ,aAAa,YAAY,EAAE;AACrC,yBAAe,CAAC;AAEhB,YAAE,gBAAe;SAClB;AAED,UAAE,MAAM,KAAK,EAAE,YAAY,aAAW,SAAG,GAAG;AAAA,iBAAG,EAAE,gBAAe;QAAA,CAAE;AAClE,UAAE,MAAM,KAAK,EAAE,gBAAgB,aAAW,SAAG,GAAG;AAC9CA,cAAM,eACJ,EAAE,QAAQ,OAAO,cAAc,EAAE,eAAe,sBAAqB,EAAG;AAC1EA,cAAM,YAAY,eAAe,EAAE,iBAAiB,IAAI;AAExD,YAAE,QAAQ,cAAc,YAAY,EAAE;AACtC,yBAAe,CAAC;AAEhB,YAAE,gBAAe;SAClB;;ACzBHD,UAAI,eAAe;AAEJ,eAAS,oBAAoB,GAAG;AAC7C,+BAAuB,GAAG;UACxB;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAgB,CACjB;AAED,+BAAuB,GAAG;UACxB;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAgB,CACjB;;AAGH,eAAS,uBACP,GACA,KAWA;;;;;;;;;;AACAC,YAAM,UAAU,EAAE;AAClBD,YAAI,yBAAyB;AAC7BA,YAAI,4BAA4B;AAChCA,YAAI,WAAW;AAEf,iBAAS,YAAY,GAAG;AACtB,cAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,GAAG;AAC7B,cAAE,QAAQ,IAAI,EAAE,QAAQ,CAAC,EAAC,SAAQ,KAAK,YAAW,CAAE;;AAItD,cAAI,iBAAiB,eAAe;AAClC,oBAAQ,UAAU,IAChB,yBAAyB,YAAY,EAAE,QAAQ,IAAI;AACrD,8BAAkB,GAAG,IAAI;AACzB,2BAAe,CAAC;AAEhB,cAAE,gBAAe;AACjB,cAAE,eAAc;;;AAIpB,iBAAS,aAAa;AACpB,+BAAqB,GAAG,IAAI;AAC5B,YAAE,aAAa,EAAE,UAAU,OAAO,IAAI,MAAM,QAAQ;AACpD,mBAAS,oBAAoB,aAAa,WAAW;AACrD,mBAAS,oBAAoB,WAAW,UAAU;AAClD,mBAAS,oBAAoB,aAAa,WAAW;AACrD,mBAAS,oBAAoB,YAAY,UAAU;AACnD,yBAAe;;AAGjB,iBAAS,UAAU,GAAG;AACpB,cAAI,iBAAiB,MAAM;AAEzB,2BAAe;AAEf,qCAAyB,QAAQ,UAAU;AAC3C,gBAAI,EAAE,SAAS;AACb,gBAAE,QAAQ,IAAI,EAAE,QAAQ,CAAC,EAAC,SAAQ,KAAK,YAAW,CAAE;;AAEtD,wCAA4B,EAAE,QAAQ;AACtC,wBACG,EAAE,gBAAgB,IAAI,EAAE,kBAAkB,MAAM,EAAE,aAAa,IAAI,EAAE,kBAAkB;AAE1F,gBAAI,CAAC,EAAE,SAAS;AACd,uBAAS,iBAAiB,aAAa,WAAW;AAClD,uBAAS,iBAAiB,WAAW,UAAU;mBAC1C;AACL,uBAAS,iBAAiB,aAAa,aAAa,EAAE,SAAS,MAAK,CAAE;AACtE,uBAAS,iBAAiB,YAAY,UAAU;;AAGlD,cAAE,aAAa,EAAE,UAAU,IAAI,IAAI,MAAM,QAAQ;;AAGnD,YAAE,gBAAe;AACjB,cAAI,EAAE,YAAY;AAChB,cAAE,eAAc;;;AAIpB,UAAE,aAAa,EAAE,iBAAiB,aAAa,SAAS;AACxD,UAAE,aAAa,EAAE,iBAAiB,cAAc,SAAS;;ACtG5C,eAAA,SAAU,GAAG;AAC1BC,YAAM,UAAU,EAAE;AAElBA,YAAM,iBAAc,WAAM;AAAA,iBAAGO,QAAY,SAAS,QAAQ;QAAA;AAC1DP,YAAM,mBAAgB,WAAM;AAAA,iBAC1BO,QAAY,EAAE,YAAY,QAAQ,KAAKA,QAAY,EAAE,YAAY,QAAQ;QAAA;AAE3E,iBAAS,qBAAqB,QAAQ,QAAQ;AAC5CP,cAAM,YAAY,KAAK,MAAM,QAAQ,SAAS;AAC9C,cAAI,WAAW,GAAG;AAChB,gBAAI,CAAC,EAAE,kBAAkB;AACvB,qBAAO;;AAET,gBACG,cAAc,KAAK,SAAS,KAC5B,aAAa,EAAE,gBAAgB,EAAE,mBAAmB,SAAS,GAC9D;AACA,qBAAO,CAAC,EAAE,SAAS;;;AAIvBA,cAAM,aAAa,QAAQ;AAC3B,cAAI,WAAW,GAAG;AAChB,gBAAI,CAAC,EAAE,kBAAkB;AACvB,qBAAO;;AAET,gBACG,eAAe,KAAK,SAAS,KAC7B,cAAc,EAAE,eAAe,EAAE,kBAAkB,SAAS,GAC7D;AACA,qBAAO,CAAC,EAAE,SAAS;;;AAGvB,iBAAO;;AAGT,UAAE,MAAM,KAAK,EAAE,eAAe,WAAS,SAAG,GAAG;AAC3C,cAAK,EAAE,sBAAsB,EAAE,mBAAkB,KAAO,EAAE,kBAAkB;AAC1E;;AAGF,cAAI,CAAC,eAAc,KAAM,CAAC,iBAAgB,GAAI;AAC5C;;AAGFD,cAAI,gBAAgB,SAAS,gBACzB,SAAS,gBACT,EAAE,cAAc;AACpB,cAAI,eAAe;AACjB,gBAAI,cAAc,YAAY,UAAU;AACtC,8BAAgB,cAAc,gBAAgB;mBACzC;AAEL,qBAAO,cAAc,YAAY;AAC/B,gCAAgB,cAAc,WAAW;;;AAG7C,gBAAI,WAAW,aAAa,GAAG;AAC7B;;;AAIJA,cAAI,SAAS;AACbA,cAAI,SAAS;AAEb,kBAAQ,EAAE,OAAK;YACb,KAAK;AACH,kBAAI,EAAE,SAAS;AACb,yBAAS,CAAC,EAAE;yBACH,EAAE,QAAQ;AACnB,yBAAS,CAAC,EAAE;qBACP;AACL,yBAAS;;AAEX;YACF,KAAK;AACH,kBAAI,EAAE,SAAS;AACb,yBAAS,EAAE;yBACF,EAAE,QAAQ;AACnB,yBAAS,EAAE;qBACN;AACL,yBAAS;;AAEX;YACF,KAAK;AACH,kBAAI,EAAE,SAAS;AACb,yBAAS,EAAE;yBACF,EAAE,QAAQ;AACnB,yBAAS,EAAE;qBACN;AACL,yBAAS;;AAEX;YACF,KAAK;AACH,kBAAI,EAAE,SAAS;AACb,yBAAS,CAAC,EAAE;yBACH,EAAE,QAAQ;AACnB,yBAAS,CAAC,EAAE;qBACP;AACL,yBAAS;;AAEX;YACF,KAAK;AACH,kBAAI,EAAE,UAAU;AACd,yBAAS,EAAE;qBACN;AACL,yBAAS,CAAC,EAAE;;AAEd;YACF,KAAK;AACH,uBAAS,EAAE;AACX;YACF,KAAK;AACH,uBAAS,CAAC,EAAE;AACZ;YACF,KAAK;AACH,uBAAS,EAAE;AACX;YACF,KAAK;AACH,uBAAS,CAAC,EAAE;AACZ;YACF;AACE;;AAGJ,cAAI,EAAE,SAAS,mBAAmB,WAAW,GAAG;AAC9C;;AAEF,cAAI,EAAE,SAAS,mBAAmB,WAAW,GAAG;AAC9C;;AAGF,kBAAQ,aAAa;AACrB,kBAAQ,cAAc;AACtB,yBAAe,CAAC;AAEhB,cAAI,qBAAqB,QAAQ,MAAM,GAAG;AACxC,cAAE,eAAc;;SAEnB;;AC1IY,eAAA,MAAU,GAAG;AAC1BC,YAAM,UAAU,EAAE;AAIlB,iBAAS,qBAAqB,QAAQ,QAAQ;AAC5CA,cAAM,mBAAmB,KAAK,MAAM,QAAQ,SAAS;AACrDA,cAAM,QAAQ,QAAQ,cAAc;AACpCA,cAAM,WAAW,mBAAmB,QAAQ,iBAAiB,QAAQ;AACrEA,cAAM,SAAS,QAAQ,eAAe;AACtCA,cAAM,UAAU,QAAQ,aAAa,QAAQ,gBAAgB,QAAQ;AAErED,cAAI;AAGJ,cAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,GAAG;AACvC,wBAAY,SAAS;iBAChB;AACL,wBAAY,UAAU;;AAGxB,iBAAO,YAAY,CAAC,EAAE,SAAS,mBAAmB;;AAGpD,iBAAS,kBAAkB,GAAG;AAC5BA,cAAI,SAAS,EAAE;AACfA,cAAI,SAAS,KAAK,EAAE;AAEpB,cAAI,OAAO,WAAW,eAAe,OAAO,WAAW,aAAa;AAElE,qBAAU,KAAK,EAAE,cAAe;AAChC,qBAAS,EAAE,cAAc;;AAG3B,cAAI,EAAE,aAAa,EAAE,cAAc,GAAG;AAEpC,sBAAU;AACV,sBAAU;;AAGZ,cAAI,WAAW,UAAU,WAAW,QAAyB;AAE3D,qBAAS;AACT,qBAAS,EAAE;;AAGb,cAAI,EAAE,UAAU;AAEd,mBAAO,CAAC,CAAC,QAAQ,CAAC,MAAM;;AAE1B,iBAAO,CAAC,QAAQ,MAAM;;AAGxB,iBAAS,wBAAwB,QAAQ,QAAQ,QAAQ;AAEvD,cAAI,CAAC,IAAI,YAAY,QAAQ,cAAc,cAAc,GAAG;AAC1D,mBAAO;;AAGT,cAAI,CAAC,QAAQ,SAAS,MAAM,GAAG;AAC7B,mBAAO;;AAGTA,cAAI,SAAS;AAEb,iBAAO,UAAU,WAAW,SAAS;AACnC,gBAAI,OAAO,UAAU,SAAS,IAAI,QAAQ,SAAS,GAAG;AACpD,qBAAO;;AAGTC,gBAAM,QAAQQ,IAAQ,MAAM;AAG5B,gBAAI,UAAU,MAAM,UAAU,MAAM,eAAe,GAAG;AACpDR,kBAAM,eAAe,OAAO,eAAe,OAAO;AAClD,kBAAI,eAAe,GAAG;AACpB,oBACG,OAAO,YAAY,KAAK,SAAS,KACjC,OAAO,YAAY,gBAAgB,SAAS,GAC7C;AACA,yBAAO;;;;AAKb,gBAAI,UAAU,MAAM,UAAU,MAAM,eAAe,GAAG;AACpDA,kBAAM,gBAAgB,OAAO,cAAc,OAAO;AAClD,kBAAI,gBAAgB,GAAG;AACrB,oBACG,OAAO,aAAa,KAAK,SAAS,KAClC,OAAO,aAAa,iBAAiB,SAAS,GAC/C;AACA,yBAAO;;;;AAKb,qBAAS,OAAO;;AAGlB,iBAAO;;AAGT,iBAAS,kBAAkB,GAAG;AAC5B,cAAA,MAAyB,kBAAkB,CAAC;AAArC,cAAA,SAAA,IAAA,CAAA;AAAQ,cAAA,SAAA,IAAA,CAAA;AAEf,cAAI,wBAAwB,EAAE,QAAQ,QAAQ,MAAM,GAAG;AACrD;;AAGFD,cAAI,gBAAgB;AACpB,cAAI,CAAC,EAAE,SAAS,kBAAkB;AAGhC,oBAAQ,aAAa,SAAS,EAAE,SAAS;AACzC,oBAAQ,cAAc,SAAS,EAAE,SAAS;qBACjC,EAAE,oBAAoB,CAAC,EAAE,kBAAkB;AAGpD,gBAAI,QAAQ;AACV,sBAAQ,aAAa,SAAS,EAAE,SAAS;mBACpC;AACL,sBAAQ,aAAa,SAAS,EAAE,SAAS;;AAE3C,4BAAgB;qBACP,EAAE,oBAAoB,CAAC,EAAE,kBAAkB;AAGpD,gBAAI,QAAQ;AACV,sBAAQ,cAAc,SAAS,EAAE,SAAS;mBACrC;AACL,sBAAQ,cAAc,SAAS,EAAE,SAAS;;AAE5C,4BAAgB;;AAGlB,yBAAe,CAAC;AAEhB,0BAAgB,iBAAiB,qBAAqB,QAAQ,MAAM;AACpE,cAAI,iBAAiB,CAAC,EAAE,SAAS;AAC/B,cAAE,gBAAe;AACjB,cAAE,eAAc;;;AAIpB,YAAI,OAAO,OAAO,YAAY,aAAa;AACzC,YAAE,MAAM,KAAK,SAAS,SAAS,iBAAiB;mBACvC,OAAO,OAAO,iBAAiB,aAAa;AACrD,YAAE,MAAM,KAAK,SAAS,cAAc,iBAAiB;;;ACtJ1C,eAAA,MAAU,GAAG;AAC1B,YAAI,CAAC,IAAI,iBAAiB,CAAC,IAAI,mBAAmB;AAChD;;AAGFC,YAAM,UAAU,EAAE;AAElBA,YAAM,QAAQ;UACZ,aAAa,CAAA;UACb,WAAW;UACX,OAAO,CAAA;UACP,YAAY;;AAGd,iBAAS,cAAc,QAAQ,QAAQ;AACrCA,cAAM,YAAY,KAAK,MAAM,QAAQ,SAAS;AAC9CA,cAAM,aAAa,QAAQ;AAC3BA,cAAM,aAAa,KAAK,IAAI,MAAM;AAClCA,cAAM,aAAa,KAAK,IAAI,MAAM;AAElC,cAAI,aAAa,YAAY;AAG3B,gBACG,SAAS,KAAK,cAAc,EAAE,gBAAgB,EAAE,mBAChD,SAAS,KAAK,cAAc,GAC7B;AAEA,qBAAO,OAAO,YAAY,KAAK,SAAS,KAAK,IAAI;;qBAE1C,aAAa,YAAY;AAGlC,gBACG,SAAS,KAAK,eAAe,EAAE,eAAe,EAAE,kBAChD,SAAS,KAAK,eAAe,GAC9B;AACA,qBAAO;;;AAIX,iBAAO;;AAGT,iBAAS,eAAe,aAAa,aAAa;AAChD,kBAAQ,aAAa;AACrB,kBAAQ,cAAc;AAEtB,yBAAe,CAAC;;AAGlB,iBAAS,SAAS,GAAG;AACnB,cAAI,EAAE,eAAe;AACnB,mBAAO,EAAE,cAAc,CAAC;;AAG1B,iBAAO;;AAGT,iBAAS,aAAa,GAAG;AACvB,cAAI,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY;AAC1D,mBAAO;;AAET,cAAI,EAAE,eAAe,EAAE,gBAAgB,SAAS,EAAE,YAAY,GAAG;AAC/D,mBAAO;;AAET,cAAI,EAAE,iBAAiB,EAAE,cAAc,WAAW,GAAG;AACnD,mBAAO;;AAET,cAAI,EAAE,eAAe,EAAE,gBAAgB,WAAW,EAAE,gBAAgB,EAAE,sBAAsB;AAC1F,mBAAO;;AAET,iBAAO;;AAGT,iBAAS,WAAW,GAAG;AACrB,cAAI,CAAC,aAAa,CAAC,GAAG;AACpB;;AAGFA,cAAMY,SAAQ,SAAS,CAAC;AAExB,gBAAM,YAAY,QAAQA,OAAM;AAChC,gBAAM,YAAY,QAAQA,OAAM;AAEhC,gBAAM,aAAY,oBAAI,KAAI,GAAG,QAAO;AAEpC,cAAI,MAAM,eAAe,MAAM;AAC7B,0BAAc,MAAM,UAAU;;;AAIlC,iBAAS,wBAAwB,QAAQ,QAAQ,QAAQ;AACvD,cAAI,CAAC,QAAQ,SAAS,MAAM,GAAG;AAC7B,mBAAO;;AAGTb,cAAI,SAAS;AAEb,iBAAO,UAAU,WAAW,SAAS;AACnC,gBAAI,OAAO,UAAU,SAAS,IAAI,QAAQ,SAAS,GAAG;AACpD,qBAAO;;AAGTC,gBAAM,QAAQQ,IAAQ,MAAM;AAG5B,gBAAI,UAAU,MAAM,UAAU,MAAM,eAAe,GAAG;AACpDR,kBAAM,eAAe,OAAO,eAAe,OAAO;AAClD,kBAAI,eAAe,GAAG;AACpB,oBACG,OAAO,YAAY,KAAK,SAAS,KACjC,OAAO,YAAY,gBAAgB,SAAS,GAC7C;AACA,yBAAO;;;;AAKb,gBAAI,UAAU,MAAM,UAAU,MAAM,eAAe,GAAG;AACpDA,kBAAM,gBAAgB,OAAO,cAAc,OAAO;AAClD,kBAAI,gBAAgB,GAAG;AACrB,oBACG,OAAO,aAAa,KAAK,SAAS,KAClC,OAAO,aAAa,iBAAiB,SAAS,GAC/C;AACA,yBAAO;;;;AAKb,qBAAS,OAAO;;AAGlB,iBAAO;;AAGT,iBAAS,UAAU,GAAG;AACpB,cAAI,aAAa,CAAC,GAAG;AACnBA,gBAAMY,SAAQ,SAAS,CAAC;AAExBZ,gBAAM,gBAAgB,EAAE,OAAOY,OAAM,OAAO,OAAOA,OAAM,MAAK;AAE9DZ,gBAAM,cAAc,cAAc,QAAQ,MAAM,YAAY;AAC5DA,gBAAM,cAAc,cAAc,QAAQ,MAAM,YAAY;AAE5D,gBAAI,wBAAwB,EAAE,QAAQ,aAAa,WAAW,GAAG;AAC/D;;AAGF,2BAAe,aAAa,WAAW;AACvC,kBAAM,cAAc;AAEpBA,gBAAM,eAAc,oBAAI,KAAI,GAAG,QAAO;AAEtCA,gBAAM,UAAU,cAAc,MAAM;AACpC,gBAAI,UAAU,GAAG;AACf,oBAAM,MAAM,IAAI,cAAc;AAC9B,oBAAM,MAAM,IAAI,cAAc;AAC9B,oBAAM,YAAY;;AAGpB,gBAAI,cAAc,aAAa,WAAW,GAAG;AAE3C,kBAAI,EAAE,YAAY;AAChB,kBAAE,eAAc;;;;;AAMxB,iBAAS,WAAW;AAClB,cAAI,EAAE,SAAS,aAAa;AAC1B,0BAAc,MAAM,UAAU;AAC9B,kBAAM,aAAa,YAAW,WAAI;AAChC,kBAAI,EAAE,eAAe;AACnB,8BAAc,MAAM,UAAU;AAC9B;;AAGF,kBAAI,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,MAAM,GAAG;AACpC,8BAAc,MAAM,UAAU;AAC9B;;AAGF,kBAAI,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,QAAQ,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,MAAM;AACpE,8BAAc,MAAM,UAAU;AAC9B;;AAGF,6BAAe,MAAM,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI,EAAE;AAErD,oBAAM,MAAM,KAAK;AACjB,oBAAM,MAAM,KAAK;eAChB,EAAE;;;AAIT,YAAI,IAAI,eAAe;AACrB,YAAE,MAAM,KAAK,SAAS,cAAc,UAAU;AAC9C,YAAE,MAAM,KAAK,SAAS,aAAa,SAAS;AAC5C,YAAE,MAAM,KAAK,SAAS,YAAY,QAAQ;mBACjC,IAAI,mBAAmB;AAChC,cAAI,OAAO,cAAc;AACvB,cAAE,MAAM,KAAK,SAAS,eAAe,UAAU;AAC/C,cAAE,MAAM,KAAK,SAAS,eAAe,SAAS;AAC9C,cAAE,MAAM,KAAK,SAAS,aAAa,QAAQ;qBAClC,OAAO,gBAAgB;AAChC,cAAE,MAAM,KAAK,SAAS,iBAAiB,UAAU;AACjD,cAAE,MAAM,KAAK,SAAS,iBAAiB,SAAS;AAChD,cAAE,MAAM,KAAK,SAAS,eAAe,QAAQ;;;;ACvMnDA,UAAM,kBAAe,WAAM;AAAA,eAAI;UAC7B,UAAU,CAAC,cAAc,cAAc,YAAY,SAAS,OAAO;UACnE,oBAAoB;UACpB,oBAAoB;UACpB,oBAAoB;UACpB,qBAAqB;UACrB,qBAAqB;UACrB,iBAAiB;UACjB,iBAAiB;UACjB,aAAa;UACb,kBAAkB;UAClB,kBAAkB;UAClB,YAAY;;MACb;AAEDA,UAAM,WAAW;QACf,cAAc;QACd,cAAca;;;;;AAMhB,UAAqB,mBACnB,SAAAC,kBAAY,SAAS,cAAmB;;oDAAJ,CAAA;AAClC,YAAI,OAAO,YAAY,UAAU;AACjC,oBAAY,SAAS,cAAc,OAAO;;AAG5C,YAAM,CAAC,WAAW,CAAC,QAAQ,UAAU;AACjC,gBAAM,IAAI,MAAM,wDAAwD;;AAG1E,aAAK,UAAU;AAEjB,gBAAU,UAAU,IAAI,IAAI,IAAI;AAE9B,aAAK,WAAW,gBAAe;AAC/B,iBAAW,OAAO,cAAc;AAChC,eAAO,SAAS,GAAG,IAAI,aAAa,GAAG;;AAGvC,aAAK,iBAAiB;AACtB,aAAK,kBAAkB;AACvB,aAAK,eAAe;AACpB,aAAK,gBAAgB;AAErBd,YAAM,QAAK,WAAM;AAAA,iBAAG,QAAQ,UAAU,IAAI,IAAI,MAAM,KAAK;QAAA;AACzDA,YAAM,OAAI,WAAM;AAAA,iBAAG,QAAQ,UAAU,OAAO,IAAI,MAAM,KAAK;QAAA;AAE3D,aAAK,QAAQQ,IAAQ,OAAO,EAAE,cAAc;AAC5C,YAAI,KAAK,UAAU,MAAM;AACzB,kBAAU,UAAU,IAAI,IAAI,GAAG;;AAE/B,aAAK,mBAAmB,WAAI;AAC1BR,cAAM,qBAAqB,QAAQ;AACnCD,cAAI,SAAS;AACb,kBAAQ,aAAa;AACrB,mBAAS,QAAQ,aAAa;AAC9B,kBAAQ,aAAa;AACvB,iBAAS;UACR;AACD,aAAK,2BAA2B,KAAK,mBACjC,QAAQ,cAAc,QAAQ,cAC9B;AACJ,aAAK,QAAQ,IAAI,aAAY;AAC/B,aAAO,gBAAgB,QAAQ,iBAAiB;AAE9C,aAAK,iBAAiBgB,IAAQ,IAAI,QAAQ,KAAK,GAAG,CAAC;AACrD,gBAAU,YAAY,KAAK,cAAc;AACvC,aAAK,aAAaA,IAAQ,IAAI,QAAQ,MAAM,GAAG,CAAC;AAClD,aAAO,eAAe,YAAY,KAAK,UAAU;AACjD,aAAO,WAAW,aAAa,YAAY,CAAC;AAC1C,aAAK,MAAM,KAAK,KAAK,YAAY,SAAS,KAAK;AAC/C,aAAK,MAAM,KAAK,KAAK,YAAY,QAAQ,IAAI;AAC7C,aAAK,mBAAmB;AACxB,aAAK,kBAAkB;AACvB,aAAK,iBAAiB;AACtBf,YAAM,aAAaQ,IAAQ,KAAK,cAAc;AAC9C,aAAK,mBAAmB,SAAS,WAAW,QAAQ,EAAE;AACtD,YAAI,MAAM,KAAK,gBAAgB,GAAG;AAChC,eAAK,0BAA0B;AACjC,eAAO,gBAAgB,MAAM,WAAW,GAAG;eACpC;AACL,eAAK,0BAA0B;;AAEjC,aAAK,mBAAmB,MAAM,WAAW,eAAe,IAAI,MAAM,WAAW,gBAAgB;AAE7FG,YAAQ,KAAK,gBAAgB,EAAE,SAAS,QAAO,CAAE;AACjD,aAAK,mBAAmB,MAAM,WAAW,UAAU,IAAI,MAAM,WAAW,WAAW;AACnFA,YAAQ,KAAK,gBAAgB,EAAE,SAAS,GAAE,CAAE;AAC5C,aAAK,aAAa;AAClB,aAAK,aAAa;AAElB,aAAK,iBAAiBI,IAAQ,IAAI,QAAQ,KAAK,GAAG,CAAC;AACrD,gBAAU,YAAY,KAAK,cAAc;AACvC,aAAK,aAAaA,IAAQ,IAAI,QAAQ,MAAM,GAAG,CAAC;AAClD,aAAO,eAAe,YAAY,KAAK,UAAU;AACjD,aAAO,WAAW,aAAa,YAAY,CAAC;AAC1C,aAAK,MAAM,KAAK,KAAK,YAAY,SAAS,KAAK;AAC/C,aAAK,MAAM,KAAK,KAAK,YAAY,QAAQ,IAAI;AAC7C,aAAK,mBAAmB;AACxB,aAAK,mBAAmB;AACxB,aAAK,gBAAgB;AACrBf,YAAM,aAAaQ,IAAQ,KAAK,cAAc;AAC9C,aAAK,kBAAkB,SAAS,WAAW,OAAO,EAAE;AACpD,YAAI,MAAM,KAAK,eAAe,GAAG;AAC/B,eAAK,yBAAyB;AAChC,eAAO,iBAAiB,MAAM,WAAW,IAAI;eACtC;AACL,eAAK,yBAAyB;;AAEhC,aAAK,uBAAuB,KAAK,QAAQ,WAAW,KAAK,UAAU,IAAI;AACvE,aAAK,mBAAmB,MAAM,WAAW,cAAc,IAAI,MAAM,WAAW,iBAAiB;AAC7FG,YAAQ,KAAK,gBAAgB,EAAE,SAAS,QAAO,CAAE;AACjD,aAAK,oBAAoB,MAAM,WAAW,SAAS,IAAI,MAAM,WAAW,YAAY;AACpFA,YAAQ,KAAK,gBAAgB,EAAE,SAAS,GAAE,CAAE;AAC5C,aAAK,cAAc;AACnB,aAAK,aAAa;AAEpB,aAAO,QAAQ;UACX,GACE,QAAQ,cAAc,IAClB,UACA,QAAQ,cAAc,KAAK,eAAe,KAAK,iBAC/C,QACA;UACN,GACE,QAAQ,aAAa,IACjB,UACA,QAAQ,aAAa,KAAK,gBAAgB,KAAK,kBAC/C,QACA;;AAGR,aAAK,UAAU;AAEjB,aAAO,SAAS,SAAS,QAAO,SAAE,aAAa;AAAA,iBAAG,SAAS,WAAW,EAAER,MAAI;QAAA,CAAC;AAE3E,aAAK,gBAAgB,KAAK,MAAM,QAAQ,SAAS;AACjD,aAAK,iBAAiB,QAAQ;AAChC,aAAO,MAAM,KAAK,KAAK,SAAS,UAAQ,SAAG,GAAG;AAAA,iBAAGA,OAAK,SAAS,CAAC;QAAA,CAAC;AAC/D,uBAAe,IAAI;MACrB;AAEF,uBAAA,UAAE,SAAA,SAAA,SAAS;AACP,YAAI,CAAC,KAAK,SAAS;AACjB;;AAIF,aAAK,2BAA2B,KAAK,mBACjC,KAAK,QAAQ,cAAc,KAAK,QAAQ,cACxC;AAGJQ,YAAQ,KAAK,gBAAgB,EAAE,SAAS,QAAO,CAAE;AACjDA,YAAQ,KAAK,gBAAgB,EAAE,SAAS,QAAO,CAAE;AACnD,aAAO,mBACH,MAAMH,IAAQ,KAAK,cAAc,EAAE,UAAU,IAC7C,MAAMA,IAAQ,KAAK,cAAc,EAAE,WAAW;AAClD,aAAO,oBACH,MAAMA,IAAQ,KAAK,cAAc,EAAE,SAAS,IAC5C,MAAMA,IAAQ,KAAK,cAAc,EAAE,YAAY;AAGjDG,YAAQ,KAAK,gBAAgB,EAAE,SAAS,OAAM,CAAE;AAChDA,YAAQ,KAAK,gBAAgB,EAAE,SAAS,OAAM,CAAE;AAEhD,uBAAe,IAAI;AAEnB,0BAAkB,MAAM,OAAO,GAAG,OAAO,IAAI;AAC7C,0BAAkB,MAAM,QAAQ,GAAG,OAAO,IAAI;AAE9CA,YAAQ,KAAK,gBAAgB,EAAE,SAAS,GAAE,CAAE;AAC5CA,YAAQ,KAAK,gBAAgB,EAAE,SAAS,GAAE,CAAE;MAC9C;AAEF,uBAAA,UAAE,WAAA,SAAA,SAAS,GAAG;AACV,YAAI,CAAC,KAAK,SAAS;AACjB;;AAGF,uBAAe,IAAI;AACnB,0BAAkB,MAAM,OAAO,KAAK,QAAQ,YAAY,KAAK,aAAa;AAC1E,0BAAkB,MAAM,QAAQ,KAAK,QAAQ,aAAa,KAAK,cAAc;AAE7E,aAAK,gBAAgB,KAAK,MAAM,KAAK,QAAQ,SAAS;AACxD,aAAO,iBAAiB,KAAK,QAAQ;MACrC;AAEF,uBAAA,UAAE,UAAA,SAAA,UAAU;AACR,YAAI,CAAC,KAAK,SAAS;AACjB;;AAGF,aAAK,MAAM,UAAS;AACtBD,eAAa,KAAK,UAAU;AAC5BA,eAAa,KAAK,UAAU;AAC5BA,eAAa,KAAK,cAAc;AAChCA,eAAa,KAAK,cAAc;AAC9B,aAAK,gBAAe;AAGpB,aAAK,UAAU;AACf,aAAK,aAAa;AAClB,aAAK,aAAa;AAClB,aAAK,iBAAiB;AACtB,aAAK,iBAAiB;AAEtB,aAAK,UAAU;MACjB;AAEF,uBAAA,UAAE,kBAAA,SAAA,kBAAkB;AAClB,aAAO,QAAQ,YAAY,KAAK,QAAQ,UACnC,MAAM,GAAG,EACT,OAAM,SAAE,MAAM;AAAA,iBAAG,CAAC,KAAK,MAAM,eAAe;QAAA,CAAC,EAC7C,KAAK,GAAG;MACb;;;;;", "names": ["let", "const", "div", "EventElement", "this", "EventManager", "ee", "processScrollDiff", "DOM.matches", "CSS.get", "DOM.query<PERSON><PERSON><PERSON>n", "DOM.remove", "CSS.set", "touch", "dragThumb", "PerfectScrollbar", "DOM.div"]}