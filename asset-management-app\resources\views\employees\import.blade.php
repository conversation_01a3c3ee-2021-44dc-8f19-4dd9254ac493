@extends('layouts.contentNavbarLayout')

@section('title', 'Import Data Karyawan')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Import Data Karyawan</h5>
                    <a href="{{ route('master.employees.index') }}" class="btn btn-secondary">
                        <i class="ri-arrow-left-line me-1"></i>Kembali
                    </a>
                </div>
                <div class="card-body">
                    
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="ri-check-circle-line me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="ri-error-warning-line me-2"></i>
                            <strong>Ter<PERSON><PERSON> k<PERSON>:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('import_errors') && count(session('import_errors')) > 0)
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="ri-alert-line me-2"></i>
                            <strong>Detail kesalahan import:</strong>
                            <div class="mt-2" style="max-height: 200px; overflow-y: auto;">
                                <ul class="mb-0">
                                    @foreach(session('import_errors') as $error)
                                        <li><small>{{ $error }}</small></li>
                                    @endforeach
                                </ul>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Step 1: Download Template -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="ri-download-line me-2"></i>
                                        Langkah 1: Download Template
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">
                                        Download template Excel untuk import data karyawan. Template sudah dilengkapi dengan:
                                    </p>
                                    <ul class="text-muted mb-3">
                                        <li>Format kolom yang benar</li>
                                        <li>Petunjuk pengisian lengkap</li>
                                        <li>Data referensi (cabang, divisi)</li>
                                        <li>Contoh data yang valid</li>
                                    </ul>
                                    <a href="{{ route('master.employees.import.template') }}" class="btn btn-primary">
                                        <i class="ri-download-2-line me-1"></i>
                                        Download Template Excel
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Upload File -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="ri-upload-line me-2"></i>
                                        Langkah 2: Upload File Excel
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('master.employees.import.process') }}" method="POST" enctype="multipart/form-data" id="importForm">
                                        @csrf
                                        
                                        <div class="mb-3">
                                            <label for="file" class="form-label">Pilih File Excel</label>
                                            <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                                            <div class="form-text">
                                                <i class="ri-information-line me-1"></i>
                                                Format: .xlsx atau .xls, Maksimal: 10MB, Maksimal 1000 baris data
                                            </div>
                                        </div>

                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-success" id="submitBtn">
                                                <i class="ri-upload-2-line me-1"></i>
                                                Import Data Karyawan
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="ri-information-line me-2"></i>
                                        Petunjuk Import Data Karyawan
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">Kolom Wajib Diisi:</h6>
                                            <ul class="text-muted">
                                                <li><strong>NIK:</strong> 16 digit, unik</li>
                                                <li><strong>Nama Lengkap:</strong> Nama lengkap karyawan</li>
                                                <li><strong>Kode Cabang:</strong> Sesuai data master cabang</li>
                                                <li><strong>Kode Divisi:</strong> Sesuai data master divisi</li>
                                                <li><strong>Jabatan:</strong> Jabatan karyawan</li>
                                                <li><strong>Status Karyawan:</strong> permanent, contract, atau intern</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-primary">Kolom Opsional:</h6>
                                            <ul class="text-muted">
                                                <li><strong>Departemen:</strong> Bagian/departemen</li>
                                                <li><strong>Email:</strong> Harus unik jika diisi</li>
                                                <li><strong>Telepon:</strong> Nomor telepon</li>
                                                <li><strong>Tanggal Bergabung:</strong> Format dd/mm/yyyy</li>
                                                <li><strong>Tanggal Lahir:</strong> Format dd/mm/yyyy</li>
                                                <li><strong>Jenis Kelamin:</strong> male atau female</li>
                                                <li><strong>Alamat:</strong> Alamat lengkap</li>
                                                <li><strong>Status Aktif:</strong> true atau false</li>
                                                <li><strong>Catatan:</strong> Catatan tambahan</li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-warning mt-3">
                                        <i class="ri-alert-line me-2"></i>
                                        <strong>Catatan Penting:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>NIK harus 16 digit dan unik dalam sistem</li>
                                            <li>Email harus unik jika diisi</li>
                                            <li>Format tanggal: dd/mm/yyyy (contoh: 15/06/1990)</li>
                                            <li>Jenis kelamin: male atau female</li>
                                            <li>Status karyawan: permanent, contract, atau intern</li>
                                            <li>Status aktif: true atau false</li>
                                            <li>Maksimal 1000 baris data per sekali import</li>
                                            <li>Pastikan kode cabang dan divisi sesuai dengan data master</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('importForm');
    const fileInput = document.getElementById('file');
    const submitBtn = document.getElementById('submitBtn');
    
    // File validation
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file type
            const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
            if (!allowedTypes.includes(file.type)) {
                alert('File harus berformat Excel (.xlsx atau .xls)');
                this.value = '';
                return;
            }
            
            // Check file size (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('Ukuran file maksimal 10MB');
                this.value = '';
                return;
            }
        }
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        if (!fileInput.files[0]) {
            e.preventDefault();
            alert('Silakan pilih file Excel terlebih dahulu');
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Memproses...';
        
        // Re-enable button after 30 seconds (fallback)
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="ri-upload-2-line me-1"></i>Import Data Karyawan';
        }, 30000);
    });
});
</script>
@endsection
