@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Pengaturan Perusahaan - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Pengaturan Perusahaan /</span> Edit Pengaturan
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Edit Pengaturan Perusahaan</h5>
          <a href="{{ route('master.company-settings.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.company-settings.update') }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row mb-3">
              <div class="col-md-8">
                <label class="form-label" for="company_name">Nama Perusahaan <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('company_name') is-invalid @enderror" 
                       id="company_name" name="company_name" value="{{ old('company_name', $setting->company_name) }}" 
                       placeholder="Contoh: PT. Bina Informatika Teknologi">
                @error('company_name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="company_code">Kode Perusahaan <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('company_code') is-invalid @enderror" 
                       id="company_code" name="company_code" value="{{ old('company_code', $setting->company_code) }}" 
                       placeholder="Contoh: BIT" maxlength="10">
                @error('company_code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode untuk nomor dokumen</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="address">Alamat</label>
              <textarea class="form-control @error('address') is-invalid @enderror" 
                        id="address" name="address" rows="3" 
                        placeholder="Alamat lengkap perusahaan">{{ old('address', $setting->address) }}</textarea>
              @error('address')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="phone">Nomor Telepon</label>
                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                       id="phone" name="phone" value="{{ old('phone', $setting->phone) }}" 
                       placeholder="Contoh: 021-12345678">
                @error('phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="email">Email</label>
                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email', $setting->email) }}" 
                       placeholder="Contoh: <EMAIL>">
                @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="website">Website</label>
              <input type="url" class="form-control @error('website') is-invalid @enderror"
                     id="website" name="website" value="{{ old('website', $setting->website) }}"
                     placeholder="Contoh: https://bit.co.id">
              @error('website')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <label class="form-label" for="asset_usage_agreement">Perjanjian Penggunaan Asset</label>
              <textarea class="form-control @error('asset_usage_agreement') is-invalid @enderror"
                        id="asset_usage_agreement" name="asset_usage_agreement" rows="4"
                        placeholder="Masukkan teks perjanjian penggunaan asset yang akan muncul di tanda terima asset">{{ old('asset_usage_agreement', $setting->asset_usage_agreement) }}</textarea>
              @error('asset_usage_agreement')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Teks ini akan muncul di dokumen tanda terima asset. Kosongkan untuk menggunakan teks default.</div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.company-settings.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Penting</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li><strong>Kode Perusahaan</strong> digunakan dalam format nomor dokumen</li>
              <li>Perubahan kode akan mempengaruhi nomor dokumen baru</li>
              <li>Pastikan kode singkat dan mudah diingat</li>
            </ul>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Format Nomor Dokumen:</h6>
            <p class="mb-2"><code>{company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}</code></p>
            <p class="mb-0">Contoh: <code>BITJKT25-PC-LAPTOP-02-0001</code></p>
          </div>

          @if($setting->id)
          <div class="alert alert-secondary">
            <h6 class="alert-heading">Data Saat Ini:</h6>
            <p class="mb-2">Nama: <strong>{{ $setting->company_name }}</strong></p>
            <p class="mb-2">Kode: <strong>{{ $setting->company_code }}</strong></p>
            <p class="mb-0">Diupdate: {{ $setting->updated_at->format('d/m/Y H:i') }}</p>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase company code
  const codeInput = document.getElementById('company_code');
  codeInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  });

  // Format phone number
  const phoneInput = document.getElementById('phone');
  phoneInput.addEventListener('input', function() {
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });
});
</script>
@endsection
