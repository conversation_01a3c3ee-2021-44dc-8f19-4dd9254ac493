<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DocumentNumber;
use App\Models\Branch;
use App\Models\AssetCategory;
use App\Models\AssetType;

class TestDocumentNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:document-number';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test document number generation system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Document Number Generation System');
        $this->info('==========================================');

        // Get test data
        $jakartaBranch = Branch::where('code', 'JKT-E')->first();
        $bandungBranch = Branch::where('code', 'BDG')->first();
        $pcCategory = AssetCategory::where('code', 'PC-LAPTOP')->first();
        $laptopType = AssetType::where('code', '02')->where('asset_category_id', $pcCategory?->id)->first();

        $this->info("Debug Info:");
        $this->line("Jakarta Branch: " . ($jakartaBranch ? $jakartaBranch->name : 'NOT FOUND'));
        $this->line("PC Category: " . ($pcCategory ? $pcCategory->name : 'NOT FOUND'));
        $this->line("Laptop Type: " . ($laptopType ? $laptopType->name : 'NOT FOUND'));

        if (!$jakartaBranch || !$pcCategory || !$laptopType) {
            $this->error('Required test data not found. Please run seeders first.');

            // Show available data
            $this->info("\nAvailable Branches:");
            Branch::all(['name', 'code'])->each(function($branch) {
                $this->line("- {$branch->name} ({$branch->code})");
            });

            $this->info("\nAvailable Categories:");
            AssetCategory::all(['name', 'code'])->each(function($category) {
                $this->line("- {$category->name} ({$category->code})");
            });

            $this->info("\nAvailable Asset Types:");
            AssetType::with('category')->get(['name', 'code', 'asset_category_id'])->each(function($type) {
                $this->line("- {$type->name} ({$type->code}) - Category: {$type->category->name}");
            });

            return;
        }

        // Test 1: Generate document number for Jakarta Laptop
        $this->info("\n1. Testing Jakarta Branch - Laptop Asset:");
        $docNumber1 = DocumentNumber::getDocumentNumber('asset', $jakartaBranch->id, $pcCategory->id, $laptopType->id);
        $this->line("Generated: {$docNumber1}");

        // Test 2: Generate another document number for Jakarta Laptop (should increment)
        $this->info("\n2. Testing Jakarta Branch - Laptop Asset (next number):");
        $docNumber2 = DocumentNumber::getDocumentNumber('asset', $jakartaBranch->id, $pcCategory->id, $laptopType->id);
        $this->line("Generated: {$docNumber2}");

        // Test 3: Generate document number for Bandung Laptop (should start from 0001)
        if ($bandungBranch) {
            $this->info("\n3. Testing Bandung Branch - Laptop Asset:");
            $docNumber3 = DocumentNumber::getDocumentNumber('asset', $bandungBranch->id, $pcCategory->id, $laptopType->id);
            $this->line("Generated: {$docNumber3}");
        }

        // Show format explanation
        $this->info("\n4. Format Explanation:");
        $this->line("Format: {company_code}{branch_code}{year}-{category_code}-{asset_type_code}-{number}");
        $this->line("Example: BITJKT-E25-PC-LAPTOP-02-0004");
        $this->line("- BIT: Company Code");
        $this->line("- JKT-E: Branch Code (Jakarta Timur)");
        $this->line("- 25: Year (2025)");
        $this->line("- PC-LAPTOP: Category Code (PC & Laptop)");
        $this->line("- 02: Asset Type Code (Laptop)");
        $this->line("- 0004: Auto Number (per branch)");

        $this->info("\n✅ Document Number Generation Test Completed!");
    }
}
