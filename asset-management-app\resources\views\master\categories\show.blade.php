@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Kategori - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Kategori /</span> Detail Kategori
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Detail Kategori: {{ $category->name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.categories.edit', $category) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.categories.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Ke<PERSON><PERSON>
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Kategori</label>
                <p class="fw-bold">{{ $category->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Kategori</label>
                <p><span class="badge bg-primary fs-6">{{ $category->code }}</span></p>
              </div>
            </div>
          </div>

          @if($category->description)
          <div class="mb-3">
            <label class="form-label text-muted">Deskripsi</label>
            <p>{{ $category->description }}</p>
          </div>
          @endif

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $category->is_active ? 'success' : 'secondary' }} fs-6">
                    {{ $category->is_active ? 'Aktif' : 'Non-Aktif' }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Jumlah Asset</label>
                <p><span class="badge bg-info fs-6">{{ $category->assets->count() }} asset</span></p>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Dibuat</label>
                <p>{{ $category->created_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Terakhir Diupdate</label>
                <p>{{ $category->updated_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Assets Card -->
      @if($category->assets->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">Asset dalam Kategori Ini ({{ $category->assets->count() }} asset)</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Kode Asset</th>
                  <th>Nama</th>
                  <th>Cabang</th>
                  <th>Status</th>
                  <th>Kondisi</th>
                </tr>
              </thead>
              <tbody>
                @foreach($category->assets as $asset)
                <tr>
                  <td><span class="badge bg-info badge-sm">{{ $asset->asset_code }}</span></td>
                  <td>{{ $asset->name }}</td>
                  <td>{{ $asset->branch->name }}</td>
                  <td>
                    <span class="badge bg-{{ $asset->status_badge }} badge-sm">
                      {{ ucfirst($asset->status) }}
                    </span>
                  </td>
                  <td>
                    <span class="badge bg-{{ $asset->condition_badge }} badge-sm">
                      {{ ucfirst($asset->condition) }}
                    </span>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
      @endif
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Kategori</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                <i class="ri-folder-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $category->name }}</h6>
              <small class="text-muted">{{ $category->code }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>{{ $category->assets->count() }} Asset Total</li>
              <li>{{ $category->assets->where('status', 'active')->count() }} Asset Aktif</li>
              <li>Dibuat {{ $category->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.categories.edit', $category) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Kategori
            </a>
            
            @if($category->is_active)
              <button class="btn btn-warning" onclick="toggleStatus(false)">
                <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
              </button>
            @else
              <button class="btn btn-success" onclick="toggleStatus(true)">
                <i class="ri-play-circle-line me-1"></i>Aktifkan
              </button>
            @endif
            
            @if($category->assets->count() == 0)
            <form action="{{ route('master.categories.destroy', $category) }}" method="POST" id="deleteForm">
              @csrf
              @method('DELETE')
              <button type="button" class="btn btn-danger w-100" onclick="confirmDelete()">
                <i class="ri-delete-bin-line me-1"></i>Hapus Kategori
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStatus(status) {
  if (confirm('Yakin ingin mengubah status kategori ini?')) {
    // Create form to toggle status
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("master.categories.update", $category) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'is_active';
    statusField.value = status ? '1' : '0';
    
    // Copy other fields
    const fields = ['name', 'code', 'description'];
    fields.forEach(field => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = field;
      input.value = branchData[field];
      form.appendChild(input);
    });
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    form.appendChild(statusField);
    
    document.body.appendChild(form);
    form.submit();
  }
}

function confirmDelete() {
  if (confirm('Yakin ingin menghapus kategori ini?\n\nTindakan ini tidak dapat dibatalkan!')) {
    document.getElementById('deleteForm').submit();
  }
}
</script>
@endsection
