@extends('layouts.contentNavbarLayout')

@section('title', 'Stock Opname - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-search-eye-line me-2"></i>
              Stock Opname Asset
            </h5>
            <small class="text-muted">Kelola dan monitor stock opname asset</small>
          </div>
          <div>
            @if(!$activeOpname)
              <a href="{{ route('stock-opnames.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>
                Buat Stock Opname
              </a>
            @else
              <span class="badge bg-warning">
                <i class="ri-lock-line me-1"></i>
                Ada Stock Opname Aktif
              </span>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Stock Opname Alert -->
  @if($activeOpname)
    <div class="row mb-4">
      <div class="col-12">
        <div class="alert alert-warning d-flex align-items-center" role="alert">
          <i class="ri-alert-line ri-24px me-3"></i>
          <div class="flex-grow-1">
            <h6 class="alert-heading mb-1">Stock Opname Sedang Berjalan</h6>
            <p class="mb-0">
              <strong>{{ $activeOpname->title ?? 'Stock Opname' }}</strong> ({{ $activeOpname->opname_number ?? 'N/A' }})
              sedang berlangsung. Operasi asset dikunci hingga stock opname selesai.
            </p>
          </div>
          <div class="ms-3">
            <a href="{{ route('stock-opnames.show', $activeOpname->id ?? '#') }}" class="btn btn-warning btn-sm">
              <i class="ri-eye-line me-1"></i>
              Lihat Detail
            </a>
          </div>
        </div>
      </div>
    </div>
  @endif

  <!-- Stock Opnames List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">
            <i class="ri-list-check-3 me-2"></i>
            Daftar Stock Opname
          </h6>
          <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse"
                  data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
            <i class="ri-filter-line me-1"></i>
            Filter
            @if(request()->hasAny(['branch_id', 'asset_category_id', 'status', 'date_from', 'date_to']))
              <span class="badge bg-primary ms-1">{{ collect(request()->only(['branch_id', 'asset_category_id', 'status', 'date_from', 'date_to']))->filter()->count() }}</span>
            @endif
          </button>
        </div>

        <!-- Compact Filters -->
        <div class="collapse show" id="filterCollapse">
          <div class="card-body border-bottom">
          <form method="GET" action="{{ route('stock-opnames.index') }}" class="row g-3 align-items-end">
            <div class="col-md-2">
              <label for="branch_id" class="form-label small">Cabang</label>
              <select name="branch_id" id="branch_id" class="form-select form-select-sm">
                <option value="">Semua Cabang</option>
                @foreach($branches as $branch)
                  <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                    {{ $branch->name }}
                  </option>
                @endforeach
              </select>
            </div>

            <div class="col-md-2">
              <label for="asset_category_id" class="form-label small">Kategori</label>
              <select name="asset_category_id" id="asset_category_id" class="form-select form-select-sm">
                <option value="">Semua Kategori</option>
                @foreach($assetCategories as $category)
                  <option value="{{ $category->id }}" {{ request('asset_category_id') == $category->id ? 'selected' : '' }}>
                    {{ $category->name }}
                  </option>
                @endforeach
              </select>
            </div>

            <div class="col-md-2">
              <label for="status" class="form-label small">Status</label>
              <select name="status" id="status" class="form-select form-select-sm">
                <option value="">Semua Status</option>
                <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>Sedang Berjalan</option>
                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
              </select>
            </div>

            <div class="col-md-2">
              <label for="date_from" class="form-label small">Dari Tanggal</label>
              <input type="date" name="date_from" id="date_from" class="form-control form-control-sm"
                     value="{{ request('date_from') }}">
            </div>

            <div class="col-md-2">
              <label for="date_to" class="form-label small">Sampai Tanggal</label>
              <input type="date" name="date_to" id="date_to" class="form-control form-control-sm"
                     value="{{ request('date_to') }}">
            </div>

            <div class="col-md-2">
              <div class="d-flex gap-1">
                <button type="submit" class="btn btn-primary btn-sm">
                  <i class="ri-search-line me-1"></i>
                  Filter
                </button>
                <a href="{{ route('stock-opnames.index') }}" class="btn btn-outline-secondary btn-sm">
                  <i class="ri-refresh-line me-1"></i>
                  Reset
                </a>
              </div>
            </div>
          </form>
          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filter Status -->
          <div class="row mb-3">
            <div class="col-md-6">
              <form method="GET" action="{{ route('stock-opnames.index') }}" class="d-flex">
                <!-- Preserve existing filters -->
                @foreach(request()->except(['search', 'page']) as $key => $value)
                  <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                @endforeach

                <div class="input-group input-group-sm">
                  <input type="text" name="search" class="form-control"
                         placeholder="Cari nomor opname, judul, atau deskripsi..."
                         value="{{ request('search') }}">
                  <button type="submit" class="btn btn-outline-primary">
                    <i class="ri-search-line"></i>
                  </button>
                </div>
              </form>
            </div>
            <div class="col-md-6 text-end">
              @if(request()->hasAny(['branch_id', 'asset_category_id', 'status', 'date_from', 'date_to', 'search']))
                <small class="text-muted">
                  <i class="ri-filter-line me-1"></i>
                  Filter aktif:
                  @if(request('branch_id'))
                    <span class="badge bg-light text-dark">{{ $branches->find(request('branch_id'))->name ?? 'Cabang' }}</span>
                  @endif
                  @if(request('asset_category_id'))
                    <span class="badge bg-light text-dark">{{ $assetCategories->find(request('asset_category_id'))->name ?? 'Kategori' }}</span>
                  @endif
                  @if(request('status'))
                    <span class="badge bg-light text-dark">{{ ucfirst(request('status')) }}</span>
                  @endif
                  @if(request('date_from') || request('date_to'))
                    <span class="badge bg-light text-dark">
                      {{ request('date_from') ? date('d/m/Y', strtotime(request('date_from'))) : '' }}
                      {{ request('date_from') && request('date_to') ? ' - ' : '' }}
                      {{ request('date_to') ? date('d/m/Y', strtotime(request('date_to'))) : '' }}
                    </span>
                  @endif
                  @if(request('search'))
                    <span class="badge bg-light text-dark">"{{ request('search') }}"</span>
                  @endif
                </small>
              @endif
            </div>
          </div>

          @if($stockOpnames->count() > 0)
            <!-- Results Count -->
            <div class="d-flex justify-content-between align-items-center mb-3">
              <small class="text-muted">
                Menampilkan {{ $stockOpnames->firstItem() }} - {{ $stockOpnames->lastItem() }}
                dari {{ $stockOpnames->total() }} stock opname
              </small>
              <small class="text-muted">
                <i class="ri-database-line me-1"></i>
                Total: {{ $stockOpnames->total() }} stock opname
              </small>
            </div>

            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Nomor Opname</th>
                    <th>Judul</th>
                    <th>Cabang</th>
                    <th>Kategori</th>
                    <th>Status</th>
                    <th>Progress</th>
                    <th>Tanggal</th>
                    <th>Dibuat Oleh</th>
                    <th width="150">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($stockOpnames as $opname)
                    <tr>
                      <td>
                        <span class="badge bg-primary">{{ $opname->opname_number }}</span>
                      </td>
                      <td>
                        <div>
                          <strong>{{ $opname->title }}</strong>
                          @if($opname->description)
                            <br><small class="text-muted">{{ Str::limit($opname->description, 50) }}</small>
                          @endif
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ $opname->branch->name }}</span>
                      </td>
                      <td>
                        @if($opname->assetCategory)
                          <span class="badge bg-primary">{{ $opname->assetCategory->name }}</span>
                        @else
                          <span class="badge bg-secondary">Semua</span>
                        @endif
                      </td>
                      <td>
                        @switch($opname->status)
                          @case('draft')
                            <span class="badge bg-secondary">Draft</span>
                            @break
                          @case('in_progress')
                            <span class="badge bg-warning">
                              <i class="ri-play-line me-1"></i>
                              Berjalan
                            </span>
                            @break
                          @case('completed')
                            <span class="badge bg-success">
                              <i class="ri-check-line me-1"></i>
                              Selesai
                            </span>
                            @break
                          @case('cancelled')
                            <span class="badge bg-danger">
                              <i class="ri-close-line me-1"></i>
                              Dibatalkan
                            </span>
                            @break
                        @endswitch
                      </td>
                      <td>
                        @if($opname->total_assets > 0)
                          @php
                            $progress = round(($opname->scanned_assets / $opname->total_assets) * 100, 1);
                          @endphp
                          <div class="d-flex align-items-center">
                            <div class="progress flex-grow-1 me-2" style="height: 6px;">
                              <div class="progress-bar" role="progressbar" 
                                   style="width: {{ $progress }}%" 
                                   aria-valuenow="{{ $progress }}" 
                                   aria-valuemin="0" 
                                   aria-valuemax="100"></div>
                            </div>
                            <small class="text-muted">{{ $progress }}%</small>
                          </div>
                          <small class="text-muted">{{ $opname->scanned_assets }}/{{ $opname->total_assets }} asset</small>
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>
                        <div>
                          <small class="text-muted">Dibuat:</small>
                          <br>{{ $opname->created_at->format('d/m/Y H:i') }}
                        </div>
                        @if($opname->start_date)
                          <div class="mt-1">
                            <small class="text-muted">Mulai:</small>
                            <br>{{ $opname->start_date->format('d/m/Y H:i') }}
                          </div>
                        @endif
                      </td>
                      <td>
                        {{ $opname->creator->name }}
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                  data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-more-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="{{ route('stock-opnames.show', $opname) }}">
                                <i class="ri-eye-line me-2"></i>
                                Lihat Detail
                              </a>
                            </li>
                            @if($opname->status === 'in_progress')
                              <li>
                                <a class="dropdown-item" href="{{ route('stock-opnames.hardcopy-form', $opname) }}" target="_blank">
                                  <i class="ri-file-excel-2-line me-2"></i>
                                  Download Form Hardcopy
                                </a>
                              </li>
                              <li><hr class="dropdown-divider"></li>
                            @endif

                            @if($opname->status === 'draft')
                              <li>
                                <form method="POST" action="{{ route('stock-opnames.start', $opname) }}">
                                  @csrf
                                  <button type="submit" class="dropdown-item text-warning"
                                          onclick="return confirm('Mulai stock opname? Asset operations akan dikunci.')">
                                    <i class="ri-play-line me-2"></i>
                                    Mulai Stock Opname
                                  </button>
                                </form>
                              </li>
                            @endif
                            
                            @if($opname->status === 'in_progress')
                              <li>
                                <a class="dropdown-item" href="{{ route('stock-opnames.scan-detail', $opname) }}">
                                  <i class="ri-qr-scan-line me-2"></i>
                                  Scan Asset
                                </a>
                              </li>
                              <li>
                                <form method="POST" action="{{ route('stock-opnames.complete', $opname) }}">
                                  @csrf
                                  <button type="submit" class="dropdown-item text-success"
                                          onclick="return confirm('Selesaikan stock opname? Asset operations akan dibuka kembali.')">
                                    <i class="ri-check-line me-2"></i>
                                    Selesaikan
                                  </button>
                                </form>
                              </li>
                            @endif
                            
                            @if($opname->status === 'completed')
                              <li>
                                <a class="dropdown-item" href="{{ route('stock-opnames.report', $opname) }}">
                                  <i class="ri-file-text-line me-2"></i>
                                  Laporan
                                </a>
                              </li>
                            @endif
                            
                            @if(in_array($opname->status, ['draft', 'in_progress']))
                              <li><hr class="dropdown-divider"></li>
                              <li>
                                <form method="POST" action="{{ route('stock-opnames.cancel', $opname) }}">
                                  @csrf
                                  <button type="submit" class="dropdown-item text-danger"
                                          onclick="return confirm('Batalkan stock opname ini?')">
                                    <i class="ri-close-line me-2"></i>
                                    Batalkan
                                  </button>
                                </form>
                              </li>
                            @endif
                          </ul>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
              {{ $stockOpnames->links() }}
            </div>
          @else
            <!-- Empty State -->
            <div class="text-center py-5">
              @if(request()->hasAny(['branch_id', 'asset_category_id', 'status', 'date_from', 'date_to', 'search']))
                <!-- Filtered Empty State -->
                <i class="ri-search-line ri-48px text-muted mb-3"></i>
                <h5 class="text-muted">Tidak Ada Hasil</h5>
                <p class="text-muted mb-4">Tidak ditemukan stock opname yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('stock-opnames.index') }}" class="btn btn-outline-primary">
                  <i class="ri-refresh-line me-1"></i>
                  Reset Filter
                </a>
              @else
                <!-- No Data Empty State -->
                <i class="ri-search-eye-line ri-48px text-muted mb-3"></i>
                <h5 class="text-muted">Belum Ada Stock Opname</h5>
                <p class="text-muted mb-4">Buat stock opname pertama untuk memulai audit asset.</p>
                @if(!$activeOpname)
                  <a href="{{ route('stock-opnames.create') }}" class="btn btn-primary">
                    <i class="ri-add-line me-1"></i>
                    Buat Stock Opname Pertama
                  </a>
                @endif
              @endif
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

@if(session('success'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-check-line me-2"></i>
      <div class="me-auto fw-semibold">Success</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('success') }}
    </div>
  </div>
@endif

@if(session('error'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-danger show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-error-warning-line me-2"></i>
      <div class="me-auto fw-semibold">Error</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('error') }}
    </div>
  </div>
@endif

@endsection

@section('page-script')
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto-submit form when filter dropdowns change
  const filterForm = document.querySelector('form[action*="stock-opnames.index"]');
  const filterSelects = filterForm.querySelectorAll('select');
  const dateInputs = filterForm.querySelectorAll('input[type="date"]');

  // Add change listeners to select elements
  filterSelects.forEach(select => {
    select.addEventListener('change', function() {
      // Add loading state
      const submitBtn = filterForm.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML = '<i class="ri-loader-4-line ri-spin me-1"></i>Loading...';
      submitBtn.disabled = true;

      // Submit form
      filterForm.submit();
    });
  });

  // Add change listeners to date inputs (with small delay)
  dateInputs.forEach(input => {
    let timeout;
    input.addEventListener('change', function() {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        const submitBtn = filterForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="ri-loader-4-line ri-spin me-1"></i>Loading...';
        submitBtn.disabled = true;

        filterForm.submit();
      }, 500); // 500ms delay for date inputs
    });
  });

  // Highlight active filters
  const activeFilters = document.querySelectorAll('select[name]:not([value=""]), input[name][value]:not([value=""])');
  activeFilters.forEach(filter => {
    if (filter.value && filter.value !== '') {
      filter.classList.add('border-primary');
    }
  });
});
</script>
@endsection
