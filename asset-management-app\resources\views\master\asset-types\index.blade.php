@extends('layouts.contentNavbarLayout')

@section('title', '<PERSON><PERSON>set - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> <PERSON><PERSON>
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('master.asset-types.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah Jenis Asset
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('master.asset-types.index') }}">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                   placeholder="Nama, kode, atau deskripsi...">
          </div>
          <div class="col-md-3">
            <label class="form-label">Kategori</label>
            <select class="form-select" name="category_id">
              <option value="">Semua Kategori</option>
              @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                  {{ $category->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" name="is_active">
              <option value="">Semua Status</option>
              <option value="1" {{ request('is_active') === '1' ? 'selected' : '' }}>Aktif</option>
              <option value="0" {{ request('is_active') === '0' ? 'selected' : '' }}>Non-Aktif</option>
            </select>
          </div>
          <div class="col-md-4">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('master.asset-types.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Asset Types Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Jenis Asset ({{ $assetTypes->total() }} jenis)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Kode</th>
            <th>Nama Jenis</th>
            <th>Kategori</th>
            <th>Deskripsi</th>
            <th>Jumlah Asset</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($assetTypes as $assetType)
          <tr>
            <td>
              <span class="badge bg-primary">{{ $assetType->code }}</span>
            </td>
            <td>
              <div>
                <strong>{{ $assetType->name }}</strong>
              </div>
            </td>
            <td>
              <span class="badge bg-info">{{ $assetType->category->name }}</span>
            </td>
            <td>
              <div class="text-truncate" style="max-width: 200px;">
                {{ $assetType->description ?: '-' }}
              </div>
            </td>
            <td>
              <span class="badge bg-secondary">{{ $assetType->assets_count }} asset</span>
            </td>
            <td>
              <span class="badge bg-{{ $assetType->is_active ? 'success' : 'secondary' }}">
                {{ $assetType->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('master.asset-types.show', $assetType) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('master.asset-types.edit', $assetType) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  @if($assetType->assets_count == 0)
                  <div class="dropdown-divider"></div>
                  <form action="{{ route('master.asset-types.destroy', $assetType) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus jenis asset ini?')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                  @endif
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="7" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-tools-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada data jenis asset</h6>
                <p class="text-muted mb-3">Belum ada jenis asset yang terdaftar atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('master.asset-types.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah Jenis Asset Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($assetTypes->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $assetTypes->firstItem() }} - {{ $assetTypes->lastItem() }} dari {{ $assetTypes->total() }} data
        </div>
        {{ $assetTypes->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
