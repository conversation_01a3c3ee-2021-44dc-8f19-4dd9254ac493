<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    protected $fillable = [
        'supplier_code',
        'name',
        'company_name',
        'address',
        'city',
        'province',
        'postal_code',
        'country',
        'phone',
        'fax',
        'email',
        'website',
        'contact_person',
        'contact_phone',
        'contact_email',
        'tax_number',
        'npwp_file',
        'business_type',
        'payment_terms',
        'credit_limit',
        'notes',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'credit_limit' => 'decimal:2'
    ];

    // Boot method for auto-generating supplier code
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($supplier) {
            if (empty($supplier->supplier_code)) {
                $supplier->supplier_code = self::generateSupplierCode();
            }
        });
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('supplier_code', $code);
    }

    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    public function scopeByCity($query, $city)
    {
        return $query->where('city', 'like', "%{$city}%");
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        $address = [];

        if ($this->address) $address[] = $this->address;
        if ($this->city) $address[] = $this->city;
        if ($this->province) $address[] = $this->province;
        if ($this->postal_code) $address[] = $this->postal_code;
        if ($this->country && $this->country !== 'Indonesia') $address[] = $this->country;

        return implode(', ', $address);
    }

    public function getPaymentTermsTextAttribute()
    {
        return match($this->payment_terms) {
            'cash' => 'Cash',
            'credit_7' => 'Credit 7 Days',
            'credit_14' => 'Credit 14 Days',
            'credit_30' => 'Credit 30 Days',
            'credit_45' => 'Credit 45 Days',
            'credit_60' => 'Credit 60 Days',
            'credit_90' => 'Credit 90 Days',
            default => 'Cash'
        };
    }

    public function getStatusBadgeAttribute()
    {
        return $this->is_active
            ? '<span class="badge bg-success">Active</span>'
            : '<span class="badge bg-secondary">Inactive</span>';
    }

    // Static methods
    public static function generateSupplierCode()
    {
        $prefix = 'SUP';
        $year = date('Y');
        $month = date('m');

        // Format: SUP + TAHUN + BULAN + NOMOR (5 digit)
        $codePrefix = $prefix . $year . $month;

        $lastSupplier = self::where('supplier_code', 'like', $codePrefix . '%')
                           ->orderBy('supplier_code', 'desc')
                           ->first();

        if ($lastSupplier) {
            $lastNumber = (int) substr($lastSupplier->supplier_code, -5);
            $newNumber = str_pad($lastNumber + 1, 5, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '00001';
        }

        return $codePrefix . $newNumber;
    }

    // File handling methods
    public function getNpwpFileUrlAttribute()
    {
        if ($this->npwp_file) {
            return asset('storage/suppliers/npwp/' . $this->npwp_file);
        }
        return null;
    }

    public function hasNpwpFile()
    {
        return !empty($this->npwp_file) && file_exists(storage_path('app/public/suppliers/npwp/' . $this->npwp_file));
    }

    public function deleteNpwpFile()
    {
        if ($this->npwp_file) {
            $filePath = storage_path('app/public/suppliers/npwp/' . $this->npwp_file);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }

    public static function getPaymentTermsOptions()
    {
        return [
            'cash' => 'Cash',
            'credit_7' => 'Credit 7 Days',
            'credit_14' => 'Credit 14 Days',
            'credit_30' => 'Credit 30 Days',
            'credit_45' => 'Credit 45 Days',
            'credit_60' => 'Credit 60 Days',
            'credit_90' => 'Credit 90 Days',
        ];
    }

    public static function getBusinessTypeOptions()
    {
        return [
            'PT' => 'PT (Perseroan Terbatas)',
            'CV' => 'CV (Commanditaire Vennootschap)',
            'UD' => 'UD (Usaha Dagang)',
            'Koperasi' => 'Koperasi',
            'Perorangan' => 'Perorangan',
            'BUMN' => 'BUMN',
            'Yayasan' => 'Yayasan',
            'Lainnya' => 'Lainnya'
        ];
    }
}
