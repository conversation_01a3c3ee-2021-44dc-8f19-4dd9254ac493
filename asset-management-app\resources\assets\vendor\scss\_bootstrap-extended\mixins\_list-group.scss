// List groups
// *******************************************************************************

@mixin list-group-item-variant($state: null, $background: null, $color: null) {
}

// Basic List groups
@mixin template-list-group-item-variant($parent, $background, $color: null) {
  $background-color: if(
    $dark-style,
    shift-color($background, -$list-group-item-bg-scale, $card-bg),
    shift-color($background, $list-group-item-bg-scale, $card-bg)
  );
  $border-color: shift-color($background, $list-group-item-color-scale, $card-bg);
  $color: shift-color($background, $list-group-item-color-scale);
  $hover-background: shade-color($background-color, $list-group-item-bg-hover-scale);
  #{$parent} {
    border-color: $border-color;
    background-color: $background-color;
    color: $color !important;
  }

  a#{$parent},
  button#{$parent} {
    color: $color;

    &.active {
      border-color: $background !important;
      background-color: $background !important;
      color: color-contrast($background) !important;
    }
  }
}

@mixin template-list-group-theme($background, $color: null) {
  @include template-list-group-item-variant('.list-group-item-primary', $background);

  .list-group-item.active {
    background-color: rgba-to-hex(rgba($background, 0.16), $rgba-to-hex-bg);
    color: $background;
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($background, 0.2) 0,
          rgba($background, 0.3) 40%,
          rgba($background, 0.4) 50%,
          rgba($background, 0.5) 60%,
          rgba($black, 0) 70%
        );
      }
    }
  }
}
