@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Lookup - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Lookup /</span> Tambah Lookup
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah Lookup</h5>
          <a href="{{ route('master.lookups.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          @if($errors->any())
            <div class="alert alert-danger alert-dismissible" role="alert">
              <h6 class="alert-heading d-flex align-items-center">
                <i class="ri-error-warning-line me-2"></i>
                Terdapat <PERSON> Input
              </h6>
              <ul class="mb-0">
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          @endif

          @if(session('error'))
            <div class="alert alert-danger alert-dismissible" role="alert">
              <h6 class="alert-heading d-flex align-items-center">
                <i class="ri-error-warning-line me-2"></i>
                Error
              </h6>
              {{ session('error') }}
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          @endif

          <form action="{{ route('master.lookups.store') }}" method="POST" id="lookupForm">
            @csrf
            
            <h6 class="mb-3">Informasi Dasar</h6>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="lookup_code">Kode Lookup <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('lookup_code') is-invalid @enderror" 
                       id="lookup_code" name="lookup_code" value="{{ old('lookup_code') }}" 
                       placeholder="Contoh: STATUS, PRIORITY, TYPE">
                @error('lookup_code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode dapat duplikat untuk grouping data</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="lookup_name">Nama Lookup <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('lookup_name') is-invalid @enderror" 
                       id="lookup_name" name="lookup_name" value="{{ old('lookup_name') }}" 
                       placeholder="Contoh: Aktif, Pending, Selesai">
                @error('lookup_name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="description">Deskripsi</label>
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" name="description" rows="3" 
                        placeholder="Deskripsi detail tentang lookup ini">{{ old('description') }}</textarea>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="category">Kategori</label>
                <input type="text" class="form-control @error('category') is-invalid @enderror" 
                       id="category" name="category" value="{{ old('category') }}" 
                       placeholder="Contoh: System, User, Asset" list="categoryList">
                <datalist id="categoryList">
                  @foreach($categories as $category)
                    <option value="{{ $category }}">
                  @endforeach
                </datalist>
                @error('category')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kategori untuk grouping lookup</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="sort_order">Urutan Tampilan <span class="text-danger">*</span></label>
                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                       id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                       min="0" placeholder="0">
                @error('sort_order')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Urutan untuk sorting (0 = paling atas)</div>
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <hr class="my-4">
            <h6 class="mb-3">Metadata (Opsional)</h6>
            <p class="text-muted mb-3">Tambahkan data tambahan dalam format key-value</p>

            <div id="metadata-container">
              <div class="metadata-item row mb-2">
                <div class="col-md-4">
                  <input type="text" class="form-control" name="metadata_keys[]" placeholder="Key">
                </div>
                <div class="col-md-6">
                  <input type="text" class="form-control" name="metadata_values[]" placeholder="Value">
                </div>
                <div class="col-md-2">
                  <button type="button" class="btn btn-outline-danger w-100 remove-metadata" disabled>
                    <i class="ri-delete-bin-line"></i>
                  </button>
                </div>
              </div>
            </div>

            <button type="button" class="btn btn-outline-primary mb-4" id="add-metadata">
              <i class="ri-add-line me-1"></i>Tambah Metadata
            </button>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.lookups.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Lookup</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips Penggunaan:</h6>
            <ul class="mb-0">
              <li><strong>Kode Lookup:</strong> Dapat duplikat untuk grouping data yang sama</li>
              <li><strong>Nama Lookup:</strong> Nilai yang akan ditampilkan</li>
              <li><strong>Kategori:</strong> Untuk mengelompokkan lookup</li>
              <li><strong>Urutan:</strong> Menentukan urutan tampilan</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Contoh Penggunaan:</h6>
            <ul class="mb-0">
              <li><strong>STATUS:</strong> Aktif, Non-Aktif, Pending</li>
              <li><strong>PRIORITY:</strong> Low, Medium, High, Urgent</li>
              <li><strong>TYPE:</strong> Internal, External, Vendor</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Metadata:</h6>
            <p class="mb-0">Gunakan metadata untuk menyimpan informasi tambahan seperti warna, icon, atau konfigurasi khusus.</p>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Kode Lookup Existing</h6>
        </div>
        <div class="card-body">
          @if(count($codes) > 0)
            <div class="d-flex flex-wrap gap-1">
              @foreach($codes as $code)
                <span class="badge bg-primary cursor-pointer" onclick="document.getElementById('lookup_code').value='{{ $code }}'">
                  {{ $code }}
                </span>
              @endforeach
            </div>
            <div class="form-text mt-2">Klik untuk menggunakan kode yang sudah ada</div>
          @else
            <p class="text-muted mb-0">Belum ada kode lookup</p>
          @endif
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Kategori Existing</h6>
        </div>
        <div class="card-body">
          @if(count($categories) > 0)
            <div class="d-flex flex-wrap gap-1">
              @foreach($categories as $category)
                <span class="badge bg-info cursor-pointer" onclick="document.getElementById('category').value='{{ $category }}'">
                  {{ $category }}
                </span>
              @endforeach
            </div>
            <div class="form-text mt-2">Klik untuk menggunakan kategori yang sudah ada</div>
          @else
            <p class="text-muted mb-0">Belum ada kategori</p>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let metadataIndex = 1;

  // Auto uppercase lookup code with validation
  document.getElementById('lookup_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase();

    // Real-time validation
    const value = this.value.trim();
    const isValid = /^[A-Z0-9_]*$/.test(value);

    if (value && !isValid) {
      this.classList.add('is-invalid');
      let feedback = this.parentNode.querySelector('.invalid-feedback');
      if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        this.parentNode.appendChild(feedback);
      }
      feedback.textContent = 'Hanya boleh mengandung huruf besar, angka, dan underscore.';
    } else {
      this.classList.remove('is-invalid');
      const feedback = this.parentNode.querySelector('.invalid-feedback');
      if (feedback && !feedback.textContent.includes('wajib')) {
        feedback.remove();
      }
    }
  });

  // Real-time validation for lookup name
  document.getElementById('lookup_name').addEventListener('input', function() {
    const value = this.value.trim();

    if (value && value.length < 2) {
      this.classList.add('is-invalid');
      let feedback = this.parentNode.querySelector('.invalid-feedback');
      if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        this.parentNode.appendChild(feedback);
      }
      feedback.textContent = 'Minimal 2 karakter.';
    } else {
      this.classList.remove('is-invalid');
      const feedback = this.parentNode.querySelector('.invalid-feedback');
      if (feedback && !feedback.textContent.includes('wajib')) {
        feedback.remove();
      }
    }
  });

  // Real-time validation for sort order
  document.getElementById('sort_order').addEventListener('input', function() {
    const value = parseInt(this.value);

    if (this.value && (isNaN(value) || value < 0 || value > 9999)) {
      this.classList.add('is-invalid');
      let feedback = this.parentNode.querySelector('.invalid-feedback');
      if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        this.parentNode.appendChild(feedback);
      }
      feedback.textContent = 'Harus antara 0-9999.';
    } else {
      this.classList.remove('is-invalid');
      const feedback = this.parentNode.querySelector('.invalid-feedback');
      if (feedback && !feedback.textContent.includes('wajib')) {
        feedback.remove();
      }
    }
  });

  // Add metadata
  document.getElementById('add-metadata').addEventListener('click', function() {
    const container = document.getElementById('metadata-container');
    const metadataHtml = `
      <div class="metadata-item row mb-2">
        <div class="col-md-4">
          <input type="text" class="form-control" name="metadata_keys[]" placeholder="Key">
        </div>
        <div class="col-md-6">
          <input type="text" class="form-control" name="metadata_values[]" placeholder="Value">
        </div>
        <div class="col-md-2">
          <button type="button" class="btn btn-outline-danger w-100 remove-metadata">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>
      </div>
    `;
    container.insertAdjacentHTML('beforeend', metadataHtml);
    metadataIndex++;
    updateRemoveButtons();
  });

  // Remove metadata
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-metadata')) {
      e.target.closest('.metadata-item').remove();
      updateRemoveButtons();
    }
  });

  function updateRemoveButtons() {
    const metadataItems = document.querySelectorAll('.metadata-item');
    metadataItems.forEach((item, index) => {
      const removeBtn = item.querySelector('.remove-metadata');
      removeBtn.disabled = metadataItems.length <= 1;
    });
  }

  // Form validation
  function validateForm() {
    let isValid = true;
    const errors = [];

    // Validate lookup code
    const lookupCode = document.getElementById('lookup_code').value.trim();
    if (!lookupCode) {
      errors.push('Kode lookup wajib diisi.');
      isValid = false;
    } else if (!/^[A-Z0-9_]+$/.test(lookupCode)) {
      errors.push('Kode lookup hanya boleh mengandung huruf besar, angka, dan underscore.');
      isValid = false;
    }

    // Validate lookup name
    const lookupName = document.getElementById('lookup_name').value.trim();
    if (!lookupName) {
      errors.push('Nama lookup wajib diisi.');
      isValid = false;
    } else if (lookupName.length < 2) {
      errors.push('Nama lookup minimal 2 karakter.');
      isValid = false;
    }

    // Validate sort order
    const sortOrder = document.getElementById('sort_order').value;
    if (!sortOrder || sortOrder < 0 || sortOrder > 9999) {
      errors.push('Urutan tampilan harus antara 0-9999.');
      isValid = false;
    }

    // Validate metadata keys
    const metadataKeys = document.querySelectorAll('input[name="metadata_keys[]"]');
    const metadataValues = document.querySelectorAll('input[name="metadata_values[]"]');

    metadataKeys.forEach((keyInput, index) => {
      const key = keyInput.value.trim();
      const value = metadataValues[index].value.trim();

      if (key && !value) {
        errors.push(`Metadata key '${key}' harus memiliki value.`);
        isValid = false;
      } else if (!key && value) {
        errors.push(`Metadata value '${value}' harus memiliki key.`);
        isValid = false;
      } else if (key && key.length > 100) {
        errors.push(`Metadata key '${key}' terlalu panjang (maksimal 100 karakter).`);
        isValid = false;
      }
    });

    if (!isValid) {
      showValidationErrors(errors);
    }

    return isValid;
  }

  function showValidationErrors(errors) {
    // Remove existing error alert
    const existingAlert = document.querySelector('.validation-error-alert');
    if (existingAlert) {
      existingAlert.remove();
    }

    // Create new error alert
    const alertHtml = `
      <div class="alert alert-danger alert-dismissible validation-error-alert" role="alert">
        <h6 class="alert-heading d-flex align-items-center">
          <i class="ri-error-warning-line me-2"></i>
          Terdapat Kesalahan Input
        </h6>
        <ul class="mb-0">
          ${errors.map(error => `<li>${error}</li>`).join('')}
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;

    // Insert alert at the top of card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertAdjacentHTML('afterbegin', alertHtml);

    // Scroll to top
    cardBody.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  // Form submission - process metadata
  document.getElementById('lookupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Validate form first
    if (!validateForm()) {
      return false;
    }

    // Remove existing metadata inputs
    const existingMetadataInputs = this.querySelectorAll('input[name^="metadata["]');
    existingMetadataInputs.forEach(input => input.remove());

    const keys = document.querySelectorAll('input[name="metadata_keys[]"]');
    const values = document.querySelectorAll('input[name="metadata_values[]"]');

    // Create individual metadata inputs
    keys.forEach((key, index) => {
      if (key.value.trim() && values[index].value.trim()) {
        const metadataInput = document.createElement('input');
        metadataInput.type = 'hidden';
        metadataInput.name = `metadata[${key.value.trim()}]`;
        metadataInput.value = values[index].value.trim();
        this.appendChild(metadataInput);
      }
    });

    // Submit form
    this.submit();
  });

  // Initialize
  updateRemoveButtons();
});
</script>

<style>
.cursor-pointer {
  cursor: pointer;
}
</style>
@endsection
