@extends('layouts.contentNavbarLayout')

@section('title', 'Buat Permintaan Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Transaksi /</span> Buat Permintaan Asset
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Permintaan Asset</h5>
          <a href="{{ route('request-assets.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          @if($errors->any())
            <div class="alert alert-danger alert-dismissible" role="alert">
              <h6 class="alert-heading d-flex align-items-center">
                <i class="ri-error-warning-line me-2"></i>
                Terdapat Ke<PERSON>ahan Input
              </h6>
              <ul class="mb-0">
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          @endif

          @if(session('error'))
            <div class="alert alert-danger alert-dismissible" role="alert">
              <h6 class="alert-heading d-flex align-items-center">
                <i class="ri-error-warning-line me-2"></i>
                Error
              </h6>
              {{ session('error') }}
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          @endif

          <form action="{{ route('request-assets.store') }}" method="POST" id="requestForm">
            @csrf
            
            <h6 class="mb-3">Informasi Permintaan</h6>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label">Nomor Permintaan</label>
                <input type="text" class="form-control" value="[Auto Generate]" readonly>
                <div class="form-text">Nomor akan dibuat otomatis saat menyimpan</div>
              </div>
              <div class="col-md-6">
                <label class="form-label">Tanggal Permohonan</label>
                <input type="text" class="form-control" value="{{ date('d/m/Y') }}" readonly>
                <div class="form-text">Terisi otomatis tanggal hari ini</div>
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6"></div>
              <div class="col-md-6">
                <label class="form-label" for="needed_date">Tanggal Dibutuhkan <span class="text-danger">*</span></label>
                <input type="date" class="form-control @error('needed_date') is-invalid @enderror" 
                       id="needed_date" name="needed_date" value="{{ old('needed_date') }}" 
                       min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                @error('needed_date')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="request_category">Kategori Permintaan <span class="text-danger">*</span></label>
                <select class="form-select @error('request_category') is-invalid @enderror" 
                        id="request_category" name="request_category">
                  <option value="">Pilih Kategori</option>
                  @foreach($categories as $category)
                    <option value="{{ $category->lookup_name }}" {{ old('request_category') === $category->lookup_name ? 'selected' : '' }}>
                      {{ $category->lookup_name }}
                    </option>
                  @endforeach
                </select>
                @error('request_category')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="item_type">Item Permintaan <span class="text-danger">*</span></label>
                <select class="form-select @error('item_type') is-invalid @enderror" 
                        id="item_type" name="item_type">
                  <option value="">Pilih Item</option>
                  @foreach($itemTypes as $itemType)
                    <option value="{{ $itemType->lookup_name }}" {{ old('item_type') === $itemType->lookup_name ? 'selected' : '' }}>
                      {{ $itemType->lookup_name }}
                    </option>
                  @endforeach
                </select>
                @error('item_type')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <h6 class="mb-3 mt-4">Informasi Pemohon</h6>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label">Diminta Oleh</label>
                <input type="text" class="form-control" value="{{ auth()->user()->name }}" readonly>
                <div class="form-text">Terisi otomatis dengan nama user</div>
              </div>
              <div class="col-md-6">
                <label class="form-label" for="position">Jabatan <span class="text-danger">*</span></label>
                <select class="form-select @error('position') is-invalid @enderror" 
                        id="position" name="position">
                  <option value="">Pilih Jabatan</option>
                  @foreach($positions as $position)
                    <option value="{{ $position->lookup_name }}" {{ old('position') === $position->lookup_name ? 'selected' : '' }}>
                      {{ $position->lookup_name }}
                    </option>
                  @endforeach
                </select>
                @error('position')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="division_id">Divisi <span class="text-danger">*</span></label>
                <select class="form-select @error('division_id') is-invalid @enderror" 
                        id="division_id" name="division_id">
                  <option value="">Pilih Divisi</option>
                  @foreach($divisions as $division)
                    <option value="{{ $division->id }}" {{ old('division_id') == $division->id ? 'selected' : '' }}>
                      {{ $division->name }}
                    </option>
                  @endforeach
                </select>
                @error('division_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="estimated_total">Estimasi Total Biaya</label>
                <input type="number" class="form-control @error('estimated_total') is-invalid @enderror" 
                       id="estimated_total" name="estimated_total" value="{{ old('estimated_total') }}" 
                       min="0" step="0.01" placeholder="0">
                @error('estimated_total')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Opsional - dalam Rupiah</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="purpose">Tujuan Permintaan <span class="text-danger">*</span></label>
              <textarea class="form-control @error('purpose') is-invalid @enderror" 
                        id="purpose" name="purpose" rows="3" 
                        placeholder="Jelaskan tujuan dan alasan permintaan asset ini">{{ old('purpose') }}</textarea>
              @error('purpose')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <hr class="my-4">
            <h6 class="mb-3">Detail Item Permintaan</h6>
            <p class="text-muted mb-3">Tambahkan item-item yang diperlukan</p>

            <div id="items-container">
              @if(old('items'))
                @foreach(old('items') as $index => $item)
                <div class="item-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Item {{ $index + 1 }}</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-item" {{ $index === 0 ? 'disabled' : '' }}>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                  
                  <div class="row">
                    <div class="col-md-5">
                      <label class="form-label">Nama Barang/Jasa <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="items[{{ $index }}][name]" 
                             value="{{ $item['name'] ?? '' }}" placeholder="Contoh: Laptop Dell Inspiron">
                    </div>
                    <div class="col-md-2">
                      <label class="form-label">Jumlah <span class="text-danger">*</span></label>
                      <input type="number" class="form-control" name="items[{{ $index }}][quantity]" 
                             value="{{ $item['quantity'] ?? '' }}" min="1" placeholder="1">
                    </div>
                    <div class="col-md-5">
                      <label class="form-label">Keterangan</label>
                      <input type="text" class="form-control" name="items[{{ $index }}][description]" 
                             value="{{ $item['description'] ?? '' }}" placeholder="Spesifikasi atau keterangan tambahan">
                    </div>
                  </div>
                </div>
                @endforeach
              @else
                <div class="item-row border rounded p-3 mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Item 1</h6>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-item" disabled>
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                  
                  <div class="row">
                    <div class="col-md-5">
                      <label class="form-label">Nama Barang/Jasa <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" name="items[0][name]" 
                             placeholder="Contoh: Laptop Dell Inspiron">
                    </div>
                    <div class="col-md-2">
                      <label class="form-label">Jumlah <span class="text-danger">*</span></label>
                      <input type="number" class="form-control" name="items[0][quantity]" 
                             min="1" placeholder="1">
                    </div>
                    <div class="col-md-5">
                      <label class="form-label">Keterangan</label>
                      <input type="text" class="form-control" name="items[0][description]" 
                             placeholder="Spesifikasi atau keterangan tambahan">
                    </div>
                  </div>
                </div>
              @endif
            </div>

            <button type="button" class="btn btn-outline-primary mb-4" id="add-item">
              <i class="ri-add-line me-1"></i>Tambah Item
            </button>

            <div class="mb-3">
              <label class="form-label" for="notes">Catatan Tambahan</label>
              <textarea class="form-control @error('notes') is-invalid @enderror" 
                        id="notes" name="notes" rows="3" 
                        placeholder="Catatan atau informasi tambahan lainnya">{{ old('notes') }}</textarea>
              @error('notes')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('request-assets.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan Permintaan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Permintaan</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Informasi Penting:</h6>
            <ul class="mb-0">
              <li><strong>Tanggal Dibutuhkan:</strong> Minimal H+1 dari hari ini</li>
              <li><strong>Kategori:</strong> Pilih sesuai jenis permintaan</li>
              <li><strong>Item:</strong> Minimal 1 item harus diisi</li>
              <li><strong>Tujuan:</strong> Jelaskan dengan detail</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Proses Persetujuan:</h6>
            <ol class="mb-0">
              <li>Permintaan dibuat (Draft)</li>
              <li>Diajukan untuk review</li>
              <li>Review oleh IT Holding</li>
              <li>Persetujuan/Penolakan</li>
              <li>Eksekusi jika disetujui</li>
            </ol>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Format Nomor Dokumen:</h6>
            <p class="mb-2"><code>COMPANY-BRANCH-REQ-YYYYMM-NNNN</code></p>
            <small class="text-muted">
              Contoh: BITJKT12-511-REQ-202506-0001
              <br>• Nomor otomatis per cabang per bulan
              <br>• Sesuai konfigurasi master dokumen
            </small>
          </div>

          <div class="alert alert-info">
            <h6 class="alert-heading">Tips:</h6>
            <ul class="mb-0">
              <li>Isi spesifikasi dengan detail</li>
              <li>Sertakan estimasi biaya jika ada</li>
              <li>Jelaskan urgensi dengan jelas</li>
              <li>Lampirkan dokumen pendukung</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let itemIndex = {{ old('items') ? count(old('items')) : 1 }};

  // Load document number preview
  loadDocumentNumberPreview();

  // Add item
  document.getElementById('add-item').addEventListener('click', function() {
    const container = document.getElementById('items-container');
    const itemHtml = `
      <div class="item-row border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Item ${itemIndex + 1}</h6>
          <button type="button" class="btn btn-outline-danger btn-sm remove-item">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>
        
        <div class="row">
          <div class="col-md-5">
            <label class="form-label">Nama Barang/Jasa <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="items[${itemIndex}][name]" 
                   placeholder="Contoh: Laptop Dell Inspiron">
          </div>
          <div class="col-md-2">
            <label class="form-label">Jumlah <span class="text-danger">*</span></label>
            <input type="number" class="form-control" name="items[${itemIndex}][quantity]" 
                   min="1" placeholder="1">
          </div>
          <div class="col-md-5">
            <label class="form-label">Keterangan</label>
            <input type="text" class="form-control" name="items[${itemIndex}][description]" 
                   placeholder="Spesifikasi atau keterangan tambahan">
          </div>
        </div>
      </div>
    `;
    container.insertAdjacentHTML('beforeend', itemHtml);
    itemIndex++;
    updateRemoveButtons();
    updateItemNumbers();
  });

  // Remove item
  document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-item')) {
      e.target.closest('.item-row').remove();
      updateRemoveButtons();
      updateItemNumbers();
    }
  });

  function updateRemoveButtons() {
    const items = document.querySelectorAll('.item-row');
    items.forEach((item, index) => {
      const removeBtn = item.querySelector('.remove-item');
      removeBtn.disabled = items.length <= 1;
    });
  }

  function updateItemNumbers() {
    const items = document.querySelectorAll('.item-row');
    items.forEach((item, index) => {
      item.querySelector('h6').textContent = `Item ${index + 1}`;
    });
  }

  // Form validation
  document.getElementById('requestForm').addEventListener('submit', function(e) {
    const items = document.querySelectorAll('.item-row');
    let hasValidItem = false;

    items.forEach(item => {
      const name = item.querySelector('input[name*="[name]"]').value.trim();
      const quantity = item.querySelector('input[name*="[quantity]"]').value.trim();
      
      if (name && quantity && parseInt(quantity) > 0) {
        hasValidItem = true;
      }
    });

    if (!hasValidItem) {
      e.preventDefault();
      alert('Minimal satu item dengan nama dan jumlah yang valid harus diisi.');
      return false;
    }
  });

  // Load document number preview
  function loadDocumentNumberPreview() {
    fetch('{{ route("request-assets.preview-document-number") }}')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const previewInput = document.querySelector('input[value="[Auto Generate]"]');
          if (previewInput) {
            previewInput.value = data.preview + ' (Preview)';
            previewInput.classList.add('text-primary');
          }
        }
      })
      .catch(error => {
        console.log('Preview nomor dokumen tidak dapat dimuat');
      });
  }

  // Initialize
  updateRemoveButtons();
});
</script>
@endsection
