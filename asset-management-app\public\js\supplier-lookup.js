/**
 * Supplier Lookup Component
 * Provides popup functionality to search and select suppliers
 */

class SupplierLookup {
    constructor(options = {}) {
        this.options = {
            inputId: options.inputId || 'supplier_id',
            displayId: options.displayId || 'supplier_display',
            buttonId: options.buttonId || 'supplier_lookup_btn',
            modalId: options.modalId || 'supplierLookupModal',
            apiUrl: options.apiUrl || '/api/suppliers/lookup',
            onSelect: options.onSelect || null,
            ...options
        };

        this.currentPage = 1;
        this.searchTerm = '';
        this.selectedSupplier = null;

        this.init();
    }

    init() {
        this.createModal();
        this.bindEvents();
        this.loadInitialData();
    }

    createModal() {
        const modalHtml = `
            <div class="modal fade" id="${this.options.modalId}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="ri-building-line me-2"></i>
                                Pilih Supplier
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Search -->
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="ri-search-line"></i>
                                    </span>
                                    <input type="text" class="form-control" id="supplier_search" 
                                           placeholder="Cari berdasarkan kode, nama, atau perusahaan...">
                                </div>
                            </div>

                            <!-- Loading -->
                            <div id="supplier_loading" class="text-center py-4" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-2">Memuat data supplier...</div>
                            </div>

                            <!-- Results -->
                            <div id="supplier_results">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="120">Kode</th>
                                                <th>Nama Supplier</th>
                                                <th>Perusahaan</th>
                                                <th>Lokasi</th>
                                                <th>Kontak</th>
                                                <th width="80">Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody id="supplier_table_body">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div id="supplier_pagination" class="d-flex justify-content-between align-items-center mt-3">
                                    <div id="supplier_info"></div>
                                    <div id="supplier_pagination_buttons"></div>
                                </div>
                            </div>

                            <!-- No Results -->
                            <div id="supplier_no_results" class="text-center py-4" style="display: none;">
                                <div class="avatar avatar-xl mx-auto mb-3">
                                    <span class="avatar-initial rounded-circle bg-light">
                                        <i class="ri-building-line ri-36px text-muted"></i>
                                    </span>
                                </div>
                                <h6 class="text-muted">Tidak ada supplier ditemukan</h6>
                                <p class="text-muted">Coba ubah kata kunci pencarian Anda</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="ri-close-line me-1"></i>
                                Tutup
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById(this.options.modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    bindEvents() {
        // Lookup button click
        const lookupBtn = document.getElementById(this.options.buttonId);
        if (lookupBtn) {
            lookupBtn.addEventListener('click', () => {
                this.openModal();
            });
        }

        // Search input
        const searchInput = document.getElementById('supplier_search');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchTerm = e.target.value;
                    this.currentPage = 1;
                    this.loadSuppliers();
                }, 300);
            });
        }

        // Clear selection button
        const clearBtn = document.getElementById(this.options.buttonId.replace('_btn', '_clear'));
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearSelection();
            });
        }
    }

    openModal() {
        const modal = new bootstrap.Modal(document.getElementById(this.options.modalId));
        modal.show();
        this.loadSuppliers();
    }

    async loadSuppliers() {
        this.showLoading();

        try {
            const params = new URLSearchParams({
                search: this.searchTerm,
                page: this.currentPage,
                per_page: 10
            });

            const response = await fetch(`${this.options.apiUrl}?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderSuppliers(data.data);
                this.renderPagination(data.pagination);
            } else {
                this.showError('Gagal memuat data supplier');
            }
        } catch (error) {
            console.error('Error loading suppliers:', error);
            this.showError('Terjadi kesalahan saat memuat data');
        } finally {
            this.hideLoading();
        }
    }

    renderSuppliers(suppliers) {
        const tbody = document.getElementById('supplier_table_body');
        const noResults = document.getElementById('supplier_no_results');
        const results = document.getElementById('supplier_results');

        if (suppliers.length === 0) {
            results.style.display = 'none';
            noResults.style.display = 'block';
            return;
        }

        results.style.display = 'block';
        noResults.style.display = 'none';

        tbody.innerHTML = suppliers.map(supplier => `
            <tr class="supplier-row" style="cursor: pointer;"
                ondblclick="supplierLookup.selectSupplier(${supplier.id}, '${supplier.supplier_code}', '${supplier.name}')"
                onmouseover="this.style.backgroundColor='#f8f9fa'"
                onmouseout="this.style.backgroundColor=''">
                <td>
                    <strong class="text-primary">${supplier.supplier_code}</strong>
                </td>
                <td>
                    <strong>${supplier.name}</strong>
                </td>
                <td>
                    ${supplier.company_name || '<span class="text-muted">-</span>'}
                </td>
                <td>
                    ${supplier.city || supplier.province ?
                        `${supplier.city || ''}${supplier.city && supplier.province ? ', ' : ''}${supplier.province || ''}` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>
                    <div>
                        ${supplier.phone ? `<div><i class="ri-phone-line me-1"></i>${supplier.phone}</div>` : ''}
                        ${supplier.email ? `<div><i class="ri-mail-line me-1"></i>${supplier.email}</div>` : ''}
                        ${!supplier.phone && !supplier.email ? '<span class="text-muted">-</span>' : ''}
                    </div>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary"
                            onclick="supplierLookup.selectSupplier(${supplier.id}, '${supplier.supplier_code}', '${supplier.name}')">
                        <i class="ri-check-line me-1"></i>
                        Pilih
                    </button>
                </td>
            </tr>
        `).join('');
    }

    renderPagination(pagination) {
        const info = document.getElementById('supplier_info');
        const buttons = document.getElementById('supplier_pagination_buttons');

        // Info
        info.innerHTML = `
            <small class="text-muted">
                Menampilkan ${((pagination.current_page - 1) * pagination.per_page) + 1} - 
                ${Math.min(pagination.current_page * pagination.per_page, pagination.total)} 
                dari ${pagination.total} supplier
            </small>
        `;

        // Buttons
        let buttonHtml = '';
        
        if (pagination.current_page > 1) {
            buttonHtml += `
                <button type="button" class="btn btn-sm btn-outline-primary me-1" 
                        onclick="supplierLookup.goToPage(${pagination.current_page - 1})">
                    <i class="ri-arrow-left-line"></i>
                </button>
            `;
        }

        if (pagination.has_more) {
            buttonHtml += `
                <button type="button" class="btn btn-sm btn-outline-primary" 
                        onclick="supplierLookup.goToPage(${pagination.current_page + 1})">
                    <i class="ri-arrow-right-line"></i>
                </button>
            `;
        }

        buttons.innerHTML = buttonHtml;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadSuppliers();
    }

    selectSupplier(id, code, name) {
        this.selectedSupplier = { id, code, name };

        // Update hidden input
        const hiddenInput = document.getElementById(this.options.inputId);
        if (hiddenInput) {
            hiddenInput.value = id;
        }

        // Update display
        const displayElement = document.getElementById(this.options.displayId);
        if (displayElement) {
            displayElement.value = `${code} - ${name}`;
        }

        // Show clear button
        const clearBtn = document.getElementById(this.options.buttonId.replace('_btn', '_clear'));
        if (clearBtn) {
            clearBtn.style.display = 'inline-block';
        }

        // Call callback if provided
        if (this.options.onSelect) {
            this.options.onSelect(this.selectedSupplier);
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById(this.options.modalId));
        if (modal) {
            modal.hide();
        }
    }

    clearSelection() {
        this.selectedSupplier = null;

        // Clear hidden input
        const hiddenInput = document.getElementById(this.options.inputId);
        if (hiddenInput) {
            hiddenInput.value = '';
        }

        // Clear display
        const displayElement = document.getElementById(this.options.displayId);
        if (displayElement) {
            displayElement.value = '';
        }

        // Hide clear button
        const clearBtn = document.getElementById(this.options.buttonId.replace('_btn', '_clear'));
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }
    }

    loadInitialData() {
        // Load existing supplier if ID is set
        const hiddenInput = document.getElementById(this.options.inputId);
        if (hiddenInput && hiddenInput.value) {
            this.loadSupplierById(hiddenInput.value);
        }
    }

    async loadSupplierById(id) {
        try {
            const response = await fetch(`${this.options.apiUrl}/${id}`);
            const data = await response.json();

            if (data.success) {
                const supplier = data.data;
                this.selectSupplier(supplier.id, supplier.supplier_code, supplier.name);
            }
        } catch (error) {
            console.error('Error loading supplier by ID:', error);
        }
    }

    showLoading() {
        document.getElementById('supplier_loading').style.display = 'block';
        document.getElementById('supplier_results').style.display = 'none';
        document.getElementById('supplier_no_results').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('supplier_loading').style.display = 'none';
    }

    showError(message) {
        // You can implement error display here
        console.error(message);
    }
}

// Global instance
let supplierLookup;
