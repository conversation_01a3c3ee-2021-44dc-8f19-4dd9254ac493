@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Cabang - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Cabang /</span> Tambah Cabang
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah Cabang</h5>
          <a href="{{ route('master.branches.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.branches.store') }}" method="POST">
            @csrf
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name"><PERSON><PERSON> <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" 
                       placeholder="Contoh: Kantor Pusat Jakarta">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Cabang <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                       id="code" name="code" value="{{ old('code') }}" 
                       placeholder="Contoh: HQ, JKT, BDG" maxlength="10">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode unik untuk cabang (maksimal 10 karakter)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="address">Alamat</label>
              <textarea class="form-control @error('address') is-invalid @enderror" 
                        id="address" name="address" rows="3" 
                        placeholder="Alamat lengkap cabang">{{ old('address') }}</textarea>
              @error('address')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="phone">Nomor Telepon</label>
                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                       id="phone" name="phone" value="{{ old('phone') }}" 
                       placeholder="Contoh: 021-12345678">
                @error('phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="email">Email</label>
                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email') }}" 
                       placeholder="Contoh: <EMAIL>">
                @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.branches.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Pengisian</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips:</h6>
            <ul class="mb-0">
              <li><strong>Nama Cabang:</strong> Gunakan nama yang jelas dan mudah diidentifikasi</li>
              <li><strong>Kode Cabang:</strong> Gunakan kode singkat dan unik (contoh: HQ, JKT, BDG)</li>
              <li><strong>Alamat:</strong> Isi alamat lengkap untuk memudahkan identifikasi</li>
              <li><strong>Kontak:</strong> Nomor telepon dan email untuk komunikasi</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Kode cabang harus unik dan tidak boleh sama</li>
              <li>Kode akan digunakan untuk generate nomor dokumen</li>
              <li>Pastikan data yang diisi sudah benar sebelum menyimpan</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase code
  const codeInput = document.getElementById('code');
  codeInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  });

  // Auto generate code from name
  const nameInput = document.getElementById('name');
  nameInput.addEventListener('input', function() {
    if (!codeInput.value) {
      const words = this.value.split(' ');
      let code = '';
      
      if (words.length === 1) {
        code = words[0].substring(0, 3).toUpperCase();
      } else {
        code = words.map(word => word.charAt(0).toUpperCase()).join('').substring(0, 10);
      }
      
      codeInput.value = code.replace(/[^A-Z0-9]/g, '');
    }
  });

  // Format phone number
  const phoneInput = document.getElementById('phone');
  phoneInput.addEventListener('input', function() {
    // Remove non-numeric characters except + and -
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });
});
</script>
@endsection
