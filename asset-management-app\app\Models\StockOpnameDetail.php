<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockOpnameDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'stock_opname_id',
        'asset_id',
        'asset_code',
        'asset_name',
        'expected_status',
        'found_status',
        'physical_condition',
        'condition_notes',
        'location_found',
        'location_match',
        'dynamic_fields_expected',
        'dynamic_fields_found',
        'scanned_by',
        'scanned_at',
        'scanner_notes',
        'has_discrepancy',
        'discrepancy_details'
    ];

    protected $casts = [
        'scanned_at' => 'datetime',
        'location_match' => 'boolean',
        'dynamic_fields_expected' => 'array',
        'dynamic_fields_found' => 'array',
        'has_discrepancy' => 'boolean',
        'discrepancy_details' => 'array'
    ];

    /**
     * Get the stock opname that owns the detail
     */
    public function stockOpname(): BelongsTo
    {
        return $this->belongsTo(StockOpname::class);
    }

    /**
     * Get the asset
     */
    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    /**
     * Get the user who scanned the asset
     */
    public function scanner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'scanned_by');
    }

    /**
     * Mark as scanned
     */
    public function markAsScanned($foundStatus, $condition, $notes = null, $locationFound = null, $dynamicFields = null)
    {
        // Ensure dynamic fields is properly handled
        $dynamicFields = $dynamicFields ?: [];

        $discrepancies = $this->checkDiscrepancies($foundStatus, $condition, $locationFound, $dynamicFields);

        $updateData = [
            'found_status' => $foundStatus,
            'physical_condition' => $condition,
            'condition_notes' => $notes,
            'location_found' => $locationFound,
            'dynamic_fields_found' => $dynamicFields,
            'scanned_by' => auth()->id(),
            'scanned_at' => now(),
            'has_discrepancy' => !empty($discrepancies),
            'discrepancy_details' => $discrepancies
        ];

        // Check if location matches expected location
        if ($locationFound && isset($this->dynamic_fields_expected['location'])) {
            $updateData['location_match'] = $locationFound === $this->dynamic_fields_expected['location'];
        }

        $this->update($updateData);

        // Update stock opname statistics
        $this->stockOpname->updateStatistics();

        return $this;
    }

    /**
     * Check for discrepancies
     */
    public function checkDiscrepancies($foundStatus, $condition, $locationFound, $dynamicFields)
    {
        $discrepancies = [];

        // Ensure dynamic fields is an array
        $dynamicFields = $dynamicFields ?: [];

        // Check status discrepancy
        if ($foundStatus === 'found' && $this->expected_status !== $this->asset->status) {
            $discrepancies['status'] = [
                'expected' => $this->expected_status,
                'found' => $this->asset->status,
                'type' => 'status_mismatch'
            ];
        }

        // Check if asset is missing
        if ($foundStatus === 'not_found') {
            $discrepancies['missing'] = [
                'type' => 'asset_missing',
                'message' => 'Asset tidak ditemukan saat stock opname'
            ];
        }

        // Check condition discrepancy (if asset was expected to be in good condition but found damaged)
        if ($foundStatus === 'found' && in_array($condition, ['poor', 'damaged'])) {
            $discrepancies['condition'] = [
                'type' => 'poor_condition',
                'condition' => $condition,
                'message' => 'Asset ditemukan dalam kondisi tidak baik'
            ];
        }

        // Check dynamic fields discrepancies
        if (!empty($dynamicFields) && !empty($this->dynamic_fields_expected)) {
            foreach ($this->dynamic_fields_expected as $field => $expectedValue) {
                $foundValue = isset($dynamicFields[$field]) ? $dynamicFields[$field] : null;
                if ($expectedValue !== $foundValue) {
                    if (!isset($discrepancies['dynamic_fields'])) {
                        $discrepancies['dynamic_fields'] = [];
                    }
                    $discrepancies['dynamic_fields'][$field] = [
                        'expected' => $expectedValue,
                        'found' => $foundValue,
                        'type' => 'field_mismatch'
                    ];
                }
            }
        }

        // Check location discrepancy
        if ($locationFound && isset($this->dynamic_fields_expected['location'])) {
            $expectedLocation = $this->dynamic_fields_expected['location'];
            if ($expectedLocation !== $locationFound) {
                if (!isset($discrepancies['dynamic_fields'])) {
                    $discrepancies['dynamic_fields'] = [];
                }
                $discrepancies['dynamic_fields']['location'] = [
                    'expected' => $expectedLocation,
                    'found' => $locationFound,
                    'type' => 'location_mismatch'
                ];
            }
        }

        return $discrepancies;
    }

    /**
     * Get discrepancy summary
     */
    public function getDiscrepancySummary()
    {
        if (!$this->has_discrepancy || !$this->discrepancy_details) {
            return null;
        }

        $summary = [];

        foreach ($this->discrepancy_details as $type => $details) {
            switch ($type) {
                case 'status':
                    $summary[] = "Status tidak sesuai: {$details['expected']} → {$details['found']}";
                    break;
                case 'missing':
                    $summary[] = "Asset hilang/tidak ditemukan";
                    break;
                case 'condition':
                    $summary[] = "Kondisi buruk: {$details['condition']}";
                    break;
                case 'dynamic_fields':
                    foreach ($details as $field => $fieldDetails) {
                        $summary[] = "Field {$field}: {$fieldDetails['expected']} → {$fieldDetails['found']}";
                    }
                    break;
            }
        }

        return implode(', ', $summary);
    }

    /**
     * Check if asset is scanned
     */
    public function isScanned(): bool
    {
        return !is_null($this->scanned_at);
    }

    /**
     * Check if asset is found
     */
    public function isFound(): bool
    {
        return $this->found_status === 'found';
    }

    /**
     * Check if asset is missing
     */
    public function isMissing(): bool
    {
        return $this->found_status === 'not_found';
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass(): string
    {
        switch ($this->found_status) {
            case 'found':
                return 'bg-success';
            case 'not_found':
                return 'bg-danger';
            case 'damaged':
                return 'bg-warning';
            default:
                return 'bg-secondary';
        }
    }

    /**
     * Get condition badge class
     */
    public function getConditionBadgeClass(): string
    {
        switch ($this->physical_condition) {
            case 'excellent':
                return 'bg-success';
            case 'good':
                return 'bg-info';
            case 'fair':
                return 'bg-warning';
            case 'poor':
            case 'damaged':
                return 'bg-danger';
            default:
                return 'bg-secondary';
        }
    }
}
