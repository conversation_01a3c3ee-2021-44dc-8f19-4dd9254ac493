<?php
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
$containerNav = $containerNav ?? 'container-fluid';
$navbarDetached = ($navbarDetached ?? '');

?>

<style>
/* Notification dropdown styles */
.notification-dropdown {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.notification-item {
  transition: background-color 0.2s ease;
  border-left: 3px solid transparent;
}

.notification-item:hover {
  background-color: rgba(105, 108, 255, 0.04);
  border-left-color: #696cff;
}

.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.notification-icon {
  transition: color 0.2s ease;
}

.notification-icon:hover {
  color: #696cff !important;
}

/* Compact scrollbar */
.notification-dropdown::-webkit-scrollbar {
  width: 4px;
}

.notification-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notification-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.notification-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>

<script>
// Auto refresh notification count every 30 seconds
document.addEventListener('DOMContentLoaded', function() {
    function refreshNotificationCount() {
        fetch('<?php echo e(route("notifications.unread-count")); ?>')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('.notification-badge');
                const icon = document.querySelector('.notification-icon');

                if (data.count > 0) {
                    if (!badge) {
                        // Create badge if it doesn't exist
                        const newBadge = document.createElement('span');
                        newBadge.className = 'position-absolute badge rounded-pill bg-danger notification-badge';
                        newBadge.style.cssText = 'top: -2px; right: -6px; font-size: 10px; min-width: 16px; height: 16px; line-height: 14px;';
                        newBadge.innerHTML = `${data.count > 9 ? '9+' : data.count}<span class="visually-hidden">unread notifications</span>`;
                        icon.parentElement.appendChild(newBadge);
                    } else {
                        // Update existing badge
                        badge.innerHTML = `${data.count > 9 ? '9+' : data.count}<span class="visually-hidden">unread notifications</span>`;
                    }
                } else {
                    // Remove badge if no notifications
                    if (badge) {
                        badge.remove();
                    }
                }
            })
            .catch(error => console.log('Error refreshing notifications:', error));
    }

    // Refresh every 30 seconds
    setInterval(refreshNotificationCount, 30000);

    // Refresh when page becomes visible again
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            refreshNotificationCount();
        }
    });
});
</script>

<!-- Navbar -->
<?php if(isset($navbarDetached) && $navbarDetached == 'navbar-detached'): ?>
<nav class="layout-navbar <?php echo e($containerNav); ?> navbar navbar-expand-xl <?php echo e($navbarDetached); ?> align-items-center bg-navbar-theme" id="layout-navbar">
  <?php endif; ?>
  <?php if(isset($navbarDetached) && $navbarDetached == ''): ?>
  <nav class="layout-navbar navbar navbar-expand-xl align-items-center bg-navbar-theme" id="layout-navbar">
    <div class="<?php echo e($containerNav); ?>">
      <?php endif; ?>

      <!--  Brand demo (display only for navbar-full and hide on below xl) -->
      <?php if(isset($navbarFull)): ?>
      <div class="navbar-brand app-brand demo d-none d-xl-flex py-0 me-6">
        <a href="<?php echo e(url('/')); ?>" class="app-brand-link gap-2">
          <span class="app-brand-logo demo"><?php echo $__env->make('_partials.macros',["height"=>60], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
          <span class="app-brand-text demo menu-text fw-semibold ms-1"><?php echo e(config('variables.templateName')); ?></span>
        </a>
        <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-xl-none">
          <i class="ri-close-fill align-middle"></i>
        </a>
      </div>
      <?php endif; ?>

      <!-- ! Not required for layout-without-menu -->
      <?php if(!isset($navbarHideToggle)): ?>
      <div class="layout-menu-toggle navbar-nav align-items-xl-center me-4 me-xl-0<?php echo e(isset($menuHorizontal) ? ' d-xl-none ' : ''); ?> <?php echo e(isset($contentNavbar) ?' d-xl-none ' : ''); ?>">
        <a class="nav-item nav-link px-0 me-xl-6" href="javascript:void(0)">
          <i class="ri-menu-fill ri-24px"></i>
        </a>
      </div>
      <?php endif; ?>

      <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
        <!-- Stock Opname Status -->
        <?php
          $userBranchLock = \App\Models\StockOpname::getUserBranchLockStatus();
        ?>
        <?php if($userBranchLock): ?>
          <div class="navbar-nav align-items-center me-3">
            <div class="nav-item">
              <a href="<?php echo e(route('stock-opnames.show', $userBranchLock)); ?>"
                 class="nav-link d-flex align-items-center text-warning"
                 title="Stock Opname Aktif - <?php echo e($userBranchLock->title); ?>">
                <i class="ri-lock-line me-1"></i>
                <span class="d-none d-sm-inline">Stock Opname</span>
              </a>
            </div>
          </div>
        <?php endif; ?>

        <!-- Search -->
        <div class="navbar-nav align-items-center">
          <div class="nav-item d-flex align-items-center">
            <i class="ri-search-line ri-22px me-1_5"></i>
            <input type="text" class="form-control border-0 shadow-none ps-1 ps-sm-2 ms-50" placeholder="Search..." aria-label="Search...">
          </div>
        </div>
        <!-- /Search -->
        <ul class="navbar-nav flex-row align-items-center ms-auto">

          <!-- Notifications -->
          <li class="nav-item navbar-dropdown dropdown-user dropdown me-2">
            <a class="nav-link dropdown-toggle hide-arrow p-2" href="javascript:void(0);" data-bs-toggle="dropdown">
              <div class="position-relative">
                <i class="ri-notification-2-line ri-20px text-muted notification-icon"></i>
                <?php
                  $notificationService = app(\App\Services\NotificationService::class);
                  $unreadCount = $notificationService->getUnreadCount(auth()->id());
                ?>
                <?php if($unreadCount > 0): ?>
                  <span class="position-absolute badge rounded-pill bg-danger notification-badge" style="top: -2px; right: -6px; font-size: 10px; min-width: 16px; height: 16px; line-height: 14px;">
                    <?php echo e($unreadCount > 9 ? '9+' : $unreadCount); ?>

                    <span class="visually-hidden">unread notifications</span>
                  </span>
                <?php endif; ?>
              </div>
            </a>
            <ul class="dropdown-menu dropdown-menu-end mt-3 py-0 notification-dropdown" style="width: 320px; max-height: 400px; overflow-y: auto;">
              <!-- Header -->
              <li class="dropdown-header px-3 py-2 border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0 fw-semibold">Notifikasi</h6>
                  <?php if($unreadCount > 0): ?>
                    <a href="<?php echo e(route('notifications.mark-all-read')); ?>" class="btn btn-xs btn-outline-primary" style="font-size: 10px; padding: 2px 8px;">
                      Tandai Dibaca
                    </a>
                  <?php endif; ?>
                </div>
              </li>

              <?php
                $notifications = $notificationService->getUnreadNotifications(auth()->id(), 5);
              ?>

              <?php if($notifications->count() > 0): ?>
                <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <li>
                    <a class="dropdown-item px-3 py-2 notification-item" href="<?php echo e(route('notifications.read', $notification->id)); ?>" style="white-space: normal;">
                      <div class="d-flex align-items-start">
                        <div class="flex-shrink-0 me-2">
                          <div class="avatar avatar-xs">
                            <span class="avatar-initial rounded-circle bg-<?php echo e($notificationService->getNotificationColor($notification->type)); ?>" style="width: 24px; height: 24px; font-size: 10px;">
                              <i class="<?php echo e($notificationService->getNotificationIcon($notification->type)); ?>"></i>
                            </span>
                          </div>
                        </div>
                        <div class="flex-grow-1" style="min-width: 0;">
                          <div class="fw-medium mb-1" style="font-size: 12px; line-height: 1.3;">
                            <?php echo e(Str::limit($notification->title, 25)); ?>

                          </div>
                          <div class="text-muted mb-1" style="font-size: 11px; line-height: 1.2;">
                            <?php echo e(Str::limit($notification->message, 45)); ?>

                          </div>
                          <div class="text-muted" style="font-size: 10px;">
                            <?php echo e($notification->time_ago); ?>

                          </div>
                        </div>
                      </div>
                    </a>
                  </li>
                  <?php if(!$loop->last): ?>
                    <li><hr class="dropdown-divider my-1"></li>
                  <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <!-- Footer -->
                <li class="border-top">
                  <a href="<?php echo e(route('notifications.index')); ?>" class="dropdown-item text-center py-2" style="font-size: 12px;">
                    <i class="ri-eye-line me-1"></i>
                    Lihat Semua Notifikasi
                  </a>
                </li>
              <?php else: ?>
                <li class="text-center py-4">
                  <div class="text-muted" style="font-size: 12px;">
                    <i class="ri-notification-off-line ri-24px d-block mb-2 opacity-50"></i>
                    Tidak ada notifikasi baru
                  </div>
                </li>
              <?php endif; ?>
            </ul>
          </li>

          <!-- User -->
          <li class="nav-item navbar-dropdown dropdown-user dropdown">
            <a class="nav-link dropdown-toggle hide-arrow p-0" href="javascript:void(0);" data-bs-toggle="dropdown">
              <div class="avatar avatar-online">
                <img src="<?php echo e(asset('assets/img/avatars/1.png')); ?>" alt class="w-px-40 h-auto rounded-circle">
              </div>
            </a>
            <ul class="dropdown-menu dropdown-menu-end mt-3 py-2">
            <li>
                <a class="dropdown-item" href="javascript:void(0);">
                  <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-2">
                      <div class="avatar avatar-online">
                        <img src="<?php echo e(asset('assets/img/avatars/1.png')); ?>" alt class="w-px-40 h-auto rounded-circle">
                      </div>
                    </div>
                    <div class="flex-grow-1">
                      <h6 class="mb-0 small"><?php echo e(Auth::user()->name ?? 'User'); ?></h6>
                      <small class="text-muted"><?php echo e(Auth::user()->role->name ?? 'No Role'); ?></small>
                      <?php if(Auth::user()->branch): ?>
                        <br><small class="text-muted"><?php echo e(Auth::user()->branch->name); ?></small>
                      <?php endif; ?>
                    </div>
                  </div>
                </a>
              </li>
              <li>
                <div class="dropdown-divider"></div>
              </li>
              <li>
                <a class="dropdown-item" href="<?php echo e(route('profile.index')); ?>">
                  <i class="ri-user-3-line ri-22px me-2"></i>
                  <span class="align-middle">My Profile</span>
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="javascript:void(0);">
                  <i class='ri-settings-4-line ri-22px me-2'></i>
                  <span class="align-middle">Settings</span>
                </a>
              </li>
              <li>
                <div class="dropdown-divider"></div>
              </li>
              <li>
                <div class="d-grid px-4 pt-2 pb-1">
                  <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="btn btn-danger d-flex w-100 border-0">
                      <small class="align-middle">Logout</small>
                      <i class="ri-logout-box-r-line ms-2 ri-16px"></i>
                    </button>
                  </form>
                </div>
              </li>
            </ul>
          </li>
          <!--/ User -->
        </ul>
      </div>

      <?php if(!isset($navbarDetached)): ?>
    </div>
    <?php endif; ?>
  </nav>
  <!-- / Navbar -->
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/layouts/sections/navbar/navbar.blade.php ENDPATH**/ ?>