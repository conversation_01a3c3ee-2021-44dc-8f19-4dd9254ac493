@extends('layouts/contentNavbarLayout')

@section('title', 'Detail Asset Digital')

@section('content')
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Detail Asset Digital</h5>
        <div class="d-flex gap-2">
          @can('asset-digitals.edit')
          <a href="{{ route('asset-digitals.edit', $assetDigital) }}" class="btn btn-primary">
            <i class="ri-pencil-line me-1"></i>Edit
          </a>
          @endcan
          <a href="{{ route('asset-digitals.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
      </div>
      
      <div class="card-body">
        <div class="row">
          <!-- Basic Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Dasar</h6>
            
            <div class="mb-3">
              <label class="form-label text-muted">Kode Asset</label>
              <div class="fw-bold">{{ $assetDigital->asset_code }}</div>
            </div>

            <div class="mb-3">
              <label class="form-label text-muted">Nama Asset Digital</label>
              <div class="fw-bold">{{ $assetDigital->name }}</div>
            </div>

            <div class="mb-3">
              <label class="form-label text-muted">Tipe Lisensi</label>
              <div>
                <span class="badge bg-label-info">{{ $assetDigital->license_type }}</span>
              </div>
            </div>

            @if($assetDigital->license_key)
            <div class="mb-3">
              <label class="form-label text-muted">License Key</label>
              <div class="d-flex align-items-center">
                <code id="licenseKey" class="me-2">{{ $assetDigital->license_key }}</code>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('licenseKey')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
            </div>
            @endif

            @if($assetDigital->vendor)
            <div class="mb-3">
              <label class="form-label text-muted">Vendor</label>
              <div>{{ $assetDigital->vendor }}</div>
            </div>
            @endif

            @if($assetDigital->version)
            <div class="mb-3">
              <label class="form-label text-muted">Versi</label>
              <div>{{ $assetDigital->version }}</div>
            </div>
            @endif

            @if($assetDigital->description)
            <div class="mb-3">
              <label class="form-label text-muted">Deskripsi</label>
              <div>{{ $assetDigital->description }}</div>
            </div>
            @endif

            <div class="mb-3">
              <label class="form-label text-muted">Status</label>
              <div>
                <span class="badge bg-label-{{ 
                  $assetDigital->status === 'active' ? 'success' : 
                  ($assetDigital->status === 'inactive' ? 'secondary' : 
                  ($assetDigital->status === 'expired' ? 'danger' : 'warning')) 
                }}">
                  {{ ucfirst($assetDigital->status) }}
                </span>
                @if($assetDigital->isExpired())
                  <span class="badge bg-label-danger ms-1">Expired</span>
                @elseif($assetDigital->isExpiringSoon())
                  <span class="badge bg-label-warning ms-1">Expiring Soon</span>
                @endif
              </div>
            </div>
          </div>

          <!-- Login & Access Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Login & Akses</h6>
            
            @if($assetDigital->username)
            <div class="mb-3">
              <label class="form-label text-muted">Username</label>
              <div class="d-flex align-items-center">
                <code id="username" class="me-2">{{ $assetDigital->username }}</code>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('username')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
            </div>
            @endif

            @if($assetDigital->password)
            <div class="mb-3">
              <label class="form-label text-muted">Password</label>
              <div class="d-flex align-items-center">
                <code id="password" class="me-2">{{ $assetDigital->decrypted_password }}</code>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('password')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
            </div>
            @endif

            @if($assetDigital->login_url)
            <div class="mb-3">
              <label class="form-label text-muted">Login URL</label>
              <div class="d-flex align-items-center">
                <a href="{{ $assetDigital->login_url }}" target="_blank" class="me-2">
                  {{ $assetDigital->login_url }}
                </a>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('loginUrl')">
                  <i class="ri-file-copy-line"></i>
                </button>
              </div>
              <div id="loginUrl" class="d-none">{{ $assetDigital->login_url }}</div>
            </div>
            @endif

            @if($assetDigital->max_users)
            <div class="mb-3">
              <label class="form-label text-muted">Usage</label>
              <div>
                <div class="d-flex justify-content-between mb-1">
                  <span>{{ $assetDigital->current_users ?? 0 }} / {{ $assetDigital->max_users }} users</span>
                  <span>{{ $assetDigital->getUsagePercentage() }}%</span>
                </div>
                <div class="progress" style="height: 6px;">
                  <div class="progress-bar" role="progressbar" 
                       style="width: {{ $assetDigital->getUsagePercentage() }}%"
                       aria-valuenow="{{ $assetDigital->getUsagePercentage() }}" 
                       aria-valuemin="0" aria-valuemax="100">
                  </div>
                </div>
              </div>
            </div>
            @endif

            <div class="mb-3">
              <label class="form-label text-muted">Cabang</label>
              <div>{{ $assetDigital->branch->name }} ({{ $assetDigital->branch->code }})</div>
            </div>

            @if($assetDigital->assignedTo)
            <div class="mb-3">
              <label class="form-label text-muted">Assigned To</label>
              <div>
                <strong>{{ $assetDigital->assignedTo->name }}</strong>
                <small class="text-muted d-block">NIK: {{ $assetDigital->assignedTo->nik }}</small>
                @if($assetDigital->assignedTo->email)
                  <small class="text-muted d-block">Email: {{ $assetDigital->assignedTo->email }}</small>
                @endif
              </div>
            </div>
            @endif
          </div>
        </div>

        <hr class="my-4">

        <div class="row">
          <!-- Purchase & Financial Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Pembelian</h6>
            
            @if($assetDigital->purchase_date)
            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Pembelian</label>
              <div>{{ $assetDigital->purchase_date->format('d/m/Y') }}</div>
            </div>
            @endif

            @if($assetDigital->expiry_date)
            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Kadaluarsa</label>
              <div>
                {{ $assetDigital->expiry_date->format('d/m/Y') }}
                @if($assetDigital->getDaysUntilExpiry() !== null)
                  <small class="text-muted d-block">
                    @if($assetDigital->isExpired())
                      Expired {{ abs($assetDigital->getDaysUntilExpiry()) }} days ago
                    @else
                      {{ $assetDigital->getDaysUntilExpiry() }} days remaining
                    @endif
                  </small>
                @endif
              </div>
            </div>
            @endif

            @if($assetDigital->purchase_price)
            <div class="mb-3">
              <label class="form-label text-muted">Harga Pembelian</label>
              <div>Rp {{ number_format($assetDigital->purchase_price, 2, ',', '.') }}</div>
            </div>
            @endif
          </div>

          <!-- Additional Information -->
          <div class="col-md-6">
            <h6 class="text-muted mb-3">Informasi Tambahan</h6>
            
            <div class="mb-3">
              <label class="form-label text-muted">Dibuat Oleh</label>
              <div>{{ $assetDigital->createdBy->name ?? 'System' }}</div>
            </div>

            <div class="mb-3">
              <label class="form-label text-muted">Tanggal Dibuat</label>
              <div>{{ $assetDigital->created_at->format('d/m/Y H:i') }}</div>
            </div>

            @if($assetDigital->updated_at != $assetDigital->created_at)
            <div class="mb-3">
              <label class="form-label text-muted">Terakhir Diperbarui</label>
              <div>{{ $assetDigital->updated_at->format('d/m/Y H:i') }}</div>
            </div>
            @endif

            @if($assetDigital->notes)
            <div class="mb-3">
              <label class="form-label text-muted">Catatan</label>
              <div>{{ $assetDigital->notes }}</div>
            </div>
            @endif
          </div>
        </div>

        @can('asset-digitals.delete')
        <hr class="my-4">
        <div class="d-flex justify-content-end">
          <form action="{{ route('asset-digitals.destroy', $assetDigital) }}" method="POST" 
                onsubmit="return confirm('Apakah Anda yakin ingin menghapus asset digital ini?')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger">
              <i class="ri-delete-bin-line me-1"></i>Hapus Asset Digital
            </button>
          </form>
        </div>
        @endcan
      </div>
    </div>
  </div>
</div>

<script>
function copyToClipboard(elementId) {
  const element = document.getElementById(elementId);
  const text = element.textContent || element.innerText;
  
  navigator.clipboard.writeText(text).then(function() {
    // Show success message
    Swal.fire({
      icon: 'success',
      title: 'Copied!',
      text: 'Text copied to clipboard',
      timer: 1500,
      showConfirmButton: false
    });
  }).catch(function(err) {
    console.error('Could not copy text: ', err);
  });
}
</script>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
      icon: 'success',
      title: 'Berhasil!',
      text: '{{ session('success') }}',
      timer: 3000,
      showConfirmButton: false
    });
  });
</script>
@endif
@endsection
