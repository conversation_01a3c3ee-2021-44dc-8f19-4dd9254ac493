@extends('layouts.contentNavbarLayout')

@section('title', 'Edit Cabang - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Cabang /</span> Edit Cabang
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Edit Cabang</h5>
          <a href="{{ route('master.branches.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.branches.update', $branch) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name"><PERSON><PERSON> <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name', $branch->name) }}" 
                       placeholder="Contoh: Kantor Pusat Jakarta">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="code">Kode Cabang <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                       id="code" name="code" value="{{ old('code', $branch->code) }}" 
                       placeholder="Contoh: HQ, JKT, BDG" maxlength="10">
                @error('code')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Kode unik untuk cabang (maksimal 10 karakter)</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="address">Alamat</label>
              <textarea class="form-control @error('address') is-invalid @enderror" 
                        id="address" name="address" rows="3" 
                        placeholder="Alamat lengkap cabang">{{ old('address', $branch->address) }}</textarea>
              @error('address')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="phone">Nomor Telepon</label>
                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                       id="phone" name="phone" value="{{ old('phone', $branch->phone) }}" 
                       placeholder="Contoh: 021-12345678">
                @error('phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="email">Email</label>
                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                       id="email" name="email" value="{{ old('email', $branch->email) }}" 
                       placeholder="Contoh: <EMAIL>">
                @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', $branch->is_active) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.branches.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Update
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Cabang</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Data Saat Ini:</h6>
            <p class="mb-2">Nama: <strong>{{ $branch->name }}</strong></p>
            <p class="mb-2">Kode: <strong>{{ $branch->code }}</strong></p>
            <p class="mb-2">Status: 
              <span class="badge bg-{{ $branch->is_active ? 'success' : 'secondary' }}">
                {{ $branch->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </p>
            <p class="mb-0">Dibuat: {{ $branch->created_at->format('d/m/Y') }}</p>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Peringatan:</h6>
            <ul class="mb-0">
              <li>Hati-hati mengubah kode cabang jika sudah ada data terkait</li>
              <li>Pastikan kode tetap unik</li>
              <li>Non-aktifkan cabang jika tidak digunakan lagi</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto uppercase code
  const codeInput = document.getElementById('code');
  codeInput.addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  });

  // Format phone number
  const phoneInput = document.getElementById('phone');
  phoneInput.addEventListener('input', function() {
    // Remove non-numeric characters except + and -
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });
});
</script>
@endsection
