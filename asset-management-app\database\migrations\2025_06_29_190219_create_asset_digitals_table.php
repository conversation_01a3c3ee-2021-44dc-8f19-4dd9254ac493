<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_digitals', function (Blueprint $table) {
            $table->id();
            $table->string('asset_code')->unique();
            $table->string('name');
            $table->string('license_type'); // Software, SaaS, Platform, etc
            $table->string('license_key')->nullable();
            $table->string('username')->nullable();
            $table->string('password')->nullable(); // Will be encrypted
            $table->string('login_url')->nullable();
            $table->text('description')->nullable();
            $table->string('vendor')->nullable();
            $table->string('version')->nullable();
            $table->date('purchase_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->decimal('purchase_price', 15, 2)->nullable();
            $table->integer('max_users')->nullable();
            $table->integer('current_users')->default(0);
            $table->enum('status', ['active', 'inactive', 'expired', 'suspended'])->default('active');
            $table->foreignId('branch_id')->constrained('branches');
            $table->foreignId('assigned_to')->nullable()->constrained('employees');
            $table->foreignId('created_by')->constrained('users');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['branch_id', 'status']);
            $table->index(['expiry_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_digitals');
    }
};
