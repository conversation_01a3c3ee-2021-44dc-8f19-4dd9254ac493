<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->string('po_number')->unique(); // Nomor PO otomatis
            $table->date('po_date'); // Tanggal PO
            $table->date('delivery_date')->nullable(); // Tanggal pengiriman yang diharapkan
            $table->foreignId('supplier_id')->constrained('suppliers'); // Supplier
            $table->foreignId('branch_id')->constrained('branches'); // Cabang
            $table->foreignId('created_by')->constrained('users'); // Dibuat oleh
            $table->foreignId('approved_by')->nullable()->constrained('users'); // Disetujui oleh
            $table->datetime('approved_at')->nullable(); // Tanggal persetujuan
            
            // Status PO
            $table->enum('status', [
                'draft', 
                'submitted', 
                'approved', 
                'rejected', 
                'sent_to_supplier', 
                'partially_received', 
                'completed', 
                'cancelled'
            ])->default('draft');
            
            // Informasi finansial
            $table->decimal('subtotal', 15, 2)->default(0); // Subtotal sebelum pajak
            $table->decimal('tax_percentage', 5, 2)->default(0); // Persentase pajak (PPN)
            $table->decimal('tax_amount', 15, 2)->default(0); // Jumlah pajak
            $table->decimal('discount_percentage', 5, 2)->default(0); // Persentase diskon
            $table->decimal('discount_amount', 15, 2)->default(0); // Jumlah diskon
            $table->decimal('total_amount', 15, 2)->default(0); // Total akhir
            
            // Informasi tambahan
            $table->text('notes')->nullable(); // Catatan PO
            $table->text('terms_conditions')->nullable(); // Syarat dan ketentuan
            $table->string('payment_terms')->nullable(); // Syarat pembayaran
            $table->text('delivery_address')->nullable(); // Alamat pengiriman
            $table->text('approval_notes')->nullable(); // Catatan persetujuan
            
            // Tracking
            $table->datetime('sent_at')->nullable(); // Kapan dikirim ke supplier
            $table->datetime('received_at')->nullable(); // Kapan diterima lengkap
            $table->foreignId('received_by')->nullable()->constrained('users'); // Diterima oleh
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'po_date']);
            $table->index(['supplier_id', 'status']);
            $table->index(['branch_id', 'status']);
            $table->index(['created_by', 'po_date']);
            $table->index('po_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};
