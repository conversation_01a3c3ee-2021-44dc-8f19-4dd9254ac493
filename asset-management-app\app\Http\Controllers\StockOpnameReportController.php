<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StockOpname;
use App\Models\StockOpnameDetail;
use App\Models\Branch;
use App\Helpers\BranchHelper;
use App\Exports\StockOpnameReportExport;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class StockOpnameReportController extends Controller
{
    /**
     * Display stock opname reports with filters
     */
    public function index(Request $request)
    {
        $query = StockOpname::with(['branch', 'creator']);

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        // Apply filters
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('start_date_from')) {
            $query->whereDate('start_date', '>=', $request->start_date_from);
        }

        if ($request->filled('start_date_to')) {
            $query->whereDate('start_date', '<=', $request->start_date_to);
        }

        $stockOpnames = $query->latest()->paginate(15)->withQueryString();

        // Get filter options
        $branches = BranchHelper::getAccessibleBranches();
        $statuses = [
            'draft' => 'Draft',
            'in_progress' => 'Sedang Berjalan',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan'
        ];

        // Calculate summary statistics
        $summaryQuery = clone $query;
        $summaryData = $summaryQuery->get();

        $summary = [
            'total_opnames' => $summaryData->count(),
            'completed_opnames' => $summaryData->where('status', 'completed')->count(),
            'in_progress_opnames' => $summaryData->where('status', 'in_progress')->count(),
            'total_assets_audited' => $summaryData->sum('total_assets'),
            'total_assets_scanned' => $summaryData->sum('scanned_assets'),
            'total_assets_found' => $summaryData->sum('found_assets'),
            'total_assets_missing' => $summaryData->sum('missing_assets'),
        ];

        return view('stock-opname-reports.index', compact(
            'stockOpnames',
            'branches',
            'statuses',
            'summary'
        ));
    }

    /**
     * Show detailed report for specific stock opname
     */
    public function show(StockOpname $stockOpname)
    {
        $stockOpname->load(['branch', 'creator']);

        $details = StockOpnameDetail::where('stock_opname_id', $stockOpname->id)
            ->with(['asset.assetCategory', 'scanner'])
            ->get();

        $groupedDetails = [
            'found' => $details->where('found_status', 'found'),
            'missing' => $details->where('found_status', 'not_found'),
            'damaged' => $details->where('found_status', 'damaged'),
            'discrepancy' => $details->where('has_discrepancy', true),
            'not_scanned' => $details->whereNull('scanned_at'),
            'excellent_condition' => $details->where('physical_condition', 'excellent'),
            'good_condition' => $details->where('physical_condition', 'good'),
            'fair_condition' => $details->where('physical_condition', 'fair'),
            'poor_condition' => $details->where('physical_condition', 'poor'),
            'damaged_condition' => $details->where('physical_condition', 'damaged'),
        ];

        // Add priority categorization for better tracking
        $groupedDetails['urgent_not_scanned'] = $details->whereNull('scanned_at');
        $groupedDetails['high_priority_missing'] = $details->where('found_status', 'not_found');
        $groupedDetails['medium_priority_discrepancy'] = $details->where('has_discrepancy', true)->where('found_status', '!=', 'not_found');
        $groupedDetails['completed_normal'] = $details->whereNotNull('scanned_at')->where('found_status', 'found')->where('has_discrepancy', false);

        $statistics = [
            'total' => $stockOpname->total_assets,
            'scanned' => $stockOpname->scanned_assets,
            'found' => $stockOpname->found_assets,
            'missing' => $stockOpname->missing_assets,
            'discrepancy' => $groupedDetails['discrepancy']->count(),
            'progress' => $stockOpname->total_assets > 0 ? round(($stockOpname->scanned_assets / $stockOpname->total_assets) * 100, 2) : 0
        ];

        return view('stock-opname-reports.show', compact('stockOpname', 'groupedDetails', 'statistics'));
    }

    /**
     * Export stock opname report to Excel
     */
    public function export(Request $request)
    {
        $filters = $request->only([
            'branch_id',
            'status',
            'date_from',
            'date_to',
            'start_date_from',
            'start_date_to'
        ]);

        $filename = 'laporan-stock-opname-' . date('Y-m-d-H-i-s') . '.xlsx';

        return Excel::download(new StockOpnameReportExport($filters), $filename);
    }

    /**
     * Export detailed report for specific stock opname
     */
    public function exportDetail(StockOpname $stockOpname)
    {
        $filename = 'detail-stock-opname-' . $stockOpname->opname_number . '-' . date('Y-m-d-H-i-s') . '.xlsx';

        return Excel::download(new StockOpnameReportExport([], $stockOpname->id), $filename);
    }

    /**
     * Get summary statistics for dashboard
     */
    public function summary(Request $request)
    {
        $query = StockOpname::query();

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        // Apply date filter if provided
        if ($request->filled('period')) {
            switch ($request->period) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', now()->month)
                          ->whereYear('created_at', now()->year);
                    break;
                case 'year':
                    $query->whereYear('created_at', now()->year);
                    break;
            }
        }

        $stockOpnames = $query->get();

        $summary = [
            'total_opnames' => $stockOpnames->count(),
            'completed_opnames' => $stockOpnames->where('status', 'completed')->count(),
            'in_progress_opnames' => $stockOpnames->where('status', 'in_progress')->count(),
            'cancelled_opnames' => $stockOpnames->where('status', 'cancelled')->count(),
            'total_assets_audited' => $stockOpnames->sum('total_assets'),
            'total_assets_scanned' => $stockOpnames->sum('scanned_assets'),
            'total_assets_found' => $stockOpnames->sum('found_assets'),
            'total_assets_missing' => $stockOpnames->sum('missing_assets'),
            'completion_rate' => $stockOpnames->count() > 0 ?
                round(($stockOpnames->where('status', 'completed')->count() / $stockOpnames->count()) * 100, 2) : 0,
            'scanning_rate' => $stockOpnames->sum('total_assets') > 0 ?
                round(($stockOpnames->sum('scanned_assets') / $stockOpnames->sum('total_assets')) * 100, 2) : 0,
        ];

        return response()->json($summary);
    }
}
