@extends('layouts.contentNavbarLayout')

@section('title', 'My Profile - Asset Management System')

@section('content')
<style>
.profile-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.profile-header {
  background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 2rem;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-section h5 {
  color: #566a7f;
  margin-bottom: 1rem;
  font-weight: 600;
}

.btn-update {
  background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-update:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(105, 108, 255, 0.4);
}

.form-control {
  border: 2px solid #e7e7ff;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 10px 14px;
}

.form-control:focus {
  border-color: #696cff;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

.form-label {
  color: #566a7f;
  font-weight: 500;
  margin-bottom: 8px;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card profile-card mb-4">
        <div class="profile-header text-center">
          <div class="profile-avatar mx-auto">
            <i class="ri-user-line"></i>
          </div>
          <h4 class="mb-1">{{ $user->name }}</h4>
          <p class="mb-0 opacity-75">{{ $user->role->name ?? 'No Role' }}</p>
          @if($user->branch)
            <small class="opacity-75">{{ $user->branch->name }}</small>
          @endif
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Profile Information -->
    <div class="col-md-6">
      <div class="card profile-card">
        <div class="card-body">
          <div class="form-section">
            <h5>
              <i class="ri-user-settings-line me-2"></i>
              Informasi Profil
            </h5>
            
            <form method="POST" action="{{ route('profile.update') }}">
              @csrf
              @method('PUT')
              
              <div class="mb-3">
                <label for="name" class="form-label">Nama Lengkap</label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name', $user->name) }}" required>
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" 
                       value="{{ $user->username }}" readonly disabled>
                <div class="form-text">Username tidak dapat diubah</div>
              </div>

              <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" 
                       value="{{ $user->email }}" readonly disabled>
                <div class="form-text">Email tidak dapat diubah</div>
              </div>

              <div class="mb-4">
                <label for="phone" class="form-label">Nomor Telepon</label>
                <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                       id="phone" name="phone" value="{{ old('phone', $user->phone) }}" 
                       placeholder="Masukkan nomor telepon">
                @error('phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <button type="submit" class="btn btn-primary btn-update">
                <i class="ri-save-line me-1"></i>
                Perbarui Profil
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Change Password -->
    <div class="col-md-6">
      <div class="card profile-card">
        <div class="card-body">
          <div class="form-section">
            <h5>
              <i class="ri-lock-password-line me-2"></i>
              Ubah Password
            </h5>
            
            <form method="POST" action="{{ route('profile.update-password') }}">
              @csrf
              @method('PUT')
              
              <div class="mb-3">
                <label for="current_password" class="form-label">Password Saat Ini</label>
                <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                       id="current_password" name="current_password" required>
                @error('current_password')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <div class="mb-3">
                <label for="password" class="form-label">Password Baru</label>
                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                       id="password" name="password" required>
                @error('password')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Password minimal 8 karakter</div>
              </div>

              <div class="mb-4">
                <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                <input type="password" class="form-control" 
                       id="password_confirmation" name="password_confirmation" required>
              </div>

              <button type="submit" class="btn btn-warning btn-update">
                <i class="ri-key-line me-1"></i>
                Ubah Password
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Info -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card profile-card">
        <div class="card-body">
          <div class="row text-center">
            <div class="col-md-3">
              <div class="d-flex flex-column">
                <i class="ri-calendar-line ri-24px text-primary mb-2"></i>
                <h6 class="mb-0">Bergabung</h6>
                <small class="text-muted">{{ $user->created_at->format('d M Y') }}</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="d-flex flex-column">
                <i class="ri-shield-check-line ri-24px text-success mb-2"></i>
                <h6 class="mb-0">Role</h6>
                <small class="text-muted">{{ $user->role->name ?? 'No Role' }}</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="d-flex flex-column">
                <i class="ri-building-line ri-24px text-info mb-2"></i>
                <h6 class="mb-0">Cabang</h6>
                <small class="text-muted">{{ $user->branch->name ?? 'No Branch' }}</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="d-flex flex-column">
                <i class="ri-team-line ri-24px text-warning mb-2"></i>
                <h6 class="mb-0">Divisi</h6>
                <small class="text-muted">{{ $user->division->name ?? 'No Division' }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Memproses...';
            
            // Re-enable after 3 seconds as fallback
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }, 3000);
        });
    });
    
    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            // Remove non-numeric characters except + and -
            this.value = this.value.replace(/[^0-9+\-\s]/g, '');
        });
    }
});
</script>

@endsection
