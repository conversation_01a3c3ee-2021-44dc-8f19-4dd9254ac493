<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\AssetType;
use App\Models\AssetCategory;
use Illuminate\Http\Request;

class AssetTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AssetType::with(['category'])->withCount('assets');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category_id')) {
            $query->where('asset_category_id', $request->category_id);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $assetTypes = $query->latest()->paginate(15);
        $categories = AssetCategory::active()->get();

        return view('master.asset-types.index', compact('assetTypes', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = AssetCategory::active()->get();
        return view('master.asset-types.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10',
            'asset_category_id' => 'required|exists:asset_categories,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Check for duplicate code within the same category
        $exists = AssetType::where('code', $validated['code'])
            ->where('asset_category_id', $validated['asset_category_id'])
            ->exists();

        if ($exists) {
            return back()->withErrors(['code' => 'Kode sudah digunakan dalam kategori ini.']);
        }

        AssetType::create($validated);

        return redirect()->route('master.asset-types.index')->with('success', 'Jenis asset berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetType $assetType)
    {
        $assetType->load(['category', 'assets' => function ($query) {
            $query->latest()->take(10);
        }]);

        return view('master.asset-types.show', compact('assetType'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetType $assetType)
    {
        $categories = AssetCategory::active()->get();
        return view('master.asset-types.edit', compact('assetType', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetType $assetType)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10',
            'asset_category_id' => 'required|exists:asset_categories,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Check for duplicate code within the same category (excluding current record)
        $exists = AssetType::where('code', $validated['code'])
            ->where('asset_category_id', $validated['asset_category_id'])
            ->where('id', '!=', $assetType->id)
            ->exists();

        if ($exists) {
            return back()->withErrors(['code' => 'Kode sudah digunakan dalam kategori ini.']);
        }

        $assetType->update($validated);

        return redirect()->route('master.asset-types.index')->with('success', 'Jenis asset berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetType $assetType)
    {
        // Check if asset type has assets
        if ($assetType->assets()->count() > 0) {
            return redirect()->route('master.asset-types.index')->with('error', 'Tidak dapat menghapus jenis asset yang memiliki asset.');
        }

        $assetType->delete();

        return redirect()->route('master.asset-types.index')->with('success', 'Jenis asset berhasil dihapus.');
    }

    /**
     * Get asset types by category (AJAX)
     */
    public function getByCategory(Request $request)
    {
        $categoryId = $request->get('category_id');
        $assetTypes = AssetType::where('asset_category_id', $categoryId)
            ->where('is_active', true)
            ->get(['id', 'name', 'code']);

        return response()->json($assetTypes);
    }
}
