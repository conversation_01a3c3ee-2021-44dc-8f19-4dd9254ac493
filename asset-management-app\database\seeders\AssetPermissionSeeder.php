<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class AssetPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create asset permissions
        $permissions = [
            [
                'name' => 'View Assets',
                'slug' => 'assets.view',
                'module' => 'Asset Management',
                'description' => 'Can view asset list and details'
            ],
            [
                'name' => 'Create Assets',
                'slug' => 'assets.create',
                'module' => 'Asset Management',
                'description' => 'Can create new assets'
            ],
            [
                'name' => 'Edit Assets',
                'slug' => 'assets.edit',
                'module' => 'Asset Management',
                'description' => 'Can edit existing assets'
            ],
            [
                'name' => 'Delete Assets',
                'slug' => 'assets.delete',
                'module' => 'Asset Management',
                'description' => 'Can delete assets'
            ],
            [
                'name' => 'Import Assets',
                'slug' => 'assets.import',
                'module' => 'Asset Management',
                'description' => 'Can import assets from Excel'
            ],
            [
                'name' => 'View Asset Categories',
                'slug' => 'asset-categories.view',
                'module' => 'Asset Management',
                'description' => 'Can view asset categories'
            ],
            [
                'name' => 'Manage Asset Categories',
                'slug' => 'asset-categories.manage',
                'module' => 'Asset Management',
                'description' => 'Can create, edit, delete asset categories'
            ]
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                $permissionData
            );
        }

        // Assign all asset permissions to super-admin role
        $superAdminRole = Role::where('slug', 'super-admin')->first();
        if ($superAdminRole) {
            $permissionIds = Permission::whereIn('slug', [
                'assets.view',
                'assets.create',
                'assets.edit',
                'assets.delete',
                'assets.import',
                'asset-categories.view',
                'asset-categories.manage'
            ])->pluck('id');

            $superAdminRole->permissions()->syncWithoutDetaching($permissionIds);
        }

        // Assign basic asset permissions to admin role
        $adminRole = Role::where('slug', 'admin')->first();
        if ($adminRole) {
            $permissionIds = Permission::whereIn('slug', [
                'assets.view',
                'assets.create',
                'assets.edit',
                'assets.import',
                'asset-categories.view'
            ])->pluck('id');

            $adminRole->permissions()->syncWithoutDetaching($permissionIds);
        }

        $this->command->info('Asset permissions created and assigned successfully!');
    }
}
