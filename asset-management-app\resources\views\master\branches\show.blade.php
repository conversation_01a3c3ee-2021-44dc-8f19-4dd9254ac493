@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Cabang - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master Cabang /</span> Detail Cabang
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Detail Cabang: {{ $branch->name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.branches.edit', $branch) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.branches.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Cabang</label>
                <p class="fw-bold">{{ $branch->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Kode Cabang</label>
                <p><span class="badge bg-primary fs-6">{{ $branch->code }}</span></p>
              </div>
            </div>
          </div>

          @if($branch->address)
          <div class="mb-3">
            <label class="form-label text-muted">Alamat</label>
            <p>{{ $branch->address }}</p>
          </div>
          @endif

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nomor Telepon</label>
                <p>{{ $branch->phone ?: '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Email</label>
                <p>{{ $branch->email ?: '-' }}</p>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $branch->is_active ? 'success' : 'secondary' }} fs-6">
                    {{ $branch->is_active ? 'Aktif' : 'Non-Aktif' }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Dibuat</label>
                <p>{{ $branch->created_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Card -->
      @if($branch->users->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">User di Cabang Ini ({{ $branch->users->count() }} user)</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Nama</th>
                  <th>Username</th>
                  <th>Role</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                @foreach($branch->users as $user)
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-xs me-2">
                        <span class="avatar-initial rounded-circle bg-label-primary">
                          {{ strtoupper(substr($user->name, 0, 2)) }}
                        </span>
                      </div>
                      {{ $user->name }}
                    </div>
                  </td>
                  <td>{{ $user->username }}</td>
                  <td>
                    <span class="badge bg-{{ $user->role->slug === 'super-admin' ? 'danger' : ($user->role->slug === 'admin' ? 'warning' : 'primary') }} badge-sm">
                      {{ $user->role->name }}
                    </span>
                  </td>
                  <td>
                    <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }} badge-sm">
                      {{ $user->is_active ? 'Aktif' : 'Non-Aktif' }}
                    </span>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
      @endif

      <!-- Assets Card -->
      @if($branch->assets->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">Asset di Cabang Ini ({{ $branch->assets->count() }} asset)</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Kode Asset</th>
                  <th>Nama</th>
                  <th>Kategori</th>
                  <th>Status</th>
                  <th>Kondisi</th>
                </tr>
              </thead>
              <tbody>
                @foreach($branch->assets->take(10) as $asset)
                <tr>
                  <td><span class="badge bg-info badge-sm">{{ $asset->asset_code }}</span></td>
                  <td>{{ $asset->name }}</td>
                  <td>{{ $asset->category->name }}</td>
                  <td>
                    <span class="badge bg-{{ $asset->status_badge }} badge-sm">
                      {{ ucfirst($asset->status) }}
                    </span>
                  </td>
                  <td>
                    <span class="badge bg-{{ $asset->condition_badge }} badge-sm">
                      {{ ucfirst($asset->condition) }}
                    </span>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
            @if($branch->assets->count() > 10)
            <div class="text-center mt-2">
              <small class="text-muted">Dan {{ $branch->assets->count() - 10 }} asset lainnya...</small>
            </div>
            @endif
          </div>
        </div>
      </div>
      @endif
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi Cabang</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                <i class="ri-building-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $branch->name }}</h6>
              <small class="text-muted">{{ $branch->code }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Statistik:</h6>
            <ul class="mb-0">
              <li>{{ $branch->users->count() }} User</li>
              <li>{{ $branch->assets->count() }} Asset</li>
              <li>Dibuat {{ $branch->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.branches.edit', $branch) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit Cabang
            </a>
            
            @if($branch->is_active)
              <button class="btn btn-warning" onclick="toggleStatus(false)">
                <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
              </button>
            @else
              <button class="btn btn-success" onclick="toggleStatus(true)">
                <i class="ri-play-circle-line me-1"></i>Aktifkan
              </button>
            @endif
            
            @if($branch->users->count() == 0 && $branch->assets->count() == 0)
            <form action="{{ route('master.branches.destroy', $branch) }}" method="POST" id="deleteForm">
              @csrf
              @method('DELETE')
              <button type="button" class="btn btn-danger w-100" onclick="confirmDelete()">
                <i class="ri-delete-bin-line me-1"></i>Hapus Cabang
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStatus(status) {
  if (confirm('Yakin ingin mengubah status cabang ini?')) {
    // Create form to toggle status
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("master.branches.update", $branch) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'is_active';
    statusField.value = status ? '1' : '0';
    
    // Copy other fields
    const branchData = {
      name: '{{ $branch->name }}',
      code: '{{ $branch->code }}',
      address: '{{ $branch->address }}',
      phone: '{{ $branch->phone }}',
      email: '{{ $branch->email }}'
    };

    const fields = ['name', 'code', 'address', 'phone', 'email'];
    fields.forEach(field => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = field;
      input.value = branchData[field];
      form.appendChild(input);
    });
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    form.appendChild(statusField);
    
    document.body.appendChild(form);
    form.submit();
  }
}

function confirmDelete() {
  if (confirm('Yakin ingin menghapus cabang ini?\n\nTindakan ini tidak dapat dibatalkan!')) {
    document.getElementById('deleteForm').submit();
  }
}
</script>
@endsection
