<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Contract;
use App\Models\Supplier;
use App\Models\Division;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class ContractController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Contract::with(['supplier', 'division', 'createdBy']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('contract_number', 'like', "%{$search}%")
                  ->orWhere('contract_name', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('division_id')) {
            $query->where('division_id', $request->division_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('contract_type')) {
            $query->where('contract_type', $request->contract_type);
        }

        if ($request->filled('expiry_filter')) {
            switch ($request->expiry_filter) {
                case 'expiring':
                    $query->expiring();
                    break;
                case 'expired':
                    $query->expired();
                    break;
            }
        }

        $contracts = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $suppliers = Supplier::active()->orderBy('name')->get();
        $divisions = Division::active()->orderBy('name')->get();

        return view('master.contracts.index', compact('contracts', 'suppliers', 'divisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $divisions = Division::active()->orderBy('name')->get();
        $branches = Branch::active()->orderBy('name')->get();
        $contractNumber = Contract::generateContractNumber();

        return view('master.contracts.create', compact('divisions', 'branches', 'contractNumber'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'contract_number' => 'required|string|max:255|unique:contracts',
            'contract_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'supplier_id' => 'required|exists:suppliers,id',
            'division_id' => 'nullable|exists:divisions,id',
            'branch_id' => 'nullable|exists:branches,id',
            'contract_date' => 'required|date',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'contract_value' => 'nullable|numeric|min:0',
            'contract_type' => 'required|in:service,supply,maintenance,lease,other',
            'status' => 'required|in:draft,active,expired,terminated,renewed',
            'terms_conditions' => 'nullable|string',
            'contract_file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'notes' => 'nullable|string',
            'auto_renewal' => 'boolean',
            'renewal_period_months' => 'nullable|integer|min:1|max:120',
            'notification_days' => 'required|integer|min:1|max:365',
        ]);

        try {
            DB::beginTransaction();

            // Handle file upload
            if ($request->hasFile('contract_file')) {
                $file = $request->file('contract_file');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('contracts', $filename, 'public');
                $validated['contract_file'] = $path;
            }

            $validated['created_by'] = auth()->id();
            $validated['updated_by'] = auth()->id();

            $contract = Contract::create($validated);

            DB::commit();

            return redirect()->route('contracts.show', $contract)
                           ->with('success', 'Kontrak berhasil dibuat.');

        } catch (\Exception $e) {
            DB::rollBack();

            // Delete uploaded file if exists
            if (isset($validated['contract_file'])) {
                Storage::disk('public')->delete($validated['contract_file']);
            }

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Gagal membuat kontrak: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Contract $contract)
    {
        $contract->load(['supplier', 'division', 'branch', 'createdBy', 'updatedBy']);

        return view('master.contracts.show', compact('contract'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contract $contract)
    {
        $divisions = Division::active()->orderBy('name')->get();
        $branches = Branch::active()->orderBy('name')->get();

        return view('master.contracts.edit', compact('contract', 'divisions', 'branches'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contract $contract)
    {
        $validated = $request->validate([
            'contract_number' => 'required|string|max:255|unique:contracts,contract_number,' . $contract->id,
            'contract_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'supplier_id' => 'required|exists:suppliers,id',
            'division_id' => 'nullable|exists:divisions,id',
            'branch_id' => 'nullable|exists:branches,id',
            'contract_date' => 'required|date',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'contract_value' => 'nullable|numeric|min:0',
            'contract_type' => 'required|in:service,supply,maintenance,lease,other',
            'status' => 'required|in:draft,active,expired,terminated,renewed',
            'terms_conditions' => 'nullable|string',
            'contract_file' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'notes' => 'nullable|string',
            'auto_renewal' => 'boolean',
            'renewal_period_months' => 'nullable|integer|min:1|max:120',
            'notification_days' => 'required|integer|min:1|max:365',
        ]);

        try {
            DB::beginTransaction();

            // Handle file upload
            if ($request->hasFile('contract_file')) {
                // Delete old file
                if ($contract->contract_file) {
                    Storage::disk('public')->delete($contract->contract_file);
                }

                $file = $request->file('contract_file');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('contracts', $filename, 'public');
                $validated['contract_file'] = $path;
            }

            $validated['updated_by'] = auth()->id();

            $contract->update($validated);

            DB::commit();

            return redirect()->route('contracts.show', $contract)
                           ->with('success', 'Kontrak berhasil diperbarui.');

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Gagal memperbarui kontrak: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contract $contract)
    {
        try {
            DB::beginTransaction();

            // Delete contract file
            if ($contract->contract_file) {
                Storage::disk('public')->delete($contract->contract_file);
            }

            $contract->delete();

            DB::commit();

            return redirect()->route('contracts.index')
                           ->with('success', 'Kontrak berhasil dihapus.');

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()
                           ->with('error', 'Gagal menghapus kontrak: ' . $e->getMessage());
        }
    }

    /**
     * Download contract file
     */
    public function downloadFile(Contract $contract)
    {
        if (!$contract->contract_file || !Storage::disk('public')->exists($contract->contract_file)) {
            return redirect()->back()->with('error', 'File kontrak tidak ditemukan.');
        }

        return Storage::disk('public')->download($contract->contract_file);
    }


}
