/**
 * Purchase Order Lookup Component
 * Provides popup functionality to search and select purchase orders
 */

class POLookup {
    constructor(options = {}) {
        this.options = {
            inputId: options.inputId || 'purchase_order_id',
            displayId: options.displayId || 'purchase_order_display',
            buttonId: options.buttonId || 'po_lookup_btn',
            modalId: options.modalId || 'poLookupModal',
            apiUrl: options.apiUrl || '/api/purchase-orders/lookup',
            onSelect: options.onSelect || null,
            ...options
        };

        this.currentPage = 1;
        this.searchTerm = '';
        this.selectedPO = null;

        this.init();
    }

    init() {
        this.createModal();
        this.bindEvents();
        this.loadInitialData();
    }

    createModal() {
        const modalHtml = `
            <div class="modal fade" id="${this.options.modalId}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="ri-file-list-3-line me-2"></i>
                                Pilih Purchase Order
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Search -->
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="ri-search-line"></i>
                                    </span>
                                    <input type="text" class="form-control" id="po_search" 
                                           placeholder="Cari nomor PO, supplier, atau tanggal...">
                                    <button class="btn btn-outline-secondary" type="button" id="po_search_btn">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Filters -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <select class="form-select" id="po_status_filter">
                                        <option value="">Semua Status</option>
                                        <option value="approved">Approved</option>
                                        <option value="sent_to_supplier">Sent to Supplier</option>
                                        <option value="partially_received">Partially Received</option>
                                        <option value="completed">Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <input type="date" class="form-control" id="po_date_from" placeholder="Dari Tanggal">
                                </div>
                                <div class="col-md-4">
                                    <input type="date" class="form-control" id="po_date_to" placeholder="Sampai Tanggal">
                                </div>
                            </div>

                            <!-- Loading -->
                            <div id="po_loading" class="text-center py-4" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-2">Memuat data...</div>
                            </div>

                            <!-- Results -->
                            <div id="po_results">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Nomor PO</th>
                                                <th>Tanggal</th>
                                                <th>Supplier</th>
                                                <th>Total</th>
                                                <th>Status</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody id="po_list">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Pagination -->
                            <div id="po_pagination" class="d-flex justify-content-between align-items-center mt-3">
                                <div id="po_info"></div>
                                <nav>
                                    <ul class="pagination pagination-sm mb-0" id="po_pagination_links">
                                        <!-- Pagination links will be generated here -->
                                    </ul>
                                </nav>
                            </div>

                            <!-- No Results -->
                            <div id="po_no_results" class="text-center py-4" style="display: none;">
                                <i class="ri-file-list-3-line text-muted" style="font-size: 3rem;"></i>
                                <div class="mt-2 text-muted">Tidak ada purchase order yang ditemukan</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById(this.options.modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    bindEvents() {
        // Open modal button
        const openButton = document.getElementById(this.options.buttonId);
        if (openButton) {
            openButton.addEventListener('click', () => this.openModal());
        }

        // Search input
        const searchInput = document.getElementById('po_search');
        if (searchInput) {
            searchInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.search();
                }
            });
        }

        // Search button
        const searchButton = document.getElementById('po_search_btn');
        if (searchButton) {
            searchButton.addEventListener('click', () => this.search());
        }

        // Filter changes
        const statusFilter = document.getElementById('po_status_filter');
        const dateFromFilter = document.getElementById('po_date_from');
        const dateToFilter = document.getElementById('po_date_to');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.search());
        }
        if (dateFromFilter) {
            dateFromFilter.addEventListener('change', () => this.search());
        }
        if (dateToFilter) {
            dateToFilter.addEventListener('change', () => this.search());
        }

        // Double click to select
        document.addEventListener('dblclick', (e) => {
            if (e.target.closest('.po-row')) {
                const row = e.target.closest('.po-row');
                this.selectPO(row);
            }
        });
    }

    openModal() {
        const modal = new bootstrap.Modal(document.getElementById(this.options.modalId));
        modal.show();
        this.loadData();
    }

    loadInitialData() {
        // Load data when component is initialized
    }

    loadData(page = 1) {
        this.currentPage = page;
        this.showLoading();

        const params = new URLSearchParams({
            page: page,
            search: this.searchTerm,
            status: document.getElementById('po_status_filter')?.value || '',
            date_from: document.getElementById('po_date_from')?.value || '',
            date_to: document.getElementById('po_date_to')?.value || ''
        });

        fetch(`${this.options.apiUrl}?${params}`)
            .then(response => response.json())
            .then(data => {
                this.hideLoading();
                this.renderResults(data);
            })
            .catch(error => {
                this.hideLoading();
                console.error('Error loading PO data:', error);
                this.showError('Gagal memuat data purchase order');
            });
    }

    search() {
        this.searchTerm = document.getElementById('po_search').value;
        this.loadData(1);
    }

    renderResults(data) {
        const tbody = document.getElementById('po_list');
        const noResults = document.getElementById('po_no_results');
        const results = document.getElementById('po_results');

        if (!data.data || data.data.length === 0) {
            results.style.display = 'none';
            noResults.style.display = 'block';
            return;
        }

        results.style.display = 'block';
        noResults.style.display = 'none';

        tbody.innerHTML = data.data.map(po => `
            <tr class="po-row" data-id="${po.id}" data-po-number="${po.po_number}" 
                data-supplier="${po.supplier_name}" data-total="${po.total_amount}">
                <td>
                    <strong>${po.po_number}</strong>
                </td>
                <td>${this.formatDate(po.po_date)}</td>
                <td>${po.supplier_name}</td>
                <td>${this.formatCurrency(po.total_amount)}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(po.status)}">${this.getStatusText(po.status)}</span>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary select-po" 
                            onclick="window.poLookup.selectPOById(${po.id})">
                        <i class="ri-check-line me-1"></i>Pilih
                    </button>
                </td>
            </tr>
        `).join('');

        this.renderPagination(data);
    }

    selectPOById(id) {
        const row = document.querySelector(`[data-id="${id}"]`);
        if (row) {
            this.selectPO(row);
        }
    }

    selectPO(row) {
        const id = row.dataset.id;
        const poNumber = row.dataset.poNumber;
        const supplier = row.dataset.supplier;
        const total = row.dataset.total;

        // Set values
        const inputElement = document.getElementById(this.options.inputId);
        const displayElement = document.getElementById(this.options.displayId);

        if (inputElement) inputElement.value = id;
        if (displayElement) displayElement.value = `${poNumber} - ${supplier}`;

        // Call custom onSelect if provided
        if (this.options.onSelect) {
            this.options.onSelect(id, poNumber, supplier, total);
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById(this.options.modalId));
        if (modal) {
            modal.hide();
        }
    }

    renderPagination(data) {
        // Implementation similar to supplier lookup pagination
        const paginationContainer = document.getElementById('po_pagination_links');
        const infoContainer = document.getElementById('po_info');

        if (infoContainer) {
            infoContainer.textContent = `Menampilkan ${data.from || 0} - ${data.to || 0} dari ${data.total || 0} data`;
        }

        if (!paginationContainer) return;

        let paginationHtml = '';

        // Previous button
        if (data.current_page > 1) {
            paginationHtml += `<li class="page-item">
                <a class="page-link" href="#" onclick="window.poLookup.loadData(${data.current_page - 1})">Previous</a>
            </li>`;
        }

        // Page numbers
        for (let i = 1; i <= data.last_page; i++) {
            if (i === data.current_page) {
                paginationHtml += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                paginationHtml += `<li class="page-item">
                    <a class="page-link" href="#" onclick="window.poLookup.loadData(${i})">${i}</a>
                </li>`;
            }
        }

        // Next button
        if (data.current_page < data.last_page) {
            paginationHtml += `<li class="page-item">
                <a class="page-link" href="#" onclick="window.poLookup.loadData(${data.current_page + 1})">Next</a>
            </li>`;
        }

        paginationContainer.innerHTML = paginationHtml;
    }

    showLoading() {
        document.getElementById('po_loading').style.display = 'block';
        document.getElementById('po_results').style.display = 'none';
        document.getElementById('po_no_results').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('po_loading').style.display = 'none';
    }

    showError(message) {
        // Simple error display - could be enhanced with toast notifications
        alert(message);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('id-ID');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR'
        }).format(amount);
    }

    getStatusColor(status) {
        const colors = {
            'draft': 'secondary',
            'submitted': 'warning',
            'approved': 'success',
            'rejected': 'danger',
            'sent_to_supplier': 'info',
            'partially_received': 'warning',
            'completed': 'success',
            'cancelled': 'danger'
        };
        return colors[status] || 'secondary';
    }

    getStatusText(status) {
        const texts = {
            'draft': 'Draft',
            'submitted': 'Submitted',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'sent_to_supplier': 'Sent to Supplier',
            'partially_received': 'Partially Received',
            'completed': 'Completed',
            'cancelled': 'Cancelled'
        };
        return texts[status] || status;
    }
}

// Make it globally available
window.POLookup = POLookup;
