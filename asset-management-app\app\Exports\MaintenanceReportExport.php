<?php

namespace App\Exports;

use App\Models\AssetMaintenance;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class MaintenanceReportExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, WithTitle
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = AssetMaintenance::with(['asset.category', 'asset.branch', 'supplier', 'requestedBy', 'assignedTo']);
        
        // Apply same filters as in controller
        if (!empty($this->filters['branch_id'])) {
            $query->whereHas('asset', function($q) {
                $q->where('branch_id', $this->filters['branch_id']);
            });
        }
        
        if (!empty($this->filters['category_id'])) {
            $query->whereHas('asset', function($q) {
                $q->where('asset_category_id', $this->filters['category_id']);
            });
        }
        
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }
        
        if (!empty($this->filters['priority'])) {
            $query->where('priority', $this->filters['priority']);
        }
        
        if (!empty($this->filters['maintenance_type'])) {
            $query->where('maintenance_type', $this->filters['maintenance_type']);
        }
        
        if (!empty($this->filters['date_from'])) {
            $query->whereDate('scheduled_date', '>=', $this->filters['date_from']);
        }
        
        if (!empty($this->filters['date_to'])) {
            $query->whereDate('scheduled_date', '<=', $this->filters['date_to']);
        }
        
        return $query->orderBy('scheduled_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'No. Maintenance',
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Cabang',
            'Tipe Maintenance',
            'Judul',
            'Deskripsi',
            'Prioritas',
            'Status',
            'Tanggal Terjadwal',
            'Tanggal Mulai',
            'Tanggal Selesai',
            'Estimasi Biaya',
            'Biaya Aktual',
            'Supplier',
            'Diminta Oleh',
            'Ditugaskan Ke',
            'Catatan'
        ];
    }

    /**
     * @param mixed $maintenance
     * @return array
     */
    public function map($maintenance): array
    {
        return [
            $maintenance->maintenance_number,
            $maintenance->asset->asset_code ?? '-',
            $maintenance->asset->name ?? '-',
            $maintenance->asset->category->name ?? '-',
            $maintenance->asset->branch->name ?? '-',
            $this->getMaintenanceTypeText($maintenance->maintenance_type),
            $maintenance->title,
            $maintenance->description,
            $this->getPriorityText($maintenance->priority),
            $this->getStatusText($maintenance->status),
            $maintenance->scheduled_date ? $maintenance->scheduled_date->format('d/m/Y') : '-',
            $maintenance->started_date ? $maintenance->started_date->format('d/m/Y') : '-',
            $maintenance->completed_date ? $maintenance->completed_date->format('d/m/Y') : '-',
            $maintenance->estimated_cost ? 'Rp ' . number_format($maintenance->estimated_cost, 0, ',', '.') : '-',
            $maintenance->actual_cost ? 'Rp ' . number_format($maintenance->actual_cost, 0, ',', '.') : '-',
            $maintenance->supplier->name ?? '-',
            $maintenance->requestedBy->name ?? '-',
            $maintenance->assignedTo->name ?? '-',
            $maintenance->notes ?? '-'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $lastRow = $sheet->getHighestRow();
        $lastColumn = $sheet->getHighestColumn();
        
        return [
            // Header row styling
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            
            // All cells border
            "A1:{$lastColumn}{$lastRow}" => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true,
                ],
            ],
            
            // Data rows
            "A2:{$lastColumn}{$lastRow}" => [
                'font' => [
                    'size' => 10,
                ],
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 20, // No. Maintenance
            'B' => 15, // Kode Asset
            'C' => 25, // Nama Asset
            'D' => 15, // Kategori
            'E' => 15, // Cabang
            'F' => 15, // Tipe Maintenance
            'G' => 25, // Judul
            'H' => 30, // Deskripsi
            'I' => 12, // Prioritas
            'J' => 15, // Status
            'K' => 15, // Tanggal Terjadwal
            'L' => 15, // Tanggal Mulai
            'M' => 15, // Tanggal Selesai
            'N' => 18, // Estimasi Biaya
            'O' => 18, // Biaya Aktual
            'P' => 20, // Supplier
            'Q' => 20, // Diminta Oleh
            'R' => 20, // Ditugaskan Ke
            'S' => 25, // Catatan
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Laporan Maintenance';
    }

    /**
     * Get maintenance type text
     */
    private function getMaintenanceTypeText($type)
    {
        return match($type) {
            'preventive' => 'Preventif',
            'corrective' => 'Korektif',
            'emergency' => 'Darurat',
            'upgrade' => 'Upgrade',
            default => ucfirst($type)
        };
    }

    /**
     * Get priority text
     */
    private function getPriorityText($priority)
    {
        return match($priority) {
            'low' => 'Rendah',
            'medium' => 'Sedang',
            'high' => 'Tinggi',
            'critical' => 'Kritis',
            default => ucfirst($priority)
        };
    }

    /**
     * Get status text
     */
    private function getStatusText($status)
    {
        return match($status) {
            'scheduled' => 'Terjadwal',
            'in_progress' => 'Sedang Berlangsung',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
            default => ucfirst($status)
        };
    }
}
