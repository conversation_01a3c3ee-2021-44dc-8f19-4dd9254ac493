<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QR Labels - Multiple Assets</title>
    <style>
        @page {
            margin: 10mm;
            size: A4 portrait;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: {{ $config->font_family }}, Arial, sans-serif;
        }

        .labels-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5mm;
            justify-content: flex-start;
            align-content: flex-start;
        }

        .qr-label {
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            border: 1px solid #000;
            background: white;
            break-inside: avoid;
            page-break-inside: avoid;
            position: relative;
            display: flex;
            align-items: center;
            padding: {{ $config->margin_top }}mm {{ $config->margin_right }}mm {{ $config->margin_bottom }}mm {{ $config->margin_left }}mm;
        }

        .qr-section {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1mm;
            flex-shrink: 0;
        }

        .qr-code-img {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            display: block;
        }

        .separator {
            width: 1px;
            height: {{ $config->qr_size }}mm;
            background-color: #000;
            margin: 0 1.5mm;
            flex-shrink: 0;
        }

        .info-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100%;
            line-height: 1.2;
        }

        .asset-name {
            font-size: {{ $config->font_size_title }}pt;
            font-weight: bold;
            margin-bottom: 1.5mm;
            word-wrap: break-word;
            overflow-wrap: break-word;
            color: #000;
            text-transform: uppercase;
        }

        .asset-code {
            font-size: {{ $config->font_size_content + 1 }}pt;
            font-weight: bold;
            margin-bottom: 1mm;
            color: #000;
            letter-spacing: 0.5px;
        }

        .asset-info {
            font-size: {{ $config->font_size_content }}pt;
            margin-bottom: 0.8mm;
            word-wrap: break-word;
            overflow-wrap: break-word;
            color: #333;
        }

        .asset-info:last-child {
            margin-bottom: 0;
        }

        .asset-info.category {
            font-style: italic;
            color: #666;
        }

        .asset-info.branch {
            font-weight: 600;
            color: #000;
        }

        /* Calculate how many labels fit per row */
        @php
            $pageWidth = 210 - 20; // A4 width minus margins
            $labelWidthWithGap = $config->width + 5; // label width + gap
            $labelsPerRow = floor($pageWidth / $labelWidthWithGap);
        @endphp

        .qr-label:nth-child({{ $labelsPerRow }}n+1) {
            clear: left;
        }
    </style>
</head>
<body>
    <div class="labels-container">
        @foreach($labelsData as $labelData)
            <div class="qr-label">
                <div class="qr-section">
                    <img src="{{ $labelData['qrCodeBase64'] }}" alt="QR Code" class="qr-code-img">
                </div>
                <div class="separator"></div>
                <div class="info-section">
                    @if($config->show_asset_name)
                        <div class="asset-name">{{ Str::limit($labelData['asset']->name, 30) }}</div>
                    @endif

                    @if($config->show_asset_code)
                        <div class="asset-code">{{ $labelData['asset']->asset_code }}</div>
                    @endif

                    @if($config->show_category && $labelData['asset']->assetCategory)
                        <div class="asset-info category">{{ Str::limit($labelData['asset']->assetCategory->name, 25) }}</div>
                    @endif

                    @if($config->show_branch && $labelData['asset']->branch)
                        <div class="asset-info branch">{{ Str::limit($labelData['asset']->branch->name, 25) }}</div>
                    @endif

                    @if($config->show_location && isset($labelData['asset']->dynamic_fields['location']))
                        <div class="asset-info">{{ Str::limit($labelData['asset']->dynamic_fields['location'], 25) }}</div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</body>
</html>
