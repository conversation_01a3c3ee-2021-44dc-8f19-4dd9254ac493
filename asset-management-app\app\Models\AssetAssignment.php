<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssetAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'asset_id',
        'employee_id',
        'assigned_by',
        'assigned_at',
        'returned_at',
        'returned_by',
        'status',
        'assignment_notes',
        'return_notes',
        'condition_when_assigned',
        'condition_when_returned'
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'returned_at' => 'datetime',
    ];

    protected $dates = [
        'assigned_at',
        'returned_at',
    ];

    // Relationships
    public function asset()
    {
        return $this->belongsTo(Asset::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function returnedBy()
    {
        return $this->belongsTo(User::class, 'returned_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeReturned($query)
    {
        return $query->where('status', 'returned');
    }

    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeByAsset($query, $assetId)
    {
        return $query->where('asset_id', $assetId);
    }

    // Accessors
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'active' => 'Aktif',
            'returned' => 'Dikembalikan',
            'transferred' => 'Dipindahkan',
            default => ucfirst($this->status)
        };
    }

    public function getConditionWhenAssignedTextAttribute()
    {
        return match($this->condition_when_assigned) {
            'excellent' => 'Sangat Baik',
            'good' => 'Baik',
            'fair' => 'Cukup',
            'poor' => 'Buruk',
            default => ucfirst($this->condition_when_assigned)
        };
    }

    public function getConditionWhenReturnedTextAttribute()
    {
        return match($this->condition_when_returned) {
            'excellent' => 'Sangat Baik',
            'good' => 'Baik',
            'fair' => 'Cukup',
            'poor' => 'Buruk',
            default => $this->condition_when_returned ? ucfirst($this->condition_when_returned) : '-'
        };
    }



    public function getDurationAttribute()
    {
        if ($this->returned_at) {
            return $this->assigned_at->diffForHumans($this->returned_at, true);
        }

        return $this->assigned_at->diffForHumans(now(), true);
    }

    // Methods
    public function returnAsset($returnedBy, $returnNotes = null, $conditionWhenReturned = null)
    {
        $this->update([
            'status' => 'returned',
            'returned_at' => now(),
            'returned_by' => $returnedBy,
            'return_notes' => $returnNotes,
            'condition_when_returned' => $conditionWhenReturned
        ]);

        // Update asset status
        $this->asset->update([
            'assigned_to' => null,
            'assigned_at' => null,
            'assignment_status' => 'returned'
        ]);
    }
}
