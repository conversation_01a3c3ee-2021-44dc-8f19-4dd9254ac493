@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Asset - Asset Management System')

@section('content')
<style>
.field-group-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #696cff;
}

.field-group-title {
  color: #566a7f;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.info-item {
  margin-bottom: 1rem;
}

.info-label {
  font-weight: 600;
  color: #566a7f;
  margin-bottom: 0.25rem;
}

.info-value {
  color: #697a8d;
}

.asset-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.asset-image-placeholder {
  padding: 3rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e7e7ff;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-eye-line me-2"></i>
              Detail Asset
            </h5>
            <small class="text-muted">{{ $asset->name }}</small>
          </div>
          <div class="d-flex gap-2">
            <a href="{{ route('assets.edit', $asset) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>
              Edit Asset
            </a>
            <a href="{{ route('assets.view-all') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Asset Details -->
  <div class="row">
    <!-- Main Information -->
    <div class="col-lg-8">
      <!-- Core Information -->
      <div class="field-group-section">
        <h6 class="field-group-title">
          <i class="ri-information-line me-2"></i>
          Informasi Dasar
        </h6>
        
        <div class="row">
          <div class="col-md-6">
            <div class="info-item">
              <div class="info-label">Kode Asset</div>
              <div class="info-value">
                <span class="badge bg-primary fs-6">{{ $asset->asset_code }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-item">
              <div class="info-label">Nama Asset</div>
              <div class="info-value">{{ $asset->name }}</div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-4">
            <div class="info-item">
              <div class="info-label">Kategori</div>
              <div class="info-value">
                <span class="badge bg-info">{{ $asset->assetCategory->name ?? '-' }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-item">
              <div class="info-label">Tipe Asset</div>
              <div class="info-value">{{ $asset->assetType->name ?? '-' }}</div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-item">
              <div class="info-label">Cabang</div>
              <div class="info-value">{{ $asset->branch->name ?? '-' }}</div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-item">
              <div class="info-label">Supplier</div>
              <div class="info-value">
                @if($asset->supplier)
                  <div>
                    <strong>{{ $asset->supplier->name }}</strong>
                    <br>
                    <small class="text-muted">{{ $asset->supplier->supplier_code }}</small>
                    @if($asset->supplier->company_name)
                      <br>
                      <small class="text-muted">{{ $asset->supplier->company_name }}</small>
                    @endif
                  </div>
                @else
                  <span class="text-muted">-</span>
                @endif
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-item">
              <div class="info-label">Status</div>
              <div class="info-value">
                @switch($asset->status)
                  @case('active')
                    <span class="badge bg-success">Active</span>
                    @break
                  @case('inactive')
                    <span class="badge bg-secondary">Inactive</span>
                    @break
                  @case('maintenance')
                    <span class="badge bg-warning">Maintenance</span>
                    @break
                  @case('disposed')
                    <span class="badge bg-danger">Disposed</span>
                    @break
                  @default
                    <span class="badge bg-secondary">{{ ucfirst($asset->status) }}</span>
                @endswitch
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-item">
              <div class="info-label">Tanggal Dibuat</div>
              <div class="info-value">{{ $asset->created_at->format('d/m/Y H:i') }}</div>
            </div>
          </div>
        </div>

        @if($asset->description)
          <div class="info-item">
            <div class="info-label">Deskripsi</div>
            <div class="info-value">{{ $asset->description }}</div>
          </div>
        @endif
      </div>

      <!-- Assignment Information -->
      <div class="field-group-section">
        <h6 class="field-group-title">
          <i class="ri-user-settings-line me-2"></i>
          Informasi Assignment
        </h6>

        <div class="row">
          <div class="col-md-6">
            <div class="info-item">
              <div class="info-label">Status Assignment</div>
              <div class="info-value">
                @if($asset->assigned_to)
                  <span class="badge bg-success">
                    <i class="ri-user-line me-1"></i>
                    Sedang Di-assign
                  </span>
                @else
                  <span class="badge bg-secondary">
                    <i class="ri-checkbox-circle-line me-1"></i>
                    Tersedia
                  </span>
                @endif
              </div>
            </div>
          </div>
          @if($asset->assigned_to)
            <div class="col-md-6">
              <div class="info-item">
                <div class="info-label">Tanggal Assignment</div>
                <div class="info-value">{{ $asset->assigned_at ? $asset->assigned_at->format('d/m/Y H:i') : '-' }}</div>
              </div>
            </div>
          @endif
        </div>

        @if($asset->assigned_to)
          <div class="row">
            <div class="col-md-6">
              <div class="info-item">
                <div class="info-label">Karyawan</div>
                <div class="info-value">
                  @if($asset->assignedEmployee)
                    <div class="d-flex align-items-center">
                      <div class="avatar avatar-sm me-2">
                        <span class="avatar-initial rounded bg-label-info">
                          {{ strtoupper(substr($asset->assignedEmployee->full_name, 0, 2)) }}
                        </span>
                      </div>
                      <div>
                        <div class="fw-bold">{{ $asset->assignedEmployee->full_name }}</div>
                        <small class="text-muted">{{ $asset->assignedEmployee->nik }}</small>
                      </div>
                    </div>
                  @else
                    -
                  @endif
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="info-item">
                <div class="info-label">Durasi Assignment</div>
                <div class="info-value">
                  @if($asset->assigned_at)
                    {{ $asset->assigned_at->diffForHumans(now(), true) }}
                  @else
                    -
                  @endif
                </div>
              </div>
            </div>
          </div>

          @if($asset->assignment_notes)
            <div class="info-item">
              <div class="info-label">Catatan Assignment</div>
              <div class="info-value">
                <div class="alert alert-info mb-0">
                  <i class="ri-information-line me-2"></i>
                  {{ $asset->assignment_notes }}
                </div>
              </div>
            </div>
          @endif

          <!-- Quick Assignment Actions -->
          <div class="info-item">
            <div class="info-label">Aksi Assignment</div>
            <div class="info-value">
              <div class="d-flex gap-2">
                @php
                  $currentAssignment = $asset->currentAssignment;
                @endphp
                @if($currentAssignment)
                  <a href="{{ route('asset-assignments.show', $currentAssignment) }}" class="btn btn-sm btn-outline-primary">
                    <i class="ri-eye-line me-1"></i>
                    Detail Assignment
                  </a>
                  <a href="{{ route('asset-assignments.return-form', $currentAssignment) }}" class="btn btn-sm btn-outline-warning">
                    <i class="ri-arrow-go-back-line me-1"></i>
                    Return Asset
                  </a>
                  <a href="{{ route('asset-assignments.transfer-form', $currentAssignment) }}" class="btn btn-sm btn-outline-info">
                    <i class="ri-exchange-line me-1"></i>
                    Transfer Asset
                  </a>
                @else
                  <a href="{{ route('asset-assignments.create', ['asset_id' => $asset->id]) }}" class="btn btn-sm btn-outline-success">
                    <i class="ri-user-add-line me-1"></i>
                    Assign ke Karyawan
                  </a>
                @endif
              </div>
            </div>
          </div>
        @else
          <!-- Quick Assign Action for Available Assets -->
          <div class="info-item">
            <div class="info-label">Aksi</div>
            <div class="info-value">
              <a href="{{ route('asset-assignments.create', ['asset_id' => $asset->id]) }}" class="btn btn-sm btn-success">
                <i class="ri-user-add-line me-1"></i>
                Assign Asset ke Karyawan
              </a>
            </div>
          </div>
        @endif
      </div>

      <!-- Dynamic Fields -->
      @if($asset->dynamic_fields && count($asset->dynamic_fields) > 0)
        @php
          $fieldConfigurations = \App\Models\AssetFieldConfiguration::forCategory($asset->asset_category_id)
              ->active()
              ->ordered()
              ->get()
              ->groupBy('field_group');
          $fieldGroups = \App\Models\AssetFieldConfiguration::getFieldGroups();
        @endphp

        @foreach($fieldConfigurations as $groupKey => $fields)
          @php
            $groupName = $fieldGroups[$groupKey] ?? ucwords(str_replace('_', ' ', $groupKey));
            
            // Get group metadata from lookup
            $groupLookup = \App\Models\Lookup::where('lookup_code', 'FIELD_GROUP')
                ->where('is_active', true)
                ->whereJsonContains('metadata->value', $groupKey)
                ->first();
            
            $groupIcon = $groupLookup->metadata['icon'] ?? 'ri-settings-4-line';
            
            // Check if group has any data
            $hasData = false;
            foreach ($fields as $field) {
              if (isset($asset->dynamic_fields[$field->field_name]) && !empty($asset->dynamic_fields[$field->field_name])) {
                $hasData = true;
                break;
              }
            }
          @endphp

          @if($hasData)
            <div class="field-group-section">
              <h6 class="field-group-title">
                <i class="{{ $groupIcon }} me-2"></i>
                {{ $groupName }}
              </h6>
              
              <div class="row">
                @foreach($fields as $field)
                  @if(isset($asset->dynamic_fields[$field->field_name]) && !empty($asset->dynamic_fields[$field->field_name]))
                    <div class="{{ $field->getColumnClass() }}">
                      <div class="info-item">
                        <div class="info-label">{{ $field->field_label }}</div>
                        <div class="info-value">
                          @if($field->field_type === 'file')
                            @if($asset->dynamic_fields[$field->field_name])
                              <a href="{{ Storage::url($asset->dynamic_fields[$field->field_name]) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="ri-download-line me-1"></i>
                                Download File
                              </a>
                            @else
                              -
                            @endif
                          @elseif($field->field_type === 'checkbox')
                            @if(is_array($asset->dynamic_fields[$field->field_name]))
                              @foreach($asset->dynamic_fields[$field->field_name] as $value)
                                <span class="badge bg-secondary me-1">{{ $value }}</span>
                              @endforeach
                            @else
                              {{ $asset->dynamic_fields[$field->field_name] }}
                            @endif
                          @elseif($field->field_type === 'url')
                            <a href="{{ $asset->dynamic_fields[$field->field_name] }}" target="_blank">
                              {{ $asset->dynamic_fields[$field->field_name] }}
                            </a>
                          @elseif($field->field_type === 'email')
                            <a href="mailto:{{ $asset->dynamic_fields[$field->field_name] }}">
                              {{ $asset->dynamic_fields[$field->field_name] }}
                            </a>
                          @else
                            {{ $asset->dynamic_fields[$field->field_name] }}
                          @endif
                        </div>
                      </div>
                    </div>
                  @endif
                @endforeach
              </div>
            </div>
          @endif
        @endforeach
      @endif
    </div>

    <!-- Sidebar Information -->
    <div class="col-lg-4">
      <!-- Asset Image -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-image-line me-2"></i>
            Foto Asset
          </h6>
        </div>
        <div class="card-body text-center">
          @if($asset->image)
            <img src="{{ Storage::url($asset->image) }}" alt="{{ $asset->name }}" class="asset-image">
          @else
            <div class="asset-image-placeholder">
              <i class="ri-image-line ri-48px text-muted mb-3"></i>
              <p class="text-muted mb-0">Belum ada foto asset</p>
              <small class="text-muted">Upload foto melalui halaman edit</small>
            </div>
          @endif
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-tools-line me-2"></i>
            Quick Actions
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('assets.edit', $asset) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-2"></i>
              Edit Asset
            </a>
            <div class="dropdown">
              <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="ri-qr-code-line me-2"></i>
                Cetak QR Label
              </button>
              <ul class="dropdown-menu">
                @php
                  $qrConfigs = \App\Models\QrLabelConfiguration::active()->get();
                @endphp
                @foreach($qrConfigs as $config)
                  <li>
                    <a class="dropdown-item" href="{{ route('assets.qr-label', ['asset' => $asset, 'config_id' => $config->id]) }}" target="_blank">
                      <i class="ri-printer-line me-2"></i>
                      {{ $config->name }}
                      @if($config->is_default)
                        <span class="badge bg-success ms-1">Default</span>
                      @endif
                    </a>
                  </li>
                @endforeach
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item" href="{{ route('qr-labels.index') }}">
                    <i class="ri-settings-4-line me-2"></i>
                    Kelola Konfigurasi
                  </a>
                </li>
              </ul>
            </div>
            <button type="button" class="btn btn-outline-success">
              <i class="ri-file-excel-line me-2"></i>
              Export Data
            </button>
            <hr>
            <form method="POST" action="{{ route('assets.destroy', $asset) }}" 
                  onsubmit="return confirm('Apakah Anda yakin ingin menghapus asset ini?')">
              @csrf
              @method('DELETE')
              <button type="submit" class="btn btn-outline-danger w-100">
                <i class="ri-delete-bin-line me-2"></i>
                Hapus Asset
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Asset History -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-history-line me-2"></i>
            Riwayat Asset
          </h6>
        </div>
        <div class="card-body">
          <div class="timeline">
            <!-- Assignment History -->
            @php
              $assignmentHistory = $asset->assignmentHistory()->with(['employee', 'assignedBy', 'returnedBy'])->get();
            @endphp

            @foreach($assignmentHistory as $assignment)
              <!-- Assignment Event -->
              <div class="timeline-item">
                <div class="timeline-marker bg-primary"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title">
                    <i class="ri-user-add-line me-1"></i>
                    Asset Di-assign
                  </h6>
                  <p class="timeline-text mb-1">
                    Ke: <strong>{{ $assignment->employee->full_name }}</strong> ({{ $assignment->employee->nik }})
                  </p>
                  <p class="timeline-text mb-1">
                    Oleh: {{ $assignment->assignedBy->name }}
                  </p>
                  <p class="timeline-text mb-1">
                    Kondisi: <span class="badge bg-{{ $assignment->condition_when_assigned === 'excellent' ? 'success' : ($assignment->condition_when_assigned === 'good' ? 'info' : ($assignment->condition_when_assigned === 'fair' ? 'warning' : 'danger')) }} badge-sm">
                      {{ $assignment->condition_when_assigned_text }}
                    </span>
                  </p>
                  <small class="timeline-text">{{ $assignment->assigned_at->format('d/m/Y H:i') }}</small>
                  @if($assignment->assignment_notes)
                    <p class="timeline-text mt-1"><em>"{{ $assignment->assignment_notes }}"</em></p>
                  @endif
                </div>
              </div>

              <!-- Return/Transfer Event -->
              @if($assignment->returned_at)
                <div class="timeline-item">
                  <div class="timeline-marker bg-{{ $assignment->status === 'returned' ? 'warning' : 'info' }}"></div>
                  <div class="timeline-content">
                    <h6 class="timeline-title">
                      <i class="ri-{{ $assignment->status === 'returned' ? 'arrow-go-back' : 'exchange' }}-line me-1"></i>
                      Asset {{ $assignment->status === 'returned' ? 'Dikembalikan' : 'Dipindahkan' }}
                    </h6>
                    <p class="timeline-text mb-1">
                      Dari: <strong>{{ $assignment->employee->full_name }}</strong> ({{ $assignment->employee->nik }})
                    </p>
                    @if($assignment->returnedBy)
                      <p class="timeline-text mb-1">
                        Oleh: {{ $assignment->returnedBy->name }}
                      </p>
                    @endif
                    @if($assignment->condition_when_returned)
                      <p class="timeline-text mb-1">
                        Kondisi: <span class="badge bg-{{ $assignment->condition_when_returned === 'excellent' ? 'success' : ($assignment->condition_when_returned === 'good' ? 'info' : ($assignment->condition_when_returned === 'fair' ? 'warning' : 'danger')) }} badge-sm">
                          {{ $assignment->condition_when_returned_text }}
                        </span>
                      </p>
                    @endif
                    <small class="timeline-text">{{ $assignment->returned_at->format('d/m/Y H:i') }}</small>
                    @if($assignment->return_notes)
                      <p class="timeline-text mt-1"><em>"{{ $assignment->return_notes }}"</em></p>
                    @endif
                  </div>
                </div>
              @endif
            @endforeach

            <!-- Current Assignment Status -->
            @if($asset->assigned_to)
              <div class="timeline-item">
                <div class="timeline-marker bg-success"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title">
                    <i class="ri-user-settings-line me-1"></i>
                    Status Saat Ini
                  </h6>
                  <p class="timeline-text mb-1">
                    <strong>Sedang di-assign ke:</strong> {{ $asset->assignedEmployee->full_name ?? 'N/A' }}
                  </p>
                  @if($asset->assigned_at)
                    <small class="timeline-text">Sejak: {{ $asset->assigned_at->format('d/m/Y H:i') }}</small>
                  @endif
                </div>
              </div>
            @else
              <div class="timeline-item">
                <div class="timeline-marker bg-secondary"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title">
                    <i class="ri-checkbox-circle-line me-1"></i>
                    Status Saat Ini
                  </h6>
                  <p class="timeline-text">Asset tersedia untuk di-assign</p>
                </div>
              </div>
            @endif

            <!-- Asset Creation -->
            <div class="timeline-item">
              <div class="timeline-marker bg-success"></div>
              <div class="timeline-content">
                <h6 class="timeline-title">
                  <i class="ri-add-circle-line me-1"></i>
                  Asset Dibuat
                </h6>
                <small class="timeline-text">{{ $asset->created_at->format('d/m/Y H:i') }}</small>
              </div>
            </div>

            @if($asset->updated_at != $asset->created_at)
              <div class="timeline-item">
                <div class="timeline-marker bg-info"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title">
                    <i class="ri-edit-line me-1"></i>
                    Terakhir Diupdate
                  </h6>
                  <small class="timeline-text">{{ $asset->updated_at->format('d/m/Y H:i') }}</small>
                </div>
              </div>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.timeline {
  position: relative;
  padding-left: 1.5rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e7e7ff;
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
}

.timeline-marker {
  position: absolute;
  left: -1rem;
  top: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 2px solid #fff;
}

.timeline-content {
  margin-left: 1rem;
}

.timeline-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.timeline-text {
  font-size: 0.75rem;
  color: #8592a3;
  margin-bottom: 0;
}
</style>

@endsection
