@extends('layouts.contentNavbarLayout')

@section('title', 'Daftar Asset - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Asset Management /</span> Daftar Asset
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('assets.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah Asset
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('assets.index') }}">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Pencarian</label>
            <input type="text" class="form-control" name="search" value="{{ request('search') }}" placeholder="Nama, kode, brand, model...">
          </div>
          <div class="col-md-2">
            <label class="form-label">Kategori</label>
            <select class="form-select" name="category">
              <option value="">Semua Kategori</option>
              @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                  {{ $category->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Status</label>
            <select class="form-select" name="status">
              <option value="">Semua Status</option>
              <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
              <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Tidak Aktif</option>
              <option value="maintenance" {{ request('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
              <option value="disposed" {{ request('status') == 'disposed' ? 'selected' : '' }}>Disposed</option>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Assignment</label>
            <select class="form-select" name="assignment_status">
              <option value="">Semua</option>
              <option value="assigned" {{ request('assignment_status') == 'assigned' ? 'selected' : '' }}>Di-assign</option>
              <option value="available" {{ request('assignment_status') == 'available' ? 'selected' : '' }}>Tersedia</option>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Kondisi</label>
            <select class="form-select" name="condition">
              <option value="">Semua Kondisi</option>
              <option value="excellent" {{ request('condition') == 'excellent' ? 'selected' : '' }}>Excellent</option>
              <option value="good" {{ request('condition') == 'good' ? 'selected' : '' }}>Good</option>
              <option value="fair" {{ request('condition') == 'fair' ? 'selected' : '' }}>Fair</option>
              <option value="poor" {{ request('condition') == 'poor' ? 'selected' : '' }}>Poor</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('assets.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Assets Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Daftar Asset ({{ $assets->total() }} item)</h5>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-success btn-sm">
          <i class="ri-file-excel-line me-1"></i>Export Excel
        </button>
        <button class="btn btn-outline-danger btn-sm">
          <i class="ri-file-pdf-line me-1"></i>Export PDF
        </button>
      </div>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Kode Asset</th>
            <th>Nama</th>
            <th>Kategori</th>
            <th>Cabang</th>
            <th>Brand/Model</th>
            <th>Status</th>
            <th>Assignment</th>
            <th>Kondisi</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($assets as $asset)
          <tr>
            <td>
              <strong>{{ $asset->asset_code }}</strong>
            </td>
            <td>
              <div class="d-flex align-items-center">
                @if($asset->image)
                  <img src="{{ asset('storage/' . $asset->image) }}" alt="{{ $asset->name }}" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                @else
                  <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="ri-image-line text-muted"></i>
                  </div>
                @endif
                <div>
                  <strong>{{ $asset->name }}</strong>
                  @if($asset->serial_number)
                    <br><small class="text-muted">SN: {{ $asset->serial_number }}</small>
                  @endif
                </div>
              </div>
            </td>
            <td>
              <span class="badge bg-info">{{ $asset->category->name }}</span>
            </td>
            <td>{{ $asset->branch->name }}</td>
            <td>
              @if($asset->brand || $asset->model)
                {{ $asset->brand }}
                @if($asset->brand && $asset->model) - @endif
                {{ $asset->model }}
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              <span class="badge bg-{{ $asset->status_badge }}">
                {{ ucfirst($asset->status) }}
              </span>
            </td>
            <td>
              @if($asset->assigned_to)
                <div class="d-flex align-items-center">
                  <div class="avatar avatar-xs me-2">
                    <span class="avatar-initial rounded bg-label-success">
                      <i class="ri-user-line ri-12px"></i>
                    </span>
                  </div>
                  <div>
                    <span class="badge bg-success">Di-assign</span>
                    @if($asset->assignedEmployee)
                      <br><small class="text-muted">{{ $asset->assignedEmployee->full_name }}</small>
                    @endif
                  </div>
                </div>
              @else
                <div class="d-flex align-items-center">
                  <div class="avatar avatar-xs me-2">
                    <span class="avatar-initial rounded bg-label-secondary">
                      <i class="ri-checkbox-circle-line ri-12px"></i>
                    </span>
                  </div>
                  <span class="badge bg-secondary">Tersedia</span>
                </div>
              @endif
            </td>
            <td>
              <span class="badge bg-{{ $asset->condition_badge }}">
                {{ ucfirst($asset->condition) }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('assets.show', $asset) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('assets.edit', $asset) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  <div class="dropdown-divider"></div>
                  <form action="{{ route('assets.destroy', $asset) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus asset ini?')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="8" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-inbox-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada data asset</h6>
                <p class="text-muted mb-3">Belum ada asset yang terdaftar atau sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('assets.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah Asset Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($assets->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $assets->firstItem() }} - {{ $assets->lastItem() }} dari {{ $assets->total() }} data
        </div>
        {{ $assets->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Show success toast
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">
          {{ session('success') }}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    setTimeout(() => {
      toast.remove();
    }, 5000);
  });
</script>
@endif
@endsection
