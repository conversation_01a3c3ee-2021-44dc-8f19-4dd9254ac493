<?php

use App\Helpers\TerbilangHelper;

if (!function_exists('terbilang')) {
    /**
     * Convert number to Indonesian words (terbilang)
     * 
     * @param int|float $number
     * @return string
     */
    function terbilang($number)
    {
        return TerbilangHelper::convert($number);
    }
}

if (!function_exists('terbilang_rupiah')) {
    /**
     * Convert number to rupiah terbilang
     * 
     * @param int|float $number
     * @return string
     */
    function terbilang_rupiah($number)
    {
        return TerbilangHelper::getTerbilang($number);
    }
}

if (!function_exists('format_currency_terbilang')) {
    /**
     * Format currency with terbilang
     * 
     * @param int|float $number
     * @param string $currency
     * @return array
     */
    function format_currency_terbilang($number, $currency = 'Rupiah')
    {
        return TerbilangHelper::formatCurrency($number, $currency);
    }
}
