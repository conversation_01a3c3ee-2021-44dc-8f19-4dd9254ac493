@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah Asset - Asset Management System')

@section('content')
<style>
.field-group-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #696cff;
}

.field-group-title {
  color: #566a7f;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.loading-spinner {
  display: none;
  text-align: center;
  padding: 2rem;
  color: #8592a3;
}

.dynamic-fields-container {
  min-height: 100px;
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-add-line me-2"></i>
              Tambah Asset Baru
            </h5>
            <small class="text-muted">Tambahkan asset baru ke dalam sistem</small>
          </div>
          <div>
            <a href="{{ route('assets.view-all') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="POST" action="{{ route('assets.store') }}" enctype="multipart/form-data">
            @csrf
            
            <!-- Core Information -->
            <div class="field-group-section">
              <h6 class="field-group-title">
                <i class="ri-information-line me-2"></i>
                Informasi Dasar (Core Fields)
              </h6>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="name" class="form-label">Nama Asset <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                           id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="asset_code" class="form-label">Kode Asset</label>
                    <input type="text" class="form-control @error('asset_code') is-invalid @enderror" 
                           id="asset_code" name="asset_code" value="{{ old('asset_code') }}" readonly>
                    <div class="form-text">Kode akan di-generate otomatis</div>
                    @error('asset_code')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label for="asset_category_id" class="form-label">Kategori Asset <span class="text-danger">*</span></label>
                    <select class="form-select @error('asset_category_id') is-invalid @enderror" 
                            id="asset_category_id" name="asset_category_id" required>
                      <option value="">Pilih Kategori</option>
                      @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ old('asset_category_id') == $category->id ? 'selected' : '' }}>
                          {{ $category->name }}
                        </option>
                      @endforeach
                    </select>
                    @error('asset_category_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label for="asset_type_id" class="form-label">Tipe Asset</label>
                    <select class="form-select @error('asset_type_id') is-invalid @enderror" 
                            id="asset_type_id" name="asset_type_id">
                      <option value="">Pilih Tipe Asset</option>
                      @foreach($assetTypes as $type)
                        <option value="{{ $type->id }}" data-category="{{ $type->asset_category_id }}" 
                                {{ old('asset_type_id') == $type->id ? 'selected' : '' }}>
                          {{ $type->name }}
                        </option>
                      @endforeach
                    </select>
                    @error('asset_type_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label for="branch_id" class="form-label">Cabang <span class="text-danger">*</span></label>
                    <select class="form-select @error('branch_id') is-invalid @enderror" 
                            id="branch_id" name="branch_id" required>
                      <option value="">Pilih Cabang</option>
                      @foreach($branches as $branch)
                        <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                          {{ $branch->name }}
                        </option>
                      @endforeach
                    </select>
                    @error('branch_id')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <!-- Supplier Field -->
              <div class="row">
                <div class="col-12">
                  <div class="mb-3">
                    <label for="supplier_display" class="form-label">Supplier</label>
                    <div class="input-group">
                      <input type="text" class="form-control @error('supplier_id') is-invalid @enderror"
                             id="supplier_display" name="supplier_display"
                             placeholder="Klik tombol untuk memilih supplier..." readonly>
                      <button type="button" class="btn btn-outline-primary" id="supplier_lookup_btn">
                        <i class="ri-search-line me-1"></i>
                        Cari Supplier
                      </button>
                      <button type="button" class="btn btn-outline-danger" id="supplier_clear" style="display: none;">
                        <i class="ri-close-line me-1"></i>
                        Hapus
                      </button>
                    </div>
                    <input type="hidden" id="supplier_id" name="supplier_id" value="{{ old('supplier_id') }}">
                    @error('supplier_id')
                      <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                    <div class="form-text">
                      <i class="ri-information-line me-1"></i>
                      Pilih supplier yang menyediakan asset ini (opsional)
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select class="form-select @error('status') is-invalid @enderror" 
                            id="status" name="status" required>
                      <option value="">Pilih Status</option>
                      <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                      <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                      <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                      <option value="disposed" {{ old('status') == 'disposed' ? 'selected' : '' }}>Disposed</option>
                    </select>
                    @error('status')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="description" class="form-label">Deskripsi</label>
                    <textarea class="form-control @error('description') is-invalid @enderror"
                              id="description" name="description" rows="3">{{ old('description') }}</textarea>
                    @error('description')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="image" class="form-label">Foto Asset</label>
                    <input type="file" class="form-control @error('image') is-invalid @enderror"
                           id="image" name="image" accept="image/*">
                    <div class="form-text">Upload foto asset (JPG, PNG, GIF - Max: 2MB)</div>
                    @error('image')
                      <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                  </div>
                </div>
              </div>
            </div>

            <!-- Dynamic Fields Container -->
            <div class="dynamic-fields-container" id="dynamic-fields-container">
              <div class="loading-spinner" id="loading-spinner">
                <i class="ri-loader-4-line ri-spin ri-24px mb-2"></i>
                <p class="mb-0">Loading field configurations...</p>
              </div>
              
              <div class="text-center text-muted" id="no-fields-message" style="display: none;">
                <i class="ri-information-line ri-24px mb-2"></i>
                <p class="mb-0">Pilih kategori asset untuk menampilkan field tambahan</p>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('assets.index') }}" class="btn btn-outline-secondary">
                Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                Simpan Asset
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const categorySelect = document.getElementById('asset_category_id');
  const typeSelect = document.getElementById('asset_type_id');
  const dynamicFieldsContainer = document.getElementById('dynamic-fields-container');
  const loadingSpinner = document.getElementById('loading-spinner');
  const noFieldsMessage = document.getElementById('no-fields-message');

  // Load dynamic fields when category changes
  categorySelect.addEventListener('change', function() {
    const categoryId = this.value;
    
    // Filter asset types based on category
    filterAssetTypes(categoryId);
    
    // Load dynamic fields
    loadDynamicFields(categoryId);
  });

  function filterAssetTypes(categoryId) {
    const options = typeSelect.querySelectorAll('option');
    
    // Reset type selection
    typeSelect.value = '';
    
    options.forEach(option => {
      if (option.value === '') {
        option.style.display = 'block';
        return;
      }
      
      const optionCategory = option.dataset.category;
      if (!categoryId || optionCategory === categoryId) {
        option.style.display = 'block';
      } else {
        option.style.display = 'none';
      }
    });
  }

  function loadDynamicFields(categoryId) {
    // Clear previous content
    const existingFields = dynamicFieldsContainer.querySelectorAll('.field-group-section:not(.core-fields)');
    existingFields.forEach(field => field.remove());
    
    if (!categoryId) {
      noFieldsMessage.style.display = 'block';
      loadingSpinner.style.display = 'none';
      return;
    }

    // Show loading
    loadingSpinner.style.display = 'block';
    noFieldsMessage.style.display = 'none';

    // Fetch field configurations
    fetch(`{{ route('assets.field-configurations') }}?category_id=${categoryId}`)
      .then(response => response.json())
      .then(data => {
        loadingSpinner.style.display = 'none';
        
        if (data.success && data.html) {
          dynamicFieldsContainer.insertAdjacentHTML('beforeend', data.html);
          
          // Initialize any special field behaviors
          initializeDynamicFields();
        } else {
          noFieldsMessage.style.display = 'block';
        }
      })
      .catch(error => {
        console.error('Error loading field configurations:', error);
        loadingSpinner.style.display = 'none';
        dynamicFieldsContainer.insertAdjacentHTML('beforeend', `
          <div class="alert alert-danger">
            <i class="ri-error-warning-line me-2"></i>
            Error loading field configurations. Please try again.
          </div>
        `);
      });
  }

  function initializeDynamicFields() {
    // Initialize dependent dropdowns
    const dependentFields = document.querySelectorAll('[data-parent-field]');
    dependentFields.forEach(field => {
      const parentFieldName = field.dataset.parentField;
      const parentField = document.querySelector(`[name="${parentFieldName}"]`);
      
      if (parentField) {
        parentField.addEventListener('change', function() {
          loadDependentOptions(field, this.value);
        });
      }
    });
  }

  function loadDependentOptions(field, parentValue) {
    const fieldId = field.dataset.fieldId;
    
    if (!parentValue) {
      field.innerHTML = '<option value="">Pilih ' + field.dataset.label + '</option>';
      return;
    }

    // Show loading in select
    field.innerHTML = '<option value="">Loading...</option>';

    // Fetch dependent options
    fetch(`{{ route('master.asset-field-configurations.dependent-options') }}?field_id=${fieldId}&parent_value=${parentValue}`)
      .then(response => response.json())
      .then(data => {
        field.innerHTML = '<option value="">Pilih ' + field.dataset.label + '</option>';
        
        if (data.success && data.options) {
          Object.entries(data.options).forEach(([key, value]) => {
            field.innerHTML += `<option value="${key}">${value}</option>`;
          });
        }
      })
      .catch(error => {
        console.error('Error loading dependent options:', error);
        field.innerHTML = '<option value="">Error loading options</option>';
      });
  }

  // Initialize on page load if category is already selected
  if (categorySelect.value) {
    filterAssetTypes(categorySelect.value);
    loadDynamicFields(categorySelect.value);
  }

  // Initialize Supplier Lookup
  supplierLookup = new SupplierLookup({
    inputId: 'supplier_id',
    displayId: 'supplier_display',
    buttonId: 'supplier_lookup_btn',
    modalId: 'supplierLookupModal',
    apiUrl: '{{ route("api.suppliers.lookup") }}'
  });
});
</script>

<!-- Include Supplier Lookup Script -->
<script src="{{ asset('js/supplier-lookup.js') }}"></script>

@endsection
