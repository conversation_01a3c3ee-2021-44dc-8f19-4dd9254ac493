# Dashboard Permintaan Asset Terbaru

## Deskripsi
Fitur dashboard permintaan asset terbaru telah ditambahkan ke halaman dashboard utama dengan kontrol akses berdasarkan role pengguna.

## Fitur yang Ditambahkan

### 1. Dashboard Card "Permintaan Asset Terbaru"
- Menampilkan 5 permintaan asset terbaru
- Menampilkan statistik permintaan bulan ini
- Menampilkan status permintaan dengan badge berwarna
- Dropdown menu dengan opsi:
  - Refresh
  - Lihat Semua Permintaan
  - Buat Permintaan Baru

### 2. Role-Based Access Control
Dashboard permintaan asset hanya dapat dilihat oleh role tertentu:
- **Super Admin** - Dapat melihat semua permintaan
- **Admin** - Dapat melihat semua permintaan  
- **Manager** - Dapat melihat semua permintaan

Role lain (seperti User biasa) tidak akan melihat dashboard permintaan asset.

### 3. Statistik Tambahan
Ditambahkan 2 statistik baru di dashboard:
- **Pending Request** - Jumlah permintaan dengan status 'submitted' atau 'reviewed'
- **Total Requests This Month** - Total permintaan yang dibuat bulan ini

## Implementasi Teknis

### Controller Changes
File: `app/Http/Controllers/dashboard/Analytics.php`

```php
// Import model RequestAsset
use App\Models\RequestAsset;

// Logika role-based access
$canViewRequests = false;
if ($user->hasRole('super-admin') || $user->hasRole('admin') || $user->hasRole('manager')) {
    $canViewRequests = true;
    $latestRequests = RequestAsset::with(['requestedByUser', 'division'])
                                ->orderBy('created_at', 'desc')
                                ->limit(5)
                                ->get();
}

// Statistik tambahan
'pending_requests' => $canViewRequests ? RequestAsset::whereIn('status', ['submitted', 'reviewed'])->count() : 0,
'total_requests_this_month' => $canViewRequests ? RequestAsset::whereMonth('created_at', Carbon::now()->month)->count() : 0,
```

### View Changes
File: `resources/views/content/dashboard/dashboards-analytics.blade.php`

1. **Statistik Card Baru**:
   - Ditambahkan card "Pending Request" dengan icon file-list dan warna warning

2. **Dashboard Card Permintaan Asset**:
   - Layout responsive dengan col-xl-4 col-md-6
   - Conditional rendering dengan `@if($canViewRequests)`
   - Status badge dengan warna dinamis berdasarkan status

## Status Badge Colors
- **Approved** - Success (hijau)
- **Rejected** - Danger (merah)  
- **Submitted** - Warning (kuning)
- **Reviewed** - Info (biru)
- **Draft/Other** - Secondary (abu-abu)

## Layout Responsiveness
- Desktop (xl): 3 kolom (Welcome + 2 dashboard cards)
- Tablet (md): 2 kolom per baris
- Mobile: 1 kolom penuh

## Testing
1. Login dengan role admin/manager/super-admin → Dashboard permintaan asset terlihat
2. Login dengan role user biasa → Dashboard permintaan asset tidak terlihat
3. Klik "Lihat Semua Permintaan" → Redirect ke halaman index request-assets
4. Klik "Buat Permintaan Baru" → Redirect ke halaman create request-assets

## Routes yang Digunakan
- `route('request-assets.index')` - Halaman daftar permintaan
- `route('request-assets.create')` - Halaman buat permintaan baru

## Dependencies
- Model RequestAsset dengan relasi requestedByUser dan division
- Role system dengan method hasRole()
- Bootstrap untuk styling dan responsive grid
- Remix Icons untuk icon dashboard
