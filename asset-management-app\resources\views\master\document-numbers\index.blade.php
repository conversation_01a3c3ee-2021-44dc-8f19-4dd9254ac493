@extends('layouts.contentNavbarLayout')

@section('title', 'Master <PERSON> - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data /</span> Nomor Do<PERSON>men
  </h4>

  <!-- Filter Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Filter & Pencarian</h5>
      <a href="{{ route('master.document-numbers.create') }}" class="btn btn-primary">
        <i class="ri-add-line me-1"></i>Tambah Konfigurasi
      </a>
    </div>
    <div class="card-body">
      <form method="GET" action="{{ route('master.document-numbers.index') }}">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Tipe Dokumen</label>
            <select class="form-select" name="document_type">
              <option value="">Semua Tipe</option>
              @foreach($documentTypes as $type)
                <option value="{{ $type }}" {{ request('document_type') == $type ? 'selected' : '' }}>
                  {{ ucfirst($type) }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Cabang</label>
            <select class="form-select" name="branch_id">
              <option value="">Semua Cabang</option>
              @foreach($branches as $branch)
                <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                  {{ $branch->name }}
                </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">Tahun</label>
            <select class="form-select" name="year">
              <option value="">Semua Tahun</option>
              @for($year = date('Y'); $year >= 2020; $year--)
                <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                  {{ $year }}
                </option>
              @endfor
            </select>
          </div>
          <div class="col-md-4">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>Filter
              </button>
              <a href="{{ route('master.document-numbers.index') }}" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>Reset
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Document Numbers Table -->
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Konfigurasi Nomor Dokumen ({{ $documentNumbers->total() }} item)</h5>
    </div>
    <div class="table-responsive text-nowrap">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Tipe Dokumen</th>
            <th>Format</th>
            <th>Cabang</th>
            <th>Kategori</th>
            <th>Jenis Asset</th>
            <th>Tahun</th>
            <th>Nomor Terakhir</th>
            <th>Status</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody class="table-border-bottom-0">
          @forelse($documentNumbers as $docNumber)
          <tr>
            <td>
              <strong>{{ ucfirst($docNumber->document_type) }}</strong>
            </td>
            <td>
              <code>{{ $docNumber->format }}</code>
            </td>
            <td>
              {{ $docNumber->branch ? $docNumber->branch->name : 'Global' }}
              @if($docNumber->branch)
                <br><small class="text-muted">{{ $docNumber->branch->code }}</small>
              @endif
            </td>
            <td>
              @if($docNumber->category)
                <span class="badge bg-info">{{ $docNumber->category->name }}</span>
                <br><small class="text-muted">{{ $docNumber->category->code }}</small>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              @if($docNumber->assetType)
                <span class="badge bg-success">{{ $docNumber->assetType->name }}</span>
                <br><small class="text-muted">{{ $docNumber->assetType->code }}</small>
              @else
                <span class="text-muted">-</span>
              @endif
            </td>
            <td>
              <span class="badge bg-secondary">{{ $docNumber->year }}</span>
            </td>
            <td>
              <span class="badge bg-secondary">{{ str_pad($docNumber->current_number, 4, '0', STR_PAD_LEFT) }}</span>
            </td>
            <td>
              <span class="badge bg-{{ $docNumber->is_active ? 'success' : 'secondary' }}">
                {{ $docNumber->is_active ? 'Aktif' : 'Non-Aktif' }}
              </span>
            </td>
            <td>
              <div class="dropdown">
                <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                  <i class="ri-more-2-line"></i>
                </button>
                <div class="dropdown-menu">
                  <a class="dropdown-item" href="{{ route('master.document-numbers.show', $docNumber) }}">
                    <i class="ri-eye-line me-1"></i> Lihat Detail
                  </a>
                  <a class="dropdown-item" href="{{ route('master.document-numbers.edit', $docNumber) }}">
                    <i class="ri-pencil-line me-1"></i> Edit
                  </a>
                  <div class="dropdown-divider"></div>
                  <form action="{{ route('master.document-numbers.destroy', $docNumber) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="dropdown-item text-danger" onclick="return confirm('Yakin ingin menghapus konfigurasi ini?')">
                      <i class="ri-delete-bin-line me-1"></i> Hapus
                    </button>
                  </form>
                </div>
              </div>
            </td>
          </tr>
          @empty
          <tr>
            <td colspan="9" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <i class="ri-inbox-line display-4 text-muted mb-2"></i>
                <h6 class="text-muted">Tidak ada konfigurasi nomor dokumen</h6>
                <p class="text-muted mb-3">Belum ada konfigurasi nomor dokumen yang dibuat.</p>
                <a href="{{ route('master.document-numbers.create') }}" class="btn btn-primary">
                  <i class="ri-add-line me-1"></i>Tambah Konfigurasi Pertama
                </a>
              </div>
            </td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>
    
    @if($documentNumbers->hasPages())
    <div class="card-footer">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Menampilkan {{ $documentNumbers->firstItem() }} - {{ $documentNumbers->lastItem() }} dari {{ $documentNumbers->total() }} data
        </div>
        {{ $documentNumbers->links() }}
      </div>
    </div>
    @endif
  </div>
</div>

@if(session('success'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('success') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif

@if(session('error'))
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">{{ session('error') }}</div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    `;
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    setTimeout(() => toast.remove(), 5000);
  });
</script>
@endif
@endsection
