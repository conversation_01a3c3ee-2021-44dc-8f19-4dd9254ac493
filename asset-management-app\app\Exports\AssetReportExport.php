<?php

namespace App\Exports;

use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\Branch;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class AssetReportExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Asset::with(['category', 'branch', 'assignedEmployee', 'supplier']);
        
        // Apply same filters as in controller
        if (!empty($this->filters['branch_id'])) {
            $query->where('branch_id', $this->filters['branch_id']);
        }
        
        if (!empty($this->filters['category_id'])) {
            $query->where('asset_category_id', $this->filters['category_id']);
        }
        
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }
        
        if (!empty($this->filters['date_from'])) {
            $query->whereDate('purchase_date', '>=', $this->filters['date_from']);
        }
        
        if (!empty($this->filters['date_to'])) {
            $query->whereDate('purchase_date', '<=', $this->filters['date_to']);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'No',
            'Kode Asset',
            'Nama Asset',
            'Kategori',
            'Cabang',
            'Supplier',
            'Brand',
            'Model',
            'Serial Number',
            'Tanggal Beli',
            'Harga Beli',
            'Kondisi',
            'Status',
            'Lokasi',
            'Assigned To',
            'Tanggal Assigned',
            'Keterangan'
        ];
    }

    /**
     * @param mixed $asset
     * @return array
     */
    public function map($asset): array
    {
        static $no = 0;
        $no++;

        return [
            $no,
            $asset->asset_code,
            $asset->name,
            $asset->category->name ?? '-',
            $asset->branch->name ?? '-',
            $asset->supplier->name ?? '-',
            $asset->brand ?? '-',
            $asset->model ?? '-',
            $asset->serial_number ?? '-',
            $asset->purchase_date ? $asset->purchase_date->format('d/m/Y') : '-',
            $asset->purchase_price ? 'Rp ' . number_format($asset->purchase_price, 0, ',', '.') : '-',
            $this->getConditionText($asset->condition),
            $this->getStatusText($asset->status),
            $asset->location ?? '-',
            $asset->assignedEmployee->name ?? '-',
            $asset->assigned_at ? $asset->assigned_at->format('d/m/Y') : '-',
            $asset->notes ?? '-'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $lastRow = $sheet->getHighestRow();
        $lastColumn = $sheet->getHighestColumn();

        return [
            // Header row styling
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ],
            // All data styling
            "A1:{$lastColumn}{$lastRow}" => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true
                ]
            ]
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Laporan Asset';
    }

    /**
     * Get condition text
     */
    private function getConditionText($condition)
    {
        return match($condition) {
            'excellent' => 'Sangat Baik',
            'good' => 'Baik',
            'fair' => 'Cukup',
            'poor' => 'Buruk',
            default => '-'
        };
    }

    /**
     * Get status text
     */
    private function getStatusText($status)
    {
        return match($status) {
            'active' => 'Aktif',
            'inactive' => 'Non-Aktif',
            'maintenance' => 'Maintenance',
            'disposed' => 'Disposed',
            default => '-'
        };
    }
}
