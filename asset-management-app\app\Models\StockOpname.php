<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StockOpname extends Model
{
    use HasFactory;

    protected $fillable = [
        'opname_number',
        'title',
        'description',
        'branch_id',
        'asset_category_id',
        'created_by',
        'status',
        'start_date',
        'end_date',
        'completed_at',
        'settings',
        'total_assets',
        'scanned_assets',
        'missing_assets',
        'found_assets',
        'notes',
        'is_locked'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'completed_at' => 'datetime',
        'settings' => 'array',
        'is_locked' => 'boolean'
    ];

    /**
     * Get the branch that owns the stock opname
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the asset category for this stock opname
     */
    public function assetCategory(): BelongsTo
    {
        return $this->belongsTo(AssetCategory::class, 'asset_category_id');
    }

    /**
     * Get the user who created the stock opname
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the stock opname details
     */
    public function details(): HasMany
    {
        return $this->hasMany(StockOpnameDetail::class);
    }

    /**
     * Get scanned details
     */
    public function scannedDetails(): HasMany
    {
        return $this->hasMany(StockOpnameDetail::class)->whereNotNull('scanned_at');
    }

    /**
     * Get missing assets
     */
    public function missingAssets(): HasMany
    {
        return $this->hasMany(StockOpnameDetail::class)->where('found_status', 'not_found');
    }

    /**
     * Get found assets
     */
    public function foundAssets(): HasMany
    {
        return $this->hasMany(StockOpnameDetail::class)->where('found_status', 'found');
    }

    /**
     * Get assets with discrepancies
     */
    public function discrepancyAssets(): HasMany
    {
        return $this->hasMany(StockOpnameDetail::class)->where('has_discrepancy', true);
    }

    /**
     * Generate opname number
     */
    public static function generateOpnameNumber($branchCode = null)
    {
        $year = date('Y');
        $month = date('m');
        $branchCode = $branchCode ?: 'SO';

        $lastOpname = self::where('opname_number', 'like', "{$branchCode}-{$year}{$month}-%")
                          ->orderBy('opname_number', 'desc')
                          ->first();

        if ($lastOpname) {
            $lastNumber = (int) substr($lastOpname->opname_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s-%04d', $branchCode, $year, $month, $newNumber);
    }

    /**
     * Check if stock opname is active (in progress)
     */
    public function isActive(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if stock opname can be edited
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft', 'in_progress']);
    }

    /**
     * Start stock opname
     */
    public function start()
    {
        $this->update([
            'status' => 'in_progress',
            'start_date' => now(),
            'is_locked' => true
        ]);

        // Generate stock opname details for all assets in branch
        $this->generateDetails();
    }

    /**
     * Complete stock opname
     */
    public function complete()
    {
        $this->update([
            'status' => 'completed',
            'end_date' => now(),
            'completed_at' => now(),
            'is_locked' => false
        ]);

        // Update statistics
        $this->updateStatistics();
    }

    /**
     * Generate stock opname details
     */
    public function generateDetails()
    {
        $query = Asset::where('branch_id', $this->branch_id);

        // Filter by asset category if specified
        if ($this->asset_category_id) {
            $query->where('asset_category_id', $this->asset_category_id);
        }

        $assets = $query->get();

        foreach ($assets as $asset) {
            StockOpnameDetail::create([
                'stock_opname_id' => $this->id,
                'asset_id' => $asset->id,
                'asset_code' => $asset->asset_code,
                'asset_name' => $asset->name,
                'expected_status' => $asset->status,
                'dynamic_fields_expected' => $asset->dynamic_fields ?: []
            ]);
        }

        $this->update(['total_assets' => $assets->count()]);
    }

    /**
     * Update statistics
     */
    public function updateStatistics()
    {
        $scannedCount = $this->details()->whereNotNull('scanned_at')->count();
        $foundCount = $this->details()->where('found_status', 'found')->count();
        $missingCount = $this->details()->where('found_status', 'not_found')->count();

        $this->update([
            'scanned_assets' => $scannedCount,
            'found_assets' => $foundCount,
            'missing_assets' => $missingCount
        ]);
    }

    /**
     * Check if there's an active stock opname for branch
     */
    public static function hasActiveOpname($branchId = null)
    {
        $query = self::where('status', 'in_progress');

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->exists();
    }

    /**
     * Get active stock opname for branch
     */
    public static function getActiveOpname($branchId = null)
    {
        $query = self::where('status', 'in_progress')->with('branch');

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->first();
    }

    /**
     * Check if specific branch has active stock opname
     */
    public static function isBranchLocked($branchId)
    {
        return self::where('status', 'in_progress')
                   ->where('is_locked', true)
                   ->where('branch_id', $branchId)
                   ->exists();
    }

    /**
     * Get active stock opname for specific branch
     */
    public static function getActiveBranchOpname($branchId)
    {
        return self::where('status', 'in_progress')
                   ->where('is_locked', true)
                   ->where('branch_id', $branchId)
                   ->with('branch')
                   ->first();
    }

    /**
     * Get all active stock opnames with branch info
     */
    public static function getAllActiveOpnames()
    {
        return self::where('status', 'in_progress')
                   ->where('is_locked', true)
                   ->with('branch')
                   ->get();
    }

    /**
     * Check if user can perform asset operations in their branch
     */
    public static function canUserPerformAssetOperations($userId = null)
    {
        $user = $userId ? \App\Models\User::find($userId) : auth()->user();

        if (!$user || !$user->branch_id) {
            return true; // Allow if no branch restriction
        }

        return !self::isBranchLocked($user->branch_id);
    }

    /**
     * Get lock status for user's branch
     */
    public static function getUserBranchLockStatus($userId = null)
    {
        $user = $userId ? \App\Models\User::find($userId) : auth()->user();

        if (!$user || !$user->branch_id) {
            return null;
        }

        return self::getActiveBranchOpname($user->branch_id);
    }
}
