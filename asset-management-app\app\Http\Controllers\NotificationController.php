<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\NotificationService;
use App\Models\Notification;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display all notifications for the authenticated user
     */
    public function index()
    {
        $notifications = Notification::forUser(auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * Mark a specific notification as read and redirect to related page
     */
    public function read($id)
    {
        $notification = Notification::where('id', $id)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        $this->notificationService->markAsRead($id);

        // Check if this is an AJAX request or from notification dropdown
        if (request()->expectsJson() || request()->header('X-Requested-With') === 'XMLHttpRequest') {
            return response()->json(['success' => true]);
        }

        // Redirect based on notification type and data
        if ($notification->data && isset($notification->data['request_id'])) {
            return redirect()->route('request-assets.show', $notification->data['request_id'])
                ->with('success', 'Notifikasi telah ditandai sebagai dibaca.');
        }

        // If called from notifications index page, stay there
        if (request()->header('referer') && str_contains(request()->header('referer'), 'notifications')) {
            return redirect()->route('notifications.index')
                ->with('success', 'Notifikasi telah ditandai sebagai dibaca.');
        }

        return redirect()->route('notifications.index');
    }

    /**
     * Mark all notifications as read for the authenticated user
     */
    public function markAllAsRead()
    {
        $this->notificationService->markAllAsRead(auth()->id());

        return redirect()->back()->with('success', 'Semua notifikasi telah ditandai sebagai dibaca.');
    }

    /**
     * Get unread notifications count (for AJAX)
     */
    public function getUnreadCount()
    {
        $count = $this->notificationService->getUnreadCount(auth()->id());

        return response()->json(['count' => $count]);
    }

    /**
     * Get recent notifications (for AJAX)
     */
    public function getRecent()
    {
        $notifications = $this->notificationService->getUnreadNotifications(auth()->id(), 10);

        return response()->json([
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'time_ago' => $notification->time_ago,
                    'icon' => $this->notificationService->getNotificationIcon($notification->type),
                    'color' => $this->notificationService->getNotificationColor($notification->type),
                    'read_url' => route('notifications.read', $notification->id),
                ];
            }),
            'count' => $notifications->count()
        ]);
    }
}
