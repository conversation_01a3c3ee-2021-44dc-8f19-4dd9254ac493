@extends('layouts.contentNavbarLayout')

@section('title', 'Konfigurasi Label QR Code - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-qr-code-line me-2"></i>
              Konfigurasi Label QR Code
            </h5>
            <small class="text-muted">Kelola konfigurasi ukuran dan format label QR Code untuk asset</small>
          </div>
          <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConfigModal">
              <i class="ri-add-line me-1"></i>
              Tambah Konfigurasi
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Existing Configurations -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-settings-4-line me-2"></i>
            Konfigurasi yang Tersedia
          </h6>
        </div>
        <div class="card-body">
          @if($configurations->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Nama</th>
                    <th>Ukuran (mm)</th>
                    <th>QR Size</th>
                    <th>Font</th>
                    <th>Field yang Ditampilkan</th>
                    <th>Status</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($configurations as $config)
                    <tr>
                      <td>
                        <strong>{{ $config->name }}</strong>
                        @if($config->is_default)
                          <span class="badge bg-success ms-2">Default</span>
                        @endif
                        @if($config->description)
                          <br><small class="text-muted">{{ $config->description }}</small>
                        @endif
                      </td>
                      <td>{{ $config->width }} x {{ $config->height }}</td>
                      <td>{{ $config->qr_size }}mm</td>
                      <td>{{ $config->font_family }} ({{ $config->font_size_title }}/{{ $config->font_size_content }})</td>
                      <td>
                        @if($config->show_asset_name) <span class="badge bg-info">Nama</span> @endif
                        @if($config->show_asset_code) <span class="badge bg-info">Kode</span> @endif
                        @if($config->show_category) <span class="badge bg-info">Kategori</span> @endif
                        @if($config->show_branch) <span class="badge bg-info">Cabang</span> @endif
                        @if($config->show_location) <span class="badge bg-info">Lokasi</span> @endif
                      </td>
                      <td>
                        @if($config->is_active)
                          <span class="badge bg-success">Aktif</span>
                        @else
                          <span class="badge bg-secondary">Nonaktif</span>
                        @endif
                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                  data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-more-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="{{ route('qr-labels.preview', ['config_id' => $config->id]) }}" target="_blank">
                                <i class="ri-eye-line me-2"></i>
                                Preview
                              </a>
                            </li>
                            @if(!$config->is_default)
                              <li>
                                <form method="POST" action="{{ route('qr-labels.destroy', $config) }}" 
                                      onsubmit="return confirm('Apakah Anda yakin ingin menghapus konfigurasi ini?')">
                                  @csrf
                                  @method('DELETE')
                                  <button type="submit" class="dropdown-item text-danger">
                                    <i class="ri-delete-bin-line me-2"></i>
                                    Hapus
                                  </button>
                                </form>
                              </li>
                            @endif
                          </ul>
                        </div>
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          @else
            <div class="text-center py-5">
              <i class="ri-qr-code-line ri-48px text-muted mb-3"></i>
              <h5 class="text-muted">Belum Ada Konfigurasi</h5>
              <p class="text-muted mb-4">Tambahkan konfigurasi label QR Code pertama Anda.</p>
              <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addConfigModal">
                <i class="ri-add-line me-1"></i>
                Tambah Konfigurasi Pertama
              </button>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>

  <!-- Predefined Sizes Info -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Ukuran Label yang Disarankan
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            @foreach($predefinedSizes as $key => $size)
              <div class="col-md-3 mb-3">
                <div class="border rounded p-3 text-center">
                  <h6>{{ $size['name'] }}</h6>
                  <p class="text-muted mb-2">{{ $size['width'] }}mm x {{ $size['height'] }}mm</p>
                  <small class="text-muted">QR: {{ $size['qr_size'] }}mm</small>
                </div>
              </div>
            @endforeach
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Configuration Modal -->
<div class="modal fade" id="addConfigModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="ri-add-line me-2"></i>
          Tambah Konfigurasi Label QR
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="POST" action="{{ route('qr-labels.store') }}">
        @csrf
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="name" class="form-label">Nama Konfigurasi <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="description" class="form-label">Deskripsi</label>
                <input type="text" class="form-control" id="description" name="description">
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="width" class="form-label">Lebar (mm) <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="width" name="width" min="10" max="200" step="0.1" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="height" class="form-label">Tinggi (mm) <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="height" name="height" min="10" max="200" step="0.1" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="qr_size" class="form-label">Ukuran QR (mm) <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="qr_size" name="qr_size" min="5" max="100" step="0.1" required>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-3">
              <div class="mb-3">
                <label for="margin_top" class="form-label">Margin Atas</label>
                <input type="number" class="form-control" id="margin_top" name="margin_top" min="0" max="50" step="0.1" value="5">
              </div>
            </div>
            <div class="col-md-3">
              <div class="mb-3">
                <label for="margin_bottom" class="form-label">Margin Bawah</label>
                <input type="number" class="form-control" id="margin_bottom" name="margin_bottom" min="0" max="50" step="0.1" value="5">
              </div>
            </div>
            <div class="col-md-3">
              <div class="mb-3">
                <label for="margin_left" class="form-label">Margin Kiri</label>
                <input type="number" class="form-control" id="margin_left" name="margin_left" min="0" max="50" step="0.1" value="5">
              </div>
            </div>
            <div class="col-md-3">
              <div class="mb-3">
                <label for="margin_right" class="form-label">Margin Kanan</label>
                <input type="number" class="form-control" id="margin_right" name="margin_right" min="0" max="50" step="0.1" value="5">
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="font_family" class="form-label">Font Family</label>
                <select class="form-select" id="font_family" name="font_family">
                  <option value="Arial">Arial</option>
                  <option value="Helvetica">Helvetica</option>
                  <option value="Times">Times</option>
                  <option value="Courier">Courier</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="font_size_title" class="form-label">Font Size Judul</label>
                <input type="number" class="form-control" id="font_size_title" name="font_size_title" min="6" max="24" value="12">
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="font_size_content" class="form-label">Font Size Konten</label>
                <input type="number" class="form-control" id="font_size_content" name="font_size_content" min="6" max="20" value="10">
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Field yang Ditampilkan</label>
            <div class="row">
              <div class="col-md-6">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="show_asset_name" name="show_asset_name" value="1" checked>
                  <label class="form-check-label" for="show_asset_name">Nama Asset</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="show_asset_code" name="show_asset_code" value="1" checked>
                  <label class="form-check-label" for="show_asset_code">Kode Asset</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="show_category" name="show_category" value="1" checked>
                  <label class="form-check-label" for="show_category">Kategori</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="show_branch" name="show_branch" value="1" checked>
                  <label class="form-check-label" for="show_branch">Cabang</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="show_location" name="show_location" value="1">
                  <label class="form-check-label" for="show_location">Lokasi</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="is_default" name="is_default" value="1">
                  <label class="form-check-label" for="is_default">Set sebagai Default</label>
                </div>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Quick Setup</label>
            <div class="btn-group w-100" role="group">
              @foreach($predefinedSizes as $key => $size)
                <button type="button" class="btn btn-outline-secondary quick-setup-btn" 
                        data-width="{{ $size['width'] }}" 
                        data-height="{{ $size['height'] }}" 
                        data-qr-size="{{ $size['qr_size'] }}">
                  {{ $size['name'] }}
                </button>
              @endforeach
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            Simpan Konfigurasi
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Quick setup buttons
  document.querySelectorAll('.quick-setup-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      document.getElementById('width').value = this.dataset.width;
      document.getElementById('height').value = this.dataset.height;
      document.getElementById('qr_size').value = this.dataset.qrSize;
    });
  });
});
</script>

@if(session('success'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-check-line me-2"></i>
      <div class="me-auto fw-semibold">Success</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('success') }}
    </div>
  </div>
@endif

@endsection
