@extends('layouts/contentNavbarLayout')

@section('title', 'Laporan Maintenance')

@section('page-style')
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}">
@endsection

@section('vendor-script')
<script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
<script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-md-12">
      <div class="card mb-6">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="ri-tools-line me-2"></i>Laporan Maintenance
          </h5>
          <div>
            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
              <i class="ri-file-excel-2-line me-1"></i>Export Excel
            </button>
          </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
              <div class="card border-primary">
                <div class="card-body text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="ri-tools-line text-primary" style="font-size: 2rem;"></i>
                  </div>
                  <h4 class="mb-1 text-primary">{{ number_format($stats['total_maintenances']) }}</h4>
                  <p class="mb-0 text-muted">Total Maintenance</p>
                </div>
              </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
              <div class="card border-warning">
                <div class="card-body text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="ri-calendar-schedule-line text-warning" style="font-size: 2rem;"></i>
                  </div>
                  <h4 class="mb-1 text-warning">{{ number_format($stats['scheduled_count']) }}</h4>
                  <p class="mb-0 text-muted">Terjadwal</p>
                </div>
              </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
              <div class="card border-info">
                <div class="card-body text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="ri-play-circle-line text-info" style="font-size: 2rem;"></i>
                  </div>
                  <h4 class="mb-1 text-info">{{ number_format($stats['in_progress_count']) }}</h4>
                  <p class="mb-0 text-muted">Sedang Berlangsung</p>
                </div>
              </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
              <div class="card border-success">
                <div class="card-body text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="ri-check-circle-line text-success" style="font-size: 2rem;"></i>
                  </div>
                  <h4 class="mb-1 text-success">{{ number_format($stats['completed_count']) }}</h4>
                  <p class="mb-0 text-muted">Selesai</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Cost Statistics -->
          <div class="row mb-4">
            <div class="col-lg-6 col-md-6 mb-3">
              <div class="card border-secondary">
                <div class="card-body text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="ri-money-dollar-circle-line text-secondary" style="font-size: 2rem;"></i>
                  </div>
                  <h5 class="mb-1 text-secondary">Rp {{ number_format($stats['total_estimated_cost'], 0, ',', '.') }}</h5>
                  <p class="mb-0 text-muted">Total Estimasi Biaya</p>
                </div>
              </div>
            </div>
            
            <div class="col-lg-6 col-md-6 mb-3">
              <div class="card border-dark">
                <div class="card-body text-center">
                  <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="ri-money-dollar-box-line text-dark" style="font-size: 2rem;"></i>
                  </div>
                  <h5 class="mb-1 text-dark">Rp {{ number_format($stats['total_actual_cost'], 0, ',', '.') }}</h5>
                  <p class="mb-0 text-muted">Total Biaya Aktual</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Filter Form -->
          <form method="GET" action="{{ route('reports.maintenance.index') }}" class="mb-4">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Cabang</label>
                <select name="branch_id" class="form-select">
                  <option value="">Semua Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ (request('branch_id') == $branch->id) ? 'selected' : '' }}>
                      {{ $branch->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              
              <div class="col-md-3">
                <label class="form-label">Kategori Asset</label>
                <select name="category_id" class="form-select">
                  <option value="">Semua Kategori</option>
                  @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ (request('category_id') == $category->id) ? 'selected' : '' }}>
                      {{ $category->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              
              <div class="col-md-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                  <option value="">Semua Status</option>
                  @foreach($statusOptions as $key => $value)
                    <option value="{{ $key }}" {{ (request('status') == $key) ? 'selected' : '' }}>
                      {{ $value }}
                    </option>
                  @endforeach
                </select>
              </div>
              
              <div class="col-md-3">
                <label class="form-label">Prioritas</label>
                <select name="priority" class="form-select">
                  <option value="">Semua Prioritas</option>
                  @foreach($priorityOptions as $key => $value)
                    <option value="{{ $key }}" {{ (request('priority') == $key) ? 'selected' : '' }}>
                      {{ $value }}
                    </option>
                  @endforeach
                </select>
              </div>
              
              <div class="col-md-3">
                <label class="form-label">Tipe Maintenance</label>
                <select name="maintenance_type" class="form-select">
                  <option value="">Semua Tipe</option>
                  @foreach($maintenanceTypeOptions as $key => $value)
                    <option value="{{ $key }}" {{ (request('maintenance_type') == $key) ? 'selected' : '' }}>
                      {{ $value }}
                    </option>
                  @endforeach
                </select>
              </div>
              
              <div class="col-md-3">
                <label class="form-label">Tanggal Dari</label>
                <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
              </div>
              
              <div class="col-md-3">
                <label class="form-label">Tanggal Sampai</label>
                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
              </div>
              
              <div class="col-md-3 d-flex align-items-end">
                <div class="btn-group w-100">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line me-1"></i>Filter
                  </button>
                  <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                    <i class="ri-refresh-line me-1"></i>Reset
                  </button>
                </div>
              </div>
            </div>
          </form>
          
          <!-- Results Table -->
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead class="table-dark">
                <tr>
                  <th>No. Maintenance</th>
                  <th>Asset</th>
                  <th>Kategori</th>
                  <th>Cabang</th>
                  <th>Tipe</th>
                  <th>Judul</th>
                  <th>Prioritas</th>
                  <th>Status</th>
                  <th>Tanggal Terjadwal</th>
                  <th>Estimasi Biaya</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                @forelse($maintenances as $maintenance)
                  <tr>
                    <td>
                      <strong>{{ $maintenance->maintenance_number }}</strong>
                    </td>
                    <td>
                      <div>
                        <strong>{{ $maintenance->asset->asset_code ?? '-' }}</strong><br>
                        <small class="text-muted">{{ $maintenance->asset->name ?? '-' }}</small>
                      </div>
                    </td>
                    <td>{{ $maintenance->asset->category->name ?? '-' }}</td>
                    <td>{{ $maintenance->asset->branch->name ?? '-' }}</td>
                    <td>
                      <span class="badge bg-info">
                        {{ $maintenance->maintenance_type_text }}
                      </span>
                    </td>
                    <td>{{ $maintenance->title }}</td>
                    <td>
                      @php
                        $priorityClass = match($maintenance->priority) {
                          'low' => 'bg-success',
                          'medium' => 'bg-warning',
                          'high' => 'bg-danger',
                          'critical' => 'bg-dark',
                          default => 'bg-secondary'
                        };
                      @endphp
                      <span class="badge {{ $priorityClass }}">
                        {{ $maintenance->priority_text }}
                      </span>
                    </td>
                    <td>
                      @php
                        $statusClass = match($maintenance->status) {
                          'scheduled' => 'bg-warning',
                          'in_progress' => 'bg-info',
                          'completed' => 'bg-success',
                          'cancelled' => 'bg-secondary',
                          default => 'bg-secondary'
                        };
                      @endphp
                      <span class="badge {{ $statusClass }}">
                        {{ $maintenance->status_text }}
                      </span>
                    </td>
                    <td>{{ $maintenance->scheduled_date ? $maintenance->scheduled_date->format('d/m/Y') : '-' }}</td>
                    <td>{{ $maintenance->estimated_cost ? 'Rp ' . number_format($maintenance->estimated_cost, 0, ',', '.') : '-' }}</td>
                    <td>
                      <a href="{{ route('asset-maintenances.show', $maintenance) }}" class="btn btn-sm btn-outline-info">
                        <i class="ri-eye-line"></i>
                      </a>
                    </td>
                  </tr>
                @empty
                  <tr>
                    <td colspan="11" class="text-center py-4">
                      <div class="d-flex flex-column align-items-center">
                        <i class="ri-inbox-line" style="font-size: 3rem; color: #ccc;"></i>
                        <p class="mt-2 text-muted">Tidak ada data maintenance yang ditemukan</p>
                      </div>
                    </td>
                  </tr>
                @endforelse
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          @if($maintenances->hasPages())
            <div class="d-flex justify-content-center mt-4">
              {{ $maintenances->appends(request()->query())->links() }}
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportToExcel() {
  const params = new URLSearchParams(window.location.search);
  const exportUrl = "{{ route('reports.maintenance.export') }}?" + params.toString();
  window.location.href = exportUrl;
}

function resetFilters() {
  window.location.href = "{{ route('reports.maintenance.index') }}";
}
</script>
@endsection
