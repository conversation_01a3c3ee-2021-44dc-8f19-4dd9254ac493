<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SupplierPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Supplier permissions
        $supplierPermissions = [
            [
                'slug' => 'supplier_view',
                'name' => 'Lihat Supplier',
                'description' => 'Dapat melihat daftar dan detail supplier',
                'module' => 'Supplier',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'slug' => 'supplier_create',
                'name' => 'Tambah Supplier',
                'description' => 'Dapat menambah supplier baru',
                'module' => 'Supplier',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'slug' => 'supplier_edit',
                'name' => 'Edit Supplier',
                'description' => 'Dapat mengedit data supplier',
                'module' => 'Supplier',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'slug' => 'supplier_delete',
                'name' => 'Hapus Supplier',
                'description' => 'Dapat menghapus supplier',
                'module' => 'Supplier',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'slug' => 'supplier_toggle_status',
                'name' => 'Ubah Status Supplier',
                'description' => 'Dapat mengaktifkan/nonaktifkan supplier',
                'module' => 'Supplier',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // Insert permissions
        foreach ($supplierPermissions as $permission) {
            DB::table('permissions')->updateOrInsert(
                ['slug' => $permission['slug']],
                $permission
            );
        }

        // Get Super Admin role
        $superAdminRole = DB::table('roles')->where('slug', 'super_admin')->first();

        if ($superAdminRole) {
            // Assign all supplier permissions to Super Admin
            foreach ($supplierPermissions as $permission) {
                $permissionRecord = DB::table('permissions')
                    ->where('slug', $permission['slug'])
                    ->first();

                if ($permissionRecord) {
                    DB::table('role_permissions')->updateOrInsert([
                        'role_id' => $superAdminRole->id,
                        'permission_id' => $permissionRecord->id
                    ], [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
        }

        $this->command->info('Supplier permissions created and assigned to Super Admin successfully!');
    }
}
