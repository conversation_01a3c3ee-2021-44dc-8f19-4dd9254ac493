<?php $__env->startSection('title', 'Laporan Asset'); ?>

<?php $__env->startSection('page-style'); ?>
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
<script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
<script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-0">
              <i class="ri-file-chart-line me-2"></i>
              Laporan Asset
            </h5>
            <small class="text-muted">Laporan data asset dengan filter cabang, kategori, tanggal, dan status</small>
          </div>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
              <i class="ri-file-excel-line me-1"></i>
              Export Excel
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
              <i class="ri-refresh-line me-1"></i>
              Reset Filter
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-primary">
                <i class="ri-computer-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['total_assets'])); ?></h4>
              <small class="text-muted">Total Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-success">
                <i class="ri-check-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['active_assets'])); ?></h4>
              <small class="text-muted">Asset Aktif</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-warning">
                <i class="ri-tools-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0"><?php echo e(number_format($stats['maintenance_assets'])); ?></h4>
              <small class="text-muted">Maintenance</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-info">
                <i class="ri-money-dollar-circle-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">Rp <?php echo e(number_format($stats['total_value'], 0, ',', '.')); ?></h5>
              <small class="text-muted">Total Nilai Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Form -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="ri-filter-line me-2"></i>
            Filter Laporan
          </h6>
        </div>
        <div class="card-body">
          <form method="GET" action="<?php echo e(route('reports.assets.index')); ?>" id="filterForm">
            <div class="row">
              <div class="col-md-3 mb-3">
                <label class="form-label">Cabang</label>
                <select name="branch_id" class="form-select">
                  <option value="">Semua Cabang</option>
                  <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($branch->id); ?>" <?php echo e(request('branch_id') == $branch->id ? 'selected' : ''); ?>>
                      <?php echo e($branch->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Kategori Asset</label>
                <select name="category_id" class="form-select">
                  <option value="">Semua Kategori</option>
                  <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                      <?php echo e($category->name); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                  <option value="">Semua Status</option>
                  <?php $__currentLoopData = $statusOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($value); ?>" <?php echo e(request('status') == $value ? 'selected' : ''); ?>>
                      <?php echo e($label); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Dari</label>
                <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Sampai</label>
                <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
              </div>
              <div class="col-md-9 mb-3 d-flex align-items-end">
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line me-1"></i>
                    Filter
                  </button>
                  <a href="<?php echo e(route('reports.assets.index')); ?>" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line me-1"></i>
                    Reset
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Asset Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="ri-table-line me-2"></i>
            Data Asset (<?php echo e($assets->total()); ?> asset)
          </h6>
          <div class="d-flex gap-2">
            <span class="badge bg-primary"><?php echo e($assets->count()); ?> ditampilkan</span>
          </div>
        </div>
        <div class="card-body">
          <?php if($assets->count() > 0): ?>
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>No</th>
                    <th>Kode Asset</th>
                    <th>Nama Asset</th>
                    <th>Kategori</th>
                    <th>Cabang</th>
                    <th>Status</th>
                    <th>Kondisi</th>
                    <th>Tanggal Beli</th>
                    <th>Harga Beli</th>
                    <th>Assigned To</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  <?php $__currentLoopData = $assets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $asset): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <tr>
                    <td><?php echo e($assets->firstItem() + $index); ?></td>
                    <td>
                      <span class="badge bg-info"><?php echo e($asset->asset_code); ?></span>
                    </td>
                    <td>
                      <div>
                        <strong><?php echo e($asset->name); ?></strong>
                        <?php if($asset->brand || $asset->model): ?>
                          <br><small class="text-muted"><?php echo e($asset->brand); ?> <?php echo e($asset->model); ?></small>
                        <?php endif; ?>
                      </div>
                    </td>
                    <td><?php echo e($asset->category->name ?? '-'); ?></td>
                    <td><?php echo e($asset->branch->name ?? '-'); ?></td>
                    <td>
                      <span class="badge bg-<?php echo e($asset->status_badge); ?>">
                        <?php echo e($asset->status_text); ?>

                      </span>
                    </td>
                    <td>
                      <span class="badge bg-<?php echo e($asset->condition_badge); ?>">
                        <?php echo e($asset->condition_text); ?>

                      </span>
                    </td>
                    <td><?php echo e($asset->purchase_date ? $asset->purchase_date->format('d/m/Y') : '-'); ?></td>
                    <td><?php echo e($asset->purchase_price ? 'Rp ' . number_format($asset->purchase_price, 0, ',', '.') : '-'); ?></td>
                    <td><?php echo e($asset->assignedToEmployee->name ?? '-'); ?></td>
                    <td>
                      <a href="<?php echo e(route('assets.show', $asset)); ?>" class="btn btn-sm btn-outline-info">
                        <i class="ri-eye-line"></i>
                      </a>
                    </td>
                  </tr>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
              <div>
                <small class="text-muted">
                  Menampilkan <?php echo e($assets->firstItem()); ?> sampai <?php echo e($assets->lastItem()); ?> dari <?php echo e($assets->total()); ?> asset
                </small>
              </div>
              <div>
                <?php echo e($assets->appends(request()->query())->links()); ?>

              </div>
            </div>
          <?php else: ?>
            <div class="text-center py-5">
              <i class="ri-file-search-line ri-48px text-muted mb-3"></i>
              <h6 class="text-muted">Tidak ada data asset yang ditemukan</h6>
              <p class="text-muted">Coba ubah filter pencarian Anda</p>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportToExcel() {
  // Get current filter values
  const form = document.getElementById('filterForm');
  const formData = new FormData(form);
  
  // Build export URL with current filters
  let exportUrl = '<?php echo e(route("reports.assets.export")); ?>';
  const params = new URLSearchParams();
  
  for (let [key, value] of formData.entries()) {
    if (value) {
      params.append(key, value);
    }
  }
  
  if (params.toString()) {
    exportUrl += '?' + params.toString();
  }
  
  // Open export URL
  window.open(exportUrl, '_blank');
}

function resetFilters() {
  window.location.href = '<?php echo e(route("reports.assets.index")); ?>';
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/reports/assets/index.blade.php ENDPATH**/ ?>