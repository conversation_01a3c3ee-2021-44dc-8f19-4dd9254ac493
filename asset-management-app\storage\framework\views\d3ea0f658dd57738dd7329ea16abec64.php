<?php
    use App\Models\StockOpname;
    
    // Get active stock opnames
    $activeOpnames = StockOpname::getAllActiveOpnames();
    $userBranchLock = StockOpname::getUserBranchLockStatus();
?>

<?php if($activeOpnames->count() > 0): ?>
    <!-- Global Stock Opname Notification -->
    <div class="alert alert-info alert-dismissible fade show mb-3" role="alert">
        <div class="d-flex align-items-start">
            <i class="ri-information-line ri-24px me-3 mt-1"></i>
            <div class="flex-grow-1">
                <h6 class="alert-heading mb-2">
                    <i class="ri-lock-line me-1"></i>
                    Stock Opname Sedang Berjalan
                </h6>
                
                <?php if($activeOpnames->count() == 1): ?>
                    <?php $opname = $activeOpnames->first(); ?>
                    <p class="mb-2">
                        <strong><?php echo e($opname->title); ?></strong> (<?php echo e($opname->opname_number); ?>) 
                        sedang berlangsung di <strong><?php echo e($opname->branch->name); ?></strong>.
                    </p>
                    
                    <?php if($userBranchLock && $userBranchLock->id == $opname->id): ?>
                        <div class="alert alert-warning mb-2 py-2">
                            <i class="ri-alert-line me-1"></i>
                            <strong>Operasi asset di cabang Anda dikunci</strong> hingga stock opname selesai.
                        </div>
                    <?php endif; ?>
                    
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="<?php echo e(route('stock-opnames.show', $opname)); ?>" class="btn btn-sm btn-outline-primary">
                            <i class="ri-eye-line me-1"></i>
                            Lihat Detail
                        </a>
                        
                        <?php if($userBranchLock && $userBranchLock->id == $opname->id): ?>
                            <a href="<?php echo e(route('stock-opnames.scan-detail', $opname)); ?>" class="btn btn-sm btn-primary">
                                <i class="ri-qr-scan-line me-1"></i>
                                Scan Asset
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <p class="mb-2">
                        Terdapat <strong><?php echo e($activeOpnames->count()); ?></strong> stock opname yang sedang berjalan:
                    </p>
                    
                    <div class="row">
                        <?php $__currentLoopData = $activeOpnames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $opname): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 mb-2">
                                <div class="border rounded p-2 bg-light">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong><?php echo e($opname->branch->name); ?></strong>
                                            <br><small class="text-muted"><?php echo e($opname->title); ?></small>
                                            <br><span class="badge bg-primary"><?php echo e($opname->opname_number); ?></span>
                                            
                                            <?php if($userBranchLock && $userBranchLock->id == $opname->id): ?>
                                                <br><span class="badge bg-warning mt-1">
                                                    <i class="ri-lock-line me-1"></i>
                                                    Cabang Anda
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <a href="<?php echo e(route('stock-opnames.show', $opname)); ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            
                                            <?php if($userBranchLock && $userBranchLock->id == $opname->id): ?>
                                                <a href="<?php echo e(route('stock-opnames.scan-detail', $opname)); ?>"
                                                   class="btn btn-sm btn-primary">
                                                    <i class="ri-qr-scan-line"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    
                    <?php if($userBranchLock): ?>
                        <div class="alert alert-warning mb-2 py-2">
                            <i class="ri-alert-line me-1"></i>
                            <strong>Operasi asset di cabang Anda dikunci</strong> hingga stock opname selesai.
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                
                <small class="text-muted">
                    <i class="ri-information-line me-1"></i>
                    Selama stock opname berlangsung, operasi tambah/edit/hapus asset akan dikunci untuk menjaga integritas data audit.
                </small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
<?php endif; ?>

<?php if(session('stock_opname_info')): ?>
    <?php $info = session('stock_opname_info'); ?>
    <!-- Specific Stock Opname Lock Notification -->
    <div class="alert alert-warning alert-dismissible fade show mb-3" role="alert">
        <div class="d-flex align-items-start">
            <i class="ri-lock-line ri-24px me-3 mt-1"></i>
            <div class="flex-grow-1">
                <h6 class="alert-heading mb-2">Operasi Asset Dikunci</h6>
                <p class="mb-2">
                    Operasi asset dikunci karena sedang ada stock opname yang berjalan di 
                    <strong><?php echo e($info['branch']); ?></strong>:
                </p>
                <div class="bg-light rounded p-2 mb-3">
                    <strong><?php echo e($info['title']); ?></strong>
                    <br><span class="badge bg-primary"><?php echo e($info['opname_number']); ?></span>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e($info['url']); ?>" class="btn btn-sm btn-outline-primary">
                        <i class="ri-eye-line me-1"></i>
                        Lihat Stock Opname
                    </a>
                    <a href="<?php echo e(route('stock-opnames.scan')); ?>" class="btn btn-sm btn-primary">
                        <i class="ri-qr-scan-line me-1"></i>
                        Scan Asset
                    </a>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
<?php endif; ?>

<!-- Stock Opname Status in Navbar (Optional) -->
<?php if($userBranchLock): ?>
    <style>
        .navbar-brand::after {
            content: " 🔒";
            font-size: 0.8em;
            color: #ff6b35;
        }
    </style>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/components/stock-opname-notification.blade.php ENDPATH**/ ?>