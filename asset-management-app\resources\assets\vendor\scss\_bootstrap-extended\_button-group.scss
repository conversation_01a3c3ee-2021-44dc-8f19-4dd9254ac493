// Button groups
// *******************************************************************************

// * Split button
// *******************************************************************************

.btn-group {
  .btn {
    box-shadow: none !important;
  }
}

.dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split,
.btn-group-lg > .btn + .dropdown-toggle-split,
.input-group-lg .btn + .dropdown-toggle-split,
.btn-xl + .dropdown-toggle-split,
.btn-group-xl > .btn + .dropdown-toggle-split {
  padding: 0.92em;
}

.btn-sm + .dropdown-toggle-split,
.btn-group-sm > .btn + .dropdown-toggle-split,
.input-group-sm .btn + .dropdown-toggle-split {
  padding: 0.8em;
}

.btn-xs + .dropdown-toggle-split,
.btn-group-xs > .btn + .dropdown-toggle-split {
  padding: 0.7em;
}

// * Sizing
// *******************************************************************************

.btn-group-xs > .btn {
  @extend .btn-xs;
}

.btn-group-xl > .btn {
  @extend .btn-xl;
}

// Button groups border

.btn-group > .btn-group:first-child > .btn:not([class*='btn-outline-']):first-child,
.input-group > .btn:not([class*='btn-outline-']):first-child,
:not(.btn-group):not(.input-group) > .btn-group > .btn:not([class*='btn-outline-']):first-child,
.input-group > .btn-group:first-child > .btn:not([class*='btn-outline-']):first-child {
  border-left-color: transparent !important;
}

.btn-group > .btn-group:last-child > .btn:not([class*='btn-outline-']):last-of-type,
.input-group > .btn:not([class*='btn-outline-']):last-of-type,
:not(.btn-group):not(.input-group) > .btn-group > .btn:not([class*='btn-outline-']):last-of-type,
.input-group > .btn-group:last-child > .btn:not([class*='btn-outline-']):last-of-type {
  border-right-color: transparent !important;
}
