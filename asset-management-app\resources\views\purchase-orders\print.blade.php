<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Purchase Order - {{ $purchaseOrder->po_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .document-title {
            font-size: 16px;
            font-weight: bold;
            margin-top: 15px;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 5px;
        }
        
        .info-label {
            width: 150px;
            font-weight: bold;
        }
        
        .info-value {
            flex: 1;
        }
        
        .two-column {
            display: flex;
            gap: 40px;
            margin-bottom: 20px;
        }
        
        .column {
            flex: 1;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .totals-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 5px 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .totals-table .total-row {
            font-weight: bold;
            border-top: 2px solid #333;
            border-bottom: 2px solid #333;
        }
        
        .terbilang {
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #333;
            background-color: #f9f9f9;
        }
        
        .signatures {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            height: 60px;
            margin-bottom: 5px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        
        .status-draft { background-color: #6c757d; }
        .status-submitted { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #198754; }
        .status-rejected { background-color: #dc3545; }
        .status-sent_to_supplier { background-color: #0d6efd; }
        .status-partially_received { background-color: #fd7e14; }
        .status-completed { background-color: #20c997; }
        .status-cancelled { background-color: #6f42c1; }
        
        @media print {
            body { margin: 0; padding: 15px; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Asset Management System') }}</div>
        <div>{{ $purchaseOrder->branch->address ?? 'Alamat Perusahaan' }}</div>
        <div class="document-title">PURCHASE ORDER</div>
        <div>
            <span class="status-badge status-{{ str_replace(' ', '_', strtolower($purchaseOrder->status)) }}">
                {{ $purchaseOrder->status_label }}
            </span>
        </div>
    </div>

    <!-- PO Information -->
    <div class="two-column">
        <div class="column">
            <div class="info-section">
                <h4>Informasi Purchase Order</h4>
                <div class="info-row">
                    <div class="info-label">Nomor PO:</div>
                    <div class="info-value">{{ $purchaseOrder->po_number }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Tanggal PO:</div>
                    <div class="info-value">{{ $purchaseOrder->po_date->format('d/m/Y') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Tanggal Pengiriman:</div>
                    <div class="info-value">{{ $purchaseOrder->delivery_date ? $purchaseOrder->delivery_date->format('d/m/Y') : '-' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Cabang:</div>
                    <div class="info-value">{{ $purchaseOrder->branch->name }}</div>
                </div>
            </div>
        </div>
        
        <div class="column">
            <div class="info-section">
                <h4>Informasi Supplier</h4>
                <div class="info-row">
                    <div class="info-label">Nama:</div>
                    <div class="info-value">{{ $purchaseOrder->supplier->name }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Kode:</div>
                    <div class="info-value">{{ $purchaseOrder->supplier->supplier_code }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Telepon:</div>
                    <div class="info-value">{{ $purchaseOrder->supplier->phone ?? '-' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Email:</div>
                    <div class="info-value">{{ $purchaseOrder->supplier->email ?? '-' }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="25%">Nama Item</th>
                <th width="10%">Kode</th>
                <th width="15%">Merk/Model</th>
                <th width="15%">Spesifikasi</th>
                <th width="8%">Qty</th>
                <th width="7%">Satuan</th>
                <th width="15%">Harga Satuan</th>
                <th width="15%">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($purchaseOrder->items as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>
                        <strong>{{ $item->item_name }}</strong>
                        @if($item->item_description)
                            <br><small>{{ $item->item_description }}</small>
                        @endif
                    </td>
                    <td>{{ $item->item_code ?: '-' }}</td>
                    <td>
                        @if($item->brand || $item->model)
                            {{ $item->brand }}{{ $item->brand && $item->model ? ' / ' : '' }}{{ $item->model }}
                        @else
                            -
                        @endif
                    </td>
                    <td>{{ $item->specification ?: '-' }}</td>
                    <td class="text-center">{{ number_format($item->quantity, 0, ',', '.') }}</td>
                    <td class="text-center">{{ $item->unit }}</td>
                    <td class="text-right">Rp {{ number_format($item->unit_price, 0, ',', '.') }}</td>
                    <td class="text-right">Rp {{ number_format($item->total_price, 0, ',', '.') }}</td>
                </tr>
                @if($item->notes)
                    <tr>
                        <td></td>
                        <td colspan="8"><em>Catatan: {{ $item->notes }}</em></td>
                    </tr>
                @endif
            @endforeach
        </tbody>
    </table>

    <!-- Totals -->
    <table class="totals-table">
        <tr>
            <td><strong>Subtotal:</strong></td>
            <td class="text-right"><strong>Rp {{ number_format($purchaseOrder->subtotal_amount, 0, ',', '.') }}</strong></td>
        </tr>
        @if($purchaseOrder->discount_amount > 0)
            <tr>
                <td>Diskon ({{ $purchaseOrder->discount_percentage }}%):</td>
                <td class="text-right">Rp {{ number_format($purchaseOrder->discount_amount, 0, ',', '.') }}</td>
            </tr>
        @endif
        @if($purchaseOrder->tax_amount > 0)
            <tr>
                <td>Pajak ({{ $purchaseOrder->tax_percentage }}%):</td>
                <td class="text-right">Rp {{ number_format($purchaseOrder->tax_amount, 0, ',', '.') }}</td>
            </tr>
        @endif
        <tr class="total-row">
            <td><strong>TOTAL:</strong></td>
            <td class="text-right"><strong>Rp {{ number_format($purchaseOrder->total_amount, 0, ',', '.') }}</strong></td>
        </tr>
    </table>

    <!-- Terbilang -->
    <div class="terbilang">
        <strong>Terbilang:</strong> {{ terbilang_rupiah($purchaseOrder->total_amount) }}
    </div>

    <!-- Additional Information -->
    @if($purchaseOrder->payment_terms || $purchaseOrder->delivery_address || $purchaseOrder->terms_conditions || $purchaseOrder->notes)
        <div style="margin-top: 20px;">
            @if($purchaseOrder->payment_terms)
                <div class="info-row">
                    <div class="info-label">Syarat Pembayaran:</div>
                    <div class="info-value">{{ $purchaseOrder->payment_terms }}</div>
                </div>
            @endif
            @if($purchaseOrder->delivery_address)
                <div class="info-row">
                    <div class="info-label">Alamat Pengiriman:</div>
                    <div class="info-value">{{ $purchaseOrder->delivery_address }}</div>
                </div>
            @endif
            @if($purchaseOrder->terms_conditions)
                <div style="margin-top: 15px;">
                    <strong>Syarat & Ketentuan:</strong><br>
                    {{ $purchaseOrder->terms_conditions }}
                </div>
            @endif
            @if($purchaseOrder->notes)
                <div style="margin-top: 15px;">
                    <strong>Catatan:</strong><br>
                    {{ $purchaseOrder->notes }}
                </div>
            @endif
        </div>
    @endif

    <!-- Signatures -->
    <div class="signatures">
        <div class="signature-box">
            <div>Dibuat Oleh:</div>
            <div class="signature-line"></div>
            <div>{{ $purchaseOrder->createdBy->name }}</div>
            <div>{{ $purchaseOrder->created_at->format('d/m/Y') }}</div>
        </div>
        
        @if($purchaseOrder->approved_by)
            <div class="signature-box">
                <div>Disetujui Oleh:</div>
                <div class="signature-line"></div>
                <div>{{ $purchaseOrder->approvedBy->name }}</div>
                <div>{{ $purchaseOrder->approved_at->format('d/m/Y') }}</div>
            </div>
        @else
            <div class="signature-box">
                <div>Disetujui Oleh:</div>
                <div class="signature-line"></div>
                <div>(...........................)</div>
                <div>Tanggal: ................</div>
            </div>
        @endif
        
        <div class="signature-box">
            <div>Supplier:</div>
            <div class="signature-line"></div>
            <div>(...........................)</div>
            <div>Tanggal: ................</div>
        </div>
    </div>
</body>
</html>
