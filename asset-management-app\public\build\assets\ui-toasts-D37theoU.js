(function(){const e=document.querySelector(".toast-placement-ex"),s=document.querySelector("#showToastPlacement");let o,c,l;function n(t){t&&t._element!==null&&(e&&(e.querySelectorAll('i[class^="ri-"]').forEach(function(a){a.classList.remove(o)}),DOMTokenList.prototype.remove.apply(e.classList,c)),t.dispose())}s&&(s.onclick=function(){l&&n(l),o=document.querySelector("#selectTypeOpt").value,c=document.querySelector("#selectPlacement").value.split(" "),e.querySelectorAll('i[class^="ri-"]').forEach(function(t){t.classList.add(o)}),DOMTokenList.prototype.add.apply(e.classList,c),l=new bootstrap.Toast(e),l.show()})})();
