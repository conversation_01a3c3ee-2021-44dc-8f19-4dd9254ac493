<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('request_assets', function (Blueprint $table) {
            $table->id();
            $table->string('request_number')->unique(); // Nomor permintaan otomatis
            $table->date('request_date'); // Tanggal permohonan
            $table->date('needed_date'); // Tanggal dibutuhkan
            $table->string('request_category'); // Kategori permintaan (dari lookup KATEGORI_REQ)
            $table->foreignId('requested_by')->constrained('users'); // Diminta oleh (user)
            $table->string('position'); // Jabatan (dari lookup JABATAN)
            $table->foreignId('division_id')->constrained('divisions'); // Divisi
            $table->string('item_type'); // Item permintaan (dari lookup ITEM_REQ)
            $table->text('purpose'); // Tujuan permintaan
            $table->json('items'); // Item permintaan detail (array of items)
            $table->enum('status', ['draft', 'submitted', 'reviewed', 'approved', 'rejected', 'completed'])->default('draft');
            $table->foreignId('approved_by')->nullable()->constrained('users'); // Disetujui oleh
            $table->timestamp('approved_at')->nullable(); // Tanggal persetujuan
            $table->text('approval_notes')->nullable(); // Catatan persetujuan
            $table->decimal('estimated_total', 15, 2)->nullable(); // Estimasi total biaya
            $table->text('notes')->nullable(); // Catatan tambahan
            $table->timestamps();

            // Indexes
            $table->index(['status', 'request_date']);
            $table->index(['requested_by', 'status']);
            $table->index(['division_id', 'status']);
            $table->index('request_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('request_assets');
    }
};
