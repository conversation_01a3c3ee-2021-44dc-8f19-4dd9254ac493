<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tanda Terima Asset - {{ $receiptNumber }}</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #333;
        }

        .company-info h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .company-info h2 {
            font-size: 14px;
            font-weight: normal;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .company-info p {
            font-size: 11px;
            color: #666;
            line-height: 1.3;
        }

        .receipt-number {
            text-align: right;
        }

        .receipt-number h3 {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .receipt-number p {
            font-size: 11px;
            color: #666;
        }

        .title {
            text-align: center;
            margin-bottom: 30px;
        }

        .title h1 {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .employee-section, .asset-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #bdc3c7;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .info-table td {
            padding: 6px 0;
            vertical-align: top;
        }

        .info-table .label {
            width: 150px;
            font-weight: bold;
            color: #34495e;
        }

        .info-table .colon {
            width: 20px;
            text-align: center;
        }

        .info-table .value {
            color: #2c3e50;
        }
        
        .agreement-section {
            margin: 30px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }

        .agreement-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .agreement-text {
            font-size: 11px;
            line-height: 1.5;
            color: #495057;
            text-align: justify;
        }

        .signatures {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }

        .signature-box {
            width: 22%;
            text-align: center;
        }

        .signature-title {
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 60px;
        }

        .signature-line {
            border-top: 1px solid #333;
            margin-bottom: 5px;
        }

        .signature-name {
            font-size: 10px;
            color: #666;
        }

        .print-info {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            font-size: 10px;
            color: #999;
            text-align: center;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            @page {
                margin: 10mm;
            }
        }
        
        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
        
        .pdf-button {
            position: fixed;
            top: 10px;
            right: 120px;
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            z-index: 1000;
        }
        
        .pdf-button:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <!-- Action Buttons -->
    <button class="print-button no-print" onclick="window.print()">
        🖨️ Print
    </button>
    <button class="pdf-button no-print" onclick="generatePDF()">
        📄 Save PDF
    </button>

    <!-- Header -->
    <div class="header">
        <div class="company-info">
            <h1>{{ \App\Models\CompanySetting::getCompanyName() }}</h1>
            <h2>{{ $assetAssignment->employee->branch->name }}</h2>
            @if(\App\Models\CompanySetting::first() && \App\Models\CompanySetting::first()->address)
                <p>{{ \App\Models\CompanySetting::first()->address }}</p>
            @endif
            @if(\App\Models\CompanySetting::first() && \App\Models\CompanySetting::first()->phone)
                <p>Telp: {{ \App\Models\CompanySetting::first()->phone }}</p>
            @endif
        </div>
        <div class="receipt-number">
            <h3>{{ $receiptNumber }}</h3>
            <p>{{ $assetAssignment->assigned_at->format('d/m/Y') }}</p>
        </div>
    </div>

    <!-- Title -->
    <div class="title">
        <h1>Tanda Terima Asset</h1>
    </div>

    <!-- Employee Information -->
    <div class="employee-section">
        <div class="section-title">Informasi Karyawan</div>
        <table class="info-table">
            <tr>
                <td class="label">Nama Karyawan</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->employee->full_name }}</td>
            </tr>
            <tr>
                <td class="label">NIK</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->employee->nik }}</td>
            </tr>
            <tr>
                <td class="label">Cabang</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->employee->branch->name }}</td>
            </tr>
            <tr>
                <td class="label">Divisi</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->employee->division->name ?? '-' }}</td>
            </tr>
            <tr>
                <td class="label">Jabatan</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->employee->position ?? '-' }}</td>
            </tr>
        </table>
    </div>

    <!-- Asset Information -->
    <div class="asset-section">
        <div class="section-title">Detail Asset</div>
        <table class="info-table">
            <tr>
                <td class="label">Kode Asset</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->asset->asset_code }}</td>
            </tr>
            <tr>
                <td class="label">Nama Asset</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->asset->name }}</td>
            </tr>
            <tr>
                <td class="label">Kategori</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->asset->assetCategory->name ?? '-' }}</td>
            </tr>
            <tr>
                <td class="label">Tipe</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->asset->assetType->name ?? '-' }}</td>
            </tr>
            <tr>
                <td class="label">Brand/Model</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->asset->brand }} {{ $assetAssignment->asset->model }}</td>
            </tr>
            <tr>
                <td class="label">Serial Number</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->asset->serial_number ?? '-' }}</td>
            </tr>
            <tr>
                <td class="label">Kondisi Saat Diserahkan</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->condition_when_assigned_text }}</td>
            </tr>
            <tr>
                <td class="label">Tanggal Assignment</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->assigned_at->format('d/m/Y H:i') }}</td>
            </tr>
            @if($assetAssignment->assignment_notes)
            <tr>
                <td class="label">Catatan</td>
                <td class="colon">:</td>
                <td class="value">{{ $assetAssignment->assignment_notes }}</td>
            </tr>
            @endif
        </table>
    </div>

    <!-- Agreement Section -->
    <div class="agreement-section">
        <div class="agreement-title">Perjanjian Penggunaan Asset</div>
        <div class="agreement-text">
            {{ \App\Models\CompanySetting::getAssetUsageAgreement() }}
        </div>
    </div>

    <!-- Signatures -->
    <div class="signatures">
        <div class="signature-box">
            <div class="signature-title">Penerima</div>
            <div class="signature-line"></div>
            <div class="signature-name">{{ $assetAssignment->employee->full_name }}</div>
        </div>
        <div class="signature-box">
            <div class="signature-title">Atasan Pemohon</div>
            <div class="signature-line"></div>
            <div class="signature-name">(...........................)</div>
        </div>
        <div class="signature-box">
            <div class="signature-title">HRD/GA</div>
            <div class="signature-line"></div>
            <div class="signature-name">(...........................)</div>
        </div>
        <div class="signature-box">
            <div class="signature-title">IT</div>
            <div class="signature-line"></div>
            <div class="signature-name">{{ $assetAssignment->assignedBy->name }}</div>
        </div>
    </div>

    <!-- Print Info -->
    <div class="print-info">
        Dicetak: {{ now()->format('d/m/Y H:i') }} oleh {{ auth()->user()->name }}
    </div>

    <script>
        function generatePDF() {
            // Hide buttons before printing
            document.querySelectorAll('.no-print').forEach(el => el.style.display = 'none');
            
            // Use browser's print to PDF functionality
            window.print();
            
            // Show buttons again after a delay
            setTimeout(() => {
                document.querySelectorAll('.no-print').forEach(el => el.style.display = 'block');
            }, 1000);
        }
    </script>
</body>
</html>
