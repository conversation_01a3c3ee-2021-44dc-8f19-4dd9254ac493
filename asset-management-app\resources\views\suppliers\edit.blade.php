@extends('layouts/contentNavbarLayout')

@section('title', 'Edit Supplier')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="ri-edit-line me-2"></i>
              Edit Supplier: {{ $supplier->name }}
            </h5>
            <div class="d-flex gap-2">
              <a href="{{ route('suppliers.show', $supplier) }}" class="btn btn-outline-info">
                <i class="ri-eye-line me-1"></i>
                Lihat Detail
              </a>
              <a href="{{ route('suppliers.index') }}" class="btn btn-outline-secondary">
                <i class="ri-arrow-left-line me-1"></i>
                <PERSON><PERSON><PERSON>
              </a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <form action="{{ route('suppliers.update', $supplier) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <!-- Basic Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="ri-information-line me-2"></i>
                  Informasi Dasar
                </h6>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="supplier_code" class="form-label">Kode Supplier</label>
                  <input type="text" class="form-control"
                         id="supplier_code" name="supplier_code" value="{{ $supplier->supplier_code }}" readonly>
                  <div class="form-text">
                    <i class="ri-lock-line me-1"></i>
                    Kode supplier tidak dapat diubah setelah dibuat
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="name" class="form-label">Nama Supplier <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('name') is-invalid @enderror" 
                         id="name" name="name" value="{{ old('name', $supplier->name) }}" required>
                  @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="company_name" class="form-label">Nama Perusahaan</label>
                  <input type="text" class="form-control @error('company_name') is-invalid @enderror" 
                         id="company_name" name="company_name" value="{{ old('company_name', $supplier->company_name) }}">
                  @error('company_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="business_type" class="form-label">Jenis Usaha</label>
                  <select class="form-select @error('business_type') is-invalid @enderror" 
                          id="business_type" name="business_type">
                    <option value="">Pilih Jenis Usaha</option>
                    @foreach($businessTypes as $key => $value)
                      <option value="{{ $key }}" {{ old('business_type', $supplier->business_type) == $key ? 'selected' : '' }}>
                        {{ $value }}
                      </option>
                    @endforeach
                  </select>
                  @error('business_type')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Address Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="ri-map-pin-line me-2"></i>
                  Informasi Alamat
                </h6>
              </div>
              
              <div class="col-12">
                <div class="mb-3">
                  <label for="address" class="form-label">Alamat</label>
                  <textarea class="form-control @error('address') is-invalid @enderror" 
                            id="address" name="address" rows="3">{{ old('address', $supplier->address) }}</textarea>
                  @error('address')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="city" class="form-label">Kota</label>
                  <input type="text" class="form-control @error('city') is-invalid @enderror" 
                         id="city" name="city" value="{{ old('city', $supplier->city) }}">
                  @error('city')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="province" class="form-label">Provinsi</label>
                  <input type="text" class="form-control @error('province') is-invalid @enderror" 
                         id="province" name="province" value="{{ old('province', $supplier->province) }}">
                  @error('province')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="postal_code" class="form-label">Kode Pos</label>
                  <input type="text" class="form-control @error('postal_code') is-invalid @enderror" 
                         id="postal_code" name="postal_code" value="{{ old('postal_code', $supplier->postal_code) }}">
                  @error('postal_code')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="country" class="form-label">Negara</label>
                  <input type="text" class="form-control @error('country') is-invalid @enderror" 
                         id="country" name="country" value="{{ old('country', $supplier->country) }}">
                  @error('country')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="ri-phone-line me-2"></i>
                  Informasi Kontak
                </h6>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="phone" class="form-label">Telepon</label>
                  <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                         id="phone" name="phone" value="{{ old('phone', $supplier->phone) }}">
                  @error('phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="fax" class="form-label">Fax</label>
                  <input type="text" class="form-control @error('fax') is-invalid @enderror" 
                         id="fax" name="fax" value="{{ old('fax', $supplier->fax) }}">
                  @error('fax')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="email" class="form-label">Email</label>
                  <input type="email" class="form-control @error('email') is-invalid @enderror" 
                         id="email" name="email" value="{{ old('email', $supplier->email) }}">
                  @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="website" class="form-label">Website</label>
                  <input type="url" class="form-control @error('website') is-invalid @enderror" 
                         id="website" name="website" value="{{ old('website', $supplier->website) }}" 
                         placeholder="https://example.com">
                  @error('website')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="contact_person" class="form-label">Nama Kontak</label>
                  <input type="text" class="form-control @error('contact_person') is-invalid @enderror" 
                         id="contact_person" name="contact_person" value="{{ old('contact_person', $supplier->contact_person) }}">
                  @error('contact_person')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="contact_phone" class="form-label">Telepon Kontak</label>
                  <input type="text" class="form-control @error('contact_phone') is-invalid @enderror" 
                         id="contact_phone" name="contact_phone" value="{{ old('contact_phone', $supplier->contact_phone) }}">
                  @error('contact_phone')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="mb-3">
                  <label for="contact_email" class="form-label">Email Kontak</label>
                  <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                         id="contact_email" name="contact_email" value="{{ old('contact_email', $supplier->contact_email) }}">
                  @error('contact_email')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Business Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary mb-3">
                  <i class="ri-briefcase-line me-2"></i>
                  Informasi Bisnis
                </h6>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="tax_number" class="form-label">Nomor NPWP</label>
                  <input type="text" class="form-control @error('tax_number') is-invalid @enderror"
                         id="tax_number" name="tax_number" value="{{ old('tax_number', $supplier->tax_number) }}">
                  @error('tax_number')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>

              <div class="col-md-6">
                <div class="mb-3">
                  <label for="npwp_file" class="form-label">File NPWP</label>
                  <input type="file" class="form-control @error('npwp_file') is-invalid @enderror"
                         id="npwp_file" name="npwp_file" accept=".pdf,.jpg,.jpeg,.png">
                  @error('npwp_file')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <div class="form-text">
                    <i class="ri-information-line me-1"></i>
                    Format: PDF, JPG, JPEG, PNG. Maksimal 2MB
                  </div>
                  @if($supplier->npwp_file)
                    <div class="mt-2">
                      <div class="alert alert-info">
                        <i class="ri-file-line me-2"></i>
                        File saat ini:
                        <a href="{{ $supplier->npwp_file_url }}" target="_blank" class="alert-link">
                          {{ $supplier->npwp_file }}
                        </a>
                        <small class="d-block mt-1">Upload file baru untuk mengganti file yang ada</small>
                      </div>
                    </div>
                  @endif
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="payment_terms" class="form-label">Syarat Pembayaran <span class="text-danger">*</span></label>
                  <select class="form-select @error('payment_terms') is-invalid @enderror" 
                          id="payment_terms" name="payment_terms" required>
                    @foreach($paymentTerms as $key => $value)
                      <option value="{{ $key }}" {{ old('payment_terms', $supplier->payment_terms) == $key ? 'selected' : '' }}>
                        {{ $value }}
                      </option>
                    @endforeach
                  </select>
                  @error('payment_terms')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="credit_limit" class="form-label">Limit Kredit</label>
                  <input type="number" class="form-control @error('credit_limit') is-invalid @enderror" 
                         id="credit_limit" name="credit_limit" value="{{ old('credit_limit', $supplier->credit_limit) }}" 
                         min="0" step="0.01">
                  @error('credit_limit')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <div class="form-text">Dalam Rupiah</div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="mb-3">
                  <div class="form-check form-switch mt-4">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                           {{ old('is_active', $supplier->is_active) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">
                      Status Aktif
                    </label>
                  </div>
                </div>
              </div>
              
              <div class="col-12">
                <div class="mb-3">
                  <label for="notes" class="form-label">Catatan</label>
                  <textarea class="form-control @error('notes') is-invalid @enderror" 
                            id="notes" name="notes" rows="3">{{ old('notes', $supplier->notes) }}</textarea>
                  @error('notes')
                    <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="row">
              <div class="col-12">
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i>
                    Update
                  </button>
                  <a href="{{ route('suppliers.show', $supplier) }}" class="btn btn-outline-secondary">
                    <i class="ri-close-line me-1"></i>
                    Batal
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
