<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QR Label - {{ $asset->asset_code }}</title>
    <style>
        @page {
            margin: 0mm;
            padding: 0mm;
            size: {{ $config->width }}mm {{ $config->height }}mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: {{ $config->font_size_content }}pt;
            overflow: hidden;
            background: white;
        }

        .qr-label {
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            border: 1px solid #000;
            background: white;
            position: absolute;
            top: 0;
            left: 0;
            display: table;
            table-layout: fixed;
        }

        .label-row {
            display: table-row;
            height: 100%;
        }

        .qr-section {
            display: table-cell;
            width: {{ $config->qr_size + $config->margin_left + 1 }}mm;
            height: 100%;
            vertical-align: middle;
            text-align: center;
            padding: {{ $config->margin_top }}mm 0mm {{ $config->margin_bottom }}mm {{ $config->margin_left }}mm;
        }

        .qr-code-img {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            display: block;
            margin: 0 auto;
        }

        .separator-section {
            display: table-cell;
            width: 3mm;
            height: 100%;
            vertical-align: middle;
            text-align: center;
            padding: {{ $config->margin_top + 2 }}mm 0mm {{ $config->margin_bottom + 2 }}mm 0mm;
        }

        .separator-line {
            width: 1px;
            height: 100%;
            background-color: #000;
            margin: 0 auto;
        }

        .info-section {
            display: table-cell;
            vertical-align: middle;
            padding: {{ $config->margin_top }}mm {{ $config->margin_right }}mm {{ $config->margin_bottom }}mm 1mm;
            line-height: 1.1;
        }

        .asset-name {
            font-size: {{ $config->font_size_title }}pt;
            font-weight: bold;
            margin-bottom: 1mm;
            word-wrap: break-word;
            overflow-wrap: break-word;
            color: #000;
            line-height: 1.1;
        }

        .asset-code {
            font-size: {{ $config->font_size_content }}pt;
            font-weight: bold;
            margin-bottom: 0.8mm;
            color: #000;
            letter-spacing: 0.3px;
            line-height: 1.1;
        }

        .asset-info {
            font-size: {{ $config->font_size_content - 1 }}pt;
            margin-bottom: 0.5mm;
            word-wrap: break-word;
            overflow-wrap: break-word;
            color: #333;
            line-height: 1.1;
        }

        .asset-info:last-child {
            margin-bottom: 0;
        }

        .asset-info.category {
            font-style: italic;
            color: #666;
        }

        .asset-info.branch {
            font-weight: 600;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="qr-label">
        <div class="label-row">
            <div class="qr-section">
                <img src="{{ $qrCodeBase64 }}" alt="QR Code" class="qr-code-img">
            </div>
            <div class="separator-section">
                <div class="separator-line"></div>
            </div>
            <div class="info-section">
                @if($config->show_asset_name)
                    <div class="asset-name">{{ Str::limit($asset->name, 25) }}</div>
                @endif

                @if($config->show_asset_code)
                    <div class="asset-code">{{ $asset->asset_code }}</div>
                @endif

                @if($config->show_category && $asset->assetCategory)
                    <div class="asset-info category">{{ Str::limit($asset->assetCategory->name, 20) }}</div>
                @endif

                @if($config->show_branch && $asset->branch)
                    <div class="asset-info branch">{{ Str::limit($asset->branch->name, 20) }}</div>
                @endif

                @if($config->show_location && isset($asset->dynamic_fields['location']))
                    <div class="asset-info">{{ Str::limit($asset->dynamic_fields['location'], 20) }}</div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
