<?php $__env->startSection('title', 'Detail <PERSON>rak - ' . $contract->contract_number); ?>

<?php $__env->startSection('page-style'); ?>
<style>
.info-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.info-card h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007bff;
  display: inline-block;
}
.info-item {
  margin-bottom: 1rem;
}
.info-label {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 0.25rem;
}
.info-value {
  font-weight: 600;
  color: #212529;
}
.expiry-status {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  text-align: center;
}
.expiry-normal { 
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}
.expiry-expiring { 
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #212529;
}
.expiry-expired { 
  background: linear-gradient(135deg, #dc3545, #e83e8c);
  color: white;
}
.contract-file {
  border: 2px dashed #007bff;
  border-radius: 12px;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
  text-align: center;
  transition: all 0.3s ease;
}
.contract-file:hover {
  border-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}
.status-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  border: 1px solid #dee2e6;
  margin-bottom: 1.5rem;
}
.audit-card {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #dadce0;
}
.value-highlight {
  font-size: 1.25rem;
  font-weight: 700;
  color: #007bff;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h4 class="mb-1">
              <i class="ri-file-text-line me-2 text-primary"></i>
              Detail Kontrak
            </h4>
            <p class="text-muted mb-0"><?php echo e($contract->contract_number); ?></p>
          </div>
          <div class="d-flex gap-2">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('contracts.edit')): ?>
            <a href="<?php echo e(route('contracts.edit', $contract)); ?>" class="btn btn-primary">
              <i class="ri-edit-line me-1"></i>Edit
            </a>
            <?php endif; ?>
            
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('contracts.download')): ?>
            <?php if($contract->contract_file): ?>
            <a href="<?php echo e(route('contracts.download', $contract)); ?>" class="btn btn-outline-primary">
              <i class="ri-download-line me-1"></i>Download File
            </a>
            <?php endif; ?>
            <?php endif; ?>
            
            <a href="<?php echo e(route('contracts.index')); ?>" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>

        <div class="card-body">
          <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
              <!-- Contract Information -->
              <div class="info-card">
                <h6>
                  <i class="ri-information-line me-2"></i>
                  Informasi Kontrak
                </h6>
                
                <div class="row">
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Nomor Kontrak</div>
                      <div class="info-value"><?php echo e($contract->contract_number); ?></div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Nama Kontrak</div>
                      <div class="info-value"><?php echo e($contract->contract_name); ?></div>
                    </div>
                  </div>
                  
                  <?php if($contract->description): ?>
                  <div class="col-12">
                    <div class="info-item">
                      <div class="info-label">Deskripsi</div>
                      <div class="info-value"><?php echo e($contract->description); ?></div>
                    </div>
                  </div>
                  <?php endif; ?>
                  
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Tipe Kontrak</div>
                      <div class="info-value">
                        <span class="badge bg-info fs-6"><?php echo e($contract->contract_type_text); ?></span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Status</div>
                      <div class="info-value">
                        <span class="badge bg-<?php echo e($contract->status_badge); ?> fs-6"><?php echo e($contract->status_text); ?></span>
                      </div>
                    </div>
                  </div>
                  
                  <?php if($contract->contract_value): ?>
                  <div class="col-12">
                    <div class="info-item">
                      <div class="info-label">Nilai Kontrak</div>
                      <div class="info-value value-highlight">Rp <?php echo e(number_format($contract->contract_value, 0, ',', '.')); ?></div>
                    </div>
                  </div>
                  <?php endif; ?>
                </div>
              </div>

              <!-- Supplier Information -->
              <div class="info-card">
                <h6>
                  <i class="ri-building-line me-2"></i>
                  Informasi Supplier
                </h6>
                
                <div class="row">
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Nama Supplier</div>
                      <div class="info-value"><?php echo e($contract->supplier->name); ?></div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Kode Supplier</div>
                      <div class="info-value"><?php echo e($contract->supplier->supplier_code); ?></div>
                    </div>
                  </div>
                  
                  <?php if($contract->supplier->email): ?>
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Email</div>
                      <div class="info-value">
                        <a href="mailto:<?php echo e($contract->supplier->email); ?>" class="text-primary">
                          <?php echo e($contract->supplier->email); ?>

                        </a>
                      </div>
                    </div>
                  </div>
                  <?php endif; ?>
                  
                  <?php if($contract->supplier->phone): ?>
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Telepon</div>
                      <div class="info-value">
                        <a href="tel:<?php echo e($contract->supplier->phone); ?>" class="text-primary">
                          <?php echo e($contract->supplier->phone); ?>

                        </a>
                      </div>
                    </div>
                  </div>
                  <?php endif; ?>
                  
                  <?php if($contract->division): ?>
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Divisi</div>
                      <div class="info-value"><?php echo e($contract->division->name); ?></div>
                    </div>
                  </div>
                  <?php endif; ?>

                  <?php if($contract->branch): ?>
                  <div class="col-md-6">
                    <div class="info-item">
                      <div class="info-label">Cabang</div>
                      <div class="info-value"><?php echo e($contract->branch->name); ?></div>
                    </div>
                  </div>
                  <?php endif; ?>
                </div>
              </div>

              <!-- Contract Dates -->
              <div class="info-card">
                <h6>
                  <i class="ri-calendar-line me-2"></i>
                  Periode Kontrak
                </h6>
                
                <div class="row">
                  <div class="col-md-4">
                    <div class="info-item">
                      <div class="info-label">Tanggal Kontrak</div>
                      <div class="info-value"><?php echo e($contract->contract_date->format('d/m/Y')); ?></div>
                    </div>
                  </div>
                  
                  <div class="col-md-4">
                    <div class="info-item">
                      <div class="info-label">Tanggal Mulai</div>
                      <div class="info-value"><?php echo e($contract->start_date->format('d/m/Y')); ?></div>
                    </div>
                  </div>
                  
                  <div class="col-md-4">
                    <div class="info-item">
                      <div class="info-label">Tanggal Berakhir</div>
                      <div class="info-value"><?php echo e($contract->end_date->format('d/m/Y')); ?></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Terms & Conditions -->
              <?php if($contract->terms_conditions): ?>
              <div class="info-card">
                <h6>
                  <i class="ri-file-list-line me-2"></i>
                  Syarat & Ketentuan
                </h6>
                <div class="info-value" style="white-space: pre-wrap; line-height: 1.6;"><?php echo e($contract->terms_conditions); ?></div>
              </div>
              <?php endif; ?>

              <!-- Notes -->
              <?php if($contract->notes): ?>
              <div class="info-card">
                <h6>
                  <i class="ri-sticky-note-line me-2"></i>
                  Catatan
                </h6>
                <div class="info-value" style="white-space: pre-wrap; line-height: 1.6;"><?php echo e($contract->notes); ?></div>
              </div>
              <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
              <!-- Expiry Status -->
              <div class="status-card">
                <h6 class="mb-3">
                  <i class="ri-time-line me-2"></i>
                  Status Kadaluarsa
                </h6>
                <div class="expiry-status expiry-<?php echo e($contract->expiry_status); ?>">
                  <?php echo e($contract->expiry_status_text); ?>

                </div>
                <?php if($contract->days_until_expiry !== null): ?>
                <div class="mt-3">
                  <small class="text-muted">
                    <?php if($contract->days_until_expiry >= 0): ?>
                      <i class="ri-calendar-check-line me-1"></i><?php echo e($contract->days_until_expiry); ?> hari lagi
                    <?php else: ?>
                      <i class="ri-calendar-close-line me-1"></i><?php echo e(abs($contract->days_until_expiry)); ?> hari yang lalu
                    <?php endif; ?>
                  </small>
                </div>
                <?php endif; ?>
              </div>

              <!-- Auto Renewal -->
              <?php if($contract->auto_renewal): ?>
              <div class="status-card">
                <h6 class="mb-3">
                  <i class="ri-refresh-line me-2"></i>
                  Perpanjangan Otomatis
                </h6>
                <div class="text-success mb-2">
                  <i class="ri-check-circle-line me-1"></i>
                  <strong>Aktif</strong>
                </div>
                <?php if($contract->renewal_period_months): ?>
                <small class="text-muted">
                  <i class="ri-time-line me-1"></i>
                  Periode: <?php echo e($contract->renewal_period_months); ?> bulan
                </small>
                <?php endif; ?>
              </div>
              <?php endif; ?>

              <!-- Contract File -->
              <?php if($contract->contract_file): ?>
              <div class="status-card">
                <h6 class="mb-3">
                  <i class="ri-file-line me-2"></i>
                  File Kontrak
                </h6>
                <div class="contract-file">
                  <div class="mb-3">
                    <i class="ri-file-pdf-line text-danger" style="font-size: 2.5rem;"></i>
                  </div>
                  <div class="fw-semibold mb-2"><?php echo e(basename($contract->contract_file)); ?></div>
                  <small class="text-muted mb-3 d-block">File kontrak resmi</small>
                  <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('contracts.download')): ?>
                  <a href="<?php echo e(route('contracts.download', $contract)); ?>" class="btn btn-primary btn-sm">
                    <i class="ri-download-line me-1"></i>Download
                  </a>
                  <?php endif; ?>
                </div>
              </div>
              <?php endif; ?>

              <!-- Notification Settings -->
              <div class="status-card">
                <h6 class="mb-3">
                  <i class="ri-notification-line me-2"></i>
                  Pengaturan Notifikasi
                </h6>
                <div class="text-center">
                  <div class="value-highlight"><?php echo e($contract->notification_days); ?></div>
                  <small class="text-muted">hari sebelum kadaluarsa</small>
                </div>
              </div>

              <!-- Audit Information -->
              <div class="audit-card">
                <h6 class="mb-3">
                  <i class="ri-history-line me-2"></i>
                  Informasi Audit
                </h6>

                <div class="info-item">
                  <div class="info-label">Dibuat oleh</div>
                  <div class="info-value"><?php echo e($contract->createdBy->name); ?></div>
                </div>

                <div class="info-item">
                  <div class="info-label">Tanggal dibuat</div>
                  <div class="info-value"><?php echo e($contract->created_at->format('d/m/Y H:i')); ?></div>
                </div>

                <?php if($contract->updatedBy && $contract->updated_at != $contract->created_at): ?>
                <div class="info-item">
                  <div class="info-label">Diperbarui oleh</div>
                  <div class="info-value"><?php echo e($contract->updatedBy->name); ?></div>
                </div>

                <div class="info-item mb-0">
                  <div class="info-label">Terakhir diperbarui</div>
                  <div class="info-value"><?php echo e($contract->updated_at->format('d/m/Y H:i')); ?></div>
                </div>
                <?php endif; ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/master/contracts/show.blade.php ENDPATH**/ ?>