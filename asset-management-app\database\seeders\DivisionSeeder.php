<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DivisionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $divisions = [
            [
                'name' => 'Information Technology',
                'code' => 'IT',
                'description' => 'Divisi yang menangani teknologi informasi, sistem, dan infrastruktur IT perusahaan',
                'head_name' => 'Budi <PERSON>o',
                'head_email' => '<EMAIL>',
                'head_phone' => '***********',
                'is_active' => true,
            ],
            [
                'name' => 'Finance & Accounting',
                'code' => 'FIN',
                'description' => 'Divisi yang menangani keuangan, akuntansi, dan pelaporan keuangan perusahaan',
                'head_name' => 'Sari Dewi',
                'head_email' => '<EMAIL>',
                'head_phone' => '***********',
                'is_active' => true,
            ],
            [
                'name' => 'Human Resources',
                'code' => 'HR',
                'description' => 'Divisi yang menangani sumber daya manusia, rekrutmen, dan pengembangan karyawan',
                'head_name' => '<PERSON>',
                'head_email' => '<EMAIL>',
                'head_phone' => '***********',
                'is_active' => true,
            ],
            [
                'name' => 'Marketing & Sales',
                'code' => 'MKT',
                'description' => 'Divisi yang menangani pemasaran, penjualan, dan hubungan dengan pelanggan',
                'head_name' => 'Lisa Permata',
                'head_email' => '<EMAIL>',
                'head_phone' => '08123456792',
                'is_active' => true,
            ],
            [
                'name' => 'Operations',
                'code' => 'OPS',
                'description' => 'Divisi yang menangani operasional harian, logistik, dan manajemen fasilitas',
                'head_name' => 'Rudi Hartono',
                'head_email' => '<EMAIL>',
                'head_phone' => '08123456793',
                'is_active' => true,
            ],
            [
                'name' => 'Research & Development',
                'code' => 'RND',
                'description' => 'Divisi yang menangani penelitian, pengembangan produk, dan inovasi',
                'head_name' => 'Dr. Maya Sari',
                'head_email' => '<EMAIL>',
                'head_phone' => '08123456794',
                'is_active' => true,
            ],
            [
                'name' => 'Quality Assurance',
                'code' => 'QA',
                'description' => 'Divisi yang menangani kontrol kualitas dan standar mutu produk/layanan',
                'head_name' => 'Andi Wijaya',
                'head_email' => '<EMAIL>',
                'head_phone' => '08123456795',
                'is_active' => true,
            ],
            [
                'name' => 'Legal & Compliance',
                'code' => 'LEG',
                'description' => 'Divisi yang menangani aspek hukum, kepatuhan, dan regulasi perusahaan',
                'head_name' => 'Indira Sari',
                'head_email' => '<EMAIL>',
                'head_phone' => '08123456796',
                'is_active' => true,
            ],
        ];

        foreach ($divisions as $division) {
            \App\Models\Division::create($division);
        }
    }
}
