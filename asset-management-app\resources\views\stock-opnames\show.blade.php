@extends('layouts.contentNavbarLayout')

@section('title', 'Detail Stock Opname - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <a href="{{ route('stock-opnames.index') }}" class="btn btn-outline-secondary btn-sm me-3">
                <i class="ri-arrow-left-line"></i>
              </a>
              <div>
                <h5 class="card-title mb-0">
                  <i class="ri-search-eye-line me-2"></i>
                  {{ $stockOpname->title }}
                </h5>
                <small class="text-muted">{{ $stockOpname->opname_number }}</small>
              </div>
            </div>
            <div>
              @switch($stockOpname->status)
                @case('draft')
                  <span class="badge bg-secondary fs-6">Draft</span>
                  @break
                @case('in_progress')
                  <span class="badge bg-warning fs-6">
                    <i class="ri-play-line me-1"></i>
                    Sedang Berjalan
                  </span>
                  @break
                @case('completed')
                  <span class="badge bg-success fs-6">
                    <i class="ri-check-line me-1"></i>
                    Selesai
                  </span>
                  @break
                @case('cancelled')
                  <span class="badge bg-danger fs-6">
                    <i class="ri-close-line me-1"></i>
                    Dibatalkan
                  </span>
                  @break
              @endswitch
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-primary">
                <i class="ri-database-2-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $statistics['total'] }}</h5>
              <small class="text-muted">Total Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-info">
                <i class="ri-qr-scan-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $statistics['scanned'] }}</h5>
              <small class="text-muted">Sudah Di-scan</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-success">
                <i class="ri-check-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $statistics['found'] }}</h5>
              <small class="text-muted">Ditemukan</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card h-100">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar flex-shrink-0 me-3">
              <span class="avatar-initial rounded bg-label-danger">
                <i class="ri-error-warning-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">{{ $statistics['missing'] }}</h5>
              <small class="text-muted">Hilang</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Progress Card -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="card-title mb-0">Progress Stock Opname</h6>
            <span class="badge bg-primary">{{ $statistics['progress'] }}%</span>
          </div>
          <div class="progress mb-2" style="height: 10px;">
            <div class="progress-bar" role="progressbar" 
                 style="width: {{ $statistics['progress'] }}%" 
                 aria-valuenow="{{ $statistics['progress'] }}" 
                 aria-valuemin="0" 
                 aria-valuemax="100"></div>
          </div>
          <small class="text-muted">
            {{ $statistics['scanned'] }} dari {{ $statistics['total'] }} asset telah di-scan
          </small>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="row">
    <!-- Left Column - Information -->
    <div class="col-lg-8">
      <!-- Stock Opname Info -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-information-line me-2"></i>
            Informasi Stock Opname
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="120">Nomor:</td>
                  <td><strong>{{ $stockOpname->opname_number }}</strong></td>
                </tr>
                <tr>
                  <td class="text-muted">Cabang:</td>
                  <td>
                    <span class="badge bg-info">{{ $stockOpname->branch->name }}</span>
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Kategori:</td>
                  <td>
                    @if($stockOpname->assetCategory)
                      <span class="badge bg-primary">{{ $stockOpname->assetCategory->name }}</span>
                    @else
                      <span class="badge bg-secondary">Semua Kategori</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td class="text-muted">Dibuat oleh:</td>
                  <td>{{ $stockOpname->creator->name }}</td>
                </tr>
                <tr>
                  <td class="text-muted">Tanggal Dibuat:</td>
                  <td>{{ $stockOpname->created_at->format('d/m/Y H:i') }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless">
                <tr>
                  <td class="text-muted" width="120">Status:</td>
                  <td>
                    @switch($stockOpname->status)
                      @case('draft')
                        <span class="badge bg-secondary">Draft</span>
                        @break
                      @case('in_progress')
                        <span class="badge bg-warning">Sedang Berjalan</span>
                        @break
                      @case('completed')
                        <span class="badge bg-success">Selesai</span>
                        @break
                      @case('cancelled')
                        <span class="badge bg-danger">Dibatalkan</span>
                        @break
                    @endswitch
                  </td>
                </tr>
                @if($stockOpname->start_date)
                  <tr>
                    <td class="text-muted">Tanggal Mulai:</td>
                    <td>{{ $stockOpname->start_date->format('d/m/Y H:i') }}</td>
                  </tr>
                @endif
                @if($stockOpname->end_date)
                  <tr>
                    <td class="text-muted">Tanggal Selesai:</td>
                    <td>{{ $stockOpname->end_date->format('d/m/Y H:i') }}</td>
                  </tr>
                @endif
                @if($stockOpname->is_locked)
                  <tr>
                    <td class="text-muted">Asset Lock:</td>
                    <td>
                      <span class="badge bg-warning">
                        <i class="ri-lock-line me-1"></i>
                        Terkunci
                      </span>
                    </td>
                  </tr>
                @endif
              </table>
            </div>
          </div>
          
          @if($stockOpname->description)
            <div class="mt-3">
              <h6>Deskripsi:</h6>
              <p class="text-muted">{{ $stockOpname->description }}</p>
            </div>
          @endif
          
          @if($stockOpname->notes)
            <div class="mt-3">
              <h6>Catatan:</h6>
              <p class="text-muted">{{ $stockOpname->notes }}</p>
            </div>
          @endif
        </div>
      </div>

      <!-- Asset Details -->
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="card-title mb-0">
            <i class="ri-list-check-3 me-2"></i>
            Detail Asset
          </h6>
          <div class="btn-group btn-group-sm" role="group">
            <input type="radio" class="btn-check" name="filterStatus" id="filterAll" value="all" checked>
            <label class="btn btn-outline-primary" for="filterAll">Semua</label>
            
            <input type="radio" class="btn-check" name="filterStatus" id="filterScanned" value="scanned">
            <label class="btn btn-outline-success" for="filterScanned">Di-scan</label>
            
            <input type="radio" class="btn-check" name="filterStatus" id="filterMissing" value="missing">
            <label class="btn btn-outline-danger" for="filterMissing">Hilang</label>
            
            <input type="radio" class="btn-check" name="filterStatus" id="filterPending" value="pending">
            <label class="btn btn-outline-secondary" for="filterPending">Pending</label>
          </div>
        </div>
        <div class="card-body">
          @if($stockOpname->details->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover" id="assetDetailsTable">
                <thead class="table-light">
                  <tr>
                    <th>Kode Asset</th>
                    <th>Nama Asset</th>
                    <th>Status</th>
                    <th>Kondisi</th>
                    <th>Scan Time</th>
                    <th>Scanner</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($stockOpname->details as $detail)
                    <tr class="asset-row" 
                        data-status="{{ $detail->isScanned() ? 'scanned' : 'pending' }}"
                        data-found="{{ $detail->found_status }}">
                      <td>
                        <span class="badge bg-primary">{{ $detail->asset_code }}</span>
                      </td>
                      <td>
                        <div>
                          <strong>{{ $detail->asset_name }}</strong>
                          @if($detail->has_discrepancy)
                            <br><small class="text-warning">
                              <i class="ri-error-warning-line me-1"></i>
                              Ada ketidaksesuaian
                            </small>
                          @endif
                        </div>
                      </td>
                      <td>
                        @if($detail->found_status)
                          <span class="badge {{ $detail->getStatusBadgeClass() }}">
                            {{ ucfirst($detail->found_status) }}
                          </span>
                        @else
                          <span class="badge bg-secondary">Belum di-scan</span>
                        @endif
                      </td>
                      <td>
                        @if($detail->physical_condition)
                          <span class="badge {{ $detail->getConditionBadgeClass() }}">
                            {{ ucfirst($detail->physical_condition) }}
                          </span>
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>
                        @if($detail->scanned_at)
                          {{ $detail->scanned_at->format('d/m/Y H:i') }}
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>
                        @if($detail->scanner)
                          {{ $detail->scanner->name }}
                        @else
                          <span class="text-muted">-</span>
                        @endif
                      </td>
                    </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
          @else
            <div class="text-center py-4">
              <i class="ri-inbox-line ri-48px text-muted mb-3"></i>
              <h6 class="text-muted">Belum Ada Detail Asset</h6>
              <p class="text-muted">Detail asset akan dibuat saat stock opname dimulai.</p>
            </div>
          @endif
        </div>
      </div>
    </div>

    <!-- Right Column - Actions -->
    <div class="col-lg-4">
      <!-- Action Card -->
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-settings-4-line me-2"></i>
            Aksi
          </h6>
        </div>
        <div class="card-body">
          @if($stockOpname->status === 'draft')
            <form method="POST" action="{{ route('stock-opnames.start', $stockOpname) }}" class="mb-3">
              @csrf
              <button type="submit" class="btn btn-warning w-100"
                      onclick="return confirm('Mulai stock opname? Asset operations akan dikunci.')">
                <i class="ri-play-line me-2"></i>
                Mulai Stock Opname
              </button>
            </form>
          @endif

          @if($stockOpname->status === 'in_progress')
            <a href="{{ route('stock-opnames.scan-detail', $stockOpname) }}" class="btn btn-primary w-100 mb-3">
              <i class="ri-qr-scan-line me-2"></i>
              Scan Asset
            </a>
            
            <form method="POST" action="{{ route('stock-opnames.complete', $stockOpname) }}" class="mb-3">
              @csrf
              <button type="submit" class="btn btn-success w-100"
                      onclick="return confirm('Selesaikan stock opname? Asset operations akan dibuka kembali.')">
                <i class="ri-check-line me-2"></i>
                Selesaikan Stock Opname
              </button>
            </form>
          @endif

          @if($stockOpname->status === 'completed')
            <a href="{{ route('stock-opnames.report', $stockOpname) }}" class="btn btn-info w-100 mb-3">
              <i class="ri-file-text-line me-2"></i>
              Lihat Laporan
            </a>
          @endif

          <!-- Hardcopy Form - Only available for in_progress status -->
          @if($stockOpname->status === 'in_progress')
            <a href="{{ route('stock-opnames.hardcopy-form', $stockOpname) }}" class="btn btn-outline-success w-100 mb-3" target="_blank">
              <i class="ri-file-excel-2-line me-2"></i>
              Download Form Hardcopy
            </a>
          @endif

          @if(in_array($stockOpname->status, ['draft', 'in_progress']))
            <form method="POST" action="{{ route('stock-opnames.cancel', $stockOpname) }}">
              @csrf
              <button type="submit" class="btn btn-outline-danger w-100"
                      onclick="return confirm('Batalkan stock opname ini?')">
                <i class="ri-close-line me-2"></i>
                Batalkan Stock Opname
              </button>
            </form>
          @endif
        </div>
      </div>

      <!-- Quick Stats -->
      @if($statistics['discrepancy'] > 0)
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0 text-warning">
              <i class="ri-error-warning-line me-2"></i>
              Ketidaksesuaian
            </h6>
          </div>
          <div class="card-body">
            <p class="text-warning mb-2">
              <strong>{{ $statistics['discrepancy'] }}</strong> asset memiliki ketidaksesuaian
            </p>
            <small class="text-muted">
              Periksa detail asset untuk melihat ketidaksesuaian yang ditemukan.
            </small>
          </div>
        </div>
      @endif
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const filterButtons = document.querySelectorAll('input[name="filterStatus"]');
  const assetRows = document.querySelectorAll('.asset-row');

  filterButtons.forEach(button => {
    button.addEventListener('change', function() {
      const filterValue = this.value;
      
      assetRows.forEach(row => {
        const status = row.dataset.status;
        const foundStatus = row.dataset.found;
        
        let show = false;
        
        switch(filterValue) {
          case 'all':
            show = true;
            break;
          case 'scanned':
            show = status === 'scanned';
            break;
          case 'missing':
            show = foundStatus === 'not_found';
            break;
          case 'pending':
            show = status === 'pending';
            break;
        }
        
        row.style.display = show ? '' : 'none';
      });
    });
  });
});
</script>

@if(session('success'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-success show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-check-line me-2"></i>
      <div class="me-auto fw-semibold">Success</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('success') }}
    </div>
  </div>
@endif

@if(session('error'))
  <div class="bs-toast toast toast-placement-ex m-2 fade bg-danger show top-0 end-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="toast-header">
      <i class="ri-error-warning-line me-2"></i>
      <div class="me-auto fw-semibold">Error</div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body">
      {{ session('error') }}
    </div>
  </div>
@endif

@endsection
