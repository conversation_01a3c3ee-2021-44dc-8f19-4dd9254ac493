<?php $__env->startSection('title', 'Master Supplier'); ?>

<?php $__env->startSection('page-style'); ?>
<style>
.filter-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.table tbody tr.table-secondary {
  opacity: 0.7;
}

.table th {
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  vertical-align: middle;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-xxl flex-grow-1 container-p-y">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="ri-building-line me-2"></i>
              Master Supplier
            </h5>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                <i class="ri-filter-line me-1"></i>
                Filter
              </button>
              <a href="<?php echo e(route('suppliers.create')); ?>" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>
                Tambah Supplier
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="collapse <?php echo e(request()->hasAny(['search', 'city', 'province', 'business_type', 'payment_terms', 'is_active']) ? 'show' : ''); ?>" id="filterCollapse">
    <div class="card filter-card mb-4">
      <div class="card-body">
        <form method="GET" action="<?php echo e(route('suppliers.index')); ?>">
          <div class="row g-3">
            <div class="col-md-3">
              <label class="form-label">Pencarian</label>
              <input type="text" class="form-control" name="search" value="<?php echo e(request('search')); ?>" 
                     placeholder="Kode, nama, email, telepon...">
            </div>
            <div class="col-md-2">
              <label class="form-label">Kota</label>
              <select class="form-select" name="city">
                <option value="">Semua Kota</option>
                <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($city); ?>" <?php echo e(request('city') == $city ? 'selected' : ''); ?>>
                    <?php echo e($city); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Provinsi</label>
              <select class="form-select" name="province">
                <option value="">Semua Provinsi</option>
                <?php $__currentLoopData = $provinces; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $province): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($province); ?>" <?php echo e(request('province') == $province ? 'selected' : ''); ?>>
                    <?php echo e($province); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Jenis Usaha</label>
              <select class="form-select" name="business_type">
                <option value="">Semua Jenis</option>
                <?php $__currentLoopData = $businessTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>" <?php echo e(request('business_type') == $key ? 'selected' : ''); ?>>
                    <?php echo e($value); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Pembayaran</label>
              <select class="form-select" name="payment_terms">
                <option value="">Semua Syarat</option>
                <?php $__currentLoopData = $paymentTerms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>" <?php echo e(request('payment_terms') == $key ? 'selected' : ''); ?>>
                    <?php echo e($value); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <div class="col-md-1">
              <label class="form-label">Status</label>
              <select class="form-select" name="is_active">
                <option value="">Semua</option>
                <option value="1" <?php echo e(request('is_active') === '1' ? 'selected' : ''); ?>>Aktif</option>
                <option value="0" <?php echo e(request('is_active') === '0' ? 'selected' : ''); ?>>Nonaktif</option>
              </select>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i>
                Cari
              </button>
              <a href="<?php echo e(route('suppliers.index')); ?>" class="btn btn-outline-secondary">
                <i class="ri-refresh-line me-1"></i>
                Reset
              </a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Suppliers Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <?php if($suppliers->count() > 0): ?>
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Kode Supplier</th>
                    <th>Nama Supplier</th>
                    <th>Perusahaan</th>
                    <th>Lokasi</th>
                    <th>Kontak</th>
                    <th>Jenis Usaha</th>
                    <th>Pembayaran</th>
                    <th>Status</th>
                    <th width="120">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="<?php echo e(!$supplier->is_active ? 'table-secondary' : ''); ?>">
                      <td>
                        <strong class="text-primary"><?php echo e($supplier->supplier_code); ?></strong>
                      </td>
                      <td>
                        <div>
                          <strong><?php echo e($supplier->name); ?></strong>
                          <?php if($supplier->contact_person): ?>
                            <br><small class="text-muted">CP: <?php echo e($supplier->contact_person); ?></small>
                          <?php endif; ?>
                        </div>
                      </td>
                      <td>
                        <?php if($supplier->company_name): ?>
                          <?php echo e($supplier->company_name); ?>

                        <?php else: ?>
                          <span class="text-muted">-</span>
                        <?php endif; ?>
                      </td>
                      <td>
                        <?php if($supplier->city || $supplier->province): ?>
                          <?php echo e($supplier->city); ?><?php echo e($supplier->city && $supplier->province ? ', ' : ''); ?><?php echo e($supplier->province); ?>

                        <?php else: ?>
                          <span class="text-muted">-</span>
                        <?php endif; ?>
                      </td>
                      <td>
                        <div>
                          <?php if($supplier->phone): ?>
                            <div><i class="ri-phone-line me-1"></i><?php echo e($supplier->phone); ?></div>
                          <?php endif; ?>
                          <?php if($supplier->email): ?>
                            <div><i class="ri-mail-line me-1"></i><?php echo e($supplier->email); ?></div>
                          <?php endif; ?>
                          <?php if(!$supplier->phone && !$supplier->email): ?>
                            <span class="text-muted">-</span>
                          <?php endif; ?>
                        </div>
                      </td>
                      <td>
                        <?php if($supplier->business_type): ?>
                          <span class="badge bg-info"><?php echo e($supplier->business_type); ?></span>
                        <?php else: ?>
                          <span class="text-muted">-</span>
                        <?php endif; ?>
                      </td>
                      <td>
                        <span class="badge bg-secondary"><?php echo e($supplier->payment_terms_text); ?></span>
                      </td>
                      <td>
                        <?php echo $supplier->status_badge; ?>

                      </td>
                      <td>
                        <div class="dropdown">
                          <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="ri-more-2-line"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li>
                              <a class="dropdown-item" href="<?php echo e(route('suppliers.show', $supplier)); ?>">
                                <i class="ri-eye-line me-2"></i>
                                Lihat Detail
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item" href="<?php echo e(route('suppliers.edit', $supplier)); ?>">
                                <i class="ri-edit-line me-2"></i>
                                Edit
                              </a>
                            </li>
                            <li>
                              <form action="<?php echo e(route('suppliers.toggle-status', $supplier)); ?>" method="POST" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <button type="submit" class="dropdown-item <?php echo e($supplier->is_active ? 'text-warning' : 'text-success'); ?>">
                                  <i class="ri-<?php echo e($supplier->is_active ? 'pause' : 'play'); ?>-line me-2"></i>
                                  <?php echo e($supplier->is_active ? 'Nonaktifkan' : 'Aktifkan'); ?>

                                </button>
                              </form>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                              <form action="<?php echo e(route('suppliers.destroy', $supplier)); ?>" method="POST"
                                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus supplier ini?')" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="dropdown-item text-danger">
                                  <i class="ri-delete-bin-line me-2"></i>
                                  Hapus
                                </button>
                              </form>
                            </li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
              </table>
            </div>
          <?php else: ?>
            <div class="text-center py-5">
              <div class="avatar avatar-xl mx-auto mb-3">
                <span class="avatar-initial rounded-circle bg-light">
                  <i class="ri-building-line ri-36px text-muted"></i>
                </span>
              </div>
              <h6 class="text-muted">Tidak ada supplier ditemukan</h6>
              <p class="text-muted">Belum ada data supplier atau sesuaikan filter pencarian Anda.</p>
              <a href="<?php echo e(route('suppliers.create')); ?>" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>
                Tambah Supplier Pertama
              </a>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <?php if($suppliers->hasPages()): ?>
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <?php echo e($suppliers->withQueryString()->links()); ?>

          </div>
        </div>
      </div>
    </div>
  <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\asset-management\asset-management-app\resources\views/suppliers/index.blade.php ENDPATH**/ ?>