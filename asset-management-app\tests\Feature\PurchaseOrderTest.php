<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Supplier;
use App\Models\Branch;
use App\Models\DocumentNumber;

class PurchaseOrderTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $supplier;
    protected $branch;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user with admin role
        $adminRole = Role::factory()->create([
            'name' => 'Admin',
            'slug' => 'admin'
        ]);
        
        $this->user = User::factory()->create([
            'role_id' => $adminRole->id
        ]);
        
        // Create test supplier
        $this->supplier = Supplier::factory()->create([
            'name' => 'Test Supplier',
            'supplier_code' => 'SUP001',
            'is_active' => true
        ]);
        
        // Create test branch
        $this->branch = Branch::factory()->create([
            'name' => 'Test Branch',
            'branch_code' => 'BR001'
        ]);
        
        // Create permissions
        $permissions = [
            'purchase-orders.view',
            'purchase-orders.create',
            'purchase-orders.edit',
            'purchase-orders.delete',
            'purchase-orders.submit',
            'purchase-orders.approve',
            'purchase-orders.print'
        ];
        
        foreach ($permissions as $permissionSlug) {
            $permission = Permission::factory()->create([
                'slug' => $permissionSlug,
                'name' => ucfirst(str_replace(['-', '.'], ' ', $permissionSlug)),
                'module' => 'Purchase Order'
            ]);
            $adminRole->permissions()->attach($permission->id);
        }
    }

    /** @test */
    public function user_can_view_purchase_orders_index()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('purchase-orders.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('purchase-orders.index');
        $response->assertSee('Daftar Purchase Order');
    }

    /** @test */
    public function user_can_view_create_purchase_order_form()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('purchase-orders.create'));
        
        $response->assertStatus(200);
        $response->assertViewIs('purchase-orders.create');
        $response->assertSee('Buat Purchase Order');
    }

    /** @test */
    public function user_can_create_purchase_order_with_items()
    {
        $this->actingAs($this->user);
        
        $poData = [
            'po_date' => now()->format('Y-m-d'),
            'delivery_date' => now()->addDays(7)->format('Y-m-d'),
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'tax_percentage' => 10,
            'discount_percentage' => 5,
            'notes' => 'Test PO notes',
            'items' => [
                [
                    'item_name' => 'Test Item 1',
                    'item_description' => 'Test Description 1',
                    'quantity' => 2,
                    'unit' => 'pcs',
                    'unit_price' => 100000
                ],
                [
                    'item_name' => 'Test Item 2',
                    'item_description' => 'Test Description 2',
                    'quantity' => 1,
                    'unit' => 'unit',
                    'unit_price' => 200000
                ]
            ]
        ];
        
        $response = $this->post(route('purchase-orders.store'), $poData);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('purchase_orders', [
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'status' => 'draft'
        ]);
        
        $purchaseOrder = PurchaseOrder::latest()->first();
        $this->assertEquals(2, $purchaseOrder->items()->count());
        $this->assertEquals(400000, $purchaseOrder->subtotal_amount); // 2*100000 + 1*200000
    }

    /** @test */
    public function purchase_order_calculates_totals_correctly()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'tax_percentage' => 10,
            'discount_percentage' => 5
        ]);
        
        // Add items
        PurchaseOrderItem::factory()->create([
            'purchase_order_id' => $purchaseOrder->id,
            'quantity' => 2,
            'unit_price' => 100000
        ]);
        
        PurchaseOrderItem::factory()->create([
            'purchase_order_id' => $purchaseOrder->id,
            'quantity' => 1,
            'unit_price' => 200000
        ]);
        
        $purchaseOrder->refresh();
        $purchaseOrder->calculateTotals();
        
        $this->assertEquals(400000, $purchaseOrder->subtotal_amount); // 2*100000 + 1*200000
        $this->assertEquals(20000, $purchaseOrder->discount_amount); // 5% of 400000
        $this->assertEquals(38000, $purchaseOrder->tax_amount); // 10% of (400000-20000)
        $this->assertEquals(418000, $purchaseOrder->total_amount); // 400000 - 20000 + 38000
    }

    /** @test */
    public function user_can_submit_purchase_order_for_approval()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'status' => 'draft'
        ]);
        
        // Add at least one item
        PurchaseOrderItem::factory()->create([
            'purchase_order_id' => $purchaseOrder->id
        ]);
        
        $response = $this->post(route('purchase-orders.submit', $purchaseOrder));
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $purchaseOrder->refresh();
        $this->assertEquals('submitted', $purchaseOrder->status);
    }

    /** @test */
    public function user_can_approve_purchase_order()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'status' => 'submitted'
        ]);
        
        $response = $this->post(route('purchase-orders.approve', $purchaseOrder), [
            'approval_notes' => 'Approved for testing'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $purchaseOrder->refresh();
        $this->assertEquals('approved', $purchaseOrder->status);
        $this->assertEquals($this->user->id, $purchaseOrder->approved_by);
        $this->assertEquals('Approved for testing', $purchaseOrder->approval_notes);
        $this->assertNotNull($purchaseOrder->approved_at);
    }

    /** @test */
    public function user_can_reject_purchase_order()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'status' => 'submitted'
        ]);
        
        $response = $this->post(route('purchase-orders.reject', $purchaseOrder), [
            'approval_notes' => 'Rejected for testing'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        $purchaseOrder->refresh();
        $this->assertEquals('rejected', $purchaseOrder->status);
        $this->assertEquals('Rejected for testing', $purchaseOrder->approval_notes);
    }

    /** @test */
    public function user_can_view_purchase_order_details()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id
        ]);
        
        $response = $this->get(route('purchase-orders.show', $purchaseOrder));
        
        $response->assertStatus(200);
        $response->assertViewIs('purchase-orders.show');
        $response->assertSee($purchaseOrder->po_number);
    }

    /** @test */
    public function user_can_edit_draft_purchase_order()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'status' => 'draft'
        ]);
        
        $response = $this->get(route('purchase-orders.edit', $purchaseOrder));
        
        $response->assertStatus(200);
        $response->assertViewIs('purchase-orders.edit');
        $response->assertSee('Edit Purchase Order');
    }

    /** @test */
    public function user_cannot_edit_approved_purchase_order()
    {
        $this->actingAs($this->user);
        
        $purchaseOrder = PurchaseOrder::factory()->create([
            'supplier_id' => $this->supplier->id,
            'branch_id' => $this->branch->id,
            'created_by' => $this->user->id,
            'status' => 'approved'
        ]);
        
        $response = $this->get(route('purchase-orders.edit', $purchaseOrder));
        
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /** @test */
    public function purchase_order_generates_correct_document_number()
    {
        // Create document number configuration
        DocumentNumber::factory()->create([
            'document_type' => 'purchase_order',
            'branch_id' => $this->branch->id,
            'prefix' => 'PO',
            'current_number' => 0
        ]);
        
        $poNumber = DocumentNumber::getPurchaseOrderNumber($this->branch->id);
        
        $this->assertStringContainsString('PO', $poNumber);
        $this->assertStringContainsString(date('Y'), $poNumber);
    }

    /** @test */
    public function terbilang_helper_converts_numbers_correctly()
    {
        $amount = 1500000;
        $terbilang = terbilang_rupiah($amount);
        
        $this->assertStringContainsString('satu juta lima ratus ribu rupiah', strtolower($terbilang));
    }
}
