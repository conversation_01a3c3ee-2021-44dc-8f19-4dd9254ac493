<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Details - {{ $asset->asset_code }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .asset-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 2rem auto;
            max-width: 800px;
        }
        
        .asset-header {
            background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .asset-code {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .asset-name {
            font-size: 1.5rem;
            opacity: 0.9;
        }
        
        .asset-body {
            padding: 2rem;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .info-title {
            color: #566a7f;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e7e7ff;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #566a7f;
        }
        
        .info-value {
            color: #697a8d;
            text-align: right;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-maintenance { background: #fff3cd; color: #856404; }
        .status-disposed { background: #f5c6cb; color: #721c24; }
        
        .dynamic-field {
            background: white;
            border: 1px solid #e7e7ff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .dynamic-field-label {
            font-weight: 600;
            color: #566a7f;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .dynamic-field-value {
            color: #697a8d;
        }
        
        .qr-footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e7e7ff;
        }
        
        .asset-image {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .asset-card {
                margin: 1rem;
                border-radius: 15px;
            }
            
            .asset-header {
                padding: 1.5rem;
            }
            
            .asset-code {
                font-size: 1.5rem;
            }
            
            .asset-name {
                font-size: 1.2rem;
            }
            
            .asset-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="asset-card">
            <!-- Header -->
            <div class="asset-header">
                <div class="asset-code">{{ $asset->asset_code }}</div>
                <div class="asset-name">{{ $asset->name }}</div>
                <div class="mt-3">
                    <span class="status-badge status-{{ $asset->status }}">
                        {{ ucfirst($asset->status) }}
                    </span>
                </div>
            </div>
            
            <!-- Body -->
            <div class="asset-body">
                <!-- Basic Information -->
                <div class="info-section">
                    <div class="info-title">
                        <i class="ri-information-line me-2"></i>
                        Informasi Dasar
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">Kategori</span>
                        <span class="info-value">
                            <span class="badge bg-info">{{ $asset->assetCategory->name ?? '-' }}</span>
                        </span>
                    </div>
                    
                    @if($asset->assetType)
                        <div class="info-item">
                            <span class="info-label">Tipe Asset</span>
                            <span class="info-value">{{ $asset->assetType->name }}</span>
                        </div>
                    @endif
                    
                    <div class="info-item">
                        <span class="info-label">Cabang</span>
                        <span class="info-value">{{ $asset->branch->name ?? '-' }}</span>
                    </div>
                    
                    @if($asset->description)
                        <div class="info-item">
                            <span class="info-label">Deskripsi</span>
                            <span class="info-value">{{ $asset->description }}</span>
                        </div>
                    @endif
                    
                    <div class="info-item">
                        <span class="info-label">Tanggal Dibuat</span>
                        <span class="info-value">{{ $asset->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                </div>
                
                <!-- Asset Image -->
                @if($asset->image)
                    <div class="info-section">
                        <div class="info-title">
                            <i class="ri-image-line me-2"></i>
                            Foto Asset
                        </div>
                        <div class="text-center">
                            <img src="{{ Storage::url($asset->image) }}" alt="{{ $asset->name }}" class="asset-image">
                        </div>
                    </div>
                @endif
                
                <!-- Dynamic Fields -->
                @if($asset->dynamic_fields && count($asset->dynamic_fields) > 0)
                    @foreach($fieldConfigurations as $groupKey => $fields)
                        @php
                            $groupName = $fieldGroups[$groupKey] ?? ucwords(str_replace('_', ' ', $groupKey));
                            
                            // Get group metadata from lookup
                            $groupLookup = \App\Models\Lookup::where('lookup_code', 'FIELD_GROUP')
                                ->where('is_active', true)
                                ->whereJsonContains('metadata->value', $groupKey)
                                ->first();
                            
                            $groupIcon = $groupLookup->metadata['icon'] ?? 'ri-settings-4-line';
                            
                            // Check if group has any data
                            $hasData = false;
                            foreach ($fields as $field) {
                                if (isset($asset->dynamic_fields[$field->field_name]) && !empty($asset->dynamic_fields[$field->field_name])) {
                                    $hasData = true;
                                    break;
                                }
                            }
                        @endphp

                        @if($hasData)
                            <div class="info-section">
                                <div class="info-title">
                                    <i class="{{ $groupIcon }} me-2"></i>
                                    {{ $groupName }}
                                </div>
                                
                                @foreach($fields as $field)
                                    @if(isset($asset->dynamic_fields[$field->field_name]) && !empty($asset->dynamic_fields[$field->field_name]))
                                        <div class="dynamic-field">
                                            <div class="dynamic-field-label">{{ $field->field_label }}</div>
                                            <div class="dynamic-field-value">
                                                @if($field->field_type === 'file')
                                                    @if($asset->dynamic_fields[$field->field_name])
                                                        <a href="{{ Storage::url($asset->dynamic_fields[$field->field_name]) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="ri-download-line me-1"></i>
                                                            Download File
                                                        </a>
                                                    @else
                                                        -
                                                    @endif
                                                @elseif($field->field_type === 'checkbox')
                                                    @if(is_array($asset->dynamic_fields[$field->field_name]))
                                                        @foreach($asset->dynamic_fields[$field->field_name] as $value)
                                                            <span class="badge bg-secondary me-1">{{ $value }}</span>
                                                        @endforeach
                                                    @else
                                                        {{ $asset->dynamic_fields[$field->field_name] }}
                                                    @endif
                                                @elseif($field->field_type === 'url')
                                                    <a href="{{ $asset->dynamic_fields[$field->field_name] }}" target="_blank">
                                                        {{ $asset->dynamic_fields[$field->field_name] }}
                                                    </a>
                                                @elseif($field->field_type === 'email')
                                                    <a href="mailto:{{ $asset->dynamic_fields[$field->field_name] }}">
                                                        {{ $asset->dynamic_fields[$field->field_name] }}
                                                    </a>
                                                @else
                                                    {{ $asset->dynamic_fields[$field->field_name] }}
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        @endif
                    @endforeach
                @endif
            </div>
            
            <!-- Footer -->
            <div class="qr-footer">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="ri-qr-code-line me-1"></i>
                            Scanned via QR Code
                        </small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <small class="text-muted">
                            <i class="ri-time-line me-1"></i>
                            {{ now()->format('d/m/Y H:i') }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
