@extends('layouts.contentNavbarLayout')

@section('title', 'Detail User - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master User /</span> Detail User
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Detail User: {{ $user->name }}</h5>
          <div class="d-flex gap-2">
            <a href="{{ route('master.users.edit', $user) }}" class="btn btn-primary btn-sm">
              <i class="ri-pencil-line me-1"></i>Edit
            </a>
            <a href="{{ route('master.users.index') }}" class="btn btn-outline-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Kembali
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Nama Lengkap</label>
                <p class="fw-bold">{{ $user->name }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Username</label>
                <p><span class="badge bg-info fs-6">{{ $user->username }}</span></p>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label text-muted">Email</label>
            <p>{{ $user->email }}</p>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label text-muted">Role</label>
                <p>
                  <span class="badge bg-{{ $user->role->slug === 'super-admin' ? 'danger' : ($user->role->slug === 'admin' ? 'warning' : 'primary') }} fs-6">
                    {{ $user->role->name }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label text-muted">Cabang</label>
                <p class="fw-bold">
                  {{ $user->branch->name }}
                  <br><small class="text-muted">{{ $user->branch->code }}</small>
                </p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label text-muted">Divisi</label>
                @if($user->division)
                  <p class="fw-bold">
                    {{ $user->division->name }}
                    <br><small class="text-muted">{{ $user->division->code }}</small>
                  </p>
                @else
                  <p class="text-muted">Belum ditentukan</p>
                @endif
              </div>
            </div>
          </div>

          @if($user->phone)
          <div class="mb-3">
            <label class="form-label text-muted">Nomor Telepon</label>
            <p>{{ $user->phone }}</p>
          </div>
          @endif

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <p>
                  <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }} fs-6">
                    {{ $user->is_active ? 'Aktif' : 'Non-Aktif' }}
                  </span>
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Terakhir Login</label>
                <p>{{ $user->updated_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Dibuat</label>
                <p>{{ $user->created_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label text-muted">Terakhir Diupdate</label>
                <p>{{ $user->updated_at->format('d/m/Y H:i') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Permissions Card -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">Permissions ({{ $user->role->permissions->count() }} permission)</h6>
        </div>
        <div class="card-body">
          @if($user->role->permissions->count() > 0)
            @php
              $permissionsByModule = $user->role->permissions->groupBy('module');
            @endphp
            @foreach($permissionsByModule as $module => $permissions)
            <div class="mb-4">
              <h6 class="text-primary mb-3">
                <i class="ri-folder-line me-1"></i>{{ ucfirst($module) }}
              </h6>
              <div class="row">
                @foreach($permissions as $permission)
                <div class="col-md-6 mb-2">
                  <div class="d-flex align-items-center">
                    <i class="ri-check-line text-success me-2"></i>
                    <span>{{ $permission->name }}</span>
                  </div>
                </div>
                @endforeach
              </div>
            </div>
            @endforeach
          @else
            <div class="text-center py-4">
              <i class="ri-shield-line display-4 text-muted mb-2"></i>
              <p class="text-muted">Tidak ada permission yang diberikan untuk user ini.</p>
            </div>
          @endif
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Informasi User</h6>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-3">
            <div class="avatar avatar-lg me-3">
              <span class="avatar-initial rounded-circle bg-label-primary">
                {{ strtoupper(substr($user->name, 0, 2)) }}
              </span>
            </div>
            <div>
              <h6 class="mb-0">{{ $user->name }}</h6>
              <small class="text-muted">{{ $user->username }}</small>
            </div>
          </div>
          
          <div class="alert alert-info">
            <h6 class="alert-heading">Informasi:</h6>
            <ul class="mb-0">
              <li>Role: {{ $user->role->name }}</li>
              <li>Cabang: {{ $user->branch->name }}</li>
              <li>Divisi: {{ $user->division ? $user->division->name : 'Belum ditentukan' }}</li>
              <li>{{ $user->role->permissions->count() }} Permission</li>
              <li>Bergabung {{ $user->created_at->diffForHumans() }}</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h6 class="mb-0">Aksi</h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('master.users.edit', $user) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i>Edit User
            </a>
            
            @if($user->is_active)
              <button class="btn btn-warning" onclick="toggleStatus(false)">
                <i class="ri-pause-circle-line me-1"></i>Non-aktifkan
              </button>
            @else
              <button class="btn btn-success" onclick="toggleStatus(true)">
                <i class="ri-play-circle-line me-1"></i>Aktifkan
              </button>
            @endif
            
            @if($user->id !== auth()->id())
            <form action="{{ route('master.users.destroy', $user) }}" method="POST" id="deleteForm">
              @csrf
              @method('DELETE')
              <button type="button" class="btn btn-danger w-100" onclick="confirmDelete()">
                <i class="ri-delete-bin-line me-1"></i>Hapus User
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleStatus(status) {
  if (confirm('Yakin ingin mengubah status user ini?')) {
    // Create form to toggle status
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("master.users.update", $user) }}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'PUT';
    
    const statusField = document.createElement('input');
    statusField.type = 'hidden';
    statusField.name = 'is_active';
    statusField.value = status ? '1' : '0';
    
    // Copy other fields
    const fields = ['name', 'username', 'email', 'role_id', 'branch_id', 'phone'];
    fields.forEach(field => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = field;
      input.value = userData[field];
      form.appendChild(input);
    });
    
    form.appendChild(csrfToken);
    form.appendChild(methodField);
    form.appendChild(statusField);
    
    document.body.appendChild(form);
    form.submit();
  }
}

function confirmDelete() {
  if (confirm('Yakin ingin menghapus user ini?\n\nTindakan ini tidak dapat dibatalkan!')) {
    document.getElementById('deleteForm').submit();
  }
}
</script>
@endsection
