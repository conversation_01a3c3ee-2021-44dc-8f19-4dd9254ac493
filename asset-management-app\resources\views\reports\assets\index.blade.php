@extends('layouts/contentNavbarLayout')

@section('title', 'Laporan Asset')

@section('page-style')
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
<link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}">
@endsection

@section('vendor-script')
<script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
<script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-0">
              <i class="ri-file-chart-line me-2"></i>
              Laporan Asset
            </h5>
            <small class="text-muted">Laporan data asset dengan filter cabang, kategori, tanggal, dan status</small>
          </div>
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
              <i class="ri-file-excel-line me-1"></i>
              Export Excel
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
              <i class="ri-refresh-line me-1"></i>
              Reset Filter
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-primary">
                <i class="ri-computer-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['total_assets']) }}</h4>
              <small class="text-muted">Total Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-success">
                <i class="ri-check-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['active_assets']) }}</h4>
              <small class="text-muted">Asset Aktif</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-warning">
                <i class="ri-tools-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h4 class="mb-0">{{ number_format($stats['maintenance_assets']) }}</h4>
              <small class="text-muted">Maintenance</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6">
      <div class="card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="avatar avatar-md me-3">
              <span class="avatar-initial rounded-circle bg-info">
                <i class="ri-money-dollar-circle-line ri-24px"></i>
              </span>
            </div>
            <div>
              <h5 class="mb-0">Rp {{ number_format($stats['total_value'], 0, ',', '.') }}</h5>
              <small class="text-muted">Total Nilai Asset</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Form -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="ri-filter-line me-2"></i>
            Filter Laporan
          </h6>
        </div>
        <div class="card-body">
          <form method="GET" action="{{ route('reports.assets.index') }}" id="filterForm">
            <div class="row">
              <div class="col-md-3 mb-3">
                <label class="form-label">Cabang</label>
                <select name="branch_id" class="form-select">
                  <option value="">Semua Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Kategori Asset</label>
                <select name="category_id" class="form-select">
                  <option value="">Semua Kategori</option>
                  @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                      {{ $category->name }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                  <option value="">Semua Status</option>
                  @foreach($statusOptions as $value => $label)
                    <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                      {{ $label }}
                    </option>
                  @endforeach
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Dari</label>
                <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Tanggal Beli Sampai</label>
                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
              </div>
              <div class="col-md-9 mb-3 d-flex align-items-end">
                <div class="d-flex gap-2">
                  <button type="submit" class="btn btn-primary">
                    <i class="ri-search-line me-1"></i>
                    Filter
                  </button>
                  <a href="{{ route('reports.assets.index') }}" class="btn btn-outline-secondary">
                    <i class="ri-refresh-line me-1"></i>
                    Reset
                  </a>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Asset Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="ri-table-line me-2"></i>
            Data Asset ({{ $assets->total() }} asset)
          </h6>
          <div class="d-flex gap-2">
            <span class="badge bg-primary">{{ $assets->count() }} ditampilkan</span>
          </div>
        </div>
        <div class="card-body">
          @if($assets->count() > 0)
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>No</th>
                    <th>Kode Asset</th>
                    <th>Nama Asset</th>
                    <th>Kategori</th>
                    <th>Cabang</th>
                    <th>Status</th>
                    <th>Kondisi</th>
                    <th>Tanggal Beli</th>
                    <th>Harga Beli</th>
                    <th>Assigned To</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($assets as $index => $asset)
                  <tr>
                    <td>{{ $assets->firstItem() + $index }}</td>
                    <td>
                      <span class="badge bg-info">{{ $asset->asset_code }}</span>
                    </td>
                    <td>
                      <div>
                        <strong>{{ $asset->name }}</strong>
                        @if($asset->brand || $asset->model)
                          <br><small class="text-muted">{{ $asset->brand }} {{ $asset->model }}</small>
                        @endif
                      </div>
                    </td>
                    <td>{{ $asset->category->name ?? '-' }}</td>
                    <td>{{ $asset->branch->name ?? '-' }}</td>
                    <td>
                      <span class="badge bg-{{ $asset->status_badge }}">
                        {{ $asset->status_text }}
                      </span>
                    </td>
                    <td>
                      <span class="badge bg-{{ $asset->condition_badge }}">
                        {{ $asset->condition_text }}
                      </span>
                    </td>
                    <td>{{ $asset->purchase_date ? $asset->purchase_date->format('d/m/Y') : '-' }}</td>
                    <td>{{ $asset->purchase_price ? 'Rp ' . number_format($asset->purchase_price, 0, ',', '.') : '-' }}</td>
                    <td>{{ $asset->assignedToEmployee->name ?? '-' }}</td>
                    <td>
                      <a href="{{ route('assets.show', $asset) }}" class="btn btn-sm btn-outline-info">
                        <i class="ri-eye-line"></i>
                      </a>
                    </td>
                  </tr>
                  @endforeach
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
              <div>
                <small class="text-muted">
                  Menampilkan {{ $assets->firstItem() }} sampai {{ $assets->lastItem() }} dari {{ $assets->total() }} asset
                </small>
              </div>
              <div>
                {{ $assets->appends(request()->query())->links() }}
              </div>
            </div>
          @else
            <div class="text-center py-5">
              <i class="ri-file-search-line ri-48px text-muted mb-3"></i>
              <h6 class="text-muted">Tidak ada data asset yang ditemukan</h6>
              <p class="text-muted">Coba ubah filter pencarian Anda</p>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportToExcel() {
  // Get current filter values
  const form = document.getElementById('filterForm');
  const formData = new FormData(form);
  
  // Build export URL with current filters
  let exportUrl = '{{ route("reports.assets.export") }}';
  const params = new URLSearchParams();
  
  for (let [key, value] of formData.entries()) {
    if (value) {
      params.append(key, value);
    }
  }
  
  if (params.toString()) {
    exportUrl += '?' + params.toString();
  }
  
  // Open export URL
  window.open(exportUrl, '_blank');
}

function resetFilters() {
  window.location.href = '{{ route("reports.assets.index") }}';
}
</script>
@endsection
