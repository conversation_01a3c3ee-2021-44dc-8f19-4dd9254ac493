<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // Check if user exists and is active
        $user = DB::table('users')
            ->join('roles', 'users.role_id', '=', 'roles.id')
            ->join('branches', 'users.branch_id', '=', 'branches.id')
            ->where('users.username', $request->username)
            ->where('users.is_active', true)
            ->where('roles.is_active', true)
            ->where('branches.is_active', true)
            ->select('users.*', 'roles.name as role_name', 'roles.slug as role_slug', 'branches.name as branch_name')
            ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'username' => ['Username atau password salah.'],
            ]);
        }

        // Login the user
        Auth::loginUsingId($user->id);

        // Store additional user info in session
        session([
            'user_role' => $user->role_slug,
            'user_branch' => $user->branch_id,
            'user_permissions' => $this->getUserPermissions($user->role_id),
        ]);

        $request->session()->regenerate();

        return redirect()->intended('/dashboard');
    }

    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/login');
    }

    private function getUserPermissions($roleId)
    {
        return DB::table('role_permissions')
            ->join('permissions', 'role_permissions.permission_id', '=', 'permissions.id')
            ->where('role_permissions.role_id', $roleId)
            ->pluck('permissions.slug')
            ->toArray();
    }
}
