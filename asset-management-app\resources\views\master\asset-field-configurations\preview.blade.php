@extends('layouts.contentNavbarLayout')

@section('title', 'Preview Form Asset - Asset Management System')

@section('content')
<style>
.preview-form {
  background: #fff;
  border: 2px dashed #e7e7ff;
  border-radius: 12px;
  padding: 2rem;
  margin: 1rem 0;
}

.field-group-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #696cff;
}

.field-group-title {
  color: #566a7f;
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.preview-field {
  margin-bottom: 1rem;
}

.preview-field label {
  font-weight: 500;
  color: #566a7f;
  margin-bottom: 0.5rem;
}

.preview-field .form-control,
.preview-field .form-select {
  border: 2px solid #e7e7ff;
  border-radius: 6px;
  transition: border-color 0.3s ease;
}

.preview-field .form-control:focus,
.preview-field .form-select:focus {
  border-color: #696cff;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #8592a3;
}

.category-selector {
  background: #fff;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>

<div class="container-xxl flex-grow-1 container-p-y">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <div>
            <h5 class="card-title mb-0">
              <i class="ri-eye-line me-2"></i>
              Preview Form Asset
            </h5>
            <small class="text-muted">Preview form input asset berdasarkan konfigurasi field</small>
          </div>
          <div>
            <a href="{{ route('master.asset-field-configurations.index') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i>
              Kembali
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Category Selector -->
  <div class="row">
    <div class="col-12">
      <div class="category-selector">
        <form method="GET" action="{{ route('master.asset-field-configurations.preview') }}">
          <div class="row align-items-end">
            <div class="col-md-4">
              <label for="category_id" class="form-label">Pilih Kategori Asset</label>
              <select name="category_id" id="category_id" class="form-select">
                <option value="">Semua Kategori (Global Fields)</option>
                @foreach(\App\Models\AssetCategory::active()->get() as $category)
                  <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                    {{ $category->name }}
                  </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary">
                <i class="ri-refresh-line me-1"></i>
                Preview
              </button>
            </div>
            <div class="col-md-6">
              <div class="text-muted">
                <small>
                  <i class="ri-information-line me-1"></i>
                  Pilih kategori untuk melihat field yang akan muncul pada form input asset
                </small>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Preview Form -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="ri-file-edit-line me-2"></i>
            Form Input Asset
            @if(request('category_id'))
              @php
                $selectedCategory = \App\Models\AssetCategory::find(request('category_id'));
              @endphp
              @if($selectedCategory)
                - {{ $selectedCategory->name }}
              @endif
            @else
              - Global Fields
            @endif
          </h6>
        </div>
        <div class="card-body">
          @if($fieldConfigurations->count() > 0)
            <div class="preview-form">
              <form>
                <!-- Core Fields (Always Present) -->
                <div class="field-group-section">
                  <div class="field-group-title">
                    <i class="ri-information-line me-2"></i>
                    Informasi Dasar (Core Fields)
                  </div>
                  
                  <div class="row">
                    <div class="col-md-6">
                      <div class="preview-field">
                        <label class="form-label">Kode Asset <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" value="AUTO-GENERATED" readonly>
                        <div class="form-text">Kode asset akan di-generate otomatis</div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="preview-field">
                        <label class="form-label">Nama Asset <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" placeholder="Masukkan nama asset">
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-4">
                      <div class="preview-field">
                        <label class="form-label">Kategori Asset <span class="text-danger">*</span></label>
                        <select class="form-select">
                          <option value="">Pilih Kategori</option>
                          <option value="1">Komputer</option>
                          <option value="2">Furniture</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="preview-field">
                        <label class="form-label">Tipe Asset <span class="text-danger">*</span></label>
                        <select class="form-select">
                          <option value="">Pilih Tipe</option>
                          <option value="1">Laptop</option>
                          <option value="2">Desktop</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="preview-field">
                        <label class="form-label">Cabang <span class="text-danger">*</span></label>
                        <select class="form-select">
                          <option value="">Pilih Cabang</option>
                          <option value="1">Jakarta</option>
                          <option value="2">Surabaya</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Dynamic Fields by Group -->
                @foreach($fieldConfigurations as $groupKey => $fields)
                  @php
                    $fieldGroups = \App\Models\AssetFieldConfiguration::getFieldGroups();
                    $groupName = $fieldGroups[$groupKey] ?? ucwords(str_replace('_', ' ', $groupKey));

                    // Get group metadata from lookup
                    $groupLookup = \App\Models\Lookup::where('lookup_code', 'FIELD_GROUP')
                        ->where('is_active', true)
                        ->whereJsonContains('metadata->value', $groupKey)
                        ->first();

                    $groupIcon = $groupLookup->metadata['icon'] ?? 'ri-settings-4-line';
                    $groupColor = $groupLookup->metadata['color'] ?? 'primary';
                  @endphp
                  
                  <div class="field-group-section">
                    <div class="field-group-title">
                      <i class="{{ $groupIcon }} me-2"></i>
                      {{ $groupName }}
                    </div>
                    
                    <div class="row">
                      @foreach($fields as $field)
                        <div class="col-md-6">
                          <div class="preview-field">
                            <label class="form-label">
                              {{ $field->field_label }}
                              @if($field->is_required)
                                <span class="text-danger">*</span>
                              @endif
                            </label>

                            @switch($field->field_type)
                              @case('text')
                              @case('email')
                              @case('url')
                              @case('tel')
                                <input type="{{ $field->field_type }}" class="form-control" 
                                       placeholder="Masukkan {{ strtolower($field->field_label) }}">
                                @break

                              @case('number')
                                <input type="number" class="form-control" 
                                       placeholder="Masukkan {{ strtolower($field->field_label) }}">
                                @break

                              @case('date')
                                <input type="date" class="form-control">
                                @break

                              @case('datetime')
                                <input type="datetime-local" class="form-control">
                                @break

                              @case('textarea')
                                <textarea class="form-control" rows="3" 
                                          placeholder="Masukkan {{ strtolower($field->field_label) }}"></textarea>
                                @break

                              @case('select')
                                <select class="form-select">
                                  <option value="">Pilih {{ $field->field_label }}</option>
                                  @php
                                    $options = $field->getFieldOptions();
                                  @endphp
                                  @if($options)
                                    @foreach($options as $key => $value)
                                      <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                  @else
                                    <option value="option1">Option 1</option>
                                    <option value="option2">Option 2</option>
                                  @endif
                                </select>
                                @break

                              @case('radio')
                                @php
                                  $options = $field->getFieldOptions();
                                @endphp
                                @if($options)
                                  @foreach($options as $key => $value)
                                    <div class="form-check">
                                      <input class="form-check-input" type="radio" name="preview_{{ $field->field_name }}" 
                                             id="radio_{{ $field->id }}_{{ $key }}" value="{{ $key }}">
                                      <label class="form-check-label" for="radio_{{ $field->id }}_{{ $key }}">
                                        {{ $value }}
                                      </label>
                                    </div>
                                  @endforeach
                                @else
                                  <div class="form-check">
                                    <input class="form-check-input" type="radio" name="preview_{{ $field->field_name }}" 
                                           id="radio_{{ $field->id }}_1" value="option1">
                                    <label class="form-check-label" for="radio_{{ $field->id }}_1">Option 1</label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="radio" name="preview_{{ $field->field_name }}" 
                                           id="radio_{{ $field->id }}_2" value="option2">
                                    <label class="form-check-label" for="radio_{{ $field->id }}_2">Option 2</label>
                                  </div>
                                @endif
                                @break

                              @case('checkbox')
                                @php
                                  $options = $field->getFieldOptions();
                                @endphp
                                @if($options)
                                  @foreach($options as $key => $value)
                                    <div class="form-check">
                                      <input class="form-check-input" type="checkbox" 
                                             id="check_{{ $field->id }}_{{ $key }}" value="{{ $key }}">
                                      <label class="form-check-label" for="check_{{ $field->id }}_{{ $key }}">
                                        {{ $value }}
                                      </label>
                                    </div>
                                  @endforeach
                                @else
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="check_{{ $field->id }}_1" value="option1">
                                    <label class="form-check-label" for="check_{{ $field->id }}_1">Option 1</label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="check_{{ $field->id }}_2" value="option2">
                                    <label class="form-check-label" for="check_{{ $field->id }}_2">Option 2</label>
                                  </div>
                                @endif
                                @break

                              @case('file')
                                <input type="file" class="form-control">
                                @break

                              @default
                                <input type="text" class="form-control" 
                                       placeholder="Masukkan {{ strtolower($field->field_label) }}">
                            @endswitch

                            @if($field->help_text)
                              <div class="form-text">{{ $field->help_text }}</div>
                            @endif
                          </div>
                        </div>
                      @endforeach
                    </div>
                  </div>
                @endforeach

                <!-- Action Buttons -->
                <div class="d-flex justify-content-end gap-2 mt-4">
                  <button type="button" class="btn btn-outline-secondary" disabled>
                    <i class="ri-close-line me-1"></i>
                    Batal
                  </button>
                  <button type="button" class="btn btn-primary" disabled>
                    <i class="ri-save-line me-1"></i>
                    Simpan Asset
                  </button>
                </div>
              </form>
            </div>

            <!-- Info -->
            <div class="alert alert-info mt-3">
              <i class="ri-information-line me-2"></i>
              <strong>Preview Mode:</strong> Form ini hanya untuk preview. Semua field dan tombol dalam keadaan non-aktif.
            </div>
          @else
            <!-- Empty State -->
            <div class="empty-state">
              <i class="ri-file-edit-line ri-48px mb-3 opacity-50"></i>
              <h5 class="text-muted">Belum Ada Field Konfigurasi</h5>
              <p class="text-muted mb-4">
                @if(request('category_id'))
                  Belum ada field yang dikonfigurasi untuk kategori ini.
                @else
                  Belum ada field global yang dikonfigurasi.
                @endif
              </p>
              <a href="{{ route('master.asset-field-configurations.create') }}" class="btn btn-primary">
                <i class="ri-add-line me-1"></i>
                Tambah Field Konfigurasi
              </a>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto-submit form when category changes
  document.getElementById('category_id').addEventListener('change', function() {
    this.form.submit();
  });
});
</script>

@endsection
