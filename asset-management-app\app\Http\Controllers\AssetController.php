<?php

namespace App\Http\Controllers;

use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\AssetType;
use App\Models\AssetFieldConfiguration;
use App\Models\Branch;
use App\Helpers\BranchHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
class AssetController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Asset::with(['assetCategory', 'assetType', 'branch', 'assignedEmployee']);

        // Apply filters
        if ($request->filled('category')) {
            $query->where('asset_category_id', $request->category);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('branch')) {
            $query->where('branch_id', $request->branch);
        }

        if ($request->filled('assignment_status')) {
            if ($request->assignment_status === 'assigned') {
                $query->whereNotNull('assigned_to');
            } elseif ($request->assignment_status === 'available') {
                $query->whereNull('assigned_to');
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('asset_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply branch access control
        $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');

        // Only apply branch filter if user has specific branch access
        if ($accessibleBranchIds->isNotEmpty()) {
            $query->whereIn('branch_id', $accessibleBranchIds);
        }

        $assets = $query->latest()->paginate(15);

        return view('assets.index', compact('assets'));
    }

    /**
     * View all assets with comprehensive data display
     */
    public function viewAll(Request $request)
    {
        $query = Asset::with(['assetCategory', 'assetType', 'branch']);

        // Apply filters
        if ($request->filled('category')) {
            $query->where('asset_category_id', $request->category);
        }

        if ($request->filled('type')) {
            $query->where('asset_type_id', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('branch')) {
            $query->where('branch_id', $request->branch);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('asset_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply branch access control
        $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');

        if ($accessibleBranchIds->isNotEmpty()) {
            $query->whereIn('branch_id', $accessibleBranchIds);
        }

        $assets = $query->latest()->paginate(20);

        // Get filter options
        $categories = AssetCategory::active()->get();
        $assetTypes = AssetType::active()->get();
        $branches = BranchHelper::getAccessibleBranches();

        return view('assets.view-all', compact('assets', 'categories', 'assetTypes', 'branches'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = AssetCategory::active()->get();
        $branches = BranchHelper::getAccessibleBranches();
        $assetTypes = AssetType::active()->get();

        return view('assets.create', compact('categories', 'branches', 'assetTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'asset_category_id' => 'required|exists:asset_categories,id',
            'asset_type_id' => 'nullable|exists:asset_types,id',
            'branch_id' => 'required|exists:branches,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive,maintenance,disposed',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'dynamic_fields' => 'nullable|array',
        ]);

        // Check branch access
        if (!BranchHelper::canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        // Get field configurations for validation
        $fieldConfigurations = AssetFieldConfiguration::forCategory($validated['asset_category_id'])
            ->active()
            ->get();

        // Validate dynamic fields
        $dynamicFieldsValidation = [];
        $dynamicFieldsData = $request->input('dynamic_fields', []);

        foreach ($fieldConfigurations as $field) {
            $fieldName = 'dynamic_fields.' . $field->field_name;
            $rules = [];

            // Required validation
            if ($field->is_required) {
                $rules[] = 'required';
            } else {
                $rules[] = 'nullable';
            }

            // Type-specific validation
            switch ($field->field_type) {
                case 'email':
                    $rules[] = 'email';
                    break;
                case 'url':
                    $rules[] = 'url';
                    break;
                case 'number':
                    $rules[] = 'numeric';
                    break;
                case 'date':
                    $rules[] = 'date';
                    break;
                case 'file':
                    $rules[] = 'file';
                    break;
            }

            // Length validation
            if ($field->min_length) {
                $rules[] = 'min:' . $field->min_length;
            }
            if ($field->max_length) {
                $rules[] = 'max:' . $field->max_length;
            }

            // Regex validation
            if ($field->validation_pattern) {
                $rules[] = 'regex:/' . $field->validation_pattern . '/';
            }

            if (!empty($rules)) {
                $dynamicFieldsValidation[$fieldName] = implode('|', $rules);
            }
        }

        // Validate dynamic fields if any
        if (!empty($dynamicFieldsValidation)) {
            $request->validate($dynamicFieldsValidation);
        }

        // Generate asset code
        $category = AssetCategory::find($validated['asset_category_id']);
        $branch = Branch::find($validated['branch_id']);
        $validated['asset_code'] = $this->generateAssetCode($category, $branch);

        // Handle main image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('assets/images', 'public');
        }

        // Handle file uploads in dynamic fields
        foreach ($fieldConfigurations as $field) {
            if ($field->field_type === 'file' && $request->hasFile('dynamic_fields.' . $field->field_name)) {
                $file = $request->file('dynamic_fields.' . $field->field_name);
                $dynamicFieldsData[$field->field_name] = $file->store('assets/dynamic', 'public');
            }
        }

        // Store dynamic fields as JSON
        $validated['dynamic_fields'] = $dynamicFieldsData;

        Asset::create($validated);

        return redirect()->route('assets.index')->with('success', 'Asset berhasil ditambahkan dengan konfigurasi field dinamis.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Asset $asset)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($asset->branch_id)) {
            abort(403, 'You do not have access to this asset.');
        }

        $asset->load(['category', 'branch', 'supplier', 'purchaseOrder.supplier']);
        return view('assets.show', compact('asset'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Asset $asset)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($asset->branch_id)) {
            abort(403, 'You do not have access to this asset.');
        }

        $categories = AssetCategory::active()->get();
        $branches = BranchHelper::getAccessibleBranches();
        $assetTypes = AssetType::active()->get();

        return view('assets.edit', compact('asset', 'categories', 'branches', 'assetTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Asset $asset)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($asset->branch_id)) {
            abort(403, 'You do not have access to this asset.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'asset_category_id' => 'required|exists:asset_categories,id',
            'asset_type_id' => 'nullable|exists:asset_types,id',
            'branch_id' => 'required|exists:branches,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive,maintenance,disposed',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'dynamic_fields' => 'nullable|array',
        ]);

        // Check new branch access
        if (!BranchHelper::canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        // Get field configurations for validation
        $fieldConfigurations = AssetFieldConfiguration::forCategory($validated['asset_category_id'])
            ->active()
            ->get();

        // Handle main image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($asset->image) {
                Storage::disk('public')->delete($asset->image);
            }
            $validated['image'] = $request->file('image')->store('assets/images', 'public');
        }

        // Validate and process dynamic fields (similar to store method)
        $dynamicFieldsData = $request->input('dynamic_fields', []);

        // Handle file uploads in dynamic fields
        foreach ($fieldConfigurations as $field) {
            if ($field->field_type === 'file') {
                if ($request->hasFile('dynamic_fields.' . $field->field_name)) {
                    // Delete old file if exists
                    if (isset($asset->dynamic_fields[$field->field_name]) && $asset->dynamic_fields[$field->field_name]) {
                        Storage::disk('public')->delete($asset->dynamic_fields[$field->field_name]);
                    }

                    $file = $request->file('dynamic_fields.' . $field->field_name);
                    $dynamicFieldsData[$field->field_name] = $file->store('assets/dynamic', 'public');
                } else {
                    // Keep existing file if no new file uploaded
                    if (isset($asset->dynamic_fields[$field->field_name])) {
                        $dynamicFieldsData[$field->field_name] = $asset->dynamic_fields[$field->field_name];
                    }
                }
            }
        }

        $validated['dynamic_fields'] = $dynamicFieldsData;

        $asset->update($validated);

        return redirect()->route('assets.show', $asset)->with('success', 'Asset berhasil diperbarui dengan konfigurasi field dinamis.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Asset $asset)
    {
        // Check branch access
        if (!BranchHelper::canAccessBranch($asset->branch_id)) {
            abort(403, 'You do not have access to this asset.');
        }

        // Delete image if exists
        if ($asset->image) {
            Storage::disk('public')->delete($asset->image);
        }

        $asset->delete();

        return redirect()->route('assets.index')->with('success', 'Asset berhasil dihapus.');
    }

    /**
     * Generate unique asset code
     */
    private function generateAssetCode($category, $branch)
    {
        $categoryCode = $category->code;
        $branchCode = $branch->code;
        $year = date('Y');

        // Get last asset number for this category and branch
        $lastAsset = Asset::where('asset_category_id', $category->id)
            ->where('branch_id', $branch->id)
            ->whereYear('created_at', $year)
            ->orderBy('id', 'desc')
            ->first();

        $number = 1;
        if ($lastAsset) {
            // Extract number from last asset code
            $lastCode = $lastAsset->asset_code;
            $parts = explode('-', $lastCode);
            if (count($parts) >= 4) {
                $number = intval($parts[3]) + 1;
            }
        }

        return sprintf('%s-%s-%s-%04d', $categoryCode, $branchCode, $year, $number);
    }

    /**
     * View asset via QR code (public access)
     */
    public function qrView(Asset $asset)
    {
        $asset->load(['assetCategory', 'assetType', 'branch']);

        // Get field configurations for dynamic fields
        $fieldConfigurations = AssetFieldConfiguration::forCategory($asset->asset_category_id)
            ->active()
            ->ordered()
            ->get()
            ->groupBy('field_group');

        $fieldGroups = AssetFieldConfiguration::getFieldGroups();

        return view('assets.qr-view', compact('asset', 'fieldConfigurations', 'fieldGroups'));
    }

    /**
     * Get field configurations for a category
     */
    public function getFieldConfigurations(Request $request)
    {
        $categoryId = $request->get('category_id');

        $fieldConfigurations = AssetFieldConfiguration::forCategory($categoryId)
            ->active()
            ->ordered()
            ->get()
            ->groupBy('field_group');

        $html = '';
        $fieldGroups = AssetFieldConfiguration::getFieldGroups();

        foreach ($fieldConfigurations as $groupKey => $fields) {
            $groupName = $fieldGroups[$groupKey] ?? ucwords(str_replace('_', ' ', $groupKey));

            // Get group metadata from lookup
            $groupLookup = \App\Models\Lookup::where('lookup_code', 'FIELD_GROUP')
                ->where('is_active', true)
                ->whereJsonContains('metadata->value', $groupKey)
                ->first();

            $groupIcon = $groupLookup->metadata['icon'] ?? 'ri-settings-4-line';

            $html .= '<div class="field-group-section mb-4">';
            $html .= '<h6 class="field-group-title"><i class="' . $groupIcon . ' me-2"></i>' . $groupName . '</h6>';
            $html .= '<div class="row">';

            foreach ($fields as $field) {
                $html .= '<div class="' . $field->getColumnClass() . '">';
                $html .= $this->generateFieldHtml($field);
                $html .= '</div>';
            }

            $html .= '</div></div>';
        }

        return response()->json([
            'success' => true,
            'html' => $html
        ]);
    }

    /**
     * Generate HTML for a field
     */
    private function generateFieldHtml($field)
    {
        $requiredMark = $field->is_required ? '<span class="text-danger">*</span>' : '';
        $fieldName = 'dynamic_fields[' . $field->field_name . ']';
        $fieldId = 'field_' . $field->field_name;

        $html = '<div class="mb-3">';
        $html .= '<label for="' . $fieldId . '" class="form-label">' . $field->field_label . ' ' . $requiredMark . '</label>';

        switch ($field->field_type) {
            case 'text':
            case 'email':
            case 'url':
            case 'tel':
                $html .= $this->generateTextInput($field, $fieldName, $fieldId);
                break;
            case 'number':
                $html .= $this->generateNumberInput($field, $fieldName, $fieldId);
                break;
            case 'date':
                $html .= '<input type="date" class="form-control" id="' . $fieldId . '" name="' . $fieldName . '">';
                break;
            case 'textarea':
                $html .= '<textarea class="form-control" id="' . $fieldId . '" name="' . $fieldName . '" rows="3"';
                if ($field->placeholder_text) $html .= ' placeholder="' . $field->placeholder_text . '"';
                $html .= '></textarea>';
                break;
            case 'select':
                $html .= $this->generateSelectInput($field, $fieldName, $fieldId);
                break;
            case 'radio':
                $html .= $this->generateRadioInput($field, $fieldName, $fieldId);
                break;
            case 'checkbox':
                $html .= $this->generateCheckboxInput($field, $fieldName, $fieldId);
                break;
            case 'file':
                $html .= '<input type="file" class="form-control" id="' . $fieldId . '" name="' . $fieldName . '">';
                break;
            default:
                $html .= '<input type="text" class="form-control" id="' . $fieldId . '" name="' . $fieldName . '">';
        }

        if ($field->help_text) {
            $html .= '<div class="form-text">' . $field->help_text . '</div>';
        }

        $html .= '</div>';
        return $html;
    }

    private function generateTextInput($field, $fieldName, $fieldId)
    {
        $html = '<input type="' . $field->field_type . '" class="form-control" id="' . $fieldId . '" name="' . $fieldName . '"';

        if ($field->placeholder_text) {
            $html .= ' placeholder="' . $field->placeholder_text . '"';
        }

        if ($field->validation_pattern) {
            $html .= ' pattern="' . $field->validation_pattern . '"';
            if ($field->validation_message) {
                $html .= ' title="' . $field->validation_message . '"';
            }
        }

        if ($field->min_length) {
            $html .= ' minlength="' . $field->min_length . '"';
        }

        if ($field->max_length) {
            $html .= ' maxlength="' . $field->max_length . '"';
        }

        if ($field->is_required) {
            $html .= ' required';
        }

        $html .= '>';
        return $html;
    }

    private function generateNumberInput($field, $fieldName, $fieldId)
    {
        $html = '<input type="number" class="form-control" id="' . $fieldId . '" name="' . $fieldName . '"';

        if ($field->placeholder_text) {
            $html .= ' placeholder="' . $field->placeholder_text . '"';
        }

        if ($field->is_required) {
            $html .= ' required';
        }

        $html .= '>';
        return $html;
    }

    private function generateSelectInput($field, $fieldName, $fieldId)
    {
        $html = '<select class="form-select" id="' . $fieldId . '" name="' . $fieldName . '"';

        if ($field->is_required) {
            $html .= ' required';
        }

        $html .= '>';
        $html .= '<option value="">Pilih ' . $field->field_label . '</option>';

        $options = $field->getFieldOptions();
        foreach ($options as $key => $value) {
            $html .= '<option value="' . $key . '">' . $value . '</option>';
        }

        $html .= '</select>';
        return $html;
    }

    private function generateRadioInput($field, $fieldName, $fieldId)
    {
        $html = '';
        $options = $field->getFieldOptions();

        foreach ($options as $key => $value) {
            $radioId = $fieldId . '_' . $key;
            $html .= '<div class="form-check">';
            $html .= '<input class="form-check-input" type="radio" name="' . $fieldName . '" id="' . $radioId . '" value="' . $key . '"';
            if ($field->is_required) $html .= ' required';
            $html .= '>';
            $html .= '<label class="form-check-label" for="' . $radioId . '">' . $value . '</label>';
            $html .= '</div>';
        }

        return $html;
    }

    private function generateCheckboxInput($field, $fieldName, $fieldId)
    {
        $html = '';
        $options = $field->getFieldOptions();

        foreach ($options as $key => $value) {
            $checkboxId = $fieldId . '_' . $key;
            $html .= '<div class="form-check">';
            $html .= '<input class="form-check-input" type="checkbox" name="' . $fieldName . '[]" id="' . $checkboxId . '" value="' . $key . '">';
            $html .= '<label class="form-check-label" for="' . $checkboxId . '">' . $value . '</label>';
            $html .= '</div>';
        }

        return $html;
    }
}
