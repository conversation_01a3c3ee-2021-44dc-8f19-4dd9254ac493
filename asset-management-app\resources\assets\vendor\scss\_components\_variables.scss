// Common
// *******************************************************************************

$ui-star-size: 1.1em !default;
$ui-stars-spacer: -0.1em !default;
$ui-star-filled-color: $yellow !default;

// Navbar (custom navbar)
// *******************************************************************************
$navbar-height: 4rem !default;
$navbar-suggestion-width: 96% !default;
$navbar-suggestion-height: 28rem !default;
$navbar-suggestion-border-radius: $border-radius-xl !default;
$navbar-dropdown-width: 23.75rem !default;
$navbar-dropdown-content-height: 25.75rem !default;
$navbar-notifications-dropdown-item-padding-y: 0.892rem !default;
$navbar-notifications-dropdown-item-padding-x: 1rem !default;
$navbar-search-shadow: 0px 4px 8px -4px rgba($black, 0.42) !default;

// Menu
// *******************************************************************************

$menu-width: 16.25rem !default;
$menu-collapsed-width: 4.25rem !default;
$menu-collapsed-layout-breakpoint: xl !default;

$menu-font-size: $font-size-base !default;

$menu-item-spacer: 0.375rem !default;

$menu-vertical-link-margin-x: 1.125rem !default;

$menu-vertical-link-padding-y: 0.5rem !default;
$menu-vertical-link-padding-x: 1.45rem !default;
$menu-vertical-menu-link-padding-y: $menu-vertical-link-padding-y !default;
$menu-vertical-menu-level-spacer: 0.5rem !default;

$menu-horizontal-spacer-y: 0.7rem !default;
$menu-horizontal-spacer-x: 0.25rem !default;
$menu-horizontal-item-spacer: 0.45rem !default;
$menu-horizontal-link-padding-y: 0.5rem !default;
$menu-horizontal-link-padding-x: 1.7rem !default;
$menu-horizontal-menu-link-padding-y: 0.5rem !default;
$menu-horizontal-sub-menu-icon-size: 0.65rem !default;
$menu-horizontal-menu-level-spacer: 1rem !default;
$menu-horizontal-box-shadow: $navbar-search-shadow !default;

$menu-sub-width: $menu-width !default;
$menu-sub-box-shadow: $box-shadow-lg !default;
$menu-control-width: 2.25rem !default;
$menu-control-arrow-size: 0.5rem !default;

$menu-icon-expanded-width: 1.5rem !default;
$menu-icon-expanded-left-spacer: 1.75rem !default;
$menu-icon-expanded-font-size: 1.375rem !default;
$menu-icon-expanded-spacer: 0.5rem !default;
$menu-animation-duration: 0.3s !default;

$menu-max-levels: 5 !default;

$menu-dark-border-color: rgba(255, 255, 255, 0.2) !default;
$menu-dark-menu-bg: rgba(0, 0, 0, 0.06) !default;
$menu-light-border-color: rgba(0, 0, 0, 0.06) !default;
$menu-light-menu-bg: rgba(0, 0, 0, 0.05) !default;

// Footer
// *******************************************************************************
$footer-fixed-box-shadow: 0px -4px 8px -4px rgba($black, 0.42) !default;
// Avatars
// *******************************************************************************

$avatar-size-xl: 4rem !default;
$avatar-size-lg: 3.5rem !default;
$avatar-size-md: 3rem !default;
$avatar-size: 2.5rem !default; // Default
$avatar-size-sm: 2rem !default;
$avatar-size-xs: 1.5rem !default;

$avatar-initial-xl: 1.875rem !default;
$avatar-initial-lg: 1.5rem !default;
$avatar-initial-md: 1.125rem !default;
$avatar-initial: $font-size-base !default;
$avatar-initial-sm: 0.75rem !default;
$avatar-initial-xs: 0.625rem !default;

$avatar-group-border: $card-bg !default;
$avatar-bg: #f0eff0 !default; // (C)

// Text Divider
// *******************************************************************************
$divider-color: $gray-200 !default;

$divider-margin-y: 1rem !default;
$divider-margin-x: 0 !default;

$divider-text-padding-y: 0rem !default;
$divider-text-padding-x: 1rem !default;

$divider-font-size: 0.9375rem !default;
$divider-icon-size: 1rem !default;
