<?php

namespace App\Http\Controllers;

use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\AssetType;
use App\Models\Branch;
use App\Models\Supplier;
use App\Models\PurchaseOrder;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class AssetImportController extends Controller
{
    /**
     * Download template Excel untuk import asset
     */
    public function downloadTemplate()
    {
        $spreadsheet = new Spreadsheet();
        
        // Sheet 1: Template Import
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Template Import Asset');
        
        // Header template
        $headers = [
            'A1' => 'name*',
            'B1' => 'asset_category_name*',
            'C1' => 'asset_type_name',
            'D1' => 'branch_code*',
            'E1' => 'supplier_code',
            'F1' => 'purchase_order_number',
            'G1' => 'description',
            'H1' => 'brand',
            'I1' => 'model',
            'J1' => 'serial_number',
            'K1' => 'purchase_date',
            'L1' => 'purchase_price',
            'M1' => 'condition',
            'N1' => 'status*',
            'O1' => 'location',
            'P1' => 'assigned_employee_nik',
            'Q1' => 'notes'
        ];
        
        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }
        
        // Style header
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
        ];
        $sheet->getStyle('A1:Q1')->applyFromArray($headerStyle);
        
        // Auto width
        foreach (range('A', 'Q') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Contoh data
        $exampleData = [
            [
                'Laptop Dell Latitude 5520',
                'Komputer',
                'Laptop',
                'JKT',
                'SUP20250600001',
                'PO-JKT-2025-0001',
                'Laptop untuk karyawan IT',
                'Dell',
                'Latitude 5520',
                'DL123456789',
                '15/01/2024',
                '15000000',
                'good',
                'active',
                'Lantai 2 - IT Room',
                '1234567890123456',
                'Asset dalam kondisi baik'
            ]
        ];
        
        $row = 2;
        foreach ($exampleData as $data) {
            $col = 'A';
            foreach ($data as $value) {
                $sheet->setCellValue($col . $row, $value);
                $col++;
            }
            $row++;
        }
        
        // Sheet 2: Petunjuk Pengisian
        $this->createInstructionSheet($spreadsheet);
        
        // Sheet 3: Data Referensi
        $this->createReferenceSheet($spreadsheet);
        
        // Download
        $writer = new Xlsx($spreadsheet);
        $filename = 'Template_Import_Asset_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $writer->save('php://output');
        exit;
    }
    
    /**
     * Create instruction sheet
     */
    private function createInstructionSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Petunjuk Pengisian');
        
        $instructions = [
            ['PETUNJUK PENGISIAN TEMPLATE IMPORT ASSET', ''],
            ['', ''],
            ['KOLOM WAJIB DIISI (ditandai dengan *)', ''],
            ['name*', 'Nama asset (wajib diisi, maksimal 255 karakter)'],
            ['asset_category_name*', 'Nama kategori asset (wajib diisi, lihat sheet "Data Referensi")'],
            ['branch_code*', 'Kode cabang (wajib diisi, lihat sheet "Data Referensi")'],
            ['status*', 'Status asset (wajib diisi: active, inactive, maintenance, disposed)'],
            ['', ''],
            ['KOLOM OPSIONAL', ''],
            ['asset_type_name', 'Nama tipe asset (opsional, lihat sheet "Data Referensi")'],
            ['supplier_code', 'Kode supplier (opsional, lihat sheet "Data Referensi")'],
            ['purchase_order_number', 'Nomor Purchase Order (opsional, lihat sheet "Data Referensi")'],
            ['description', 'Deskripsi asset (opsional)'],
            ['brand', 'Merek asset (opsional)'],
            ['model', 'Model asset (opsional)'],
            ['serial_number', 'Nomor seri asset (opsional)'],
            ['purchase_date', 'Tanggal pembelian (format: dd/mm/yyyy, contoh: 15/01/2024)'],
            ['purchase_price', 'Harga pembelian (angka saja tanpa titik/koma, contoh: 15000000)'],
            ['condition', 'Kondisi asset (excellent, good, fair, poor)'],
            ['location', 'Lokasi asset (opsional)'],
            ['assigned_employee_nik', 'NIK karyawan yang ditugaskan (opsional, lihat sheet "Data Referensi")'],
            ['notes', 'Catatan tambahan (opsional)'],
            ['', ''],
            ['ATURAN PENTING:', ''],
            ['1. Jangan mengubah nama kolom di baris pertama', ''],
            ['2. Kolom yang ditandai * wajib diisi', ''],
            ['3. Gunakan format tanggal dd/mm/yyyy', ''],
            ['4. Harga dalam angka tanpa pemisah ribuan', ''],
            ['5. Pastikan kode referensi sesuai dengan data di sheet "Data Referensi"', ''],
            ['6. Asset code akan dibuat otomatis oleh sistem', ''],
            ['7. Maksimal 1000 baris data per import', ''],
            ['', ''],
            ['CONTOH PENGISIAN:', ''],
            ['Lihat baris ke-2 di sheet "Template Import Asset"', '']
        ];
        
        $row = 1;
        foreach ($instructions as $instruction) {
            $sheet->setCellValue('A' . $row, $instruction[0]);
            $sheet->setCellValue('B' . $row, $instruction[1]);
            $row++;
        }
        
        // Style title
        $sheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => '4472C4']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
        ]);
        
        // Style headers
        $headerRows = [3, 9, 24];
        foreach ($headerRows as $headerRow) {
            $sheet->getStyle('A' . $headerRow)->applyFromArray([
                'font' => ['bold' => true, 'color' => ['rgb' => '2F5597']],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E7F3FF']]
            ]);
        }
        
        // Auto width
        $sheet->getColumnDimension('A')->setWidth(30);
        $sheet->getColumnDimension('B')->setWidth(60);
    }
    
    /**
     * Create reference data sheet
     */
    private function createReferenceSheet($spreadsheet)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Data Referensi');
        
        $currentCol = 'A';
        $startRow = 1;
        
        // Asset Categories
        $categories = AssetCategory::active()->get();
        $sheet->setCellValue($currentCol . $startRow, 'KATEGORI ASSET');
        $sheet->getStyle($currentCol . $startRow)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']]
        ]);
        
        $row = $startRow + 1;
        foreach ($categories as $category) {
            $sheet->setCellValue($currentCol . $row, $category->name);
            $row++;
        }
        
        // Asset Types
        $currentCol = 'C';
        $assetTypes = AssetType::with('category')->active()->get();
        $sheet->setCellValue($currentCol . $startRow, 'TIPE ASSET');
        $sheet->setCellValue(chr(ord($currentCol) + 1) . $startRow, 'KATEGORI');
        $sheet->getStyle($currentCol . $startRow . ':' . chr(ord($currentCol) + 1) . $startRow)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']]
        ]);
        
        $row = $startRow + 1;
        foreach ($assetTypes as $type) {
            $sheet->setCellValue($currentCol . $row, $type->name);
            $sheet->setCellValue(chr(ord($currentCol) + 1) . $row, $type->category->name);
            $row++;
        }
        
        // Branches
        $currentCol = 'F';
        $branches = Branch::active()->get();
        $sheet->setCellValue($currentCol . $startRow, 'KODE CABANG');
        $sheet->setCellValue(chr(ord($currentCol) + 1) . $startRow, 'NAMA CABANG');
        $sheet->getStyle($currentCol . $startRow . ':' . chr(ord($currentCol) + 1) . $startRow)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']]
        ]);
        
        $row = $startRow + 1;
        foreach ($branches as $branch) {
            $sheet->setCellValue($currentCol . $row, $branch->code);
            $sheet->setCellValue(chr(ord($currentCol) + 1) . $row, $branch->name);
            $row++;
        }
        
        // Employees
        $currentCol = 'I';
        $employees = Employee::active()->get();
        $sheet->setCellValue($currentCol . $startRow, 'NIK KARYAWAN');
        $sheet->setCellValue(chr(ord($currentCol) + 1) . $startRow, 'NAMA KARYAWAN');
        $sheet->getStyle($currentCol . $startRow . ':' . chr(ord($currentCol) + 1) . $startRow)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']]
        ]);

        $row = $startRow + 1;
        foreach ($employees as $employee) {
            $sheet->setCellValue($currentCol . $row, $employee->nik);
            $sheet->setCellValue(chr(ord($currentCol) + 1) . $row, $employee->full_name);
            $row++;
        }

        // Auto width for all columns
        foreach (range('A', 'K') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * Import asset data from Excel
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls|max:10240' // Max 10MB
        ]);

        try {
            $file = $request->file('file');
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Remove header row
            $header = array_shift($rows);

            // Validate header
            $expectedHeaders = [
                'name', 'asset_category_name', 'asset_type_name', 'branch_code',
                'supplier_code', 'purchase_order_number', 'description', 'brand',
                'model', 'serial_number', 'purchase_date', 'purchase_price',
                'condition', 'status', 'location', 'assigned_employee_nik', 'notes'
            ];

            $cleanHeader = array_map(function($h) {
                return str_replace('*', '', trim($h));
            }, $header);

            if ($cleanHeader !== $expectedHeaders) {
                return redirect()->back()->withErrors([
                    'file' => 'Format header tidak sesuai template. Silakan download template terbaru.'
                ]);
            }

            // Validate max rows
            if (count($rows) > 1000) {
                return redirect()->back()->withErrors([
                    'file' => 'Maksimal 1000 baris data per import.'
                ]);
            }

            // Process import
            $results = $this->processImport($rows);

            return redirect()->back()->with('success',
                "Import berhasil! {$results['success']} data berhasil diimport, {$results['failed']} data gagal."
            )->with('import_errors', $results['errors']);

        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'file' => 'Terjadi kesalahan saat memproses file: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process import data
     */
    private function processImport($rows)
    {
        $success = 0;
        $failed = 0;
        $errors = [];

        // Cache reference data
        $categories = AssetCategory::active()->pluck('id', 'name')->toArray();
        $assetTypes = AssetType::active()->with('category')->get()->groupBy('category.name');
        $branches = Branch::active()->pluck('id', 'code')->toArray();
        $suppliers = Supplier::where('is_active', true)->pluck('id', 'supplier_code')->toArray();
        $purchaseOrders = PurchaseOrder::pluck('id', 'po_number')->toArray();
        $employees = Employee::active()->pluck('id', 'nik')->toArray();

        DB::beginTransaction();

        try {
            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2; // +2 because we removed header and Excel starts from 1

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    $data = $this->validateAndPrepareRowData($row, $rowNumber, [
                        'categories' => $categories,
                        'assetTypes' => $assetTypes,
                        'branches' => $branches,
                        'suppliers' => $suppliers,
                        'purchaseOrders' => $purchaseOrders,
                        'employees' => $employees
                    ]);

                    // Generate asset code
                    $category = AssetCategory::find($data['asset_category_id']);
                    $branch = Branch::find($data['branch_id']);
                    $data['asset_code'] = $this->generateAssetCode($category, $branch);

                    Asset::create($data);
                    $success++;

                } catch (\Exception $e) {
                    $failed++;
                    $errors[] = "Baris {$rowNumber}: " . $e->getMessage();
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return [
            'success' => $success,
            'failed' => $failed,
            'errors' => $errors
        ];
    }

    /**
     * Validate and prepare row data
     */
    private function validateAndPrepareRowData($row, $rowNumber, $references)
    {
        $data = [];

        // Required fields validation
        if (empty(trim($row[0]))) {
            throw new \Exception("Nama asset wajib diisi");
        }
        if (empty(trim($row[1]))) {
            throw new \Exception("Kategori asset wajib diisi");
        }
        if (empty(trim($row[3]))) {
            throw new \Exception("Kode cabang wajib diisi");
        }
        if (empty(trim($row[13]))) {
            throw new \Exception("Status asset wajib diisi");
        }

        // Basic fields
        $data['name'] = trim($row[0]);
        $data['description'] = trim($row[6]) ?: null;
        $data['brand'] = trim($row[7]) ?: null;
        $data['model'] = trim($row[8]) ?: null;
        $data['serial_number'] = trim($row[9]) ?: null;
        $data['location'] = trim($row[14]) ?: null;
        $data['notes'] = trim($row[16]) ?: null;

        // Asset Category
        $categoryName = trim($row[1]);
        if (!isset($references['categories'][$categoryName])) {
            throw new \Exception("Kategori asset '{$categoryName}' tidak ditemukan");
        }
        $data['asset_category_id'] = $references['categories'][$categoryName];

        // Asset Type (optional)
        if (!empty(trim($row[2]))) {
            $typeName = trim($row[2]);
            $typeFound = false;

            if (isset($references['assetTypes'][$categoryName])) {
                foreach ($references['assetTypes'][$categoryName] as $type) {
                    if ($type->name === $typeName) {
                        $data['asset_type_id'] = $type->id;
                        $typeFound = true;
                        break;
                    }
                }
            }

            if (!$typeFound) {
                throw new \Exception("Tipe asset '{$typeName}' tidak ditemukan untuk kategori '{$categoryName}'");
            }
        }

        // Branch
        $branchCode = trim($row[3]);
        if (!isset($references['branches'][$branchCode])) {
            throw new \Exception("Kode cabang '{$branchCode}' tidak ditemukan");
        }
        $data['branch_id'] = $references['branches'][$branchCode];

        // Supplier (optional)
        if (!empty(trim($row[4]))) {
            $supplierCode = trim($row[4]);
            if (!isset($references['suppliers'][$supplierCode])) {
                throw new \Exception("Kode supplier '{$supplierCode}' tidak ditemukan");
            }
            $data['supplier_id'] = $references['suppliers'][$supplierCode];
        }

        // Purchase Order (optional)
        if (!empty(trim($row[5]))) {
            $poNumber = trim($row[5]);
            if (!isset($references['purchaseOrders'][$poNumber])) {
                throw new \Exception("Nomor PO '{$poNumber}' tidak ditemukan");
            }
            $data['purchase_order_id'] = $references['purchaseOrders'][$poNumber];
        }

        // Purchase Date
        if (!empty(trim($row[10]))) {
            try {
                $dateStr = trim($row[10]);
                if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $dateStr, $matches)) {
                    $data['purchase_date'] = $matches[3] . '-' . str_pad($matches[2], 2, '0', STR_PAD_LEFT) . '-' . str_pad($matches[1], 2, '0', STR_PAD_LEFT);
                } else {
                    throw new \Exception("Format tanggal tidak valid");
                }
            } catch (\Exception $e) {
                throw new \Exception("Format tanggal pembelian tidak valid (gunakan dd/mm/yyyy)");
            }
        }

        // Purchase Price
        if (!empty(trim($row[11]))) {
            $price = trim($row[11]);
            if (!is_numeric($price) || $price < 0) {
                throw new \Exception("Harga pembelian harus berupa angka positif");
            }
            $data['purchase_price'] = $price;
        }

        // Condition
        if (!empty(trim($row[12]))) {
            $condition = trim($row[12]);
            if (!in_array($condition, ['excellent', 'good', 'fair', 'poor'])) {
                throw new \Exception("Kondisi asset harus: excellent, good, fair, atau poor");
            }
            $data['condition'] = $condition;
        } else {
            $data['condition'] = 'good'; // default
        }

        // Status
        $status = trim($row[13]);
        if (!in_array($status, ['active', 'inactive', 'maintenance', 'disposed'])) {
            throw new \Exception("Status asset harus: active, inactive, maintenance, atau disposed");
        }
        $data['status'] = $status;

        // Assigned Employee (optional)
        if (!empty(trim($row[15]))) {
            $employeeNik = trim($row[15]);
            if (!isset($references['employees'][$employeeNik])) {
                throw new \Exception("NIK karyawan '{$employeeNik}' tidak ditemukan");
            }
            $data['assigned_to'] = $references['employees'][$employeeNik];
            $data['assigned_at'] = now();
            $data['assignment_status'] = 'assigned';
        }

        return $data;
    }

    /**
     * Generate unique asset code
     */
    private function generateAssetCode($category, $branch)
    {
        $categoryCode = $category->code;
        $branchCode = $branch->code;
        $year = date('Y');

        // Get last asset number for this category and branch
        $lastAsset = Asset::where('asset_category_id', $category->id)
            ->where('branch_id', $branch->id)
            ->whereYear('created_at', $year)
            ->orderBy('id', 'desc')
            ->first();

        $nextNumber = 1;
        if ($lastAsset) {
            // Extract number from asset code (format: CATEGORY-BRANCH-YEAR-NUMBER)
            $parts = explode('-', $lastAsset->asset_code);
            if (count($parts) >= 4) {
                $nextNumber = intval(end($parts)) + 1;
            }
        }

        return $categoryCode . '-' . $branchCode . '-' . $year . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
}
