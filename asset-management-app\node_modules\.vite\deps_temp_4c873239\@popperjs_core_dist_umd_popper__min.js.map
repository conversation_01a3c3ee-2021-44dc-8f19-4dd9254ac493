{"version": 3, "sources": ["../../@popperjs/core/src/dom-utils/getWindow.js", "../../@popperjs/core/src/dom-utils/instanceOf.js", "../../@popperjs/core/src/utils/math.js", "../../@popperjs/core/src/utils/userAgent.js", "../../@popperjs/core/src/dom-utils/isLayoutViewport.js", "../../@popperjs/core/src/dom-utils/getBoundingClientRect.js", "../../@popperjs/core/src/dom-utils/getWindowScroll.js", "../../@popperjs/core/src/dom-utils/getNodeName.js", "../../@popperjs/core/src/dom-utils/getDocumentElement.js", "../../@popperjs/core/src/dom-utils/getWindowScrollBarX.js", "../../@popperjs/core/src/dom-utils/getComputedStyle.js", "../../@popperjs/core/src/dom-utils/isScrollParent.js", "../../@popperjs/core/src/dom-utils/getCompositeRect.js", "../../@popperjs/core/src/dom-utils/getNodeScroll.js", "../../@popperjs/core/src/dom-utils/getHTMLElementScroll.js", "../../@popperjs/core/src/dom-utils/getLayoutRect.js", "../../@popperjs/core/src/dom-utils/getParentNode.js", "../../@popperjs/core/src/dom-utils/getScrollParent.js", "../../@popperjs/core/src/dom-utils/listScrollParents.js", "../../@popperjs/core/src/dom-utils/isTableElement.js", "../../@popperjs/core/src/dom-utils/getOffsetParent.js", "../../@popperjs/core/src/enums.js", "../../@popperjs/core/src/utils/orderModifiers.js", "../../@popperjs/core/src/dom-utils/contains.js", "../../@popperjs/core/src/utils/rectToClientRect.js", "../../@popperjs/core/src/dom-utils/getClippingRect.js", "../../@popperjs/core/src/dom-utils/getViewportRect.js", "../../@popperjs/core/src/dom-utils/getDocumentRect.js", "../../@popperjs/core/src/utils/getBasePlacement.js", "../../@popperjs/core/src/utils/getVariation.js", "../../@popperjs/core/src/utils/getMainAxisFromPlacement.js", "../../@popperjs/core/src/utils/computeOffsets.js", "../../@popperjs/core/src/utils/mergePaddingObject.js", "../../@popperjs/core/src/utils/getFreshSideObject.js", "../../@popperjs/core/src/utils/expandToHashMap.js", "../../@popperjs/core/src/utils/detectOverflow.js", "../../@popperjs/core/src/createPopper.js", "../../@popperjs/core/src/utils/debounce.js", "../../@popperjs/core/src/utils/mergeByName.js", "../../@popperjs/core/src/modifiers/eventListeners.js", "../../@popperjs/core/src/modifiers/popperOffsets.js", "../../@popperjs/core/src/modifiers/computeStyles.js", "../../@popperjs/core/src/modifiers/applyStyles.js", "../../@popperjs/core/src/modifiers/offset.js", "../../@popperjs/core/src/utils/getOppositePlacement.js", "../../@popperjs/core/src/utils/getOppositeVariationPlacement.js", "../../@popperjs/core/src/utils/computeAutoPlacement.js", "../../@popperjs/core/src/modifiers/flip.js", "../../@popperjs/core/src/utils/within.js", "../../@popperjs/core/src/modifiers/preventOverflow.js", "../../@popperjs/core/src/utils/getAltAxis.js", "../../@popperjs/core/src/modifiers/arrow.js", "../../@popperjs/core/src/modifiers/hide.js", "../../@popperjs/core/src/popper-lite.js", "../../@popperjs/core/src/popper.js"], "sourcesContent": ["// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\ntype Navigator = Navigator & { userAgentData?: NavigatorUAData };\n\ninterface NavigatorUAData {\n  brands: Array<{ brand: string, version: string }>;\n  mobile: boolean;\n  platform: string;\n}\n\nexport default function getUAString(): string {\n  const uaData = (navigator: Navigator).userAgentData;\n\n  if (uaData?.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands\n      .map((item) => `${item.brand}/${item.version}`)\n      .join(' ');\n  }\n\n  return navigator.userAgent;\n}\n", "// @flow\nimport getUAString from '../utils/userAgent';\n\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n", "// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport { round } from '../utils/math';\nimport getWindow from './getWindow';\nimport isLayoutViewport from './isLayoutViewport';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement,\n  includeScale: boolean = false,\n  isFixedStrategy: boolean = false\n): ClientRectObject {\n  const clientRect = element.getBoundingClientRect();\n  let scaleX = 1;\n  let scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX =\n      (element: HTMLElement).offsetWidth > 0\n        ? round(clientRect.width) / (element: HTMLElement).offsetWidth || 1\n        : 1;\n    scaleY =\n      (element: HTMLElement).offsetHeight > 0\n        ? round(clientRect.height) / (element: HTMLElement).offsetHeight || 1\n        : 1;\n  }\n\n  const { visualViewport } = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n\n  const x =\n    (clientRect.left +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) /\n    scaleX;\n  const y =\n    (clientRect.top +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) /\n    scaleY;\n  const width = clientRect.width / scaleX;\n  const height = clientRect.height / scaleY;\n\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\nimport { round } from '../utils/math';\n\nfunction isElementScaled(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  const scaleX = round(rect.width) / element.offsetWidth || 1;\n  const scaleY = round(rect.height) / element.offsetHeight || 1;\n\n  return scaleX !== 1 || scaleY !== 1;\n}\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const offsetParentIsScaled =\n    isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(\n    elementOrVirtualElement,\n    offsetParentIsScaled,\n    isFixed\n  );\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement, isShadowRoot } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\nimport getUAString from '../utils/userAgent';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = /firefox/i.test(getUAString());\n  const isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    const elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  let currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject, PositioningStrategy } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const rect = getBoundingClientRect(element, false, strategy === 'fixed');\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element, strategy))\n    : isElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent, strategy)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent, strategy);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport isLayoutViewport from './isLayoutViewport';\nimport type { PositioningStrategy } from '../types';\n\nexport default function getViewportRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    const layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || (!layoutViewport && strategy === 'fixed')) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { State, SideObject, Padding, PositioningStrategy } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  strategy: PositioningStrategy,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    strategy = state.strategy,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary,\n    strategy\n  );\n\n  const referenceClientRect = getBoundingClientRect(state.elements.reference);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const { defaultModifiers = [], defaultOptions = DEFAULT_OPTIONS } =\n    generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(setOptionsAction) {\n        const options =\n          typeof setOptionsAction === 'function'\n            ? setOptionsAction(state.options)\n            : setOptionsAction;\n\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport getWindow from '../dom-utils/getWindow';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  scroll: boolean,\n  resize: boolean,\n};\n\nconst passive = { passive: true };\n\nfunction effect({ state, instance, options }: ModifierArguments<Options>) {\n  const { scroll = true, resize = true } = options;\n\n  const window = getWindow(state.elements.popper);\n  const scrollParents = [\n    ...state.scrollParents.reference,\n    ...state.scrollParents.popper,\n  ];\n\n  if (scroll) {\n    scrollParents.forEach(scrollParent => {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return () => {\n    if (scroll) {\n      scrollParents.forEach(scrollParent => {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type EventListenersModifier = Modifier<'eventListeners', Options>;\nexport default ({\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: () => {},\n  effect,\n  data: {},\n}: EventListenersModifier);\n", "// @flow\nimport type { ModifierArguments, Modifier } from '../types';\nimport computeOffsets from '../utils/computeOffsets';\n\nfunction popperOffsets({ state, name }: ModifierArguments<{||}>) {\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement,\n  });\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PopperOffsetsModifier = Modifier<'popperOffsets', {||}>;\nexport default ({\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {},\n}: PopperOffsetsModifier);\n", "// @flow\nimport type {\n  PositioningStrategy,\n  Offsets,\n  Modifier,\n  ModifierArguments,\n  Rect,\n  Window,\n} from '../types';\nimport {\n  type BasePlacement,\n  type Variation,\n  top,\n  left,\n  right,\n  bottom,\n  end,\n} from '../enums';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getWindow from '../dom-utils/getWindow';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getComputedStyle from '../dom-utils/getComputedStyle';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getVariation from '../utils/getVariation';\nimport { round } from '../utils/math';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type RoundOffsets = (\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>\n) => Offsets;\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets?: boolean | RoundOffsets,\n};\n\nconst unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto',\n};\n\n// Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\nfunction roundOffsetsByDPR({ x, y }, win: Window): Offsets {\n  const dpr = win.devicePixelRatio || 1;\n\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0,\n  };\n}\n\nexport function mapToStyles({\n  popper,\n  popperRect,\n  placement,\n  variation,\n  offsets,\n  position,\n  gpuAcceleration,\n  adaptive,\n  roundOffsets,\n  isFixed,\n}: {\n  popper: HTMLElement,\n  popperRect: Rect,\n  placement: BasePlacement,\n  variation: ?Variation,\n  offsets: $Shape<{ x: number, y: number, centerOffset: number }>,\n  position: PositioningStrategy,\n  gpuAcceleration: boolean,\n  adaptive: boolean,\n  roundOffsets: boolean | RoundOffsets,\n  isFixed: boolean,\n}) {\n  let { x = 0, y = 0 } = offsets;\n\n  ({ x, y } =\n    typeof roundOffsets === 'function' ? roundOffsets({ x, y }) : { x, y });\n\n  const hasX = offsets.hasOwnProperty('x');\n  const hasY = offsets.hasOwnProperty('y');\n\n  let sideX: string = left;\n  let sideY: string = top;\n\n  const win: Window = window;\n\n  if (adaptive) {\n    let offsetParent = getOffsetParent(popper);\n    let heightProp = 'clientHeight';\n    let widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (\n        getComputedStyle(offsetParent).position !== 'static' &&\n        position === 'absolute'\n      ) {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    }\n\n    // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n    offsetParent = (offsetParent: Element);\n\n    if (\n      placement === top ||\n      ((placement === left || placement === right) && variation === end)\n    ) {\n      sideY = bottom;\n      const offsetY =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.height\n          : // $FlowFixMe[prop-missing]\n            offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (\n      placement === left ||\n      ((placement === top || placement === bottom) && variation === end)\n    ) {\n      sideX = right;\n      const offsetX =\n        isFixed && offsetParent === win && win.visualViewport\n          ? win.visualViewport.width\n          : // $FlowFixMe[prop-missing]\n            offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  const commonStyles = {\n    position,\n    ...(adaptive && unsetSides),\n  };\n\n  ({ x, y } =\n    roundOffsets === true\n      ? roundOffsetsByDPR({ x, y }, getWindow(popper))\n      : { x, y });\n\n  if (gpuAcceleration) {\n    return {\n      ...commonStyles,\n      [sideY]: hasY ? '0' : '',\n      [sideX]: hasX ? '0' : '',\n      // Layer acceleration can disable subpixel rendering which causes slightly\n      // blurry text on low PPI displays, so we want to use 2D transforms\n      // instead\n      transform:\n        (win.devicePixelRatio || 1) <= 1\n          ? `translate(${x}px, ${y}px)`\n          : `translate3d(${x}px, ${y}px, 0)`,\n    };\n  }\n\n  return {\n    ...commonStyles,\n    [sideY]: hasY ? `${y}px` : '',\n    [sideX]: hasX ? `${x}px` : '',\n    transform: '',\n  };\n}\n\nfunction computeStyles({ state, options }: ModifierArguments<Options>) {\n  const {\n    gpuAcceleration = true,\n    adaptive = true,\n    // defaults to use builtin `roundOffsetsByDPR`\n    roundOffsets = true,\n  } = options;\n\n  const commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed',\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = {\n      ...state.styles.popper,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.popperOffsets,\n        position: state.options.strategy,\n        adaptive,\n        roundOffsets,\n      }),\n    };\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = {\n      ...state.styles.arrow,\n      ...mapToStyles({\n        ...commonStyles,\n        offsets: state.modifiersData.arrow,\n        position: 'absolute',\n        adaptive: false,\n        roundOffsets,\n      }),\n    };\n  }\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-placement': state.placement,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ComputeStylesModifier = Modifier<'computeStyles', Options>;\nexport default ({\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {},\n}: ComputeStylesModifier);\n", "// @flow\nimport type { Modifier, ModifierArguments } from '../types';\nimport getNodeName from '../dom-utils/getNodeName';\nimport { isHTMLElement } from '../dom-utils/instanceOf';\n\n// This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles({ state }: ModifierArguments<{||}>) {\n  Object.keys(state.elements).forEach((name) => {\n    const style = state.styles[name] || {};\n\n    const attributes = state.attributes[name] || {};\n    const element = state.elements[name];\n\n    // arrow is optional + virtual elements\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    }\n\n    // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n    Object.assign(element.style, style);\n\n    Object.keys(attributes).forEach((name) => {\n      const value = attributes[name];\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect({ state }: ModifierArguments<{||}>) {\n  const initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0',\n    },\n    arrow: {\n      position: 'absolute',\n    },\n    reference: {},\n  };\n\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return () => {\n    Object.keys(state.elements).forEach((name) => {\n      const element = state.elements[name];\n      const attributes = state.attributes[name] || {};\n\n      const styleProperties = Object.keys(\n        state.styles.hasOwnProperty(name)\n          ? state.styles[name]\n          : initialStyles[name]\n      );\n\n      // Set all values to an empty string to unset them\n      const style = styleProperties.reduce((style, property) => {\n        style[property] = '';\n        return style;\n      }, {});\n\n      // arrow is optional + virtual elements\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n\n      Object.keys(attributes).forEach((attribute) => {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ApplyStylesModifier = Modifier<'applyStyles', {||}>;\nexport default ({\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect,\n  requires: ['computeStyles'],\n}: ApplyStylesModifier);\n", "// @flow\nimport type { Placement } from '../enums';\nimport type { ModifierArguments, Modifier, Rect, Offsets } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport { top, left, right, placements } from '../enums';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetsFunction = ({\n  popper: Rect,\n  reference: Rect,\n  placement: Placement,\n}) => [?number, ?number];\n\ntype Offset = OffsetsFunction | [?number, ?number];\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  offset: Offset,\n};\n\nexport function distanceAndSkiddingToXY(\n  placement: Placement,\n  rects: { popper: Rect, reference: Rect },\n  offset: Offset\n): Offsets {\n  const basePlacement = getBasePlacement(placement);\n  const invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  let [skidding, distance] =\n    typeof offset === 'function'\n      ? offset({\n          ...rects,\n          placement,\n        })\n      : offset;\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n\n  return [left, right].indexOf(basePlacement) >= 0\n    ? { x: distance, y: skidding }\n    : { x: skidding, y: distance };\n}\n\nfunction offset({ state, options, name }: ModifierArguments<Options>) {\n  const { offset = [0, 0] } = options;\n\n  const data = placements.reduce((acc, placement) => {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n\n  const { x, y } = data[state.placement];\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type OffsetModifier = Modifier<'offset', Options>;\nexport default ({\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset,\n}: OffsetModifier);\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n\nexport default function getOppositePlacement(placement: Placement): Placement {\n  return (placement.replace(\n    /left|right|bottom|top/g,\n    matched => hash[matched]\n  ): any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nconst hash = { start: 'end', end: 'start' };\n\nexport default function getOppositeVariationPlacement(\n  placement: Placement\n): Placement {\n  return (placement.replace(/start|end/g, matched => hash[matched]): any);\n}\n", "// @flow\nimport type { State, Padding } from '../types';\nimport type {\n  Placement,\n  ComputedPlacement,\n  Boundary,\n  RootBoundary,\n} from '../enums';\nimport getVariation from './getVariation';\nimport {\n  variationPlacements,\n  basePlacements,\n  placements as allPlacements,\n} from '../enums';\nimport detectOverflow from './detectOverflow';\nimport getBasePlacement from './getBasePlacement';\n\ntype Options = {\n  placement: Placement,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  flipVariations: boolean,\n  allowedAutoPlacements?: Array<Placement>,\n};\n\ntype OverflowsMap = { [ComputedPlacement]: number };\n\nexport default function computeAutoPlacement(\n  state: $Shape<State>,\n  options: Options = {}\n): Array<ComputedPlacement> {\n  const {\n    placement,\n    boundary,\n    rootBoundary,\n    padding,\n    flipVariations,\n    allowedAutoPlacements = allPlacements,\n  } = options;\n\n  const variation = getVariation(placement);\n\n  const placements = variation\n    ? flipVariations\n      ? variationPlacements\n      : variationPlacements.filter(\n          (placement) => getVariation(placement) === variation\n        )\n    : basePlacements;\n\n  let allowedPlacements = placements.filter(\n    (placement) => allowedAutoPlacements.indexOf(placement) >= 0\n  );\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  }\n\n  // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n  const overflows: OverflowsMap = allowedPlacements.reduce((acc, placement) => {\n    acc[placement] = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      padding,\n    })[getBasePlacement(placement)];\n\n    return acc;\n  }, {});\n\n  return Object.keys(overflows).sort((a, b) => overflows[a] - overflows[b]);\n}\n", "// @flow\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { ModifierArguments, Modifier, Padding } from '../types';\nimport getOppositePlacement from '../utils/getOppositePlacement';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getOppositeVariationPlacement from '../utils/getOppositeVariationPlacement';\nimport detectOverflow from '../utils/detectOverflow';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\nimport { bottom, top, start, right, left, auto } from '../enums';\nimport getVariation from '../utils/getVariation';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  mainAxis: boolean,\n  altAxis: boolean,\n  fallbackPlacements: Array<Placement>,\n  padding: Padding,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  altBoundary: boolean,\n  flipVariations: boolean,\n  allowedAutoPlacements: Array<Placement>,\n};\n\nfunction getExpandedFallbackPlacements(placement: Placement): Array<Placement> {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  const oppositePlacement = getOppositePlacement(placement);\n\n  return [\n    getOppositeVariationPlacement(placement),\n    oppositePlacement,\n    getOppositeVariationPlacement(oppositePlacement),\n  ];\n}\n\nfunction flip({ state, options, name }: ModifierArguments<Options>) {\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = true,\n    fallbackPlacements: specifiedFallbackPlacements,\n    padding,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    flipVariations = true,\n    allowedAutoPlacements,\n  } = options;\n\n  const preferredPlacement = state.options.placement;\n  const basePlacement = getBasePlacement(preferredPlacement);\n  const isBasePlacement = basePlacement === preferredPlacement;\n\n  const fallbackPlacements =\n    specifiedFallbackPlacements ||\n    (isBasePlacement || !flipVariations\n      ? [getOppositePlacement(preferredPlacement)]\n      : getExpandedFallbackPlacements(preferredPlacement));\n\n  const placements = [preferredPlacement, ...fallbackPlacements].reduce(\n    (acc, placement) => {\n      return acc.concat(\n        getBasePlacement(placement) === auto\n          ? computeAutoPlacement(state, {\n              placement,\n              boundary,\n              rootBoundary,\n              padding,\n              flipVariations,\n              allowedAutoPlacements,\n            })\n          : placement\n      );\n    },\n    []\n  );\n\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n\n  const checksMap = new Map();\n  let makeFallbackChecks = true;\n  let firstFittingPlacement = placements[0];\n\n  for (let i = 0; i < placements.length; i++) {\n    const placement = placements[i];\n    const basePlacement = getBasePlacement(placement);\n    const isStartVariation = getVariation(placement) === start;\n    const isVertical = [top, bottom].indexOf(basePlacement) >= 0;\n    const len = isVertical ? 'width' : 'height';\n\n    const overflow = detectOverflow(state, {\n      placement,\n      boundary,\n      rootBoundary,\n      altBoundary,\n      padding,\n    });\n\n    let mainVariationSide: any = isVertical\n      ? isStartVariation\n        ? right\n        : left\n      : isStartVariation\n      ? bottom\n      : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    const altVariationSide: any = getOppositePlacement(mainVariationSide);\n\n    const checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(\n        overflow[mainVariationSide] <= 0,\n        overflow[altVariationSide] <= 0\n      );\n    }\n\n    if (checks.every((check) => check)) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    const numberOfChecks = flipVariations ? 3 : 1;\n\n    for (let i = numberOfChecks; i > 0; i--) {\n      const fittingPlacement = placements.find((placement) => {\n        const checks = checksMap.get(placement);\n        if (checks) {\n          return checks.slice(0, i).every((check) => check);\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        break;\n      }\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type FlipModifier = Modifier<'flip', Options>;\nexport default ({\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: { _skip: false },\n}: FlipModifier);\n", "// @flow\nimport { max as mathMax, min as mathMin } from './math';\n\nexport function within(min: number, value: number, max: number): number {\n  return mathMax(min, mathMin(value, max));\n}\n\nexport function withinMaxClamp(min: number, value: number, max: number) {\n  const v = within(min, value, max);\n  return v > max ? max : v;\n}\n", "// @flow\nimport { top, left, right, bottom, start } from '../enums';\nimport type { Placement, Boundary, RootBoundary } from '../enums';\nimport type { Rect, ModifierArguments, Modifier, Padding } from '../types';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport getAltAxis from '../utils/getAltAxis';\nimport { within, withinMaxClamp } from '../utils/within';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport detectOverflow from '../utils/detectOverflow';\nimport getVariation from '../utils/getVariation';\nimport getFreshSideObject from '../utils/getFreshSideObject';\nimport { min as mathMin, max as mathMax } from '../utils/math';\n\ntype TetherOffset =\n  | (({\n      popper: Rect,\n      reference: Rect,\n      placement: Placement,\n    }) => number | { mainAxis: number, altAxis: number })\n  | number\n  | { mainAxis: number, altAxis: number };\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  /* Prevents boundaries overflow on the main axis */\n  mainAxis: boolean,\n  /* Prevents boundaries overflow on the alternate axis */\n  altAxis: boolean,\n  /* The area to check the popper is overflowing in */\n  boundary: Boundary,\n  /* If the popper is not overflowing the main area, fallback to this one */\n  rootBoundary: RootBoundary,\n  /* Use the reference's \"clippingParents\" boundary context */\n  altBoundary: boolean,\n  /**\n   * Allows the popper to overflow from its boundaries to keep it near its\n   * reference element\n   */\n  tether: boolean,\n  /* Offsets when the `tether` option should activate */\n  tetherOffset: TetherOffset,\n  /* Sets a padding to the provided boundary */\n  padding: Padding,\n};\n\nfunction preventOverflow({ state, options, name }: ModifierArguments<Options>) {\n  const {\n    mainAxis: checkMainAxis = true,\n    altAxis: checkAltAxis = false,\n    boundary,\n    rootBoundary,\n    altBoundary,\n    padding,\n    tether = true,\n    tetherOffset = 0,\n  } = options;\n\n  const overflow = detectOverflow(state, {\n    boundary,\n    rootBoundary,\n    padding,\n    altBoundary,\n  });\n  const basePlacement = getBasePlacement(state.placement);\n  const variation = getVariation(state.placement);\n  const isBasePlacement = !variation;\n  const mainAxis = getMainAxisFromPlacement(basePlacement);\n  const altAxis = getAltAxis(mainAxis);\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const tetherOffsetValue =\n    typeof tetherOffset === 'function'\n      ? tetherOffset({\n          ...state.rects,\n          placement: state.placement,\n        })\n      : tetherOffset;\n  const normalizedTetherOffsetValue =\n    typeof tetherOffsetValue === 'number'\n      ? { mainAxis: tetherOffsetValue, altAxis: tetherOffsetValue }\n      : { mainAxis: 0, altAxis: 0, ...tetherOffsetValue };\n  const offsetModifierState = state.modifiersData.offset\n    ? state.modifiersData.offset[state.placement]\n    : null;\n\n  const data = { x: 0, y: 0 };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    const mainSide = mainAxis === 'y' ? top : left;\n    const altSide = mainAxis === 'y' ? bottom : right;\n    const len = mainAxis === 'y' ? 'height' : 'width';\n    const offset = popperOffsets[mainAxis];\n\n    const min = offset + overflow[mainSide];\n    const max = offset - overflow[altSide];\n\n    const additive = tether ? -popperRect[len] / 2 : 0;\n\n    const minLen = variation === start ? referenceRect[len] : popperRect[len];\n    const maxLen = variation === start ? -popperRect[len] : -referenceRect[len];\n\n    // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n    const arrowElement = state.elements.arrow;\n    const arrowRect =\n      tether && arrowElement\n        ? getLayoutRect(arrowElement)\n        : { width: 0, height: 0 };\n    const arrowPaddingObject = state.modifiersData['arrow#persistent']\n      ? state.modifiersData['arrow#persistent'].padding\n      : getFreshSideObject();\n    const arrowPaddingMin = arrowPaddingObject[mainSide];\n    const arrowPaddingMax = arrowPaddingObject[altSide];\n\n    // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n    const arrowLen = within(0, referenceRect[len], arrowRect[len]);\n\n    const minOffset = isBasePlacement\n      ? referenceRect[len] / 2 -\n        additive -\n        arrowLen -\n        arrowPaddingMin -\n        normalizedTetherOffsetValue.mainAxis\n      : minLen -\n        arrowLen -\n        arrowPaddingMin -\n        normalizedTetherOffsetValue.mainAxis;\n    const maxOffset = isBasePlacement\n      ? -referenceRect[len] / 2 +\n        additive +\n        arrowLen +\n        arrowPaddingMax +\n        normalizedTetherOffsetValue.mainAxis\n      : maxLen +\n        arrowLen +\n        arrowPaddingMax +\n        normalizedTetherOffsetValue.mainAxis;\n\n    const arrowOffsetParent =\n      state.elements.arrow && getOffsetParent(state.elements.arrow);\n    const clientOffset = arrowOffsetParent\n      ? mainAxis === 'y'\n        ? arrowOffsetParent.clientTop || 0\n        : arrowOffsetParent.clientLeft || 0\n      : 0;\n\n    const offsetModifierValue = offsetModifierState?.[mainAxis] ?? 0;\n    const tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    const tetherMax = offset + maxOffset - offsetModifierValue;\n\n    const preventedOffset = within(\n      tether ? mathMin(min, tetherMin) : min,\n      offset,\n      tether ? mathMax(max, tetherMax) : max\n    );\n\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    const mainSide = mainAxis === 'x' ? top : left;\n    const altSide = mainAxis === 'x' ? bottom : right;\n    const offset = popperOffsets[altAxis];\n\n    const len = altAxis === 'y' ? 'height' : 'width';\n\n    const min = offset + overflow[mainSide];\n    const max = offset - overflow[altSide];\n\n    const isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    const offsetModifierValue = offsetModifierState?.[altAxis] ?? 0;\n    const tetherMin = isOriginSide\n      ? min\n      : offset -\n        referenceRect[len] -\n        popperRect[len] -\n        offsetModifierValue +\n        normalizedTetherOffsetValue.altAxis;\n    const tetherMax = isOriginSide\n      ? offset +\n        referenceRect[len] +\n        popperRect[len] -\n        offsetModifierValue -\n        normalizedTetherOffsetValue.altAxis\n      : max;\n\n    const preventedOffset =\n      tether && isOriginSide\n        ? withinMaxClamp(tetherMin, offset, tetherMax)\n        : within(tether ? tetherMin : min, offset, tether ? tetherMax : max);\n\n    popperOffsets[altAxis] = preventedOffset;\n    data[altAxis] = preventedOffset - offset;\n  }\n\n  state.modifiersData[name] = data;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type PreventOverflowModifier = Modifier<'preventOverflow', Options>;\nexport default ({\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset'],\n}: PreventOverflowModifier);\n", "// @flow\n\nexport default function getAltAxis(axis: 'x' | 'y'): 'x' | 'y' {\n  return axis === 'x' ? 'y' : 'x';\n}\n", "// @flow\nimport type { Modifier, ModifierArguments, Padding, Rect } from '../types';\nimport type { Placement } from '../enums';\nimport getBasePlacement from '../utils/getBasePlacement';\nimport getLayoutRect from '../dom-utils/getLayoutRect';\nimport contains from '../dom-utils/contains';\nimport getOffsetParent from '../dom-utils/getOffsetParent';\nimport getMainAxisFromPlacement from '../utils/getMainAxisFromPlacement';\nimport { within } from '../utils/within';\nimport mergePaddingObject from '../utils/mergePaddingObject';\nimport expandToHashMap from '../utils/expandToHashMap';\nimport { left, right, basePlacements, top, bottom } from '../enums';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  element: HTMLElement | string | null,\n  padding:\n    | Padding\n    | (({|\n        popper: Rect,\n        reference: Rect,\n        placement: Placement,\n      |}) => Padding),\n};\n\nconst toPaddingObject = (padding, state) => {\n  padding =\n    typeof padding === 'function'\n      ? padding({ ...state.rects, placement: state.placement })\n      : padding;\n\n  return mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n};\n\nfunction arrow({ state, name, options }: ModifierArguments<Options>) {\n  const arrowElement = state.elements.arrow;\n  const popperOffsets = state.modifiersData.popperOffsets;\n  const basePlacement = getBasePlacement(state.placement);\n  const axis = getMainAxisFromPlacement(basePlacement);\n  const isVertical = [left, right].indexOf(basePlacement) >= 0;\n  const len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  const paddingObject = toPaddingObject(options.padding, state);\n  const arrowRect = getLayoutRect(arrowElement);\n  const minProp = axis === 'y' ? top : left;\n  const maxProp = axis === 'y' ? bottom : right;\n\n  const endDiff =\n    state.rects.reference[len] +\n    state.rects.reference[axis] -\n    popperOffsets[axis] -\n    state.rects.popper[len];\n  const startDiff = popperOffsets[axis] - state.rects.reference[axis];\n\n  const arrowOffsetParent = getOffsetParent(arrowElement);\n  const clientSize = arrowOffsetParent\n    ? axis === 'y'\n      ? arrowOffsetParent.clientHeight || 0\n      : arrowOffsetParent.clientWidth || 0\n    : 0;\n\n  const centerToReference = endDiff / 2 - startDiff / 2;\n\n  // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n  const min = paddingObject[minProp];\n  const max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  const center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  const offset = within(min, center, max);\n\n  // Prevents breaking syntax highlighting...\n  const axisProp: string = axis;\n  state.modifiersData[name] = {\n    [axisProp]: offset,\n    centerOffset: offset - center,\n  };\n}\n\nfunction effect({ state, options }: ModifierArguments<Options>) {\n  let { element: arrowElement = '[data-popper-arrow]' } = options;\n\n  if (arrowElement == null) {\n    return;\n  }\n\n  // CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type ArrowModifier = Modifier<'arrow', Options>;\nexport default ({\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow'],\n}: ArrowModifier);\n", "// @flow\nimport type {\n  ModifierArguments,\n  Modifier,\n  Rect,\n  SideObject,\n  Offsets,\n} from '../types';\nimport { top, bottom, left, right } from '../enums';\nimport detectOverflow from '../utils/detectOverflow';\n\nfunction getSideOffsets(\n  overflow: SideObject,\n  rect: Rect,\n  preventedOffsets: Offsets = { x: 0, y: 0 }\n): SideObject {\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x,\n  };\n}\n\nfunction isAnySideFullyClipped(overflow: SideObject): boolean {\n  return [top, right, bottom, left].some((side) => overflow[side] >= 0);\n}\n\nfunction hide({ state, name }: ModifierArguments<{||}>) {\n  const referenceRect = state.rects.reference;\n  const popperRect = state.rects.popper;\n  const preventedOffsets = state.modifiersData.preventOverflow;\n\n  const referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference',\n  });\n  const popperAltOverflow = detectOverflow(state, {\n    altBoundary: true,\n  });\n\n  const referenceClippingOffsets = getSideOffsets(\n    referenceOverflow,\n    referenceRect\n  );\n  const popperEscapeOffsets = getSideOffsets(\n    popperAltOverflow,\n    popperRect,\n    preventedOffsets\n  );\n\n  const isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  const hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n\n  state.modifiersData[name] = {\n    referenceClippingOffsets,\n    popperEscapeOffsets,\n    isReferenceHidden,\n    hasPopperEscaped,\n  };\n\n  state.attributes.popper = {\n    ...state.attributes.popper,\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped,\n  };\n}\n\n// eslint-disable-next-line import/no-unused-modules\nexport type HideModifier = Modifier<'hide', {||}>;\nexport default ({\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide,\n}: HideModifier);\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n", "// @flow\nimport { popperGenerator, detectOverflow } from './createPopper';\n\nimport eventListeners from './modifiers/eventListeners';\nimport popperOffsets from './modifiers/popperOffsets';\nimport computeStyles from './modifiers/computeStyles';\nimport applyStyles from './modifiers/applyStyles';\nimport offset from './modifiers/offset';\nimport flip from './modifiers/flip';\nimport preventOverflow from './modifiers/preventOverflow';\nimport arrow from './modifiers/arrow';\nimport hide from './modifiers/hide';\n\nexport type * from './types';\n\nconst defaultModifiers = [\n  eventListeners,\n  popperOffsets,\n  computeStyles,\n  applyStyles,\n  offset,\n  flip,\n  preventOverflow,\n  arrow,\n  hide,\n];\n\nconst createPopper = popperGenerator({ defaultModifiers });\n\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper as createPopperLite } from './popper-lite';\n// eslint-disable-next-line import/no-unused-modules\nexport * from './modifiers';\n"], "mappings": ";;;;;;;;;;;AAIe,eAASA,EAAUC,IAAAA;AAAAA,YACpB,QAARA,GAAAA,QACKC;AAAAA,YAGe,sBAApBD,GAAKE,SAAAA,GAAkC;AAAA,cACnCC,KAAgBH,GAAKG;AAAAA,iBACpBA,MAAgBA,GAAcC,eAAwBH;QAAAA;AAAAA,eAGxDD;MAAAA;ACTT,eAASK,EAAUL,IAAAA;AAAAA,eAEVA,cADYD,EAAUC,EAAAA,EAAMM,WACEN,cAAgBM;MAAAA;AAKvD,eAASC,EAAcP,IAAAA;AAAAA,eAEdA,cADYD,EAAUC,EAAAA,EAAMQ,eACER,cAAgBQ;MAAAA;AAKvD,eAASC,EAAaT,IAAAA;AAAAA,eAEM,eAAA,OAAfU,eAIJV,cADYD,EAAUC,EAAAA,EAAMU,cACEV,cAAgBU;MAAAA;ACxBhD,UAAMC,IAAMC,KAAKD,KACXE,IAAMD,KAAKC,KACXC,IAAQF,KAAKE;ACMX,eAASC,IAAAA;AAAAA,YAChBC,KAAUC,UAAsBC;AAAAA,eAAAA,QAElCF,MAAAA,GAAQG,UAAUC,MAAMC,QAAQL,GAAOG,MAAAA,IAClCH,GAAOG,OACXG,IAAI,SAACC,IAAAA;AAAAA,iBAAYA,GAAKC,QAAAA,MAASD,GAAKE;QAAAA,CAAAA,EACpCC,KAAK,GAAA,IAGHT,UAAUU;MAAAA;ACfJ,eAASC,IAAAA;AAAAA,eAAAA,CACd,iCAAiCC,KAAKd,EAAAA,CAAAA;MAAAA;ACGjC,eAASe,EACtBC,IACAC,IACAC,IAAAA;AAAAA,mBADAD,OAAAA,KAAAA,QAAwB,WACxBC,OAAAA,KAAAA;AAA2B,YAErBC,KAAaH,GAAQD,sBAAAA,GACvBK,KAAS,GACTC,KAAS;AAETJ,QAAAA,MAAgBzB,EAAcwB,EAAAA,MAChCI,KACGJ,GAAsBM,cAAc,KACjCvB,EAAMoB,GAAWI,KAAAA,IAAUP,GAAsBM,eACjD,GACND,KACGL,GAAsBQ,eAAe,KAClCzB,EAAMoB,GAAWM,MAAAA,IAAWT,GAAsBQ,gBAClD;AAAA,YAGAE,MAAmBpC,EAAU0B,EAAAA,IAAWhC,EAAUgC,EAAAA,IAAW9B,QAA7DwC,gBACFC,KAAAA,CAAoBd,EAAAA,KAAsBK,IAE1CU,MACHT,GAAWU,QACTF,MAAoBD,KAAiBA,GAAeI,aAAa,MACpEV,IACIW,MACHZ,GAAWa,OACTL,MAAoBD,KAAiBA,GAAeO,YAAY,MACnEZ,IACIE,KAAQJ,GAAWI,QAAQH,IAC3BK,KAASN,GAAWM,SAASJ;AAAAA,eAE5B,EACLE,OAAAA,IACAE,QAAAA,IACAO,KAAKD,IACLG,OAAON,KAAIL,IACXY,QAAQJ,KAAIN,IACZI,MAAMD,IACNA,GAAAA,IACAG,GAAAA,GAAAA;MAAAA;AC7CW,eAASK,EAAgBnD,IAAAA;AAAAA,YAChCoD,KAAMrD,EAAUC,EAAAA;AAAAA,eAIf,EACLqD,YAJiBD,GAAIE,aAKrBC,WAJgBH,GAAII,YAAAA;MAAAA;ACJT,eAASC,EAAY1B,IAAAA;AAAAA,eAC3BA,MAAWA,GAAQ2B,YAAY,IAAIC,YAAAA,IAAgB;MAAA;ACA7C,eAASC,EACtB7B,IAAAA;AAAAA,iBAIG1B,EAAU0B,EAAAA,IACPA,GAAQ5B,gBAER4B,GAAQ8B,aAAa5D,OAAO4D,UAChCC;MAAAA;ACRW,eAASC,EAAoBhC,IAAAA;AAAAA,eASxCD,EAAsB8B,EAAmB7B,EAAAA,CAAAA,EAAUa,OACnDO,EAAgBpB,EAAAA,EAASsB;MAAAA;ACZd,eAASW,EACtBjC,IAAAA;AAAAA,eAEOhC,EAAUgC,EAAAA,EAASiC,iBAAiBjC,EAAAA;MAAAA;ACH9B,eAASkC,EAAelC,IAAAA;AAAAA,YAAAA,KAEMiC,EAAiBjC,EAAAA,GAApDmC,KAAAA,GAAAA,UAAUC,KAAAA,GAAAA,WAAWC,KAAAA,GAAAA;AAAAA,eACtB,6BAA6BvC,KAAKqC,KAAWE,KAAYD,EAAAA;MAAAA;ACenD,eAASE,EACtBC,IACAC,IACAC,IAAAA;AAAAA,mBAAAA,OAAAA,KAAAA;AAAmB,YCjBiBxE,ICLO+B,IFwBrC0C,KAA0BlE,EAAcgE,EAAAA,GACxCG,KACJnE,EAAcgE,EAAAA,KAjBlB,SAAyBxC,IAAAA;AAAAA,cACjB4C,KAAO5C,GAAQD,sBAAAA,GACfK,KAASrB,EAAM6D,GAAKrC,KAAAA,IAASP,GAAQM,eAAe,GACpDD,KAAStB,EAAM6D,GAAKnC,MAAAA,IAAUT,GAAQQ,gBAAgB;AAAA,iBAE1C,MAAXJ,MAA2B,MAAXC;QAAAA,EAY0BmC,EAAAA,GAC3CT,KAAkBF,EAAmBW,EAAAA,GACrCI,KAAO7C,EACXwC,IACAI,IACAF,EAAAA,GAGEI,KAAS,EAAEvB,YAAY,GAAGE,WAAW,EAAA,GACrCsB,KAAU,EAAElC,GAAG,GAAGG,GAAG,EAAA;AAAA,gBAErB2B,MAAAA,CAA6BA,MAAAA,CAA4BD,SAE3B,WAA9Bf,EAAYc,EAAAA,KAEZN,EAAeH,EAAAA,OAEfc,MCtCgC5E,KDsCTuE,QCrCdxE,EAAUC,EAAAA,KAAUO,EAAcP,EAAAA,ICLxC,EACLqD,aAFyCtB,KDSb/B,ICPRqD,YACpBE,WAAWxB,GAAQwB,UAAAA,IDIZJ,EAAgBnD,EAAAA,IDuCnBO,EAAcgE,EAAAA,MAChBM,KAAU/C,EAAsByC,IAAAA,IAAc,GACtC5B,KAAK4B,GAAaO,YAC1BD,GAAQ/B,KAAKyB,GAAaQ,aACjBjB,OACTe,GAAQlC,IAAIoB,EAAoBD,EAAAA,KAI7B,EACLnB,GAAGgC,GAAK/B,OAAOgC,GAAOvB,aAAawB,GAAQlC,GAC3CG,GAAG6B,GAAK5B,MAAM6B,GAAOrB,YAAYsB,GAAQ/B,GACzCR,OAAOqC,GAAKrC,OACZE,QAAQmC,GAAKnC,OAAAA;MAAAA;AGvDF,eAASwC,EAAcjD,IAAAA;AAAAA,YAC9BG,KAAaJ,EAAsBC,EAAAA,GAIrCO,KAAQP,GAAQM,aAChBG,KAAST,GAAQQ;AAAAA,eAEjB3B,KAAKqE,IAAI/C,GAAWI,QAAQA,EAAAA,KAAU,MACxCA,KAAQJ,GAAWI,QAGjB1B,KAAKqE,IAAI/C,GAAWM,SAASA,EAAAA,KAAW,MAC1CA,KAASN,GAAWM,SAGf,EACLG,GAAGZ,GAAQc,YACXC,GAAGf,GAAQiB,WACXV,OAAAA,IACAE,QAAAA,GAAAA;MAAAA;ACrBW,eAAS0C,EAAcnD,IAAAA;AAAAA,eACP,WAAzB0B,EAAY1B,EAAAA,IACPA,KAOPA,GAAQoD,gBACRpD,GAAQqD,eACP3E,EAAasB,EAAAA,IAAWA,GAAQsD,OAAO,SAExCzB,EAAmB7B,EAAAA;MAAAA;ACZR,eAASuD,EAAgBtF,IAAAA;AAAAA,eAClC,CAAC,QAAQ,QAAQ,WAAA,EAAauF,QAAQ9B,EAAYzD,EAAAA,CAAAA,KAAU,IAEvDA,GAAKG,cAAcqF,OAGxBjF,EAAcP,EAAAA,KAASiE,EAAejE,EAAAA,IACjCA,KAGFsF,EAAgBJ,EAAclF,EAAAA,CAAAA;MAAAA;ACHxB,eAASyF,EACtB1D,IACA2D,IAAAA;AAAAA,YAAAA;AAAAA,mBAAAA,OAAAA,KAAgC,CAAA;AAAA,YAE1BC,KAAeL,EAAgBvD,EAAAA,GAC/B6D,KAASD,QAAAA,SAAAA,KAAiB5D,GAAQ5B,iBAAAA,SAAR0F,GAAuBL,OACjDpC,KAAMrD,EAAU4F,EAAAA,GAChBG,KAASF,KACX,CAACxC,EAAAA,EAAK2C,OACJ3C,GAAIX,kBAAkB,CAAA,GACtBwB,EAAe0B,EAAAA,IAAgBA,KAAe,CAAA,CAAA,IAEhDA,IACEK,KAAcN,GAAKK,OAAOD,EAAAA;AAAAA,eAEzBF,KACHI,KAEAA,GAAYD,OAAON,EAAkBP,EAAcY,EAAAA,CAAAA,CAAAA;MAAAA;AC5B1C,eAASG,EAAelE,IAAAA;AAAAA,eAC9B,CAAC,SAAS,MAAM,IAAA,EAAMwD,QAAQ9B,EAAY1B,EAAAA,CAAAA,KAAa;MAAA;ACKhE,eAASmE,EAAoBnE,IAAAA;AAAAA,eAExBxB,EAAcwB,EAAAA,KAEwB,YAAvCiC,EAAiBjC,EAAAA,EAASoE,WAKrBpE,GAAQwC,eAHN;MAAA;AAsDI,eAAS6B,EAAgBrE,IAAAA;AAAAA,iBAChC9B,KAASF,EAAUgC,EAAAA,GAErBwC,KAAe2B,EAAoBnE,EAAAA,GAGrCwC,MACA0B,EAAe1B,EAAAA,KAC6B,aAA5CP,EAAiBO,EAAAA,EAAc4B,WAE/B5B,CAAAA,KAAe2B,EAAoB3B,EAAAA;AAAAA,eAInCA,OAC+B,WAA9Bd,EAAYc,EAAAA,KACoB,WAA9Bd,EAAYc,EAAAA,KACiC,aAA5CP,EAAiBO,EAAAA,EAAc4B,YAE5BlG,KAGFsE,MApET,SAA4BxC,IAAAA;AAAAA,cACpBsE,KAAY,WAAWxE,KAAKd,EAAAA,CAAAA;AAAAA,cACrB,WAAWc,KAAKd,EAAAA,CAAAA,KAEjBR,EAAcwB,EAAAA,KAGI,YADTiC,EAAiBjC,EAAAA,EACrBoE,SAAAA,QACN;AAAA,cAIPG,KAAcpB,EAAcnD,EAAAA;AAAAA,eAE5BtB,EAAa6F,EAAAA,MACfA,KAAcA,GAAYjB,OAI1B9E,EAAc+F,EAAAA,KACd,CAAC,QAAQ,MAAA,EAAQf,QAAQ9B,EAAY6C,EAAAA,CAAAA,IAAgB,KACrD;AAAA,gBACMC,KAAMvC,EAAiBsC,EAAAA;AAAAA,gBAMT,WAAlBC,GAAIC,aACgB,WAApBD,GAAIE,eACY,YAAhBF,GAAIG,WAAAA,OACJ,CAAC,aAAa,aAAA,EAAenB,QAAQgB,GAAII,UAAAA,KACxCN,MAAgC,aAAnBE,GAAII,cACjBN,MAAaE,GAAIK,UAAyB,WAAfL,GAAIK,OAAAA,QAEzBN;AAEPA,YAAAA,KAAcA,GAAYlB;UAAAA;AAAAA,iBAIvB;QAAA,EA2BmCrD,EAAAA,KAAY9B;MAAAA;AC1FjD,UAAM8C,IAAa,OACbG,IAAmB,UACnBD,IAAiB,SACjBL,IAAe,QACfiE,IAAe,QAMfC,IAAuC,CAAC/D,GAAKG,GAAQD,GAAOL,CAAAA,GAE5DmE,IAAiB,SACjBC,IAAa,OAIbC,IAAuB,YAIvBC,IAAmB,UAiBnBC,IAAiDL,EAAeM,OAC3E,SAACC,IAAgCC,IAAAA;AAAAA,eAC/BD,GAAItB,OAAO,CAAKuB,KAAAA,MAAaP,GAAmBO,KAAAA,MAAaN,CAAAA,CAAAA;MAAAA,GAC/D,CAAA,CAAA,GAEWO,IAA+B,CAAA,EAAA,OAAIT,GAAAA,CAAgBD,CAAAA,CAAAA,EAAMO,OACpE,SACEC,IACAC,IAAAA;AAAAA,eAEAD,GAAItB,OAAO,CACTuB,IACIA,KAAAA,MAAaP,GACbO,KAAAA,MAAaN,CAAAA,CAAAA;MAAAA,GAErB,CAAA,CAAA,GAeWQ,IAAwC,CAXb,cACZ,QACU,aAEE,cACZ,QACU,aAEI,eACZ,SACU,YAAA;AC/DxC,eAASC,EAAMC,IAAAA;AAAAA,YACPpG,KAAM,oBAAIqG,OACVC,KAAU,oBAAIC,OACdC,KAAS,CAAA;AAAA,iBAONC,GAAKC,IAAAA;AACZJ,UAAAA,GAAQK,IAAID,GAASE,IAAAA,GAAAA,CAAAA,EAAAA,OAGfF,GAASG,YAAY,CAAA,GACrBH,GAASI,oBAAoB,CAAA,CAAA,EAG1BC,QAAQ,SAAAC,IAAAA;AAAAA,gBAAAA,CACVV,GAAQW,IAAID,EAAAA,GAAM;AAAA,kBACfE,KAAclH,GAAImH,IAAIH,EAAAA;AAExBE,cAAAA,MACFT,GAAKS,EAAAA;YAAAA;UAAAA,CAAAA,GAKXV,GAAOY,KAAKV,EAAAA;QAAAA;AAAAA,eAvBdN,GAAUW,QAAQ,SAAAL,IAAAA;AAChB1G,UAAAA,GAAIqH,IAAIX,GAASE,MAAMF,EAAAA;QAAAA,CAAAA,GAyBzBN,GAAUW,QAAQ,SAAAL,IAAAA;AACXJ,UAAAA,GAAQW,IAAIP,GAASE,IAAAA,KAExBH,GAAKC,EAAAA;QAAAA,CAAAA,GAIFF;MAAAA;ACxCM,eAASc,EAASC,IAAiBC,IAAAA;AAAAA,YAC1CC,KAAWD,GAAME,eAAeF,GAAME,YAAAA;AAAAA,YAGxCH,GAAOD,SAASE,EAAAA,EAAAA,QAAAA;AAIf,YAAIC,MAAYtI,EAAasI,EAAAA,GAAW;AAAA,cACvCE,KAAOH;AAAAA,aACR;AAAA,gBACGG,MAAQJ,GAAOK,WAAWD,EAAAA,EAAAA,QAAAA;AAI9BA,YAAAA,KAAOA,GAAK7D,cAAc6D,GAAK5D;UAAAA,SACxB4D;QAAAA;AAAAA,eAAAA;MAIJ;ACpBM,eAASE,EAAiBxE,IAAAA;AAAAA,eAAAA,OAAAA,OAAAA,CAAAA,GAElCA,IAAAA,EACH/B,MAAM+B,GAAKhC,GACXI,KAAK4B,GAAK7B,GACVG,OAAO0B,GAAKhC,IAAIgC,GAAKrC,OACrBY,QAAQyB,GAAK7B,IAAI6B,GAAKnC,OAAAA,CAAAA;MAAAA;AC2B1B,eAAS4G,EACPrH,IACAsH,IACAC,IAAAA;AAAAA,eAEOD,OAAmBpC,IACtBkC,ECnCS,SACbpH,IACAuH,IAAAA;AAAAA,cAEMlG,KAAMrD,EAAUgC,EAAAA,GAChBwH,KAAO3F,EAAmB7B,EAAAA,GAC1BU,KAAiBW,GAAIX,gBAEvBH,KAAQiH,GAAKC,aACbhH,KAAS+G,GAAKE,cACd9G,KAAI,GACJG,KAAI;AAAA,cAEJL,IAAgB;AAClBH,YAAAA,KAAQG,GAAeH,OACvBE,KAASC,GAAeD;AAAAA,gBAElBkH,KAAiB9H,EAAAA;AAAAA,aAEnB8H,MAAAA,CAAoBA,MAA+B,YAAbJ,QACxC3G,KAAIF,GAAeI,YACnBC,KAAIL,GAAeO;UAAAA;AAAAA,iBAIhB,EACLV,OAAAA,IACAE,QAAAA,IACAG,GAAGA,KAAIoB,EAAoBhC,EAAAA,GAC3Be,GAAAA,GAAAA;QAAAA,EDMmCf,IAASuH,EAAAA,CAAAA,IAC1CjJ,EAAUgJ,EAAAA,IAzBhB,SACEtH,IACAuH,IAAAA;AAAAA,cAEM3E,KAAO7C,EAAsBC,IAAAA,OAA6B,YAAbuH,EAAAA;AAAAA,iBAEnD3E,GAAK5B,MAAM4B,GAAK5B,MAAMhB,GAAQgD,WAC9BJ,GAAK/B,OAAO+B,GAAK/B,OAAOb,GAAQ+C,YAChCH,GAAKzB,SAASyB,GAAK5B,MAAMhB,GAAQ0H,cACjC9E,GAAK1B,QAAQ0B,GAAK/B,OAAOb,GAAQyH,aACjC7E,GAAKrC,QAAQP,GAAQyH,aACrB7E,GAAKnC,SAAST,GAAQ0H,cACtB9E,GAAKhC,IAAIgC,GAAK/B,MACd+B,GAAK7B,IAAI6B,GAAK5B,KAEP4B;QAAAA,EAWwB0E,IAAgBC,EAAAA,IAC3CH,EEnCS,SAAyBpH,IAAAA;AAAAA,cAAAA,IAChCwH,KAAO3F,EAAmB7B,EAAAA,GAC1B4H,KAAYxG,EAAgBpB,EAAAA,GAC5ByD,KAAAA,SAAAA,KAAOzD,GAAQ5B,iBAAAA,SAAR0F,GAAuBL,MAE9BlD,KAAQ3B,EACZ4I,GAAKK,aACLL,GAAKC,aACLhE,KAAOA,GAAKoE,cAAc,GAC1BpE,KAAOA,GAAKgE,cAAc,CAAA,GAEtBhH,KAAS7B,EACb4I,GAAKM,cACLN,GAAKE,cACLjE,KAAOA,GAAKqE,eAAe,GAC3BrE,KAAOA,GAAKiE,eAAe,CAAA,GAGzB9G,KAAAA,CAAKgH,GAAUtG,aAAaU,EAAoBhC,EAAAA,GAC9Ce,KAAAA,CAAK6G,GAAUpG;AAAAA,iBAE4B,UAA7CS,EAAiBwB,MAAQ+D,EAAAA,EAAMO,cACjCnH,MAAKhC,EAAI4I,GAAKC,aAAahE,KAAOA,GAAKgE,cAAc,CAAA,IAAKlH,KAGrD,EAAEA,OAAAA,IAAOE,QAAAA,IAAQG,GAAAA,IAAGG,GAAAA,GAAAA;QAAAA,EFUUc,EAAmB7B,EAAAA,CAAAA,CAAAA;MAAAA;AA8B3C,eAASgI,EACtBhI,IACAiI,IACAC,IACAX,IAAAA;AAAAA,YAEMY,KACS,sBAAbF,KA/BJ,SAA4BjI,IAAAA;AAAAA,cACpBoI,KAAkB1E,EAAkBP,EAAcnD,EAAAA,CAAAA,GAGlDqI,KADJ,CAAC,YAAY,OAAA,EAAS7E,QAAQvB,EAAiBjC,EAAAA,EAASoE,QAAAA,KAAa,KAEhD5F,EAAcwB,EAAAA,IAC/BqE,EAAgBrE,EAAAA,IAChBA;AAAAA,iBAED1B,EAAU+J,EAAAA,IAKRD,GAAgBvD,OACrB,SAACyC,IAAAA;AAAAA,mBACChJ,EAAUgJ,EAAAA,KACVT,EAASS,IAAgBe,EAAAA,KACO,WAAhC3G,EAAY4F,EAAAA;UAAAA,CAAAA,IARP,CAAA;QAAA,EAsBgBtH,EAAAA,IACnB,CAAA,EAAGgE,OAAOiE,EAAAA,GACVG,KAAAA,CAAAA,EAAAA,OAAsBD,IAAAA,CAAqBD,EAAAA,CAAAA,GAC3CI,KAAsBF,GAAgB,CAAA,GAEtCG,KAAeH,GAAgB/C,OAAO,SAACmD,IAASlB,IAAAA;AAAAA,cAC9C1E,KAAOyE,EAA2BrH,IAASsH,IAAgBC,EAAAA;AAAAA,iBAEjEiB,GAAQxH,MAAMpC,EAAIgE,GAAK5B,KAAKwH,GAAQxH,GAAAA,GACpCwH,GAAQtH,QAAQpC,EAAI8D,GAAK1B,OAAOsH,GAAQtH,KAAAA,GACxCsH,GAAQrH,SAASrC,EAAI8D,GAAKzB,QAAQqH,GAAQrH,MAAAA,GAC1CqH,GAAQ3H,OAAOjC,EAAIgE,GAAK/B,MAAM2H,GAAQ3H,IAAAA,GAE/B2H;QAAAA,GACNnB,EAA2BrH,IAASsI,IAAqBf,EAAAA,CAAAA;AAAAA,eAE5DgB,GAAahI,QAAQgI,GAAarH,QAAQqH,GAAa1H,MACvD0H,GAAa9H,SAAS8H,GAAapH,SAASoH,GAAavH,KACzDuH,GAAa3H,IAAI2H,GAAa1H,MAC9B0H,GAAaxH,IAAIwH,GAAavH,KAEvBuH;MAAAA;AGrGM,eAASE,EACtBlD,IAAAA;AAAAA,eAEQA,GAAUmD,MAAM,GAAA,EAAK,CAAA;MAAA;ACHhB,eAASC,EAAapD,IAAAA;AAAAA,eAC3BA,GAAUmD,MAAM,GAAA,EAAK,CAAA;MAAA;ACDhB,eAASE,EACtBrD,IAAAA;AAAAA,eAEO,CAAC,OAAO,QAAA,EAAU/B,QAAQ+B,EAAAA,KAAc,IAAI,MAAM;MAAA;ACM5C,eAASsD,EAAAA,IAAAA;AAAAA,YAelB/F,IAdJgG,KAAAA,GAAAA,WACA9I,KAAAA,GAAAA,SACAuF,KAAAA,GAAAA,WAOMwD,KAAgBxD,KAAYkD,EAAiBlD,EAAAA,IAAa,MAC1DyD,KAAYzD,KAAYoD,EAAapD,EAAAA,IAAa,MAClD0D,KAAUH,GAAUlI,IAAIkI,GAAUvI,QAAQ,IAAIP,GAAQO,QAAQ,GAC9D2I,KAAUJ,GAAU/H,IAAI+H,GAAUrI,SAAS,IAAIT,GAAQS,SAAS;AAAA,gBAG9DsI,IAAAA;UAAAA,KACD/H;AACH8B,YAAAA,KAAU,EACRlC,GAAGqI,IACHlI,GAAG+H,GAAU/H,IAAIf,GAAQS,OAAAA;AAAAA;UAAAA,KAGxBU;AACH2B,YAAAA,KAAU,EACRlC,GAAGqI,IACHlI,GAAG+H,GAAU/H,IAAI+H,GAAUrI,OAAAA;AAAAA;UAAAA,KAG1BS;AACH4B,YAAAA,KAAU,EACRlC,GAAGkI,GAAUlI,IAAIkI,GAAUvI,OAC3BQ,GAAGmI,GAAAA;AAAAA;UAAAA,KAGFrI;AACHiC,YAAAA,KAAU,EACRlC,GAAGkI,GAAUlI,IAAIZ,GAAQO,OACzBQ,GAAGmI,GAAAA;AAAAA;UAAAA;AAILpG,YAAAA,KAAU,EACRlC,GAAGkI,GAAUlI,GACbG,GAAG+H,GAAU/H,EAAAA;QAAAA;AAAAA,YAIboI,KAAWJ,KACbH,EAAyBG,EAAAA,IACzB;AAAA,YAEY,QAAZI,IAAkB;AAAA,cACdC,KAAmB,QAAbD,KAAmB,WAAW;AAAA,kBAElCH,IAAAA;YAAAA,KACDhE;AACHlC,cAAAA,GAAQqG,EAAAA,IACNrG,GAAQqG,EAAAA,KAAaL,GAAUM,EAAAA,IAAO,IAAIpJ,GAAQoJ,EAAAA,IAAO;AAAA;YAAA,KAExDnE;AACHnC,cAAAA,GAAQqG,EAAAA,IACNrG,GAAQqG,EAAAA,KAAaL,GAAUM,EAAAA,IAAO,IAAIpJ,GAAQoJ,EAAAA,IAAO;UAAA;QAAA;AAAA,eAM1DtG;MAAAA;AC5EM,eAASuG,EACtBC,IAAAA;AAAAA,eAAAA,OAAAA,OAAAA,CAAAA,GCDO,EACLtI,KAAK,GACLE,OAAO,GACPC,QAAQ,GACRN,MAAM,EAAA,GDCHyI,EAAAA;MAAAA;AEPQ,eAASC,EAGtBC,IAAUC,IAAAA;AAAAA,eACHA,GAAKpE,OAAO,SAACqE,IAASC,IAAAA;AAAAA,iBAC3BD,GAAQC,EAAAA,IAAOH,IACRE;QAAAA,GACN,CAAA,CAAA;MAAA;ACwBU,eAASE,EACtBC,IACAC,IAAAA;AAAAA,mBAAAA,OAAAA,KAA2B,CAAA;AAAA,YAAAC,KAUvBD,IAAAA,KAAAA,GAPFvE,WAAAA,KAAAA,WAAAA,KAAYsE,GAAMtE,YAAAA,IAAAA,KAAAA,GAClBgC,UAAAA,KAAAA,WAAAA,KAAWsC,GAAMtC,WAAAA,IAAAA,KAAAA,GACjBU,UAAAA,KAAAA,WAAAA,KdvB8C,oBAAA+B,IAAAC,KAAAF,GcwB9C7B,cAAAA,KAAAA,WAAAA,KAAehD,IAAAA,IAAAA,KAAAA,GACfgF,gBAAAA,KAAAA,WAAAA,KAAiB/E,IAAAA,IAAAA,KAAAA,GACjBgF,aAAAA,KAAAA,WAAAA,MAAAA,IAAAA,KAAAA,GACAC,SAAAA,KAAAA,WAAAA,KAAU,IAAAC,IAGNf,KAAgBD,EACD,YAAA,OAAZe,KACHA,KACAb,EAAgBa,IAASrF,CAAAA,CAAAA,GAGzBuF,KAAaJ,OAAmB/E,Id9BF,cc8BuBA,GAErDoF,KAAaV,GAAMW,MAAMrF,QACzBnF,KAAU6J,GAAMY,SAASN,KAAcG,KAAaJ,EAAAA,GAEpDQ,KAAqB1C,EACzB1J,EAAU0B,EAAAA,IACNA,KACAA,GAAQ2K,kBAAkB9I,EAAmBgI,GAAMY,SAAStF,MAAAA,GAChE8C,IACAC,IACAX,EAAAA,GAGIqD,KAAsB7K,EAAsB8J,GAAMY,SAAS3B,SAAAA,GAE3D+B,KAAgBhC,EAAe,EACnCC,WAAW8B,IACX5K,SAASuK,IACThD,UAAU,YACVhC,WAAAA,GAAAA,CAAAA,GAGIuF,KAAmB1D,EAAAA,OAAAA,OAAAA,CAAAA,GACpBmD,IACAM,EAAAA,CAAAA,GAGCE,KACJb,OAAmB/E,IAAS2F,KAAmBF,IAI3CI,KAAkB,EACtBhK,KAAK0J,GAAmB1J,MAAM+J,GAAkB/J,MAAMsI,GAActI,KACpEG,QACE4J,GAAkB5J,SAClBuJ,GAAmBvJ,SACnBmI,GAAcnI,QAChBN,MAAM6J,GAAmB7J,OAAOkK,GAAkBlK,OAAOyI,GAAczI,MACvEK,OACE6J,GAAkB7J,QAAQwJ,GAAmBxJ,QAAQoI,GAAcpI,MAAAA,GAGjE+J,KAAapB,GAAMqB,cAAcC;AAAAA,YAGnCjB,OAAmB/E,KAAU8F,IAAY;AAAA,cACrCE,KAASF,GAAW1F,EAAAA;AAE1B6F,iBAAO3B,KAAKuB,EAAAA,EAAiB1E,QAAQ,SAACqD,IAAAA;AAAAA,gBAC9B0B,KAAW,CAACnK,GAAOC,CAAAA,EAAQqC,QAAQmG,EAAAA,KAAQ,IAAI,IAAA,IAC/C2B,KAAO,CAACtK,GAAKG,CAAAA,EAAQqC,QAAQmG,EAAAA,KAAQ,IAAI,MAAM;AACrDqB,YAAAA,GAAgBrB,EAAAA,KAAQwB,GAAOG,EAAAA,IAAQD;UAAAA,CAAAA;QAAAA;AAAAA,eAIpCL;MAAAA;AC5FT,UAAMO,IAAuC,EAC3ChG,WAAW,UACXI,WAAW,CAAA,GACX4B,UAAU,WAAA;AAQZ,eAASiE,IAAAA;AAAAA,iBAAAA,KAAAA,UAAAA,QAAoBC,KAAAA,IAAAA,MAAAA,EAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA,KAAAA,CAAAA,GAAAA,EAAAA,IAAAA,UAAAA,EAAAA;AAAAA,eAAAA,CACnBA,GAAKC,KACX,SAAC1L,IAAAA;AAAAA,iBAAAA,EACGA,MAAoD,cAAA,OAAlCA,GAAQD;QAAAA,CAAAA;MAAAA;AAI3B,eAAS4L,EAAgBC,IAAAA;AAAAA,mBAAAA,OAAAA,KAAwC,CAAA;AAAA,YAAAC,KAEpED,IAAAA,KAAAA,GADME,kBAAAA,KAAAA,WAAAA,KAAmB,CAAA,IAAA/B,IAAAgC,KAAAF,GAAIG,gBAAAA,KAAAA,WAAAA,KAAiBT,IAAAA;AAAAA,eAGzC,SACLzC,IACA3D,IACA2E,IAAAA;AAAAA,qBAAAA,OAAAA,KAA6CkC;AAAAA,cCzCbC,IAC9BC,ID0CErC,KAAuB,EACzBtE,WAAW,UACX4G,kBAAkB,CAAA,GAClBrC,SAAAA,OAAAA,OAAAA,CAAAA,GAAcyB,GAAoBS,EAAAA,GAClCd,eAAe,CAAA,GACfT,UAAU,EACR3B,WAAAA,IACA3D,QAAAA,GAAAA,GAEFiH,YAAY,CAAA,GACZC,QAAQ,CAAA,EAAA,GAGNC,KAAsC,CAAA,GACtCC,KAAAA,OAEEC,KAAW,EACf3C,OAAAA,IACA4C,YAAAA,SAAWC,IAAAA;AAAAA,gBACH5C,KACwB,cAAA,OAArB4C,KACHA,GAAiB7C,GAAMC,OAAAA,IACvB4C;AAENC,YAAAA,GAAAA,GAEA9C,GAAMC,UAAAA,OAAAA,OAAAA,CAAAA,GAEDkC,IACAnC,GAAMC,SACNA,EAAAA,GAGLD,GAAM+C,gBAAgB,EACpB9D,WAAWxK,EAAUwK,EAAAA,IACjBpF,EAAkBoF,EAAAA,IAClBA,GAAU6B,iBACVjH,EAAkBoF,GAAU6B,cAAAA,IAC5B,CAAA,GACJxF,QAAQzB,EAAkByB,EAAAA,EAAAA;AAAAA,gBEhFlCQ,IAEMkH,IFmFMV,Kd3CC,SACbxG,IAAAA;AAAAA,kBAGMwG,KAAmBzG,EAAMC,EAAAA;AAAAA,qBAGxBF,EAAeJ,OAAO,SAACC,IAAKwH,IAAAA;AAAAA,uBAC1BxH,GAAItB,OACTmI,GAAiBtH,OAAO,SAAAoB,IAAAA;AAAAA,yBAAYA,GAAS6G,UAAUA;gBAAAA,CAAAA,CAAAA;cAAAA,GAExD,CAAA,CAAA;YAAA,GgBrDHnH,KAAAA,CAAAA,EAAAA,OFsFwBmG,IAAqBjC,GAAMC,QAAQnE,SAAAA,GEpFrDkH,KAASlH,GAAUN,OAAO,SAACwH,IAAQE,IAAAA;AAAAA,kBACjCC,KAAWH,GAAOE,GAAQ5G,IAAAA;AAAAA,qBAChC0G,GAAOE,GAAQ5G,IAAAA,IAAQ6G,KAAAA,OAAAA,OAAAA,CAAAA,GAEdA,IACAD,IAAAA,EACHjD,SAAAA,OAAAA,OAAAA,CAAAA,GAAckD,GAASlD,SAAYiD,GAAQjD,OAAAA,GAC3CmD,MAAAA,OAAAA,OAAAA,CAAAA,GAAWD,GAASC,MAASF,GAAQE,IAAAA,EAAAA,CAAAA,IAEvCF,IACGF;YAAAA,GACN,CAAA,CAAA,GAGIzB,OAAO3B,KAAKoD,EAAAA,EAAQtN,IAAI,SAAAoK,IAAAA;AAAAA,qBAAOkD,GAAOlD,EAAAA;YAAAA,CAAAA,EAAAA;AAAAA,mBF0EvCE,GAAMsC,mBAAmBA,GAAiBtH,OAAO,SAACqI,IAAAA;AAAAA,qBAAMA,GAAEC;YAAAA,CAAAA,GAsG5DtD,GAAMsC,iBAAiB7F,QAAQ,SAAA8G,IAAA;AAAA,kBAAGjH,KAAAA,GAAAA,MAAAA,KAAAA,GAAM2D,SAAAA,KAAAA,WAAAA,KAAU,CAAA,IAAAuD,IAAIC,KAAAA,GAAAA;AAAAA,kBAC9B,cAAA,OAAXA,IAAuB;AAAA,oBAC1BC,KAAYD,GAAO,EAAEzD,OAAAA,IAAO1D,MAAAA,IAAMqG,UAAAA,IAAU1C,SAAAA,GAAAA,CAAAA,GAC5C0D,KAAS,WAAA;gBAAA;AACflB,gBAAAA,GAAiB3F,KAAK4G,MAAaC,EAAAA;cAAAA;YAAAA,CAAAA,GAtG9BhB,GAASiB,OAAAA;UAAAA,GAQlBC,aAAAA,WAAAA;AAAAA,gBAAAA,CACMnB,IAAAA;AAAAA,kBAAAA,KAI0B1C,GAAMY,UAA5B3B,KAAAA,GAAAA,WAAW3D,KAAAA,GAAAA;AAAAA,kBAIdqG,EAAiB1C,IAAW3D,EAAAA,GAAAA;AAKjC0E,gBAAAA,GAAMW,QAAQ,EACZ1B,WAAWxG,EACTwG,IACAzE,EAAgBc,EAAAA,GACW,YAA3B0E,GAAMC,QAAQvC,QAAAA,GAEhBpC,QAAQlC,EAAckC,EAAAA,EAAAA,GAQxB0E,GAAM8D,QAAAA,OAEN9D,GAAMtE,YAAYsE,GAAMC,QAAQvE,WAMhCsE,GAAMsC,iBAAiB7F,QACrB,SAACL,IAAAA;AAAAA,yBACE4D,GAAMqB,cAAcjF,GAASE,IAAAA,IAAAA,OAAAA,OAAAA,CAAAA,GACzBF,GAASgH,IAAAA;gBAAAA,CAAAA;AAAAA,yBAITW,KAAQ,GAAGA,KAAQ/D,GAAMsC,iBAAiB0B,QAAQD,KAAAA,KAAAA,SACrD/D,GAAM8D,OAAAA;AAAAA,sBAAAA,KAMyB9D,GAAMsC,iBAAiByB,EAAAA,GAAlD3B,KAAAA,GAAAA,IAAAA,KAAAA,GAAInC,SAAAA,KAAAA,WAAAA,KAAU,CAAA,IAAAgE,IAAI3H,KAAAA,GAAAA;AAER,gCAAA,OAAP8F,OACTpC,KAAQoC,GAAG,EAAEpC,OAAAA,IAAOC,SAAAA,IAAS3D,MAAAA,IAAMqG,UAAAA,GAAAA,CAAAA,KAAe3C;gBAAAA,MARlDA,CAAAA,GAAM8D,QAAAA,OACNC,KAAAA;cAAS;YAAA;UAAA,GAcfH,SCpK8BxB,KDqK5B,WAAA;AAAA,mBACE,IAAI8B,QAAuB,SAACC,IAAAA;AAC1BxB,cAAAA,GAASkB,YAAAA,GACTM,GAAQnE,EAAAA;YAAAA,CAAAA;UAAAA,GCtKX,WAAA;AAAA,mBACAqC,OACHA,KAAU,IAAI6B,QAAW,SAAAC,IAAAA;AACvBD,sBAAQC,QAAAA,EAAUC,KAAK,WAAA;AACrB/B,gBAAAA,KAAAA,QACA8B,GAAQ/B,GAAAA,CAAAA;cAAAA,CAAAA;YAAAA,CAAAA,IAKPC;UAAAA,IDgKLgC,SAAAA,WAAAA;AACEvB,YAAAA,GAAAA,GACAJ,KAAAA;UAAc,EAAA;AAAA,cAAA,CAIbf,EAAiB1C,IAAW3D,EAAAA,EAAAA,QACxBqH;AAAAA,mBAwBAG,KAAAA;AACPL,YAAAA,GAAiBhG,QAAQ,SAAC2F,IAAAA;AAAAA,qBAAOA,GAAAA;YAAAA,CAAAA,GACjCK,KAAmB,CAAA;UAAA;AAAA,iBAvBrBE,GAASC,WAAW3C,EAAAA,EAASmE,KAAK,SAACpE,IAAAA;AAAAA,aAC5B0C,MAAezC,GAAQqE,iBAC1BrE,GAAQqE,cAActE,EAAAA;UAAAA,CAAAA,GAwBnB2C;QAAAA;MAAAA;AGxMX,UAAM4B,IAAU,EAAEA,SAAAA,KAAS;AAAA,UAAA,KAoCX,EACdjI,MAAM,kBACNgH,SAAAA,MACAL,OAAO,SACPb,IAAI,WAAA;MAAA,GACJqB,QAvCF,SAAAF,IAAA;AAAA,YAAkBvD,KAAAA,GAAAA,OAAO2C,KAAAA,GAAAA,UAAU1C,KAAAA,GAAAA,SAAAA,KACQA,GAAjCjH,QAAAA,KAAAA,WAAAA,MAAAA,IAAAA,KAAiCiH,GAAlBuE,QAAAA,KAAAA,WAAAA,MAAAA,IAEjBnQ,KAASF,EAAU6L,GAAMY,SAAStF,MAAAA,GAClCyH,KAAAA,CAAAA,EAAAA,OACD/C,GAAM+C,cAAc9D,WACpBe,GAAM+C,cAAczH,MAAAA;AAAAA,eAGrBtC,MACF+J,GAActG,QAAQ,SAAA1C,IAAAA;AACpBA,UAAAA,GAAa0K,iBAAiB,UAAU9B,GAASiB,QAAQW,CAAAA;QAAAA,CAAAA,GAIzDC,MACFnQ,GAAOoQ,iBAAiB,UAAU9B,GAASiB,QAAQW,CAAAA,GAG9C,WAAA;AACDvL,UAAAA,MACF+J,GAActG,QAAQ,SAAA1C,IAAAA;AACpBA,YAAAA,GAAa2K,oBAAoB,UAAU/B,GAASiB,QAAQW,CAAAA;UAAAA,CAAAA,GAI5DC,MACFnQ,GAAOqQ,oBAAoB,UAAU/B,GAASiB,QAAQW,CAAAA;QAAAA;MAAAA,GAa1DnB,MAAM,CAAA,EAAA;AAAA,UAAA,KCjCQ,EACd9G,MAAM,iBACNgH,SAAAA,MACAL,OAAO,QACPb,IAnBF,SAAAmB,IAAA;AAAA,YAAyBvD,KAAAA,GAAAA,OAAO1D,KAAAA,GAAAA;AAK9B0D,QAAAA,GAAMqB,cAAc/E,EAAAA,IAAQ0C,EAAe,EACzCC,WAAWe,GAAMW,MAAM1B,WACvB9I,SAAS6J,GAAMW,MAAMrF,QACrBoC,UAAU,YACVhC,WAAWsE,GAAMtE,UAAAA,CAAAA;MAAAA,GAWnB0H,MAAM,CAAA,EAAA,GCcFuB,KAAa,EACjBxN,KAAK,QACLE,OAAO,QACPC,QAAQ,QACRN,MAAM,OAAA;AAeD,eAAS4N,GAAAA,IAAAA;AAAAA,YAAAA,IACdtJ,KAAAA,GAAAA,QACAoF,KAAAA,GAAAA,YACAhF,KAAAA,GAAAA,WACAyD,KAAAA,GAAAA,WACAlG,KAAAA,GAAAA,SACAsB,KAAAA,GAAAA,UACAsK,KAAAA,GAAAA,iBACAC,KAAAA,GAAAA,UACAC,KAAAA,GAAAA,cACAnM,KAAAA,GAAAA,SAAAA,KAauBK,GAAjBlC,GAAAA,KAAAA,WAAAA,KAAI,IAAAiO,IAAAxE,KAAavH,GAAV/B,GAAAA,KAAAA,WAAAA,KAAI,IAAAsJ,IAAAzJ,KAGS,cAAA,OAAjBgO,KAA8BA,GAAa,EAAEhO,GAAAA,IAAGG,GAAAA,GAAAA,CAAAA,IAAO,EAAEH,GAAAA,IAAGG,GAAAA,GAAAA;AADlEH,QAAAA,KAAAA,GAAAA,GAAGG,KAAAA,GAAAA;AAAAA,YAGA+N,KAAOhM,GAAQiM,eAAe,GAAA,GAC9BC,KAAOlM,GAAQiM,eAAe,GAAA,GAEhCE,KAAgBpO,GAChBqO,KAAgBlO,GAEdK,KAAcnD;AAAAA,YAEhByQ,IAAU;AAAA,cACRnM,KAAe6B,EAAgBc,EAAAA,GAC/BgK,KAAa,gBACbC,KAAY;AAAA,cAEZ5M,OAAiBxE,EAAUmH,EAAAA,KAIiB,aAA5ClD,EAHFO,KAAeX,EAAmBsD,EAAAA,CAAAA,EAGDf,YAClB,eAAbA,OAEA+K,KAAa,gBACbC,KAAY,gBAKhB5M,KAAgBA,IAGd+C,OAAcvE,MACZuE,OAAc1E,KAAQ0E,OAAcrE,MAAU8H,OAAc/D,EAE9DiK,CAAAA,KAAQ/N,GAMRJ,OAJE0B,MAAWD,OAAiBnB,MAAOA,GAAIX,iBACnCW,GAAIX,eAAeD,SAEnB+B,GAAa2M,EAAAA,KACJ5E,GAAW9J,QAC1BM,MAAK2N,KAAkB,IAAA;AAAK,cAI5BnJ,OAAc1E,MACZ0E,OAAcvE,KAAOuE,OAAcpE,MAAW6H,OAAc/D,EAE9DgK,CAAAA,KAAQ/N,GAMRN,OAJE6B,MAAWD,OAAiBnB,MAAOA,GAAIX,iBACnCW,GAAIX,eAAeH,QAEnBiC,GAAa4M,EAAAA,KACJ7E,GAAWhK,OAC1BK,MAAK8N,KAAkB,IAAA;QAAK;AAAA,YAAAW,IAI1BC,KAAAA,OAAAA,OAAAA,EACJlL,UAAAA,GAAAA,GACIuK,MAAYH,EAAAA,GAAAA,KAAAA,SAIhBI,KApGJ,SAAAxB,IAAqC/L,IAAAA;AAAAA,cAART,KAAAA,GAAAA,GAAGG,KAAAA,GAAAA,GACxBwO,KAAMlO,GAAImO,oBAAoB;AAAA,iBAE7B,EACL5O,GAAG7B,EAAM6B,KAAI2O,EAAAA,IAAOA,MAAO,GAC3BxO,GAAGhC,EAAMgC,KAAIwO,EAAAA,IAAOA,MAAO,EAAA;QAAA,EAgGL,EAAE3O,GAAAA,IAAGG,GAAAA,GAAAA,GAAK/C,EAAUmH,EAAAA,CAAAA,IACtC,EAAEvE,GAAAA,IAAGG,GAAAA,GAAAA;AAAAA,eAHRH,KAAAA,GAAAA,GAAGG,KAAAA,GAAAA,GAKF2N,KAAAA,OAAAA,OAAAA,CAAAA,GAEGY,MAAAA,KAAAA,CAAAA,GACFJ,EAAAA,IAAQF,KAAO,MAAM,IAAAK,GACrBJ,EAAAA,IAAQH,KAAO,MAAM,IAAAO,GAItB5K,aACGpD,GAAImO,oBAAoB,MAAM,IAAA,eACd5O,KAAAA,SAAQG,KAAAA,QAAAA,iBACNH,KAAAA,SAAQG,KAAAA,UAAAA,GAAAA,IAAAA,OAAAA,OAAAA,CAAAA,GAK5BuO,MAAAA,KAAAA,CAAAA,GACFJ,EAAAA,IAAQF,KAAUjO,KAAAA,OAAQ,IAAAsM,GAC1B4B,EAAAA,IAAQH,KAAUlO,KAAAA,OAAQ,IAAAyM,GAC3B5I,YAAW,IAAA4I,GAAA;MAAA;AAAA,UAAA,KAuDC,EACdlH,MAAM,iBACNgH,SAAAA,MACAL,OAAO,eACPb,IAvDF,SAAAmB,IAAA;AAAA,YAAyBvD,KAAAA,GAAAA,OAAOC,KAAAA,GAAAA,SAAAA,KAM1BA,GAJF4E,iBAAAA,KAAAA,WAAAA,MAAAA,IAAAA,KAIE5E,GAHF6E,UAAAA,KAAAA,WAAAA,MAAAA,IAAAA,KAGE7E,GADF8E,cAAAA,KAAAA,WAAAA,MAAAA,IAGIU,KAAe,EACnB/J,WAAWkD,EAAiBoB,GAAMtE,SAAAA,GAClCyD,WAAWL,EAAakB,GAAMtE,SAAAA,GAC9BJ,QAAQ0E,GAAMY,SAAStF,QACvBoF,YAAYV,GAAMW,MAAMrF,QACxBuJ,iBAAAA,IACAjM,SAAoC,YAA3BoH,GAAMC,QAAQvC,SAAAA;AAGgB,gBAArCsC,GAAMqB,cAAcL,kBACtBhB,GAAMwC,OAAOlH,SAAAA,OAAAA,OAAAA,CAAAA,GACR0E,GAAMwC,OAAOlH,QACbsJ,GAAAA,OAAAA,OAAAA,CAAAA,GACEa,IAAAA,EACHxM,SAAS+G,GAAMqB,cAAcL,eAC7BzG,UAAUyF,GAAMC,QAAQvC,UACxBoH,UAAAA,IACAC,cAAAA,GAAAA,CAAAA,CAAAA,CAAAA,IAK2B,QAA7B/E,GAAMqB,cAAcuE,UACtB5F,GAAMwC,OAAOoD,QAAAA,OAAAA,OAAAA,CAAAA,GACR5F,GAAMwC,OAAOoD,OACbhB,GAAAA,OAAAA,OAAAA,CAAAA,GACEa,IAAAA,EACHxM,SAAS+G,GAAMqB,cAAcuE,OAC7BrL,UAAU,YACVuK,UAAAA,OACAC,cAAAA,GAAAA,CAAAA,CAAAA,CAAAA,IAKN/E,GAAMuC,WAAWjH,SAAAA,OAAAA,OAAAA,CAAAA,GACZ0E,GAAMuC,WAAWjH,QAAAA,EAAAA,yBACK0E,GAAMtE,UAAAA,CAAAA;MAAAA,GAWjC0H,MAAM,CAAA,EAAA;AAAA,UAAA,KC7IQ,EACd9G,MAAM,eACNgH,SAAAA,MACAL,OAAO,SACPb,IAtFF,SAAAmB,IAAA;AAAA,YAAuBvD,KAAAA,GAAAA;AACrBuB,eAAO3B,KAAKI,GAAMY,QAAAA,EAAUnE,QAAQ,SAACH,IAAAA;AAAAA,cAC7BuJ,KAAQ7F,GAAMwC,OAAOlG,EAAAA,KAAS,CAAA,GAE9BiG,KAAavC,GAAMuC,WAAWjG,EAAAA,KAAS,CAAA,GACvCnG,KAAU6J,GAAMY,SAAStE,EAAAA;AAG1B3H,YAAcwB,EAAAA,KAAa0B,EAAY1B,EAAAA,MAO5CoL,OAAOuE,OAAO3P,GAAQ0P,OAAOA,EAAAA,GAE7BtE,OAAO3B,KAAK2C,EAAAA,EAAY9F,QAAQ,SAACH,IAAAA;AAAAA,gBACzBqD,KAAQ4C,GAAWjG,EAAAA;AAAAA,sBACrBqD,KACFxJ,GAAQ4P,gBAAgBzJ,EAAAA,IAExBnG,GAAQ6P,aAAa1J,IAAAA,SAAMqD,KAAiB,KAAKA,EAAAA;UAAAA,CAAAA;QAAAA,CAAAA;MAAAA,GAiEvD8D,QA3DF,SAAAF,IAAA;AAAA,YAAkBvD,KAAAA,GAAAA,OACViG,KAAgB,EACpB3K,QAAQ,EACNf,UAAUyF,GAAMC,QAAQvC,UACxB1G,MAAM,KACNG,KAAK,KACL+O,QAAQ,IAAA,GAEVN,OAAO,EACLrL,UAAU,WAAA,GAEZ0E,WAAW,CAAA,EAAA;AAAA,eAGbsC,OAAOuE,OAAO9F,GAAMY,SAAStF,OAAOuK,OAAOI,GAAc3K,MAAAA,GACzD0E,GAAMwC,SAASyD,IAEXjG,GAAMY,SAASgF,SACjBrE,OAAOuE,OAAO9F,GAAMY,SAASgF,MAAMC,OAAOI,GAAcL,KAAAA,GAGnD,WAAA;AACLrE,iBAAO3B,KAAKI,GAAMY,QAAAA,EAAUnE,QAAQ,SAACH,IAAAA;AAAAA,gBAC7BnG,KAAU6J,GAAMY,SAAStE,EAAAA,GACzBiG,KAAavC,GAAMuC,WAAWjG,EAAAA,KAAS,CAAA,GASvCuJ,KAPkBtE,OAAO3B,KAC7BI,GAAMwC,OAAO0C,eAAe5I,EAAAA,IACxB0D,GAAMwC,OAAOlG,EAAAA,IACb2J,GAAc3J,EAAAA,CAAAA,EAIUd,OAAO,SAACqK,IAAOM,IAAAA;AAAAA,qBAC3CN,GAAMM,EAAAA,IAAY,IACXN;YAAAA,GACN,CAAA,CAAA;AAGElR,cAAcwB,EAAAA,KAAa0B,EAAY1B,EAAAA,MAI5CoL,OAAOuE,OAAO3P,GAAQ0P,OAAOA,EAAAA,GAE7BtE,OAAO3B,KAAK2C,EAAAA,EAAY9F,QAAQ,SAAC2J,IAAAA;AAC/BjQ,cAAAA,GAAQ4P,gBAAgBK,EAAAA;YAAAA,CAAAA;UAAAA,CAAAA;QAAAA;MAAAA,GAc9B7J,UAAU,CAAC,eAAA,EAAA;AAAA,UAAA,KChCG,EACdD,MAAM,UACNgH,SAAAA,MACAL,OAAO,QACP1G,UAAU,CAAC,eAAA,GACX6F,IAzBF,SAAAmB,IAAA;AAAA,YAAkBvD,KAAAA,GAAAA,OAAOC,KAAAA,GAAAA,SAAS3D,KAAAA,GAAAA,MAAAA,KACJ2D,GAApBqB,QAAAA,KAAAA,WAAAA,KAAS,CAAC,GAAG,CAAA,IAAA+E,IAEfjD,KAAOzH,EAAWH,OAAO,SAACC,IAAKC,IAAAA;AAAAA,iBACnCD,GAAIC,EAAAA,IA5BD,SACLA,IACAiF,IACAW,IAAAA;AAAAA,gBAEMpC,KAAgBN,EAAiBlD,EAAAA,GACjC4K,KAAiB,CAACtP,GAAMG,CAAAA,EAAKwC,QAAQuF,EAAAA,KAAkB,IAAA,KAAS,GAAAgD,KAGlD,cAAA,OAAXZ,KACHA,GAAAA,OAAAA,OAAAA,CAAAA,GACKX,IAAAA,EACHjF,WAAAA,GAAAA,CAAAA,CAAAA,IAEF4F,IANDiF,KAAAA,GAAAA,CAAAA,GAAUC,KAAAA,GAAAA,CAAAA;AAAAA,mBAQfD,KAAWA,MAAY,GACvBC,MAAYA,MAAY,KAAKF,IAEtB,CAACtP,GAAMK,CAAAA,EAAOsC,QAAQuF,EAAAA,KAAkB,IAC3C,EAAEnI,GAAGyP,IAAUtP,GAAGqP,GAAAA,IAClB,EAAExP,GAAGwP,IAAUrP,GAAGsP,GAAAA;UAAAA,EAOqB9K,IAAWsE,GAAMW,OAAOW,EAAAA,GAC1D7F;QAAAA,GACN,CAAA,CAAA,GAAAgL,KAEcrD,GAAKpD,GAAMtE,SAAAA,GAApB3E,KAAAA,GAAAA,GAAGG,KAAAA,GAAAA;AAE8B,gBAArC8I,GAAMqB,cAAcL,kBACtBhB,GAAMqB,cAAcL,cAAcjK,KAAKA,IACvCiJ,GAAMqB,cAAcL,cAAc9J,KAAKA,KAGzC8I,GAAMqB,cAAc/E,EAAAA,IAAQ8G;MAAAA,EAAAA,GCxDxBsD,KAAO,EAAE1P,MAAM,SAASK,OAAO,QAAQC,QAAQ,OAAOH,KAAK,SAAA;AAElD,eAASwP,GAAqBjL,IAAAA;AAAAA,eACnCA,GAAUkL,QAChB,0BACA,SAAAC,IAAAA;AAAAA,iBAAWH,GAAKG,EAAAA;QAAAA,CAAAA;MAAAA;ACLpB,UAAMH,KAAO,EAAEvL,OAAO,OAAOC,KAAK,QAAA;AAEnB,eAAS0L,GACtBpL,IAAAA;AAAAA,eAEQA,GAAUkL,QAAQ,cAAc,SAAAC,IAAAA;AAAAA,iBAAWH,GAAKG,EAAAA;QAAAA,CAAAA;MAAAA;ACoB3C,eAASE,GACtB/G,IACAC,IAAAA;AAAAA,mBAAAA,OAAAA,KAAmB,CAAA;AAAA,YAAAuD,KASfvD,IANFvE,KAAAA,GAAAA,WACA0C,KAAAA,GAAAA,UACAC,KAAAA,GAAAA,cACAkC,KAAAA,GAAAA,SACAyG,KAAAA,GAAAA,gBAAAA,KAAAA,GACAC,uBAAAA,KAAAA,WAAAA,KAAwBC,IAAAA,IAGpB/H,KAAYL,EAAapD,EAAAA,GAEzBC,KAAawD,KACf6H,KACEzL,IACAA,EAAoBP,OAClB,SAACU,IAAAA;AAAAA,iBAAcoD,EAAapD,EAAAA,MAAeyD;QAAAA,CAAAA,IAE/CjE,GAEAiM,KAAoBxL,GAAWX,OACjC,SAACU,IAAAA;AAAAA,iBAAcuL,GAAsBtN,QAAQ+B,EAAAA,KAAc;QAAA,CAAA;AAG5B,cAA7ByL,GAAkBnD,WACpBmD,KAAoBxL;AAAAA,YAIhByL,KAA0BD,GAAkB3L,OAAO,SAACC,IAAKC,IAAAA;AAAAA,iBAC7DD,GAAIC,EAAAA,IAAaqE,EAAeC,IAAO,EACrCtE,WAAAA,IACA0C,UAAAA,IACAC,cAAAA,IACAkC,SAAAA,GAAAA,CAAAA,EACC3B,EAAiBlD,EAAAA,CAAAA,GAEbD;QAAAA,GACN,CAAA,CAAA;AAAA,eAEI8F,OAAO3B,KAAKwH,EAAAA,EAAWjL,KAAK,SAAC8H,IAAGoD,IAAAA;AAAAA,iBAAMD,GAAUnD,EAAAA,IAAKmD,GAAUC,EAAAA;QAAAA,CAAAA;MAAAA;AAAAA,UAAAA,KCkGxD,EACd/K,MAAM,QACNgH,SAAAA,MACAL,OAAO,QACPb,IAvIF,SAAAmB,IAAA;AAAA,YAAgBvD,KAAAA,GAAAA,OAAOC,KAAAA,GAAAA,SAAS3D,KAAAA,GAAAA;AAAAA,YAAAA,CAC1B0D,GAAMqB,cAAc/E,EAAAA,EAAMgL,OAAAA;AAAAA,mBAAAA,KAc1BrH,GATFX,UAAUiI,KAAAA,WAAAA,MAAAA,IAAAA,KASRtH,GARFuH,SAASC,KAAAA,WAAAA,MAAAA,IACWC,KAOlBzH,GAPF0H,oBACApH,KAMEN,GANFM,SACAnC,KAKE6B,GALF7B,UACAC,KAIE4B,GAJF5B,cACAiC,KAGEL,GAHFK,aAAAA,KAGEL,GAFF+G,gBAAAA,KAAAA,WAAAA,MAAAA,IACAC,KACEhH,GADFgH,uBAGIW,KAAqB5H,GAAMC,QAAQvE,WACnCwD,KAAgBN,EAAiBgJ,EAAAA,GAGjCD,KACJD,OAHsBxI,OAAkB0I,MAAAA,CAInBZ,KACjB,CAACL,GAAqBiB,EAAAA,CAAAA,IAtC9B,SAAuClM,IAAAA;AAAAA,gBACjCkD,EAAiBlD,EAAAA,MAAeT,EAAAA,QAC3B,CAAA;AAAA,gBAGH4M,KAAoBlB,GAAqBjL,EAAAA;AAAAA,mBAExC,CACLoL,GAA8BpL,EAAAA,GAC9BmM,IACAf,GAA8Be,EAAAA,CAAAA;UAAAA,EA6BID,EAAAA,IAE9BjM,KAAa,CAACiM,EAAAA,EAAAA,OAAuBD,EAAAA,EAAoBnM,OAC7D,SAACC,IAAKC,IAAAA;AAAAA,mBACGD,GAAItB,OACTyE,EAAiBlD,EAAAA,MAAeT,IAC5B8L,GAAqB/G,IAAO,EAC1BtE,WAAAA,IACA0C,UAAAA,IACAC,cAAAA,IACAkC,SAAAA,IACAyG,gBAAAA,IACAC,uBAAAA,GAAAA,CAAAA,IAEFvL,EAAAA;UAAAA,GAGR,CAAA,CAAA,GAGIoM,KAAgB9H,GAAMW,MAAM1B,WAC5ByB,KAAaV,GAAMW,MAAMrF,QAEzByM,KAAY,oBAAIhM,OAClBiM,KAAAA,MACAC,KAAwBtM,GAAW,CAAA,GAE9BuG,KAAI,GAAGA,KAAIvG,GAAWqI,QAAQ9B,MAAK;AAAA,gBACpCxG,KAAYC,GAAWuG,EAAAA,GACvBhD,KAAgBN,EAAiBlD,EAAAA,GACjCwM,KAAmBpJ,EAAapD,EAAAA,MAAeP,GAC/CgN,KAAa,CAAChR,GAAKG,CAAAA,EAAQqC,QAAQuF,EAAAA,KAAkB,GACrDK,KAAM4I,KAAa,UAAU,UAE7B7P,KAAWyH,EAAeC,IAAO,EACrCtE,WAAAA,IACA0C,UAAAA,IACAC,cAAAA,IACAiC,aAAAA,IACAC,SAAAA,GAAAA,CAAAA,GAGE6H,KAAyBD,KACzBD,KACE7Q,IACAL,IACFkR,KACA5Q,IACAH;AAEA2Q,YAAAA,GAAcvI,EAAAA,IAAOmB,GAAWnB,EAAAA,MAClC6I,KAAoBzB,GAAqByB,EAAAA;AAAAA,gBAGrCC,KAAwB1B,GAAqByB,EAAAA,GAE7CE,KAAS,CAAA;AAAA,gBAEXf,MACFe,GAAOxL,KAAKxE,GAAS4G,EAAAA,KAAkB,CAAA,GAGrCuI,MACFa,GAAOxL,KACLxE,GAAS8P,EAAAA,KAAsB,GAC/B9P,GAAS+P,EAAAA,KAAqB,CAAA,GAI9BC,GAAOC,MAAM,SAACC,IAAAA;AAAAA,qBAAUA;YAAAA,CAAAA,GAAQ;AAClCP,cAAAA,KAAwBvM,IACxBsM,KAAAA;AAAqB;YAAA;AAIvBD,YAAAA,GAAUhL,IAAIrB,IAAW4M,EAAAA;UAAAA;AAAAA,cAGvBN,GAAAA,UAAAA,KAAAA,SAIO9F,IAAAA;AAAAA,gBACDuG,KAAmB9M,GAAW+M,KAAK,SAAChN,IAAAA;AAAAA,kBAClC4M,KAASP,GAAUlL,IAAInB,EAAAA;AAAAA,kBACzB4M,GAAAA,QACKA,GAAOK,MAAM,GAAGzG,EAAAA,EAAGqG,MAAM,SAACC,IAAAA;AAAAA,uBAAUA;cAAAA,CAAAA;YAAAA,CAAAA;AAAAA,gBAI3CC,GAAAA,QACFR,KAAwBQ,IAAAA;UAAAA,GATnBvG,KAFc8E,KAAiB,IAAI,GAEf9E,KAAI,GAAGA,MAAK;AAAA,gBAAA,YAAA0G,GAAhC1G,EAAAA,EAUL;UAAA;AAKFlC,UAAAA,GAAMtE,cAAcuM,OACtBjI,GAAMqB,cAAc/E,EAAAA,EAAMgL,QAAAA,MAC1BtH,GAAMtE,YAAYuM,IAClBjI,GAAM8D,QAAAA;QAAQ;MAAA,GAWhBtH,kBAAkB,CAAC,QAAA,GACnB4G,MAAM,EAAEkE,OAAAA,MAAO,EAAA;AC5KV,eAASuB,GAAO5T,IAAa0K,IAAe5K,IAAAA;AAAAA,eAC1C+T,EAAQ7T,IAAK8T,EAAQpJ,IAAO5K,EAAAA,CAAAA;MAAAA;AAAAA,UAAAA,KCiNrB,EACduH,MAAM,mBACNgH,SAAAA,MACAL,OAAO,QACPb,IA1KF,SAAAmB,IAAA;AAAA,YAA2BvD,KAAAA,GAAAA,OAAOC,KAAAA,GAAAA,SAAS3D,KAAAA,GAAAA,MAAAA,KAUrC2D,GARFX,UAAUiI,KAAAA,WAAAA,MAAAA,IAAAA,KAQRtH,GAPFuH,SAASC,KAAAA,WAAAA,MAAAA,IACTrJ,KAME6B,GANF7B,UACAC,KAKE4B,GALF5B,cACAiC,KAIEL,GAJFK,aACAC,KAGEN,GAHFM,SAAAA,KAGEN,GAFF+I,QAAAA,KAAAA,WAAAA,MAAAA,IAAAA,KAEE/I,GADFgJ,cAAAA,KAAAA,WAAAA,KAAe,IAAAjE,IAGX1M,KAAWyH,EAAeC,IAAO,EACrC5B,UAAAA,IACAC,cAAAA,IACAkC,SAAAA,IACAD,aAAAA,GAAAA,CAAAA,GAEIpB,KAAgBN,EAAiBoB,GAAMtE,SAAAA,GACvCyD,KAAYL,EAAakB,GAAMtE,SAAAA,GAC/BwN,KAAAA,CAAmB/J,IACnBG,KAAWP,EAAyBG,EAAAA,GACpCsI,KClEU,QDkEWlI,KClEL,MAAM,KDmEtB0B,KAAgBhB,GAAMqB,cAAcL,eACpC8G,KAAgB9H,GAAMW,MAAM1B,WAC5ByB,KAAaV,GAAMW,MAAMrF,QACzB6N,KACoB,cAAA,OAAjBF,KACHA,GAAAA,OAAAA,OAAAA,CAAAA,GACKjJ,GAAMW,OAAAA,EACTjF,WAAWsE,GAAMtE,UAAAA,CAAAA,CAAAA,IAEnBuN,IACAG,KACyB,YAAA,OAAtBD,KACH,EAAE7J,UAAU6J,IAAmB3B,SAAS2B,GAAAA,IAAAA,OAAAA,OAAAA,EACtC7J,UAAU,GAAGkI,SAAS,EAAA,GAAM2B,EAAAA,GAC9BE,KAAsBrJ,GAAMqB,cAAcC,SAC5CtB,GAAMqB,cAAcC,OAAOtB,GAAMtE,SAAAA,IACjC,MAEE0H,KAAO,EAAErM,GAAG,GAAGG,GAAG,EAAA;AAAA,YAEnB8J,IAAAA;AAAAA,cAIDuG,IAAe;AAAA,gBAAA+B,IACXC,KAAwB,QAAbjK,KAAmBnI,IAAMH,GACpCwS,KAAuB,QAAblK,KAAmBhI,IAASD,GACtCkI,KAAmB,QAAbD,KAAmB,WAAW,SACpCgC,KAASN,GAAc1B,EAAAA,GAEvBrK,KAAMqM,KAAShJ,GAASiR,EAAAA,GACxBxU,KAAMuM,KAAShJ,GAASkR,EAAAA,GAExBC,KAAWT,KAAAA,CAAUtI,GAAWnB,EAAAA,IAAO,IAAI,GAE3CmK,KAASvK,OAAchE,IAAQ2M,GAAcvI,EAAAA,IAAOmB,GAAWnB,EAAAA,GAC/DoK,KAASxK,OAAchE,IAAAA,CAASuF,GAAWnB,EAAAA,IAAAA,CAAQuI,GAAcvI,EAAAA,GAIjEqK,KAAe5J,GAAMY,SAASgF,OAC9BiE,KACJb,MAAUY,KACNxQ,EAAcwQ,EAAAA,IACd,EAAElT,OAAO,GAAGE,QAAQ,EAAA,GACpBkT,MAAqB9J,GAAMqB,cAAc,kBAAA,IAC3CrB,GAAMqB,cAAc,kBAAA,EAAoBd,UhBhHvC,EACLpJ,KAAK,GACLE,OAAO,GACPC,QAAQ,GACRN,MAAM,EAAA,GgB8GA+S,MAAkBD,IAAmBP,EAAAA,GACrCS,MAAkBF,IAAmBN,EAAAA,GAOrCS,MAAWpB,GAAO,GAAGf,GAAcvI,EAAAA,GAAMsK,GAAUtK,EAAAA,CAAAA,GAEnD2K,MAAYhB,KACdpB,GAAcvI,EAAAA,IAAO,IACrBkK,KACAQ,MACAF,MACAX,GAA4B9J,WAC5BoK,KACAO,MACAF,MACAX,GAA4B9J,UAC1B6K,MAAYjB,KAAAA,CACbpB,GAAcvI,EAAAA,IAAO,IACtBkK,KACAQ,MACAD,MACAZ,GAA4B9J,WAC5BqK,KACAM,MACAD,MACAZ,GAA4B9J,UAE1B8K,MACJpK,GAAMY,SAASgF,SAASpL,EAAgBwF,GAAMY,SAASgF,KAAAA,GACnDyE,MAAeD,MACJ,QAAb9K,KACE8K,IAAkBjR,aAAa,IAC/BiR,IAAkBlR,cAAc,IAClC,GAEEoR,MAAAA,SAAAA,KAAAA,QAAsBjB,KAAAA,SAAAA,GAAsB/J,EAAAA,KAAAA,KAAa,GAEzDiL,MAAYjJ,KAAS6I,MAAYG,KAEjCE,MAAkB3B,GACtBG,KAASD,EAAQ9T,IAJDqM,KAAS4I,MAAYI,MAAsBD,GAAAA,IAIxBpV,IACnCqM,IACA0H,KAASF,EAAQ/T,IAAKwV,GAAAA,IAAaxV,EAAAA;AAGrCiM,YAAAA,GAAc1B,EAAAA,IAAYkL,KAC1BpH,GAAK9D,EAAAA,IAAYkL,MAAkBlJ;UAAAA;AAAAA,cAGjCmG,IAAc;AAAA,gBAAAgD,KACVlB,MAAwB,QAAbjK,KAAmBnI,IAAMH,GACpCwS,MAAuB,QAAblK,KAAmBhI,IAASD,GACtCiK,MAASN,GAAcwG,EAAAA,GAEvBjI,MAAkB,QAAZiI,KAAkB,WAAW,SAEnCvS,MAAMqM,MAAShJ,GAASiR,GAAAA,GACxBxU,MAAMuM,MAAShJ,GAASkR,GAAAA,GAExBkB,MAAAA,OAAe,CAACvT,GAAKH,CAAAA,EAAM2C,QAAQuF,EAAAA,GAEnCoL,MAAAA,SAAAA,MAAAA,QAAsBjB,KAAAA,SAAAA,GAAsB7B,EAAAA,KAAAA,MAAY,GACxDmD,MAAYD,MACdzV,MACAqM,MACAwG,GAAcvI,GAAAA,IACdmB,GAAWnB,GAAAA,IACX+K,MACAlB,GAA4B5B,SAC1B+C,KAAYG,MACdpJ,MACAwG,GAAcvI,GAAAA,IACdmB,GAAWnB,GAAAA,IACX+K,MACAlB,GAA4B5B,UAC5BzS,KAEEyV,KACJxB,MAAU0B,MDjMT,SAAwBzV,IAAa0K,IAAe5K,IAAAA;AAAAA,kBACnDiQ,KAAI6D,GAAO5T,IAAK0K,IAAO5K,EAAAA;AAAAA,qBACtBiQ,KAAIjQ,KAAMA,KAAMiQ;YAAAA,ECgMA2F,KAAWrJ,KAAQiJ,EAAAA,IAClC1B,GAAOG,KAAS2B,MAAY1V,KAAKqM,KAAQ0H,KAASuB,KAAYxV,GAAAA;AAEpEiM,YAAAA,GAAcwG,EAAAA,IAAWgD,IACzBpH,GAAKoE,EAAAA,IAAWgD,KAAkBlJ;UAAAA;AAGpCtB,UAAAA,GAAMqB,cAAc/E,EAAAA,IAAQ8G;QAAAA;MAAAA,GAU5B5G,kBAAkB,CAAC,QAAA,EAAA;AAAA,UAAA,KE3GL,EACdF,MAAM,SACNgH,SAAAA,MACAL,OAAO,QACPb,IA7EF,SAAAmB,IAAA;AAAA,YAAAvB,IAAiBhC,KAAAA,GAAAA,OAAO1D,KAAAA,GAAAA,MAAM2D,KAAAA,GAAAA,SACtB2J,KAAe5J,GAAMY,SAASgF,OAC9B5E,KAAgBhB,GAAMqB,cAAcL,eACpC9B,KAAgBN,EAAiBoB,GAAMtE,SAAAA,GACvC+F,KAAO1C,EAAyBG,EAAAA,GAEhCK,KADa,CAACvI,GAAMK,CAAAA,EAAOsC,QAAQuF,EAAAA,KAAkB,IAClC,WAAW;AAAA,YAE/B0K,MAAiB5I,IAAAA;AAAAA,cAIhBvB,KAzBgB,SAACc,IAASP,IAAAA;AAAAA,mBAMzBR,EACc,YAAA,QANrBe,KACqB,cAAA,OAAZA,KACHA,GAAAA,OAAAA,OAAAA,CAAAA,GAAaP,GAAMW,OAAAA,EAAOjF,WAAWsE,GAAMtE,UAAAA,CAAAA,CAAAA,IAC3C6E,MAIAA,KACAb,EAAgBa,IAASrF,CAAAA,CAAAA;UAAAA,EAgBO+E,GAAQM,SAASP,EAAAA,GACjD6J,KAAYzQ,EAAcwQ,EAAAA,GAC1BgB,KAAmB,QAATnJ,KAAetK,IAAMH,GAC/B6T,KAAmB,QAATpJ,KAAenK,IAASD,GAElCyT,KACJ9K,GAAMW,MAAM1B,UAAUM,EAAAA,IACtBS,GAAMW,MAAM1B,UAAUwC,EAAAA,IACtBT,GAAcS,EAAAA,IACdzB,GAAMW,MAAMrF,OAAOiE,EAAAA,GACfwL,KAAY/J,GAAcS,EAAAA,IAAQzB,GAAMW,MAAM1B,UAAUwC,EAAAA,GAExD2I,KAAoB5P,EAAgBoP,EAAAA,GACpCoB,KAAaZ,KACN,QAAT3I,KACE2I,GAAkBvM,gBAAgB,IAClCuM,GAAkBxM,eAAe,IACnC,GAEEqN,KAAoBH,KAAU,IAAIC,KAAY,GAI9C9V,KAAMwK,GAAcmL,EAAAA,GACpB7V,KAAMiW,KAAanB,GAAUtK,EAAAA,IAAOE,GAAcoL,EAAAA,GAClDK,KAASF,KAAa,IAAInB,GAAUtK,EAAAA,IAAO,IAAI0L,IAC/C3J,KAASuH,GAAO5T,IAAKiW,IAAQnW,EAAAA,GAG7BoW,KAAmB1J;AACzBzB,UAAAA,GAAMqB,cAAc/E,EAAAA,MAAAA,KAAAA,CAAAA,GACjB6O,EAAAA,IAAW7J,IAAAA,GACZ8J,eAAc9J,KAAS4J,IAAAA;QAAAA;MAAAA,GAkCzBzH,QA9BF,SAAAF,IAAA;AAAA,YAAkBvD,KAAAA,GAAAA,OAAAA,KAAAA,GAAOC,QACjB9J,SAASyT,KAAAA,WAAAA,KAAe,wBAAApG;AAEV,gBAAhBoG,OAKwB,YAAA,OAAjBA,OACTA,KAAe5J,GAAMY,SAAStF,OAAO+P,cAAczB,EAAAA,OAOhD5M,EAASgD,GAAMY,SAAStF,QAAQsO,EAAAA,MAIrC5J,GAAMY,SAASgF,QAAQgE;MAAAA,GAWvBrN,UAAU,CAAC,eAAA,GACXC,kBAAkB,CAAC,iBAAA,EAAA;AC3GrB,eAAS8O,GACPhT,IACAS,IACAwS,IAAAA;AAAAA,eAAAA,WAAAA,OAAAA,KAA4B,EAAExU,GAAG,GAAGG,GAAG,EAAA,IAEhC,EACLC,KAAKmB,GAASnB,MAAM4B,GAAKnC,SAAS2U,GAAiBrU,GACnDG,OAAOiB,GAASjB,QAAQ0B,GAAKrC,QAAQ6U,GAAiBxU,GACtDO,QAAQgB,GAAShB,SAASyB,GAAKnC,SAAS2U,GAAiBrU,GACzDF,MAAMsB,GAAStB,OAAO+B,GAAKrC,QAAQ6U,GAAiBxU,EAAAA;MAAAA;AAIxD,eAASyU,GAAsBlT,IAAAA;AAAAA,eACtB,CAACnB,GAAKE,GAAOC,GAAQN,CAAAA,EAAM6K,KAAK,SAAC4J,IAAAA;AAAAA,iBAASnT,GAASmT,EAAAA,KAAS;QAAA,CAAA;MAAA;AAAA,UAAA,KA4CrD,EACdnP,MAAM,QACNgH,SAAAA,MACAL,OAAO,QACPzG,kBAAkB,CAAC,iBAAA,GACnB4F,IA9CF,SAAAmB,IAAA;AAAA,YAAgBvD,KAAAA,GAAAA,OAAO1D,KAAAA,GAAAA,MACfwL,KAAgB9H,GAAMW,MAAM1B,WAC5ByB,KAAaV,GAAMW,MAAMrF,QACzBiQ,KAAmBvL,GAAMqB,cAAcqK,iBAEvCC,KAAoB5L,EAAeC,IAAO,EAC9CK,gBAAgB,YAAA,CAAA,GAEZuL,KAAoB7L,EAAeC,IAAO,EAC9CM,aAAAA,KAAa,CAAA,GAGTuL,KAA2BP,GAC/BK,IACA7D,EAAAA,GAEIgE,KAAsBR,GAC1BM,IACAlL,IACA6K,EAAAA,GAGIQ,KAAoBP,GAAsBK,EAAAA,GAC1CG,KAAmBR,GAAsBM,EAAAA;AAE/C9L,QAAAA,GAAMqB,cAAc/E,EAAAA,IAAQ,EAC1BuP,0BAAAA,IACAC,qBAAAA,IACAC,mBAAAA,IACAC,kBAAAA,GAAAA,GAGFhM,GAAMuC,WAAWjH,SAAAA,OAAAA,OAAAA,CAAAA,GACZ0E,GAAMuC,WAAWjH,QAAAA,EAAAA,gCACYyQ,IAAAA,uBACTC,GAAAA,CAAAA;MAAAA,EAAAA,GC9CrBC,KAAenK,EAAgB,EAAEG,kBAPd,CACvBiK,IACAlL,IACAmL,IACAC,EAAAA,EAAAA,CAAAA,GCCInK,KAAmB,CACvBiK,IACAlL,IACAmL,IACAC,IACA9K,IACA+K,IACAX,IACA9F,IACA0G,EAAAA,GAGIL,KAAenK,EAAgB,EAAEG,kBAAAA,GAAAA,CAAAA;AAAAA,QAAAA,cAAAA,IAAAA,EAAAA,QAAAA,IAAAA,EAAAA,gBAAAA,IAAAA,EAAAA,eAAAA,IAAAA,EAAAA,mBAAAA,IAAAA,EAAAA,mBAAAA,IAAAA,EAAAA,iBAAAA,GAAAA,EAAAA,iBAAAA,IAAAA,EAAAA,OAAAA,IAAAA,EAAAA,OAAAA,IAAAA,EAAAA,SAAAA,IAAAA,EAAAA,kBAAAA,GAAAA,EAAAA,gBAAAA,IAAAA,EAAAA,kBAAAA,IAAAA,OAAAA,eAAAA,GAAAA,cAAAA,EAAAA,OAAAA,KAAAA,CAAAA;IAAAA,CAAAA;;;", "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom", "getWindowScroll", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getNodeName", "nodeName", "toLowerCase", "getDocumentElement", "document", "documentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "overflow", "overflowX", "overflowY", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "rect", "scroll", "offsets", "clientLeft", "clientTop", "getLayoutRect", "abs", "getParentNode", "assignedSlot", "parentNode", "host", "getScrollParent", "indexOf", "body", "listScrollParents", "list", "scrollParent", "isBody", "_element$ownerDocumen", "target", "concat", "updatedList", "isTableElement", "getTrueOffsetParent", "position", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "auto", "basePlacements", "start", "end", "viewport", "popper", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "order", "modifiers", "Map", "visited", "Set", "result", "sort", "modifier", "add", "name", "requires", "requiresIfExists", "for<PERSON>ach", "dep", "has", "depModifier", "get", "push", "set", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "strategy", "html", "clientWidth", "clientHeight", "layoutViewport", "winScroll", "scrollWidth", "scrollHeight", "direction", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "clippingParents", "clipperElement", "firstClippingParent", "clippingRect", "accRect", "getBasePlacement", "split", "getVariation", "getMainAxisFromPlacement", "computeOffsets", "reference", "basePlacement", "variation", "commonX", "commonY", "mainAxis", "len", "mergePaddingObject", "paddingObject", "expandToHashMap", "value", "keys", "hashMap", "key", "detectOverflow", "state", "options", "r", "f", "u", "elementContext", "altBoundary", "padding", "g", "altContext", "popperRect", "rects", "elements", "clippingClientRect", "contextElement", "referenceClientRect", "popperOffsets", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "modifiersData", "offset", "Object", "multiply", "axis", "DEFAULT_OPTIONS", "areValidElements", "args", "some", "popperGenerator", "generatorOptions", "t", "defaultModifiers", "i", "defaultOptions", "fn", "pending", "orderedModifiers", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "merged", "phase", "current", "existing", "data", "m", "enabled", "e", "n", "effect", "cleanupFn", "noopFn", "update", "forceUpdate", "reset", "index", "length", "a", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "passive", "resize", "addEventListener", "removeEventListener", "unsetSides", "mapToStyles", "gpuAcceleration", "adaptive", "roundOffsets", "v", "hasX", "hasOwnProperty", "hasY", "sideX", "sideY", "heightProp", "widthProp", "R", "commonStyles", "dpr", "devicePixelRatio", "arrow", "style", "assign", "removeAttribute", "setAttribute", "initialStyles", "margin", "property", "attribute", "o", "invertDistance", "skidding", "distance", "s", "hash", "getOppositePlacement", "replace", "matched", "getOppositeVariationPlacement", "computeAutoPlacement", "flipVariations", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "b", "_skip", "checkMainAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "preferredPlacement", "oppositePlacement", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "fittingPlacement", "find", "slice", "I", "within", "mathMax", "mathMin", "tether", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "q", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowElement", "arrowRect", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "arrowOffsetParent", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "ue", "isOriginSide", "tetherMin", "minProp", "maxProp", "endDiff", "startDiff", "clientSize", "centerToReference", "center", "axisProp", "centerOffset", "querySelector", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "side", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "createPopper", "eventListeners", "computeStyles", "applyStyles", "flip", "hide"]}