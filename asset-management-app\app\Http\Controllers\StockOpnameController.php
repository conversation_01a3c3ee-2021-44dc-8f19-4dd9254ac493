<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StockOpname;
use App\Models\StockOpnameDetail;
use App\Models\Asset;
use App\Models\Branch;
use App\Models\AssetCategory;
use App\Helpers\BranchHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class StockOpnameController extends Controller
{
    /**
     * Display a listing of stock opnames
     */
    public function index(Request $request)
    {
        $query = StockOpname::with(['branch', 'creator', 'assetCategory']);

        // Apply branch access control
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        // Apply filters
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('asset_category_id')) {
            $query->where('asset_category_id', $request->asset_category_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('opname_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $stockOpnames = $query->latest()->paginate(15)->withQueryString();

        // Get filter options
        $branches = BranchHelper::getAccessibleBranches();
        $assetCategories = AssetCategory::orderBy('name')->get();
        $activeOpname = StockOpname::getActiveOpname();

        return view('stock-opnames.index', compact('stockOpnames', 'branches', 'assetCategories', 'activeOpname'));
    }

    /**
     * Show the form for creating a new stock opname
     */
    public function create()
    {
        $branches = BranchHelper::getAccessibleBranches();
        $assetCategories = AssetCategory::orderBy('name')->get();

        // Get active stock opnames for information
        $activeOpnames = StockOpname::getAllActiveOpnames();

        return view('stock-opnames.create', compact('branches', 'assetCategories', 'activeOpnames'));
    }

    /**
     * Store a newly created stock opname
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'branch_id' => 'required|exists:branches,id',
            'asset_category_id' => 'nullable|exists:asset_categories,id',
            'notes' => 'nullable|string'
        ]);

        // Check if there's already an active stock opname with same branch and category
        $existingOpname = StockOpname::where('branch_id', $validated['branch_id'])
            ->where('asset_category_id', $validated['asset_category_id'])
            ->whereIn('status', ['draft', 'in_progress'])
            ->first();

        if ($existingOpname) {
            $branch = Branch::find($validated['branch_id']);
            $category = $validated['asset_category_id'] ? AssetCategory::find($validated['asset_category_id']) : null;
            $categoryText = $category ? "kategori {$category->name}" : "semua kategori";

            return redirect()->back()
                ->with('error', "Tidak dapat membuat stock opname baru. Sudah ada stock opname aktif di cabang {$branch->name} untuk {$categoryText}: {$existingOpname->title}")
                ->withInput();
        }

        // Check if there are assets available for the selected branch and category
        $assetQuery = Asset::where('branch_id', $validated['branch_id']);
        if ($validated['asset_category_id']) {
            $assetQuery->where('asset_category_id', $validated['asset_category_id']);
        }

        $assetCount = $assetQuery->count();

        if ($assetCount === 0) {
            $branch = Branch::find($validated['branch_id']);
            $category = $validated['asset_category_id'] ? AssetCategory::find($validated['asset_category_id']) : null;
            $categoryText = $category ? "kategori {$category->name}" : "semua kategori";

            return redirect()->back()
                ->with('error', "Tidak dapat membuat stock opname. Tidak ada asset yang ditemukan di cabang {$branch->name} untuk {$categoryText}. Silakan pilih cabang dan kategori yang memiliki asset.")
                ->withInput();
        }

        $branch = Branch::find($validated['branch_id']);
        $opnameNumber = StockOpname::generateOpnameNumber($branch->code);

        $stockOpname = StockOpname::create([
            'opname_number' => $opnameNumber,
            'title' => $validated['title'],
            'description' => $validated['description'],
            'branch_id' => $validated['branch_id'],
            'asset_category_id' => $validated['asset_category_id'],
            'created_by' => auth()->id(),
            'notes' => $validated['notes']
        ]);

        return redirect()->route('stock-opnames.show', $stockOpname)
            ->with('success', 'Stock opname berhasil dibuat.');
    }

    /**
     * Display the specified stock opname
     */
    public function show(StockOpname $stockOpname)
    {
        $stockOpname->load(['branch', 'creator', 'assetCategory', 'details.asset', 'details.scanner']);

        $statistics = [
            'total' => $stockOpname->total_assets,
            'scanned' => $stockOpname->scanned_assets,
            'found' => $stockOpname->found_assets,
            'missing' => $stockOpname->missing_assets,
            'discrepancy' => $stockOpname->details()->where('has_discrepancy', true)->count(),
            'progress' => $stockOpname->total_assets > 0 ? round(($stockOpname->scanned_assets / $stockOpname->total_assets) * 100, 2) : 0
        ];

        return view('stock-opnames.show', compact('stockOpname', 'statistics'));
    }

    /**
     * Start stock opname
     */
    public function start(StockOpname $stockOpname)
    {
        if ($stockOpname->status !== 'draft') {
            return redirect()->back()->with('error', 'Stock opname sudah dimulai atau selesai.');
        }

        // Check if there's already an active stock opname in the same branch
        $activeBranchOpname = StockOpname::getActiveBranchOpname($stockOpname->branch_id);
        if ($activeBranchOpname && $activeBranchOpname->id !== $stockOpname->id) {
            return redirect()->back()->with('error',
                "Tidak dapat memulai stock opname. Masih ada stock opname lain yang sedang berjalan di cabang {$stockOpname->branch->name}: {$activeBranchOpname->title}");
        }

        $stockOpname->start();

        return redirect()->route('stock-opnames.show', $stockOpname)
            ->with('success', 'Stock opname berhasil dimulai. Asset operations telah dikunci.');
    }

    /**
     * Complete stock opname
     */
    public function complete(StockOpname $stockOpname)
    {
        if ($stockOpname->status !== 'in_progress') {
            return redirect()->back()->with('error', 'Stock opname tidak sedang berjalan.');
        }

        $stockOpname->complete();

        return redirect()->route('stock-opnames.show', $stockOpname)
            ->with('success', 'Stock opname berhasil diselesaikan. Asset operations telah dibuka kembali.');
    }

    /**
     * Cancel stock opname
     */
    public function cancel(StockOpname $stockOpname)
    {
        if (!in_array($stockOpname->status, ['draft', 'in_progress'])) {
            return redirect()->back()->with('error', 'Stock opname tidak dapat dibatalkan.');
        }

        $stockOpname->update([
            'status' => 'cancelled',
            'is_locked' => false,
            'end_date' => now()
        ]);

        return redirect()->route('stock-opnames.index')
            ->with('success', 'Stock opname berhasil dibatalkan.');
    }

    /**
     * Show scan selection interface
     */
    public function scanIndex()
    {
        // Get active stock opnames accessible to user
        $query = StockOpname::where('status', 'in_progress')
                           ->where('is_locked', true)
                           ->with(['branch', 'creator']);

        // Apply branch filter for non-super admin users
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty()) {
                $query->whereIn('branch_id', $accessibleBranchIds);
            }
        }

        $activeOpnames = $query->latest()->get();
        $branches = BranchHelper::getAccessibleBranches();

        return view('stock-opnames.scan-index', compact('activeOpnames', 'branches'));
    }

    /**
     * Show scan interface
     */
    public function scan(StockOpname $stockOpname)
    {
        if ($stockOpname->status !== 'in_progress') {
            return redirect()->route('stock-opnames.scan')
                ->with('error', 'Stock opname tidak sedang berjalan.');
        }

        // Check if user has access to this branch
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if ($accessibleBranchIds->isNotEmpty() && !$accessibleBranchIds->contains($stockOpname->branch_id)) {
                return redirect()->route('stock-opnames.scan')
                    ->with('error', 'Anda tidak memiliki akses ke stock opname ini.');
            }
        }

        return view('stock-opnames.scan', compact('stockOpname'));
    }

    /**
     * Process scanned asset
     */
    public function processScan(Request $request, StockOpname $stockOpname)
    {
        try {
            $validated = $request->validate([
                'asset_id' => 'required|exists:assets,id',
                'found_status' => 'required|in:found,not_found,damaged',
                'physical_condition' => 'required|in:excellent,good,fair,poor,damaged',
                'condition_notes' => 'nullable|string|max:1000',
                'location_found' => 'nullable|string|max:255',
                'dynamic_fields' => 'nullable|array'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid: ' . implode(', ', array_flatten($e->errors())),
                'errors' => $e->errors()
            ], 422);
        }

        $asset = Asset::find($validated['asset_id']);

        // Check if asset belongs to the same branch
        if ($asset->branch_id !== $stockOpname->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'Asset tidak termasuk dalam stock opname ini.'
            ], 400);
        }

        // Find or create stock opname detail
        $detail = StockOpnameDetail::where('stock_opname_id', $stockOpname->id)
            ->where('asset_id', $asset->id)
            ->first();

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Asset tidak ditemukan dalam daftar stock opname.'
            ], 400);
        }

        // Check if already scanned
        if ($detail->isScanned()) {
            return response()->json([
                'success' => false,
                'message' => 'Asset sudah pernah di-scan sebelumnya.',
                'detail' => $detail
            ], 400);
        }

        // Mark as scanned with safe dynamic fields handling
        try {
            $detail->markAsScanned(
                $validated['found_status'],
                $validated['physical_condition'],
                $validated['condition_notes'] ?? null,
                $validated['location_found'] ?? null,
                $validated['dynamic_fields'] ?? []
            );

            return response()->json([
                'success' => true,
                'message' => 'Asset berhasil di-scan dan dicatat.',
                'detail' => $detail->load('asset'),
                'statistics' => [
                    'scanned' => $stockOpname->fresh()->scanned_assets,
                    'total' => $stockOpname->total_assets,
                    'progress' => $stockOpname->total_assets > 0 ? round(($stockOpname->fresh()->scanned_assets / $stockOpname->total_assets) * 100, 2) : 0
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error saving stock opname scan: ' . $e->getMessage(), [
                'stock_opname_id' => $stockOpname->id,
                'asset_id' => $validated['asset_id'],
                'error' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan data scan. Silakan coba lagi.'
            ], 500);
        }
    }

    /**
     * Get asset info for scanning
     */
    public function getAssetInfo(Request $request, StockOpname $stockOpname)
    {
        $assetId = $request->get('asset_id');
        $assetCode = $request->get('asset_code');

        // Find asset by ID or code
        if ($assetId) {
            $asset = Asset::with(['assetCategory', 'assetType', 'branch'])->find($assetId);
        } elseif ($assetCode) {
            $asset = Asset::with(['assetCategory', 'assetType', 'branch'])
                          ->where('asset_code', $assetCode)
                          ->first();
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Asset ID atau kode asset harus disediakan.'
            ], 400);
        }

        if (!$asset) {
            return response()->json([
                'success' => false,
                'message' => 'Asset tidak ditemukan.'
            ], 404);
        }

        // Check if asset belongs to the same branch
        if ($asset->branch_id !== $stockOpname->branch_id) {
            return response()->json([
                'success' => false,
                'message' => 'Asset tidak termasuk dalam stock opname ini.'
            ], 400);
        }

        // Get stock opname detail
        $detail = StockOpnameDetail::where('stock_opname_id', $stockOpname->id)
            ->where('asset_id', $asset->id)
            ->first();

        if (!$detail) {
            return response()->json([
                'success' => false,
                'message' => 'Asset tidak ditemukan dalam daftar stock opname.'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'asset' => $asset,
            'detail' => $detail,
            'already_scanned' => $detail->isScanned(),
            'scanned_at' => $detail->scanned_at ? $detail->scanned_at->format('d/m/Y H:i') : null
        ]);
    }

    /**
     * Show stock opname report
     */
    public function report(StockOpname $stockOpname)
    {
        $stockOpname->load(['branch', 'creator']);

        $details = StockOpnameDetail::where('stock_opname_id', $stockOpname->id)
            ->with(['asset', 'scanner'])
            ->get();

        $groupedDetails = [
            'found' => $details->where('found_status', 'found'),
            'missing' => $details->where('found_status', 'not_found'),
            'damaged' => $details->where('found_status', 'damaged'),
            'discrepancy' => $details->where('has_discrepancy', true),
            'not_scanned' => $details->whereNull('scanned_at')
        ];

        return view('stock-opnames.report', compact('stockOpname', 'groupedDetails'));
    }

    /**
     * Generate hardcopy form for stock opname
     */
    public function generateHardcopyForm(StockOpname $stockOpname)
    {
        // Check if stock opname is in progress
        if ($stockOpname->status !== 'in_progress') {
            return redirect()->back()
                ->with('error', 'Form hardcopy hanya tersedia untuk stock opname yang sedang berjalan (in progress).');
        }

        // Check if user has access to this stock opname
        if (!auth()->user()->isSuperAdmin()) {
            $accessibleBranchIds = BranchHelper::getAccessibleBranches()->pluck('id');
            if (!$accessibleBranchIds->contains($stockOpname->branch_id)) {
                abort(403, 'Anda tidak memiliki akses ke stock opname ini.');
            }
        }

        // Get assets from stock opname details (assets that are actually being audited)
        $stockOpnameDetails = $stockOpname->details()->with(['asset.assetCategory', 'asset.assetType', 'asset.branch'])
            ->orderBy('id')
            ->get();

        $assets = $stockOpnameDetails->map(function($detail) {
            return $detail->asset;
        });

        // Create new Spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('Asset Management System')
            ->setTitle('Form Stock Opname Hardcopy')
            ->setSubject('Stock Opname Form')
            ->setDescription('Form hardcopy untuk stock opname sebagai backup sistem');

        // Set sheet title
        $sheet->setTitle('Form Stock Opname');

        // Header Information
        $sheet->setCellValue('A1', 'FORM STOCK OPNAME HARDCOPY');
        $sheet->mergeCells('A1:J1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Stock Opname Information
        $sheet->setCellValue('A3', 'Nomor Stock Opname:');
        $sheet->setCellValue('B3', $stockOpname->opname_number);
        $sheet->setCellValue('D3', 'Tanggal Stock Opname:');
        $sheet->setCellValue('E3', $stockOpname->start_date ? $stockOpname->start_date->format('d/m/Y') : date('d/m/Y'));

        $sheet->setCellValue('A4', 'Cabang:');
        $sheet->setCellValue('B4', $stockOpname->branch->name);
        $sheet->setCellValue('D4', 'Status:');
        $sheet->setCellValue('E4', ucfirst($stockOpname->status));

        $sheet->setCellValue('A5', 'Keterangan:');
        $sheet->setCellValue('B5', $stockOpname->description ?: '-');

        // Table Headers
        $headers = [
            'A7' => 'No',
            'B7' => 'Cabang',
            'C7' => 'Nomor Asset',
            'D7' => 'Nama Asset',
            'E7' => 'Tipe Asset',
            'F7' => 'Jenis Asset',
            'G7' => 'Nomor Stock Opname',
            'H7' => 'Tgl Stock Opname',
            'I7' => 'Kondisi Fisik',
            'J7' => 'Keterangan'
        ];

        foreach ($headers as $cell => $header) {
            $sheet->setCellValue($cell, $header);
        }

        // Style headers
        $headerRange = 'A7:J7';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('E3F2FD');
        $sheet->getStyle($headerRange)->getBorders()->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);
        $sheet->getStyle($headerRange)->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        // Data rows
        $row = 8;
        foreach ($assets as $index => $asset) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $asset->branch->name);
            $sheet->setCellValue('C' . $row, $asset->asset_code);
            $sheet->setCellValue('D' . $row, $asset->name);
            $sheet->setCellValue('E' . $row, $asset->assetType->name ?? '-');
            $sheet->setCellValue('F' . $row, $asset->assetCategory->name ?? '-');
            $sheet->setCellValue('G' . $row, $stockOpname->opname_number); // Nomor Stock Opname
            $sheet->setCellValue('H' . $row, $stockOpname->start_date ? $stockOpname->start_date->format('d/m/Y') : date('d/m/Y')); // Tanggal Stock Opname
            $sheet->setCellValue('I' . $row, ''); // Empty for manual input (Kondisi Fisik)
            $sheet->setCellValue('J' . $row, ''); // Empty for manual input (Keterangan)

            // Style data rows
            $dataRange = 'A' . $row . ':J' . $row;
            $sheet->getStyle($dataRange)->getBorders()->getAllBorders()
                ->setBorderStyle(Border::BORDER_THIN);
            $sheet->getStyle('A' . $row)->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_CENTER);

            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'J') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Set minimum column widths
        $sheet->getColumnDimension('C')->setWidth(15); // Nomor Asset
        $sheet->getColumnDimension('D')->setWidth(25); // Nama Asset
        $sheet->getColumnDimension('G')->setWidth(18); // Nomor Stock Opname
        $sheet->getColumnDimension('H')->setWidth(15); // Tanggal Stock Opname
        $sheet->getColumnDimension('I')->setWidth(15); // Kondisi Fisik
        $sheet->getColumnDimension('J')->setWidth(20); // Keterangan

        // Footer instructions
        $lastRow = $row + 2;
        $sheet->setCellValue('A' . $lastRow, 'PETUNJUK PENGISIAN:');
        $sheet->getStyle('A' . $lastRow)->getFont()->setBold(true);

        $lastRow++;
        $sheet->setCellValue('A' . $lastRow, '1. Kondisi Fisik: Baik / Rusak Ringan / Rusak Berat / Hilang');
        $lastRow++;
        $sheet->setCellValue('A' . $lastRow, '2. Keterangan: Catatan tambahan jika diperlukan');
        $lastRow++;
        $sheet->setCellValue('A' . $lastRow, '3. Tandai dengan tanda centang (✓) jika asset ditemukan');

        // Signature section
        $lastRow += 3;
        $sheet->setCellValue('A' . $lastRow, 'Petugas Stock Opname:');
        $sheet->setCellValue('E' . $lastRow, 'Supervisor:');

        $lastRow += 4;
        $sheet->setCellValue('A' . $lastRow, 'Nama: ________________');
        $sheet->setCellValue('E' . $lastRow, 'Nama: ________________');

        $lastRow++;
        $sheet->setCellValue('A' . $lastRow, 'Tanda Tangan: ________________');
        $sheet->setCellValue('E' . $lastRow, 'Tanda Tangan: ________________');

        $lastRow++;
        $sheet->setCellValue('A' . $lastRow, 'Tanggal: ________________');
        $sheet->setCellValue('E' . $lastRow, 'Tanggal: ________________');

        // Generate filename
        $filename = 'Form_Stock_Opname_' . $stockOpname->opname_number . '_' . $stockOpname->branch->code . '_' . date('Y-m-d') . '.xlsx';

        // Create writer and output
        $writer = new Xlsx($spreadsheet);

        // Set headers for download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
}
