<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\DocumentNumber;
use App\Models\Branch;
use App\Models\AssetCategory;
use App\Helpers\BranchHelper;
use Illuminate\Http\Request;

class DocumentNumberController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = DocumentNumber::with(['branch', 'category', 'assetType']);

        // Apply filters
        if ($request->filled('document_type')) {
            $query->where('document_type', $request->document_type);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }

        $documentNumbers = $query->latest()->paginate(15);
        $branches = BranchHelper::getAccessibleBranches();
        $categories = AssetCategory::active()->get();

        // Get unique document types
        $documentTypes = DocumentNumber::distinct()->pluck('document_type');

        return view('master.document-numbers.index', compact('documentNumbers', 'branches', 'categories', 'documentTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = BranchHelper::getAccessibleBranches();
        $categories = AssetCategory::active()->get();
        return view('master.document-numbers.create', compact('branches', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'document_type' => 'required|string|max:255',
            'format' => 'required|string|max:255',
            'current_number' => 'required|integer|min:0',
            'year' => 'required|integer|min:2020|max:2099',
            'branch_id' => 'required|exists:branches,id',
            'asset_category_id' => 'nullable|exists:asset_categories,id',
            'asset_type_id' => 'nullable|exists:asset_types,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Check branch access
        if ($validated['branch_id'] && !BranchHelper::canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        // Check for duplicate
        $exists = DocumentNumber::where('document_type', $validated['document_type'])
            ->where('branch_id', $validated['branch_id'])
            ->where('asset_category_id', $validated['asset_category_id'])
            ->where('asset_type_id', $validated['asset_type_id'])
            ->where('year', $validated['year'])
            ->exists();

        if ($exists) {
            return back()->withErrors(['document_type' => 'Kombinasi tipe dokumen, cabang, kategori, jenis asset, dan tahun sudah ada.']);
        }

        DocumentNumber::create($validated);

        return redirect()->route('master.document-numbers.index')->with('success', 'Document number configuration created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(DocumentNumber $documentNumber)
    {
        // Check branch access
        if ($documentNumber->branch_id && !BranchHelper::canAccessBranch($documentNumber->branch_id)) {
            abort(403, 'You do not have access to this document number.');
        }

        return view('master.document-numbers.show', compact('documentNumber'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DocumentNumber $documentNumber)
    {
        // Check branch access
        if ($documentNumber->branch_id && !BranchHelper::canAccessBranch($documentNumber->branch_id)) {
            abort(403, 'You do not have access to this document number.');
        }

        $branches = BranchHelper::getAccessibleBranches();
        return view('master.document-numbers.edit', compact('documentNumber', 'branches'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DocumentNumber $documentNumber)
    {
        // Check branch access
        if ($documentNumber->branch_id && !BranchHelper::canAccessBranch($documentNumber->branch_id)) {
            abort(403, 'You do not have access to this document number.');
        }

        $validated = $request->validate([
            'document_type' => 'required|string|max:255',
            'prefix' => 'required|string|max:10',
            'format' => 'required|string|max:255',
            'current_number' => 'required|integer|min:0',
            'year' => 'required|integer|min:2020|max:2099',
            'branch_id' => 'nullable|exists:branches,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Check new branch access
        if ($validated['branch_id'] && !BranchHelper::canAccessBranch($validated['branch_id'])) {
            abort(403, 'You do not have access to this branch.');
        }

        // Check for duplicate (excluding current record)
        $exists = DocumentNumber::where('document_type', $validated['document_type'])
            ->where('branch_id', $validated['branch_id'])
            ->where('year', $validated['year'])
            ->where('id', '!=', $documentNumber->id)
            ->exists();

        if ($exists) {
            return back()->withErrors(['document_type' => 'Document number configuration already exists for this type, branch, and year.']);
        }

        $documentNumber->update($validated);

        return redirect()->route('master.document-numbers.index')->with('success', 'Document number configuration updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DocumentNumber $documentNumber)
    {
        // Check branch access
        if ($documentNumber->branch_id && !BranchHelper::canAccessBranch($documentNumber->branch_id)) {
            abort(403, 'You do not have access to this document number.');
        }

        $documentNumber->delete();

        return redirect()->route('master.document-numbers.index')->with('success', 'Document number configuration deleted successfully.');
    }
}
