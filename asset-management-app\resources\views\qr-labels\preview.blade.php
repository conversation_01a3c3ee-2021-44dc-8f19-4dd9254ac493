<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Preview QR Label</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: {{ $config->font_family }}, sans-serif;
            background: #f5f5f5;
        }
        
        .preview-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .preview-info {
            margin-bottom: 20px;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
        }
        
        .label-container {
            border: 2px dashed #ccc;
            padding: 20px;
            background: white;
            display: inline-block;
            margin: 20px auto;
        }
        
        .qr-label {
            width: {{ $config->width }}mm;
            height: {{ $config->height }}mm;
            border: 1px solid #000;
            background: white;
            position: relative;
            display: flex;
            align-items: center;
            padding: {{ $config->margin_top }}mm {{ $config->margin_right }}mm {{ $config->margin_bottom }}mm {{ $config->margin_left }}mm;
        }

        .qr-section {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1mm;
            flex-shrink: 0;
        }

        .qr-code-img {
            width: {{ $config->qr_size }}mm;
            height: {{ $config->qr_size }}mm;
            display: block;
        }

        .separator {
            width: 1px;
            height: {{ $config->qr_size }}mm;
            background-color: #000;
            margin: 0 1.5mm;
            flex-shrink: 0;
        }

        .info-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100%;
            line-height: 1.2;
        }
        
        .asset-name {
            font-size: {{ $config->font_size_title }}pt;
            font-weight: bold;
            margin-bottom: 1.5mm;
            color: #000;
            text-transform: uppercase;
        }

        .asset-code {
            font-size: {{ $config->font_size_content + 1 }}pt;
            font-weight: bold;
            margin-bottom: 1mm;
            color: #000;
            letter-spacing: 0.5px;
        }

        .asset-info {
            font-size: {{ $config->font_size_content }}pt;
            margin-bottom: 0.8mm;
            color: #333;
        }

        .asset-info:last-child {
            margin-bottom: 0;
        }

        .asset-info.category {
            font-style: italic;
            color: #666;
        }

        .asset-info.branch {
            font-weight: 600;
            color: #000;
        }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            .preview-container {
                box-shadow: none;
                padding: 0;
            }
            .preview-info, .controls {
                display: none;
            }
            .label-container {
                border: none;
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-info">
            <h3>Preview Label QR Code</h3>
            <p><strong>Konfigurasi:</strong> {{ $config->name }}</p>
            <p><strong>Ukuran:</strong> {{ $config->width }}mm x {{ $config->height }}mm</p>
            <p><strong>QR Size:</strong> {{ $config->qr_size }}mm</p>
            <p><strong>Font:</strong> {{ $config->font_family }} ({{ $config->font_size_title }}/{{ $config->font_size_content }}px)</p>
        </div>
        
        <div style="text-align: center;">
            <div class="label-container">
                <div class="qr-label">
                    <div class="qr-section">
                        <img src="{{ $qrCodeBase64 }}" alt="QR Code" class="qr-code-img">
                    </div>
                    <div class="separator"></div>
                    <div class="info-section">
                        @if($config->show_asset_name)
                            <div class="asset-name">{{ $sampleAsset->name }}</div>
                        @endif

                        @if($config->show_asset_code)
                            <div class="asset-code">{{ $sampleAsset->asset_code }}</div>
                        @endif

                        @if($config->show_category)
                            <div class="asset-info category">{{ $sampleAsset->assetCategory->name }}</div>
                        @endif

                        @if($config->show_branch)
                            <div class="asset-info branch">{{ $sampleAsset->branch->name }}</div>
                        @endif

                        @if($config->show_location)
                            <div class="asset-info">{{ $sampleAsset->location }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="window.print()" class="btn btn-primary">
                Print Preview
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                Tutup
            </button>
        </div>
    </div>
</body>
</html>
