<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SupplierController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Supplier::query();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('supplier_code', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        if ($request->filled('province')) {
            $query->where('province', 'like', "%{$request->province}%");
        }

        if ($request->filled('business_type')) {
            $query->where('business_type', $request->business_type);
        }

        if ($request->filled('payment_terms')) {
            $query->where('payment_terms', $request->payment_terms);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $suppliers = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $cities = Supplier::distinct()->pluck('city')->filter()->sort();
        $provinces = Supplier::distinct()->pluck('province')->filter()->sort();
        $businessTypes = Supplier::getBusinessTypeOptions();
        $paymentTerms = Supplier::getPaymentTermsOptions();

        return view('suppliers.index', compact('suppliers', 'cities', 'provinces', 'businessTypes', 'paymentTerms'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $supplierCode = Supplier::generateSupplierCode();
        $businessTypes = Supplier::getBusinessTypeOptions();
        $paymentTerms = Supplier::getPaymentTermsOptions();

        return view('suppliers.create', compact('supplierCode', 'businessTypes', 'paymentTerms'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'city' => 'nullable|string|max:100',
            'province' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'tax_number' => 'nullable|string|max:50',
            'npwp_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'business_type' => 'nullable|string|max:50',
            'payment_terms' => 'required|in:cash,credit_7,credit_14,credit_30,credit_45,credit_60,credit_90',
            'credit_limit' => 'nullable|numeric|min:0|max:999999999999.99',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean'
        ], [
            'name.required' => 'Nama supplier wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'npwp_file.file' => 'File NPWP harus berupa file.',
            'npwp_file.mimes' => 'File NPWP harus berformat PDF, JPG, JPEG, atau PNG.',
            'npwp_file.max' => 'Ukuran file NPWP maksimal 2MB.',
            'contact_email.email' => 'Format email kontak tidak valid.',
            'website.url' => 'Format website tidak valid.',
            'payment_terms.required' => 'Syarat pembayaran wajib dipilih.',
            'credit_limit.numeric' => 'Limit kredit harus berupa angka.',
            'credit_limit.min' => 'Limit kredit tidak boleh negatif.'
        ]);

        try {
            DB::beginTransaction();

            // Handle NPWP file upload
            if ($request->hasFile('npwp_file')) {
                $file = $request->file('npwp_file');
                $fileName = time() . '_' . $file->getClientOriginalName();

                // Create directory if not exists
                $uploadPath = 'public/suppliers/npwp';
                if (!Storage::exists($uploadPath)) {
                    Storage::makeDirectory($uploadPath);
                }

                // Store file
                $file->storeAs($uploadPath, $fileName);
                $validated['npwp_file'] = $fileName;
            }

            $supplier = Supplier::create($validated);

            DB::commit();

            return redirect()->route('suppliers.show', $supplier)
                           ->with('success', 'Supplier berhasil ditambahkan.');

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error creating supplier: ' . $e->getMessage());

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Terjadi kesalahan saat menyimpan supplier.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Supplier $supplier)
    {
        return view('suppliers.show', compact('supplier'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Supplier $supplier)
    {
        $businessTypes = Supplier::getBusinessTypeOptions();
        $paymentTerms = Supplier::getPaymentTermsOptions();

        return view('suppliers.edit', compact('supplier', 'businessTypes', 'paymentTerms'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Supplier $supplier)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'city' => 'nullable|string|max:100',
            'province' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'tax_number' => 'nullable|string|max:50',
            'npwp_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'business_type' => 'nullable|string|max:50',
            'payment_terms' => 'required|in:cash,credit_7,credit_14,credit_30,credit_45,credit_60,credit_90',
            'credit_limit' => 'nullable|numeric|min:0|max:999999999999.99',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean'
        ], [
            'name.required' => 'Nama supplier wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'npwp_file.file' => 'File NPWP harus berupa file.',
            'npwp_file.mimes' => 'File NPWP harus berformat PDF, JPG, JPEG, atau PNG.',
            'npwp_file.max' => 'Ukuran file NPWP maksimal 2MB.',
            'contact_email.email' => 'Format email kontak tidak valid.',
            'website.url' => 'Format website tidak valid.',
            'payment_terms.required' => 'Syarat pembayaran wajib dipilih.',
            'credit_limit.numeric' => 'Limit kredit harus berupa angka.',
            'credit_limit.min' => 'Limit kredit tidak boleh negatif.'
        ]);

        try {
            DB::beginTransaction();

            // Handle NPWP file upload
            if ($request->hasFile('npwp_file')) {
                // Delete old file if exists
                if ($supplier->npwp_file) {
                    $oldFilePath = storage_path('app/public/suppliers/npwp/' . $supplier->npwp_file);
                    if (file_exists($oldFilePath)) {
                        unlink($oldFilePath);
                    }
                }

                $file = $request->file('npwp_file');
                $fileName = time() . '_' . $file->getClientOriginalName();

                // Create directory if not exists
                $uploadPath = 'public/suppliers/npwp';
                if (!Storage::exists($uploadPath)) {
                    Storage::makeDirectory($uploadPath);
                }

                // Store file
                $file->storeAs($uploadPath, $fileName);
                $validated['npwp_file'] = $fileName;
            }

            $supplier->update($validated);

            DB::commit();

            return redirect()->route('suppliers.show', $supplier)
                           ->with('success', 'Supplier berhasil diperbarui.');

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error updating supplier: ' . $e->getMessage());

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Terjadi kesalahan saat memperbarui supplier.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Supplier $supplier)
    {
        try {
            DB::beginTransaction();

            // Check if supplier is being used in other tables
            // Add checks here when implementing purchase orders, etc.

            // Delete NPWP file if exists
            if ($supplier->npwp_file) {
                $filePath = storage_path('app/public/suppliers/npwp/' . $supplier->npwp_file);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }

            $supplier->delete();

            DB::commit();

            return redirect()->route('suppliers.index')
                           ->with('success', 'Supplier berhasil dihapus.');

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error deleting supplier: ' . $e->getMessage());

            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan saat menghapus supplier.');
        }
    }

    /**
     * Toggle supplier status
     */
    public function toggleStatus(Supplier $supplier)
    {
        try {
            $supplier->update(['is_active' => !$supplier->is_active]);

            $status = $supplier->is_active ? 'diaktifkan' : 'dinonaktifkan';

            return redirect()->back()
                           ->with('success', "Supplier berhasil {$status}.");

        } catch (\Exception $e) {
            \Log::error('Error toggling supplier status: ' . $e->getMessage());

            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan saat mengubah status supplier.');
        }
    }

    /**
     * Download NPWP file
     */
    public function downloadNpwpFile(Supplier $supplier)
    {
        if (!$supplier->npwp_file) {
            return redirect()->back()
                           ->with('error', 'File NPWP tidak ditemukan.');
        }

        $filePath = storage_path('app/public/suppliers/npwp/' . $supplier->npwp_file);

        if (!file_exists($filePath)) {
            return redirect()->back()
                           ->with('error', 'File NPWP tidak ditemukan di server.');
        }

        return response()->download($filePath);
    }

    /**
     * Delete NPWP file
     */
    public function deleteNpwpFile(Supplier $supplier)
    {
        try {
            if ($supplier->npwp_file) {
                $filePath = storage_path('app/public/suppliers/npwp/' . $supplier->npwp_file);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }

                $supplier->update(['npwp_file' => null]);

                return redirect()->back()
                               ->with('success', 'File NPWP berhasil dihapus.');
            }

            return redirect()->back()
                           ->with('error', 'Tidak ada file NPWP untuk dihapus.');

        } catch (\Exception $e) {
            \Log::error('Error deleting NPWP file: ' . $e->getMessage());

            return redirect()->back()
                           ->with('error', 'Terjadi kesalahan saat menghapus file NPWP.');
        }
    }
}
