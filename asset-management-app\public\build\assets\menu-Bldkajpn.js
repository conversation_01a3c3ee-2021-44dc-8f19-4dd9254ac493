const u=["transitionend","webkitTransitionEnd","oTransitionEnd"];class s{constructor(e,n={},t=null){this._el=e,this._animate=n.animate!==!1,this._accordion=n.accordion!==!1,this._closeChildren=!!n.closeChildren,this._onOpen=n.onOpen||(()=>{}),this._onOpened=n.onOpened||(()=>{}),this._onClose=n.onClose||(()=>{}),this._onClosed=n.onClosed||(()=>{}),this._psScroll=null,this._topParent=null,this._menuBgClass=null,e.classList.add("menu"),e.classList[this._animate?"remove":"add"]("menu-no-animation"),e.classList.add("menu-vertical");const i=t||window.PerfectScrollbar;i?(this._scrollbar=new i(e.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!s._hasClass("layout-menu-fixed layout-menu-fixed-offcanvas")}),window.Helpers.menuPsScroll=this._scrollbar):e.querySelector(".menu-inner").classList.add("overflow-auto");const l=e.classList;for(let o=0;o<l.length;o++)l[o].startsWith("bg-")&&(this._menuBgClass=l[o]);e.setAttribute("data-bg-class",this._menuBgClass),this._bindEvents(),e.menuInstance=this}_bindEvents(){this._evntElClick=e=>{if(e.target.closest("ul")&&e.target.closest("ul").classList.contains("menu-inner")){const t=s._findParent(e.target,"menu-item",!1);t&&(this._topParent=t.childNodes[0])}const n=e.target.classList.contains("menu-toggle")?e.target:s._findParent(e.target,"menu-toggle",!1);n&&(e.preventDefault(),n.getAttribute("data-hover")!=="true"&&this.toggle(n))},window.Helpers.isMobileDevice&&this._el.addEventListener("click",this._evntElClick),this._evntWindowResize=()=>{this.update(),this._lastWidth!==window.innerWidth&&(this._lastWidth=window.innerWidth,this.update());const e=document.querySelector("[data-template^='horizontal-menu']");!this._horizontal&&!e&&this.manageScroll()},window.addEventListener("resize",this._evntWindowResize)}static childOf(e,n){if(e.parentNode){for(;(e=e.parentNode)&&e!==n;);return!!e}return!1}_unbindEvents(){this._evntElClick&&(this._el.removeEventListener("click",this._evntElClick),this._evntElClick=null),this._evntElMouseOver&&(this._el.removeEventListener("mouseover",this._evntElMouseOver),this._evntElMouseOver=null),this._evntElMouseOut&&(this._el.removeEventListener("mouseout",this._evntElMouseOut),this._evntElMouseOut=null),this._evntWindowResize&&(window.removeEventListener("resize",this._evntWindowResize),this._evntWindowResize=null),this._evntBodyClick&&(document.body.removeEventListener("click",this._evntBodyClick),this._evntBodyClick=null),this._evntInnerMousemove&&(this._inner.removeEventListener("mousemove",this._evntInnerMousemove),this._evntInnerMousemove=null),this._evntInnerMouseleave&&(this._inner.removeEventListener("mouseleave",this._evntInnerMouseleave),this._evntInnerMouseleave=null)}static _isRoot(e){return!s._findParent(e,"menu-item",!1)}static _findParent(e,n,t=!0){if(e.tagName.toUpperCase()==="BODY")return null;for(e=e.parentNode;e.tagName.toUpperCase()!=="BODY"&&!e.classList.contains(n);)e=e.parentNode;if(e=e.tagName.toUpperCase()!=="BODY"?e:null,!e&&t)throw new Error(`Cannot find \`.${n}\` parent element`);return e}static _findChild(e,n){const t=e.childNodes,i=[];for(let l=0,o=t.length;l<o;l++)if(t[l].classList){let r=0;for(let a=0;a<n.length;a++)t[l].classList.contains(n[a])&&(r+=1);n.length===r&&i.push(t[l])}return i}static _findMenu(e){let n=e.childNodes[0],t=null;for(;n&&!t;)n.classList&&n.classList.contains("menu-sub")&&(t=n),n=n.nextSibling;if(!t)throw new Error("Cannot find `.menu-sub` element for the current `.menu-toggle`");return t}static _hasClass(e,n=window.Helpers.ROOT_EL){let t=!1;return e.split(" ").forEach(i=>{n.classList.contains(i)&&(t=!0)}),t}open(e,n=this._closeChildren){const t=this._findUnopenedParent(s._getItem(e,!0),n);if(!t)return;const i=s._getLink(t,!0);s._promisify(this._onOpen,this,t,i,s._findMenu(t)).then(()=>{!this._horizontal||!s._isRoot(t)?this._animate&&!this._horizontal?(window.requestAnimationFrame(()=>this._toggleAnimation(!0,t,!1)),this._accordion&&this._closeOther(t,n)):this._animate?this._onOpened&&this._onOpened(this,t,i,s._findMenu(t)):(t.classList.add("open"),this._onOpened&&this._onOpened(this,t,i,s._findMenu(t)),this._accordion&&this._closeOther(t,n)):this._onOpened&&this._onOpened(this,t,i,s._findMenu(t))}).catch(()=>{})}close(e,n=this._closeChildren,t=!1){const i=s._getItem(e,!0),l=s._getLink(e,!0);!i.classList.contains("open")||i.classList.contains("disabled")||s._promisify(this._onClose,this,i,l,s._findMenu(i),t).then(()=>{if(!this._horizontal||!s._isRoot(i))if(this._animate&&!this._horizontal)window.requestAnimationFrame(()=>this._toggleAnimation(!1,i,n));else{if(i.classList.remove("open"),n){const o=i.querySelectorAll(".menu-item.open");for(let r=0,a=o.length;r<a;r++)o[r].classList.remove("open")}this._onClosed&&this._onClosed(this,i,l,s._findMenu(i))}else this._onClosed&&this._onClosed(this,i,l,s._findMenu(i))}).catch(()=>{})}_closeOther(e,n){const t=s._findChild(e.parentNode,["menu-item","open"]);for(let i=0,l=t.length;i<l;i++)t[i]!==e&&this.close(t[i],n)}toggle(e,n=this._closeChildren){const t=s._getItem(e,!0);t.classList.contains("open")?this.close(t,n):this.open(t,n)}static _getItem(e,n){let t=null;const i=n?"menu-toggle":"menu-link";if(e.classList.contains("menu-item")?s._findChild(e,[i]).length&&(t=e):e.classList.contains(i)&&(t=e.parentNode.classList.contains("menu-item")?e.parentNode:null),!t)throw new Error(`${n?"Toggable ":""}\`.menu-item\` element not found.`);return t}static _getLink(e,n){let t=[];const i=n?"menu-toggle":"menu-link";if(e.classList.contains(i)?t=[e]:e.classList.contains("menu-item")&&(t=s._findChild(e,[i])),!t.length)throw new Error(`\`${i}\` element not found.`);return t[0]}_findUnopenedParent(e,n){let t=[],i=null;for(;e;)e.classList.contains("disabled")?(i=null,t=[]):(e.classList.contains("open")||(i=e),t.push(e)),e=s._findParent(e,"menu-item",!1);if(!i)return null;if(t.length===1)return i;t=t.slice(0,t.indexOf(i));for(let l=0,o=t.length;l<o;l++)if(t[l].classList.add("open"),this._accordion){const r=s._findChild(t[l].parentNode,["menu-item","open"]);for(let a=0,h=r.length;a<h;a++)if(r[a]!==t[l]&&(r[a].classList.remove("open"),n)){const d=r[a].querySelectorAll(".menu-item.open");for(let c=0,m=d.length;c<m;c++)d[c].classList.remove("open")}}return i}_toggleAnimation(e,n,t){const i=s._getLink(n,!0),l=s._findMenu(n);s._unbindAnimationEndEvent(n);const o=Math.round(i.getBoundingClientRect().height);n.style.overflow="hidden";const r=()=>{n.classList.remove("menu-item-animating"),n.classList.remove("menu-item-closing"),n.style.overflow=null,n.style.height=null,this.update()};e?(n.style.height=`${o}px`,n.classList.add("menu-item-animating"),n.classList.add("open"),s._bindAnimationEndEvent(n,()=>{r(),this._onOpened(this,n,i,l)}),setTimeout(()=>{n.style.height=`${o+Math.round(l.getBoundingClientRect().height)}px`},50)):(n.style.height=`${o+Math.round(l.getBoundingClientRect().height)}px`,n.classList.add("menu-item-animating"),n.classList.add("menu-item-closing"),s._bindAnimationEndEvent(n,()=>{if(n.classList.remove("open"),r(),t){const a=n.querySelectorAll(".menu-item.open");for(let h=0,d=a.length;h<d;h++)a[h].classList.remove("open")}this._onClosed(this,n,i,l)}),setTimeout(()=>{n.style.height=`${o}px`},50))}static _bindAnimationEndEvent(e,n){const t=l=>{l.target===e&&(s._unbindAnimationEndEvent(e),n(l))};let i=window.getComputedStyle(e).transitionDuration;i=parseFloat(i)*(i.indexOf("ms")!==-1?1:1e3),e._menuAnimationEndEventCb=t,u.forEach(l=>e.addEventListener(l,e._menuAnimationEndEventCb,!1)),e._menuAnimationEndEventTimeout=setTimeout(()=>{t({target:e})},i+50)}_getItemOffset(e){let n=this._inner.childNodes[0],t=0;for(;n!==e;)n.tagName&&(t+=Math.round(n.getBoundingClientRect().width)),n=n.nextSibling;return t}static _promisify(e,...n){const t=e(...n);return t instanceof Promise?t:t===!1?Promise.reject():Promise.resolve()}get _innerWidth(){const e=this._inner.childNodes;let n=0;for(let t=0,i=e.length;t<i;t++)e[t].tagName&&(n+=Math.round(e[t].getBoundingClientRect().width));return n}get _innerPosition(){return parseInt(this._inner.style[this._rtl?"marginRight":"marginLeft"]||"0px",10)}set _innerPosition(e){return this._inner.style[this._rtl?"marginRight":"marginLeft"]=`${e}px`,e}static _unbindAnimationEndEvent(e){const n=e._menuAnimationEndEventCb;e._menuAnimationEndEventTimeout&&(clearTimeout(e._menuAnimationEndEventTimeout),e._menuAnimationEndEventTimeout=null),n&&(u.forEach(t=>e.removeEventListener(t,n,!1)),e._menuAnimationEndEventCb=null)}closeAll(e=this._closeChildren){const n=this._el.querySelectorAll(".menu-inner > .menu-item.open");for(let t=0,i=n.length;t<i;t++)this.close(n[t],e)}static setDisabled(e,n){s._getItem(e,!1).classList[n?"add":"remove"]("disabled")}static isActive(e){return s._getItem(e,!1).classList.contains("active")}static isOpened(e){return s._getItem(e,!1).classList.contains("open")}static isDisabled(e){return s._getItem(e,!1).classList.contains("disabled")}update(){this._scrollbar&&this._scrollbar.update()}manageScroll(){const{PerfectScrollbar:e}=window,n=document.querySelector(".menu-inner");if(window.innerWidth<window.Helpers.LAYOUT_BREAKPOINT)this._scrollbar!==null&&(this._scrollbar.destroy(),this._scrollbar=null),n.classList.add("overflow-auto");else{if(this._scrollbar===null){const t=new e(document.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!1});this._scrollbar=t}n.classList.remove("overflow-auto")}}destroy(){if(!this._el)return;this._unbindEvents();const e=this._el.querySelectorAll(".menu-item");for(let t=0,i=e.length;t<i;t++)s._unbindAnimationEndEvent(e[t]),e[t].classList.remove("menu-item-animating"),e[t].classList.remove("open"),e[t].style.overflow=null,e[t].style.height=null;const n=this._el.querySelectorAll(".menu-menu");for(let t=0,i=n.length;t<i;t++)n[t].style.marginRight=null,n[t].style.marginLeft=null;this._el.classList.remove("menu-no-animation"),this._wrapper&&(this._prevBtn.parentNode.removeChild(this._prevBtn),this._nextBtn.parentNode.removeChild(this._nextBtn),this._wrapper.parentNode.insertBefore(this._inner,this._wrapper),this._wrapper.parentNode.removeChild(this._wrapper),this._inner.style.marginLeft=null,this._inner.style.marginRight=null),this._el.menuInstance=null,delete this._el.menuInstance,this._el=null,this._animate=null,this._accordion=null,this._closeChildren=null,this._onOpen=null,this._onOpened=null,this._onClose=null,this._onClosed=null,this._scrollbar&&(this._scrollbar.destroy(),this._scrollbar=null),this._inner=null,this._prevBtn=null,this._wrapper=null,this._nextBtn=null}}window.Menu=s;
