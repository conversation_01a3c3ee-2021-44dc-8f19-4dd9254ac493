@extends('layouts.contentNavbarLayout')

@section('title', 'Tambah User - Asset Management System')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
  <h4 class="fw-bold py-3 mb-4">
    <span class="text-muted fw-light">Master Data / Master User /</span> Tambah User
  </h4>

  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Form Tambah User</h5>
          <a href="{{ route('master.users.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Kembali
          </a>
        </div>
        <div class="card-body">
          <form action="{{ route('master.users.store') }}" method="POST">
            @csrf
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="name"><PERSON><PERSON> <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name') }}" 
                       placeholder="Contoh: John Doe">
                @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="username">Username <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('username') is-invalid @enderror" 
                       id="username" name="username" value="{{ old('username') }}" 
                       placeholder="Contoh: johndoe">
                @error('username')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Username harus unik dan akan digunakan untuk login</div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="email">Email <span class="text-danger">*</span></label>
              <input type="email" class="form-control @error('email') is-invalid @enderror" 
                     id="email" name="email" value="{{ old('email') }}" 
                     placeholder="Contoh: <EMAIL>">
              @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="row mb-3">
              <div class="col-md-6">
                <label class="form-label" for="password">Password <span class="text-danger">*</span></label>
                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                       id="password" name="password" placeholder="Minimal 8 karakter">
                @error('password')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-6">
                <label class="form-label" for="password_confirmation">Konfirmasi Password <span class="text-danger">*</span></label>
                <input type="password" class="form-control" 
                       id="password_confirmation" name="password_confirmation" 
                       placeholder="Ulangi password">
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-md-4">
                <label class="form-label" for="role_id">Role <span class="text-danger">*</span></label>
                <select class="form-select @error('role_id') is-invalid @enderror" id="role_id" name="role_id">
                  <option value="">Pilih Role</option>
                  @foreach($roles as $role)
                    <option value="{{ $role->id }}" {{ old('role_id') == $role->id ? 'selected' : '' }}>
                      {{ $role->name }}
                    </option>
                  @endforeach
                </select>
                @error('role_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="branch_id">Cabang <span class="text-danger">*</span></label>
                <select class="form-select @error('branch_id') is-invalid @enderror" id="branch_id" name="branch_id">
                  <option value="">Pilih Cabang</option>
                  @foreach($branches as $branch)
                    <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                      {{ $branch->name }} ({{ $branch->code }})
                    </option>
                  @endforeach
                </select>
                @error('branch_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              <div class="col-md-4">
                <label class="form-label" for="division_id">Divisi</label>
                <select class="form-select @error('division_id') is-invalid @enderror" id="division_id" name="division_id">
                  <option value="">Pilih Divisi (Opsional)</option>
                  @foreach($divisions as $division)
                    <option value="{{ $division->id }}" {{ old('division_id') == $division->id ? 'selected' : '' }}>
                      {{ $division->name }} ({{ $division->code }})
                    </option>
                  @endforeach
                </select>
                @error('division_id')
                  <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label" for="phone">Nomor Telepon</label>
              <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                     id="phone" name="phone" value="{{ old('phone') }}" 
                     placeholder="Contoh: 08123456789">
              @error('phone')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                       {{ old('is_active', true) ? 'checked' : '' }}>
                <label class="form-check-label" for="is_active">
                  Aktif
                </label>
              </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
              <a href="{{ route('master.users.index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i>Batal
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>Simpan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Panduan Pengisian</h6>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">Tips:</h6>
            <ul class="mb-0">
              <li><strong>Username:</strong> Gunakan format yang mudah diingat dan unik</li>
              <li><strong>Password:</strong> Minimal 8 karakter, kombinasi huruf dan angka</li>
              <li><strong>Role:</strong> Tentukan hak akses sesuai dengan tanggung jawab</li>
              <li><strong>Cabang:</strong> Pilih cabang tempat user bekerja</li>
            </ul>
          </div>
          
          <div class="alert alert-warning">
            <h6 class="alert-heading">Perhatian:</h6>
            <ul class="mb-0">
              <li>Username dan email harus unik</li>
              <li>Password akan dienkripsi secara otomatis</li>
              <li>User hanya bisa mengakses data sesuai cabang yang dipilih</li>
            </ul>
          </div>

          <div class="alert alert-secondary">
            <h6 class="alert-heading">Role yang Tersedia:</h6>
            <ul class="mb-0">
              @foreach($roles as $role)
                <li><strong>{{ $role->name }}:</strong> {{ $role->description ?: 'Role untuk ' . $role->name }}</li>
              @endforeach
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto generate username from name
  const nameInput = document.getElementById('name');
  const usernameInput = document.getElementById('username');
  
  nameInput.addEventListener('input', function() {
    if (!usernameInput.value) {
      const username = this.value.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '')
        .substring(0, 20);
      usernameInput.value = username;
    }
  });

  // Format phone number
  const phoneInput = document.getElementById('phone');
  phoneInput.addEventListener('input', function() {
    this.value = this.value.replace(/[^0-9+\-\s]/g, '');
  });

  // Password strength indicator
  const passwordInput = document.getElementById('password');
  const confirmPasswordInput = document.getElementById('password_confirmation');
  
  confirmPasswordInput.addEventListener('input', function() {
    if (this.value && this.value !== passwordInput.value) {
      this.setCustomValidity('Password tidak cocok');
    } else {
      this.setCustomValidity('');
    }
  });
});
</script>
@endsection
